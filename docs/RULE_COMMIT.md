# Commit Message Guidelines

This document outlines the rules for writing commit messages to maintain consistency and clarity in our project history. Each commit message should be structured with a specific type and scope to convey the purpose of the changes. Below are the commit message types we use:

## Commit Message Types

### build

Changes that affect the build system or external dependencies.

- **Example scopes**: gulp, broccoli, npm
- **Example**: `build: update npm dependencies`

### ci

Changes to our Continuous Integration (CI) configuration files and scripts.

- **Example scopes**: GitLab CI, Circle, BrowserStack, SauceLabs
- **Example**: `ci: add BrowserStack configuration`

### chore

Miscellaneous tasks that do not touch production code, such as updating npm dependencies.

- **Example**: `chore: update npm dependencies`

### docs

Documentation only changes.

- **Example**: `docs: update README with setup instructions`

### feat

A new feature.

- **Example**: `feat: add user authentication`

### fix

A bug fix.

- **Example**: `fix: correct login error handling`

### perf

A code change that improves performance.

- **Example**: `perf: optimize image loading`

### refactor

A code change that neither fixes a bug nor adds a feature.

- **Example**: `refactor: reorganize user model`

### revert

Reverts a previous commit.

- **Example**: `revert: undo feature add user authentication`

### style

Changes that do not affect the meaning of the code, such as formatting, adding white-space, or missing semi-colons.

- **Example**: `style: fix formatting in user model`

### test

Adding missing tests or correcting existing tests.

- **Example**: `test: add unit tests for authentication service`

Each commit message should follow this format to ensure clarity and consistency in our project history.
