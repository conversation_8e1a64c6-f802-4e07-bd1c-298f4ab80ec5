const fabric = require('@umijs/fabric');

module.exports = {
  extends: [require.resolve('@umijs/fabric/dist/eslint')],
  globals: {
    ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION: true,
    page: true,
    REACT_APP_ENV: true,
  },
  rules: {
    // 'react/jsx-no-literals': ['error'],
    // 'max-lines': ['error', { max: 500, skipBlankLines: true, skipComments: true }],
    // 'max-lines-per-function': ['error', { max: 50, skipBlankLines: true, skipComments: true }],
    'react/no-array-index-key': 0,
    'react-hooks/exhaustive-deps': 0,
  },
};
