import fileDownload from 'js-file-download';
import moment from 'moment';
import { parse } from 'querystring';
import { dateFormat, equipOptionRentalCar, equipParamRentalCar, planItemStatus } from './constants';
import adoBrandSVG from '@/assets/flight/ado.svg';
import anaBrandSV<PERSON> from '@/assets/flight/ana.svg';
import apjBrandSVG from '@/assets/flight/apj.svg';
import jalBrandSVG from '@/assets/flight/jal.svg';
import jjpBrandSVG from '@/assets/flight/jjp.svg';
import sfjBrandSVG from '@/assets/flight/sfj.svg';
import skyBrandSVG from '@/assets/flight/sky.svg';
import { isArray, isEmpty } from 'lodash';

const reg =
  /(((^https?:(?:\/\/)?)(?:[-;:&=\+\$,\w]+@)?[A-Za-z0-9.-]+(?::\d+)?|(?:www.|[-;:&=\+\$,\w]+@)[A-Za-z0-9.-]+)((?:\/[\+~%\/.\w-_]*)?\??(?:[-\+=&;%@.\w_]*)#?(?:[\w]*))?)$/;
export const isUrl = (path) => reg.test(path);
export const isAntDesignPro = () => {
  if (ANT_DESIGN_PRO_ONLY_DO_NOT_USE_IN_YOUR_PRODUCTION === 'site') {
    return true;
  }

  return window.location.hostname === 'preview.pro.ant.design';
}; // 给官方演示站点用，用于关闭真实开发环境不需要使用的特性

export const isAntDesignProOrDev = () => {
  const { NODE_ENV } = process.env;

  if (NODE_ENV === 'development') {
    return true;
  }

  return isAntDesignPro();
};
export const getPageQuery = () => parse(window.location.href.split('?')[1]);

export function isEmail(emailAddress) {
  let regexEmail = /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/;
  if (emailAddress.match(regexEmail)) {
    return true;
  } else {
    return false;
  }
}

export const disableDateRanges = (range = { startDate: false, endDate: false }) => {
  const { startDate, endDate } = range;

  return function disabledDate(current) {
    let startCheck = true;
    let endCheck = true;
    if (startDate) {
      startCheck = current && current < moment(startDate, 'YYYY-MM-DD HH:mm');
    }
    if (endDate) {
      endCheck = current && current > moment(endDate, 'YYYY-MM-DD HH:mm');
    }
    return (startDate && startCheck) || (endDate && endCheck);
  };
};

export const renderStyleCard = (isFirstItem, isCenterItem, isLastItem) => {
  if (isFirstItem) {
    return {
      borderBottom: 0,
      borderBottomLeftRadius: 0,
      borderBottomRightRadius: 0,
    };
  } else if (isCenterItem) {
    return {
      borderBottom: 0,
      borderRadius: 0,
    };
  } else if (isLastItem) {
    return {
      borderTopLeftRadius: 0,
      borderTopRightRadius: 0,
    };
  }
};

export const checkDateValid = (year = null, month = null, day = null) => {
  const date = moment(`${year}/${month}/${day}`, 'YYYY/M/D', true);

  const isValid = date?.isValid();

  return isValid;
};

export const checkYearMonthValid = (year = null, month = null) => {
  const date = moment(`${year}/${month}`, 'YYYY/M', true);

  const isValid = date?.isValid();

  return isValid;
};

export const renderKatakanaName = (user) => {
  return user?.katakana_1 && user?.katakana_2 ? `${user?.katakana_1} ${user?.katakana_2}` : null;
};

export const renderKanjiName = (user) => {
  return user?.kanji_1 && user?.kanji_2 ? `${user?.kanji_1} ${user?.kanji_2}` : null;
};

export const renderRomajiName = (user) => {
  return user?.romaji_1 && user?.romaji_2 ? `${user?.romaji_1} ${user?.romaji_2}` : null;
};

export const renderNameFirstNamePriority = (user) => {
  if (user?.kanji_1 && user?.kanji_2) {
    return renderKanjiName(user);
  } else if (user?.katakana_1 && user?.katakana_2) {
    return renderKatakanaName(user);
  } else if (user?.romaji_1 && user?.romaji_2) {
    return renderRomajiName(user);
  } else {
    return '';
  }
};

export const formatOptionsPlanlistRentalCar = (options) => {
  let initArray = ['', '', '', '', '', '', '', ''];

  options?.forEach((value, index) => {
    initArray[value - 1] = value;
  });

  return initArray?.join(',');
};

export const formatEquipParamRentalCar = (options) => {
  if (
    options?.includes(equipOptionRentalCar?.nonSmoking) &&
    options?.includes(equipOptionRentalCar?.smoking)
  ) {
    return null;
  } else if (options?.includes(equipOptionRentalCar?.nonSmoking)) {
    return equipParamRentalCar?.nonSmoking;
  } else if (options?.includes(equipOptionRentalCar?.smoking)) {
    return equipParamRentalCar?.smoking;
  }
  return null;
};

export const checkOptionCarActive = (options) => {
  if (options.length > 0 && !options?.some((ele) => ele?.optpric == 0)) {
    return true;
  }
  return false;
};

export const checkOptionETCCarActive = (equipments) => {
  const findItems = equipments?.filter((ele) => ele?.veqcode == '0004');

  if (equipments.length > 0 && findItems?.length > 0) {
    return true;
  }
  return false;
};

export const checkOptionNaviCarActive = (equipments) => {
  const findItems = equipments?.filter((ele) => ele?.veqcode == '0003');
  if (equipments.length > 0 && findItems?.length > 0) {
    return true;
  }
  return false;
};

export const renderFromNowTime = (startTime) => {
  const currentDate = moment();

  const diff = currentDate.diff(moment(startTime, dateFormat.defaultDate), 'days');
  if (diff > 1) {
    return moment(startTime).format(dateFormat.date);
  } else {
    return moment(startTime).fromNow();
  }
};

export const getNameDayOfWeek = (day) => {
  const days = {
    0: '日',
    1: '月',
    2: '火',
    3: '水',
    4: '木',
    5: '金',
    6: '土',
  };

  return days?.[day] || '';
};

export const isValidPassword = (value) => {
  const containsUppercase = /[A-Z]/.test(value);
  const containsLowercase = /[a-z]/.test(value);
  const containsNumber = /[0-9]/.test(value);
  const containsSpecialCharacter = /[!@#$%^&*()_+\-=[\]{}|'"/\\.,`<>:;?~]/.test(value);
  return containsUppercase && containsLowercase && containsNumber && containsSpecialCharacter;
};

export const convertParagraph = (para) => {
  // split br
  let arrayNotSpace = para.split(' ');

  arrayNotSpace = arrayNotSpace?.map((itemNotSpace) => {
    let itemNotBreak = itemNotSpace?.split('\n');

    itemNotBreak = itemNotBreak?.map((item) => {
      if (item?.includes('https://') || item?.includes('http://')) {
        return `<a href="${item}" target="_blank">${item}</a>`;
      } else {
        return item;
      }
    });

    return itemNotBreak?.join('\n');
  });

  arrayNotSpace = arrayNotSpace?.join(' ');

  return arrayNotSpace.replaceAll('\n', '<br/>');
};

export const buildHotKeys = (e) => {
  const key = e?.key?.toLowerCase();
  if (['shift', 'meta', 'alt', 'control'].includes(key)) {
    return key;
  }
  let hotKeyPattern = '';
  if (e.altKey) {
    hotKeyPattern += 'alt+';
  }
  if (e.ctrlKey) {
    hotKeyPattern += 'ctrl+';
  }
  if (e.metaKey && !e.ctrlKey) {
    hotKeyPattern += 'meta+';
  }
  if (e.shiftKey) {
    hotKeyPattern += 'shift+';
  }
  hotKeyPattern += key;
  return hotKeyPattern;
};

// utils file
export const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

export const fileSizeInMegaBytes = (bytes) => {
  return bytes / (1024 * 1024);
};
export const checkFileSizeLimit = (file, maximumUploadLimit) => {
  const fileSize = file?.file?.size || file?.size;
  const fileSizeInMB = fileSizeInMegaBytes(fileSize);
  return fileSizeInMB <= maximumUploadLimit;
};

export const getFileNameFromChatwoot = (url) => {
  let arr = url?.split('/');

  let name = decodeURI(arr?.[arr?.length - 1]);
  return name || 'file-error';
};

export const downloadImage = async (imageSrc) => {
  try {
    const { data, response } = {};

    if (response?.status == 201 || response?.status == 200) {
      fileDownload(data, getFileNameFromChatwoot(imageSrc));
    }
  } catch (error) {
    console.log({ error });
  }
};

export const generateBlobUrl = async (imageSrc) => {
  try {
    const { data, response } = {};

    if (response?.status == 201 || response?.status == 200) {
      const file = new Blob([data], { type: 'application/pdf' });
      const blobUrl = URL.createObjectURL(file);

      return blobUrl;
    }
  } catch (error) {
    console.log({ error });
  }
};

export const renderNameBooking = (booking) => {
  let title = '';
  let timeStr = '';

  if (booking?.step_2?.booking_destinations) {
    const destinationArray = [];

    booking?.step_2?.booking_destinations?.map((des) => {
      if (des?.destination_name) {
        destinationArray?.push(des?.destination_name);
      }
    });

    title = `${booking?.step_2?.departure_info?.name} - ${
      booking?.step_2?.booking_destinations?.[0]?.ken_info?.name
        ? booking?.step_2?.booking_destinations?.[0]?.ken_info?.name
        : null
    }`;
  } else if (booking?.title) {
    title = booking?.title;
  }

  if (
    booking?.step_2?.booking_destinations &&
    booking?.step_2?.start_date &&
    booking?.step_2?.end_date
  ) {
    let numDay = moment(booking?.step_2?.end_date)
      .add(1, 'days')
      .diff(moment(booking?.step_2?.start_date), 'days');
    let numNight = moment(booking?.step_2?.end_date).diff(
      moment(booking?.step_2?.start_date),
      'days',
    );

    timeStr = `${numNight}泊${numDay}日`;
  }

  return `${title} ${timeStr}`;
};

export const getStringAmenities = (amenities) => {
  const specialIDs = [2191, 2192, 1073742787];
  if (!amenities) return '朝食なし';

  const amenitiesArray = Object.values(amenities);
  if (!amenitiesArray?.length) return '朝食なし';

  // // Filter array has different id with special id
  const newAmenitiesArray = amenitiesArray?.filter(
    (ele) => +ele?.id && !specialIDs.includes(+ele?.id),
  );

  if (!newAmenitiesArray?.length) return '朝食なし';

  // Get uniqueNames
  const uniqueNames = newAmenitiesArray
    ?.map((ele) => ele?.name)
    ?.filter((name, index, self) => index === self.indexOf(name));
  return uniqueNames?.join(', ');
};

export const formatCurrency = (price) => {
  return Number(price || 0).toLocaleString('ja-JP');
};

export const getTextPlanItemStatusButton = (status) => {
  let textPlanItemStatusButton;
  switch (status) {
    case planItemStatus?.FULL_INFORMATION:
      textPlanItemStatusButton = '予約者情報を更新する';
      break;
    case planItemStatus?.INFO_REQUIRED:
      textPlanItemStatusButton = '予約者情報を保存する';
      break;
    case planItemStatus?.BOOKED:
      textPlanItemStatusButton = 'この予約をキャンセルする';
      break;
    case planItemStatus?.BOOK_FAIL:
      textPlanItemStatusButton = '予約失敗';
      break;
    case planItemStatus?.CANCEL_WITH_REFUND:
      textPlanItemStatusButton = 'キャンセル/返金あり';
      break;
    case planItemStatus?.CANCEL_NO_REFUND:
      textPlanItemStatusButton = 'キャンセル/返金不可';
      break;
    case planItemStatus?.UNAVAILABLE:
      textPlanItemStatusButton = '満室';
      break;
    default:
      textPlanItemStatusButton = '';
  }
  return textPlanItemStatusButton;
};

export const getPathImageBrandFlight = (serviceFlight) => {
  let path = '';
  switch (serviceFlight) {
    case 'ADO':
      path = adoBrandSVG;
      break;
    case 'ANA':
      path = anaBrandSVG;
      break;
    case 'APJ':
      path = apjBrandSVG;
      break;
    case 'JAL':
      path = jalBrandSVG;
      break;
    case 'JJP':
      path = jjpBrandSVG;
      break;
    case 'SFJ':
      path = sfjBrandSVG;
      break;
    case 'SKY':
      path = skyBrandSVG;
      break;
    default:
      path = '';
  }
  return path;
};

// area data of station and airport in rental car tab
export const getTreeDataByPrefectureInRentalCarTab = (list) => {
  const groupAreaByPrefectureName = _.groupBy(list, 'prfname');

  const treeData = _.map(groupAreaByPrefectureName, (innerList, key) => {
    const children = _.map(innerList, (item) => {
      return { title: item?.ascname, value: item?.asccode };
    });
    return { title: key, value: key, children: children };
  });

  return treeData;
};

export const formatProvincesBeforeFilter = (provinces) => {
  const kenNameArray = [];
  const subAreaNameArray = [];
  const meshNameArray = [];
  for (let provinceKey in provinces) {
    const provinceName = provinces?.[provinceKey]?.province_name;
    const subAreas = provinces?.[provinceKey]?.sub_areas;
    if (isEmpty(subAreas) && provinceName) {
      kenNameArray.push(provinceName);
      continue;
    }
    for (let subAreaKey in subAreas) {
      const subAreaName = subAreas?.[subAreaKey]?.sub_area_name;
      const meshs = subAreas?.[subAreaKey]?.meshs;
      if (isEmpty(meshs) && subAreaName) {
        subAreaNameArray.push(subAreaName);
        continue;
      }
      for (let meshKey in meshs) {
        const meshName = meshs?.[meshKey]?.mesh_name;
        if (meshName) meshNameArray.push(meshName);
      }
    }
  }
  return { kenNameArray, subAreaNameArray, meshNameArray };
};

// data airport in flight tab
export const getTreeDataAirportByPrefectureInFlightTab = (airports, codeSelected) => {
  const groupAreaByPrefectureName = _.groupBy(airports, 'airport_prefecture');

  const treeData = _.map(groupAreaByPrefectureName, (innerList, key) => {
    const children = _.map(innerList, (item) => {
      return {
        title: item?.airport_name,
        value: item?.airport_code,
        disabled: item?.airport_code === codeSelected,
      };
    });
    return { title: key, value: key, children: children };
  });

  return treeData;
};

export const formatRoomInfo = (roomInfo) => {
  // room_info example: "2-5,2|2-3,1"
  const rooms = roomInfo?.split('|')?.map((room) => {
    const adults = +room?.split('-')?.[0];
    const childrenString = room?.split('-')?.[1];
    let childrens = 0;
    let children_ages = [];
    if (childrenString) {
      children_ages = childrenString?.split(',')?.map((age) => +age);
      childrens = children_ages?.length;
    }
    return {
      adults,
      childrens,
      children_ages,
    };
  });

  return rooms;
};

export const renderTimeFlightDirect = (depTime, desTime) => {
  const depMoment = moment(depTime, 'HH:mm');
  const desMoment = moment(desTime, 'HH:mm');

  const timeDifference = desMoment.diff(depMoment);

  const formattedTimeDifference = moment.utc(timeDifference).format('HH:mm');
  const hours = moment(formattedTimeDifference, 'HH:mm').hours();
  const minutes = moment(formattedTimeDifference, 'HH:mm').minutes();

  return `${hours > 0 ? `${hours} 時間 ` : ''}${minutes}分`;
};

export const getAirportNameByCode = (airportCode, masterDataAirport) => {
  const airport = masterDataAirport?.find((ele) => ele?.airport_code === airportCode);
  return `${airport?.airport_name || ''}`;
};

export const getErrorMessagePayment = (resultCode) => {
  let error = '';
  switch (resultCode) {
    case 100:
      error = 'カード番号必須チェックエラー';
      break;
    case 101:
      error = 'カード番号フォーマットエラー(数字以外を含む)';
      break;
    case 102:
      error = 'カード番号フォーマットエラー(10-16 桁の範囲外)';
      break;
    case 110:
      error = '有効期限必須チェックエラー';
      break;
    case 111:
      error = '有効期限フォーマットエラー(数字以外を含む)';
      break;
    case 112:
      error = '有効期限フォーマットエラー(6 又は 4 桁以外)';
      break;
    case 113:
      error = '有効期限フォーマットエラー(月が 13 以上)';
      break;
    case 121:
      error = 'セキュリティコードフォーマットエラー(数字以外を含む';
      break;
    case 122:
      error = 'セキュリティコード桁数エラー';
      break;
    case 551:
      error = 'トークン用 apikey が存在しない ID';
      break;
    default:
      error = '購入処理中にエラーが発生しました';
      break;
  }
  return error;
};

export const flatMapDataFlights = (dataFlights) => {
  if (!isArray(dataFlights)) return [];
  return dataFlights?.flatMap((flightItem) =>
    flightItem?.ticket?.map((ticketItem) => ({ ...flightItem, ticket: { ...ticketItem } })),
  );
};

export const getTextSeatTypeBySeatCode = (seatCode) => {
  let text = '';
  switch (seatCode) {
    case 'F':
      text = 'ファーストクラス';
      break;
    case 'P':
      text = 'プレミアムクラス';
      break;
    case 'J':
      text = 'クラス';
      break;
    case 'Y':
      text = 'その他';
      break;
    default:
      text = '';
  }
  return text;
};

export const maxDayInMonth = (year, month) => {
  let maxDayInMonth = 31;
  if (+month === 4 || +month === 6 || +month === 9 || +month === 11) maxDayInMonth = 30;
  else if (+month === 2) {
    if (+year % 4 === 0 && (+year % 100 !== 0 || +year % 400 === 0)) {
      maxDayInMonth = 29;
    } else {
      maxDayInMonth = 28;
    }
  }

  return maxDayInMonth;
};
