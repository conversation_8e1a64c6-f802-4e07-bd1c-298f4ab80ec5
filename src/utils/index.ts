import moment from 'moment';

export interface BaseOptionType {
  label: string;
  value: string;
  id: number;
}

export const validateValue = (_, value) => {
  if (!value?.value) {
    return Promise.reject('※必須項目が未入力です。');
  }

  return Promise.resolve();
};

export function formatPercent(
  num: string | number | undefined | null,
  fix?: number,
  symbol?: string,
): string {
  if (num === 0) return '0.00' + (symbol || '');
  if (!num) return '';
  let formattedNumber = num;
  if (typeof num === 'string') {
    const number = parseFloat(num);
    if (!isNaN(number)) {
      formattedNumber = number.toFixed(fix || 2);
    }
  } else {
    if (!isFinite(num)) return '0.00' + (symbol || '');
    formattedNumber = num.toFixed(fix || 2);
  }
  return formattedNumber.toString() + (symbol || '');
}

export function formatMoney(money: number): string {
  try {
    if (isNaN(money)) return '';
    return money.toFixed(0).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  } catch (error) {
    return '';
  }
}

export function formatInvoiceNumber(stt: number): string {
  try {
    if (stt < 10) {
      return '00' + stt;
    } else if (stt < 100) {
      return '0' + stt;
    }
    return stt.toString();
  } catch (error) {
    console.log('error formatInvoiceNumber', error);
    return '';
  }
}

export function roundNumber(num: number) {
  if (num > 0) {
    return Math.round(num);
  } else {
    return Math.round(num * -1) * -1;
  }
}

export function getDatesInRange(startDate, endDate) {
  const date = new Date(startDate.getTime());

  const dates = [];

  while (date <= endDate) {
    dates.push(moment(date).format('YYYY-MM-DD'));
    date.setDate(date.getDate() + 1);
  }

  return dates;
}

export const groupBy = (key) => (array) =>
  array.reduce((objectsByKeyValue, obj) => {
    const value = obj[key];
    objectsByKeyValue[value] = (objectsByKeyValue[value] || []).concat(obj);
    return objectsByKeyValue;
  }, {});

export const groupByLargeGerneId = groupBy('large_genre_id');
export const groupByAreaId = groupBy('area_id');
export const groupByProvinceId = groupBy('province_id');
export const groupBySubAreaId = groupBy('sub_area_id');
export const groupByKenId = groupBy('ken_id');
export const groupByPlanDay = groupBy('day');
export const groupByDate = groupBy('date');
export const groupByPlanDate = groupBy('date');
export const groupByReferenceID = groupBy('reference_id');
export const groupByReferenceType = groupBy('reference_type');
export const groupByMiddleGerneId = groupBy('middle_genre_id');

export function generateRandomId() {
  // return Math.random().toString(36).substr(2, 9);
  const now = Date.now().toString(36); // Chuyển sang chuỗi base-36 để gọn hơn
  const randomStr = Math.random().toString(36).substring(2, 8); // Lấy 6 ký tự từ chuỗi ngẫu nhiên

  return now + randomStr;
}

export function countRangeDate(_startDate: string, _endDate: string) {
  console.log(_startDate, _endDate);

  const startDate = new Date(_startDate);
  const endDate = new Date(_endDate);
  const diffTime = Math.abs(endDate.valueOf() - startDate.valueOf());
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  return diffDays + 1;
}

// Hàm để tách các phần tử riêng biệt dựa trên key
export const getUniqueItemsByKey = <T, K extends keyof T>(array: T[], key: K): T[] => {
  const map = new Map<T[K], T>();
  array.forEach((item) => {
    if (!map.has(item[key])) {
      map.set(item[key], item);
    }
  });
  return Array.from(map.values());
};

export const toHalfWidth = (str: string) => {
  try {
    return str
      ?.replace(/[\uFF01-\uFF5E]/g, (ch) => String.fromCharCode(ch.charCodeAt(0) - 0xfee0))
      ?.replace(/\u3000/g, ' ');
  } catch (error) {
    console.log('toHalfWidth error', str, error);
    return str;
  }
};

export const filterBusinessPartner = (input: string | number, option: BaseOptionType) => {
  try {
    const halfWidthValue = toHalfWidth(input?.toString()).toLowerCase();
    const result =
      (toHalfWidth(option?.label as string)?.toLowerCase() ?? '').includes(halfWidthValue) ||
      (toHalfWidth(option?.value as string)?.toLowerCase() ?? '').includes(halfWidthValue);
    return result;
  } catch (error) {
    return false;
  }
};
