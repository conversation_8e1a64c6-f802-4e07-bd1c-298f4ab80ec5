export const referenceTypes = {
  anchor: 'anchor',
  hotel: 'hotel',
  spot: 'spot',
  flight: 'flight',
  car: 'car',
  tabione: 'tabione',
};

export const referenceTypesTitle = {
  hotel: 'ホテル',
  spot: '現地ツアー',
};

export const favoriteActions = {
  like: 'like',
  unlike: 'unlike',
};

export const genderData = [
  { id: 1, name: 'Male' },
  { id: 2, name: 'Female' },
];

export const genderEnglishData = [
  { id: 1, name: 'Male' },
  { id: 2, name: 'Female' },
];

export const genderText = {
  1: { id: 1, name: '男性' },
  2: { id: 2, name: '女性' },
};

export const relationshipData = [
  {
    id: 1,
    name: '夫婦',
    value: 1,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  {
    id: 2,
    name: '子供',
    value: 2,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  {
    id: 3,
    name: '親',
    value: 3,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  {
    id: 4,
    name: '祖父母',
    value: 4,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  {
    id: 5,
    name: '親族',
    value: 5,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  {
    id: 6,
    name: '恋人・パートナー',
    value: 6,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  {
    id: 7,
    name: '友人',
    value: 7,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  {
    id: 8,
    name: '団体・コミュニティ',
    value: 8,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  {
    id: 9,
    name: 'その他',
    value: 9,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
];

export const relationshipText = {
  1: {
    id: 1,
    name: '夫婦',
    value: 1,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  2: {
    id: 2,
    name: '子供',
    value: 2,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  3: {
    id: 3,
    name: '親',
    value: 3,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  4: {
    id: 4,
    name: '祖父母',
    value: 4,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  5: {
    id: 5,
    name: '親族',
    value: 5,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  6: {
    id: 6,
    name: '恋人・パートナー',
    value: 6,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  7: {
    id: 7,
    name: '友人',
    value: 7,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  8: {
    id: 8,
    name: '団体・コミュニティ',
    value: 8,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
  9: {
    id: 9,
    name: 'その他',
    value: 9,
    created_at: '2023-02-03T01:50:19.000000Z',
    updated_at: '2023-02-03T01:50:19.000000Z',
  },
};

export const destinationTypes = {
  mesh: 'mesh',
  waterfall: 'waterfall',
};

export const dateFormat = {
  defaultDate: 'YYYY/MM/DD',
  dateTime: 'YYYY/MM/DD hh:mm:ss a',
  date: 'YYYY/MM/DD hh:mm a',
  dateTimeString: 'YYYYMMDDHHMM',
  dateTimeStringYYYYMMDD: 'YYYYMMDD',
};

export const placeDetail = {};

export const sortHotelData = [
  // 'desc': true
  // 'asc': false
  {
    title: 'おすすめ順',
    value: JSON.stringify({
      value: true,
      by: 'expedia_formula',
    }),
  },
  {
    title: '料金高い順',
    value: JSON.stringify({
      value: true,
      by: 'price',
    }),
  },
  {
    title: '料金安い順',
    value: JSON.stringify({
      value: false,
      by: 'price',
    }),
  },
  {
    title: '評価高い順',
    value: JSON.stringify({
      value: true,
      by: 'ratings_guest_overall',
    }),
  },
  {
    title: 'お気に入り',
    value: JSON.stringify({
      value: true,
      by: 'favorites',
    }),
  },
];

export const planItemStatus = {
  FULL_INFORMATION: 1,
  INFO_REQUIRED: 2,
  BOOKED: 3,
  BOOK_FAIL: 4,
  CANCEL_WITH_REFUND: 5,
  CANCEL_NO_REFUND: 6,
  UNAVAILABLE: 7,
  RESERVE: 8,
};

export const planStatus = {
  SELECTED: 1,
  INFO_FILLED: 2,
  MISSING_INFO: 3,
  DISABLED: 4,
  PLAN_BOOKED: 5,
};

export const bookingStatus = {
  1: '作成中',
  2: '作成中',
  3: '旅程確定',
  4: '変更依頼',
  5: '予約完了',
  6: '決済完了',
  7: '催行済み',
  8: '決済失敗',
  9: '予約失敗',
};

export const reciprocalStatusBookingArray = [
  {
    id: 1,
    name: '対応中',
  },
  {
    id: 2,
    name: '保留',
  },
  {
    id: 3,
    name: '離脱',
  },
  {
    id: 4,
    name: '完了',
  },
];

export const reciprocalStatusBooking = {
  1: '対応中',
  2: '保留',
  3: '離脱',
  4: '完了',
};

export const maxPlanCount = 3;

export const airports = [
  {
    asccode: 'AP0001',
    ascname: 'Kagoshima Airport',
    prfcode: '46',
    prfname: 'KAGOSHIMA',
    seccode: '46003',
    linecode: null,
    mainflag: '1',
  },
  {
    asccode: 'AP0001',
    ascname: 'Yamagata Airport',
    prfcode: '06',
    prfname: 'YAMAGATA',
    seccode: '06002',
    linecode: null,
    mainflag: '1',
  },
];

export const stations = [
  {
    asccode: 'ST0046',
    ascname: 'Okushiri Airport',
    prfcode: '01',
    prfname: 'HOKKAIDO',
    seccode: '01003',
    linecode: null,
    mainflag: '1',
  },
  {
    asccode: 'ST0046',
    ascname: 'Kansai Airport',
    prfcode: '27',
    prfname: 'OSAKA',
    seccode: '27004',
    linecode: null,
    mainflag: '1',
  },
  {
    asccode: 'ST0046',
    ascname: 'Aomori Port',
    prfcode: '02',
    prfname: 'AOMORI',
    seccode: '02001',
    linecode: null,
    mainflag: '1',
  },
  {
    asccode: 'ST0046',
    ascname: 'Shimonoseki Port',
    prfcode: '35',
    prfname: 'YAMAGUCHI',
    seccode: '35004',
    linecode: null,
    mainflag: '1',
  },
];

export const equipParamRentalCar = {
  smoking: 0,
  nonSmoking: 1,
};

export const equipOptionRentalCar = {
  smoking: '01',
  nonSmoking: '02',
};

export const equipDataRentalCar = [
  {
    veqgrup: '01',
    veqname: '喫煙',
  },
  {
    veqgrup: '02',
    veqname: '禁煙',
  },
  {
    veqgrup: '03',
    veqname: '免責補償込',
  },
  {
    veqgrup: '04',
    veqname: 'カーナビ',
  },
  {
    veqgrup: '05',
    veqname: 'ETC車載器',
  },
  {
    veqgrup: '06',
    veqname: '4WD',
  },
  {
    veqgrup: '07',
    veqname: '送迎',
  },
  {
    veqgrup: '08',
    veqname: 'スタッドレス',
  },
];

export const avatarType = {
  avatar: 'avatar',
  plan: 'plan',
};

export const rentalCarImageRoot = 'https://www2.web-rentacar.com/photo/';

export const messageType = {
  sent: 1,
  received: 0,
};

export const companyStatus = {
  used: 1,
  unused: 2,
};

export const MESSAGE_STATUS = {
  FAILED: 'failed',
  SENT: 'sent',
  DELIVERED: 'delivered',
  READ: 'read',
  PROGRESS: 'progress',
};

export const getRelationshipText = (value, relationships) => {
  let relationship = relationships?.find((ele) => ele?.id == value);

  return relationship?.name;
};

export const occupationAccount = {
  adminOwner: 1, // super admin
  internalAdmin: 2,
  outernalAdmin: 3, //
};

export const ROLE_OPTIONS = [
  { value: occupationAccount.adminOwner, label: '管理者' },
  { value: occupationAccount.internalAdmin, label: '内部スタッフ' },
  { value: occupationAccount.outernalAdmin, label: '外部スタッフ' },
];

export const ITEM_PER_PAGE = '30';
export const ITEM_PER_PAGE_PLAN_ITEM = 20;
export const SORT_ORDER_DEFAULT = 'desc';
export const SORT_FIELD_DEFAULT = 'created_at';

export const PKIND_FLIGHT_OPTIONS = [
  { label: '普通席のみ', value: '1' },
  { label: 'アップグレード席を含む', value: '2' },
];

export const BRAND_FLIGHT_OPTIONS = [
  { label: 'ANA', value: 'ana' },
  { label: 'JAL', value: 'jal' },
  { label: 'ADO', value: 'ado' },
  { label: 'SFJ', value: 'sfj' },
  { label: 'SNA', value: 'sna' },
  { label: 'FDA', value: 'fda' },
  { label: 'IBX', value: 'ibx' },
];

export const SEAT_TYPE_FLIGHT_OPTIONS = [
  { label: 'ファーストクラス', value: 'F' },
  { label: 'プレミアムクラス', value: 'P' },
  { label: 'クラス', value: 'J' },
  { label: 'その他', value: 'Y' },
];

export const FLIGHT_TYPE = {
  oneWay: 'one_way',
  roundTrip: 'round_trip',
};

export const FLIGHT_TIME_OPTIONS = [
  { label: '全て', value: '0-24' },
  { label: '午前', value: '0-11' },
  { label: '12:00 ~', value: '12-13' },
  { label: '14:00 ~', value: '14-15' },
  { label: '16:00 ~', value: '16-17' },
  { label: '18:00 ~', value: '18-24' },
];

export const AGES_NUMBER = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17];
export const AGES_FLIGHT_NUMBER = [3, 4, 5, 6, 7, 8, 9, 10, 11];

export const DEFAULT_CENTER = {
  lat: 26.341094,
  lng: 127.82865,
};

export const CREDIT_CARD_OPTIONS = [
  { id: 'visa', name: 'Visa' },
  { id: 'jcb', name: 'JCB' },
  { id: 'amex', name: 'AMEX' },
  { id: 'ダイナース', name: 'ダイナース' },
  { id: 'マスター', name: 'マスター' },
];

export const MAX_SIZE_TERMS_CONDITION_PDF = 20; // 20MB

export const OCCUPATION_TYPE = 'admin';

export const ARRAY_OF_YEARS = [...Array(new Date().getFullYear() - 1899).keys()].map(
  (year) => `${year + 1900}`,
);

export const ARRAY_OF_MONTHS_IN_YEAR = [...Array(12).keys()].map((month) => `${month + 1}`);

export const ARRAY_OF_DAYS_IN_MONTH = [...Array(31).keys()].map((day) => `${day + 1}`);

export const SUPORTED_LANGUAGES = [
  {
    value: 'English',
    name: 'English',
  },
  {
    value: 'Spanish',
    name: 'Spanish',
  },
];

export const MONTH = [
  { label: '1月', value: '1' },
  { label: '2月', value: '2' },
  { label: '3月', value: '3' },
  { label: '4月', value: '4' },
  { label: '5月', value: '5' },
  { label: '6月', value: '6' },
  { label: '7月', value: '7' },
  { label: '8月', value: '8' },
  { label: '9月', value: '9' },
  { label: '10月', value: '10' },
  { label: '11月', value: '11' },
  { label: '12月', value: '12' },
];
export const sortOrderOption = [
  {
    value: 'asc',
    label: '昇順',
  },
  {
    value: 'desc',
    label: '降順',
  },
];

export const HotelRank = [
  {
    value: 1,
    label: '1★',
  },
  {
    value: 2,
    label: '2★',
  },
  {
    value: 3,
    label: '3★',
  },
  {
    value: 4,
    label: '4★',
  },
  {
    value: 5,
    label: '5★',
  },
];

export const PRICE_LIST_TITLE = '料金形態';
export const CANCEL_PRICE_TITLE = '取消料';
