export function getPermission() {
  const authorityString =
    typeof str === 'undefined' && localStorage ? localStorage.getItem('permissions') : str; // authorityString could be admin, "admin", ["admin"]

  let authority;

  try {
    if (authorityString) {
      authority = JSON.parse(authorityString);
    }
  } catch (e) {
    authority = authorityString;
  }

  if (typeof authority === 'string' || typeof authority === 'number') {
    return [authority];
  }
  return authority;
}

export function removeAuthority() {
  localStorage.removeItem('authority');
  localStorage.removeItem('permissions');
  localStorage.removeItem('accessToken');
  window.location.href = '/user/login';
}

export function setToken(newToken) {
  localStorage.setItem('accessToken', newToken);
}

export function getToken() {
  return localStorage.getItem('accessToken');
}
