/** Request 网络请求工具 更详细的 api 文档: https://github.com/umijs/umi-request */
// import { extend } from 'umi-request';
// import { notification } from 'antd';
import { getToken, removeAuthority } from '@/utils/authority';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import axios from 'axios';

type RequestOptions = AxiosRequestConfig;

// config axios- fix CORS
axios.defaults.baseURL = process.env.API_URL;
axios.defaults.headers.post['Content-Type'] = 'application/json';
axios.defaults.headers.post['Access-Control-Allow-Origin'] = '*';
axios.defaults.headers.get['Access-Control-Allow-Origin'] = '*';
axios.defaults.headers.delete['Content-Type'] = 'application/json';
axios.defaults.headers.put['Content-Type'] = 'application/json';

// add Locale
axios.defaults.headers['Locale'] = 'ja';

function getCookie(value) {
  const reg = new RegExp(`(^| )${value}=([^;]*)(;|$)`);
  const arr = document.cookie.match(reg);
  try {
    if (arr) {
      return unescape(arr[2]);
    }
  } catch (error) {
    return null;
  }
  return null;
}
export const instance: AxiosInstance = axios.create({
  baseURL: process.env.API_URL,
  withCredentials: false,
});

instance.interceptors.request //REQUEST
  .use(
    async function (config) {
      const access_token = await getToken();
      const headers = { ...config.headers };
      if (access_token) {
        headers['Authorization'] = `Bearer ${access_token}`;
      }
      headers['X-Requested-With'] = 'XMLHttpRequest';
      headers.Accept = 'application/json';
      headers['Content-Type'] = 'application/json';
      headers['X-XSRF-TOKEN'] = getCookie('XSRF-TOKEN');
      config.headers = {
        ...headers,
        ...config?.headers,
      };

      config.params = {
        ...config.params,
      };
      return config;
    },
    (error) => {
      if (error && error.request) {
      }
      return Promise.reject(error);
    },
  );

instance.interceptors.response.use(
  (response) => {
    return {
      response,
      data: response.data,
      status: response.status,
      error: undefined,
    };
  },
  (error) => {
    console.log('error', error);
    if (error?.response?.status == 401) {
      removeAuthority();
    }

    if (error.message == `Cannot read properties of undefined (reading 'access_token')`) {
      removeAuthority();
    }
    return { response: error, error: error.response, status: error?.response?.status };
  },
);

const request = async <T>(
  url: string,
  options?: RequestOptions,
): Promise<AxiosResponse<T> & { error?: any }> => {
  return instance.request({ ...options, url: url });
};

export default request;
