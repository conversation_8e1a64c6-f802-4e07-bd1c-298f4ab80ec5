import type { TourGuideResource } from '@/@types/tourGuide';
import { removeUndefinedValuesFromObject } from '@/utils/object';

class TourGuideDto {
  createTourGuide(data: TourGuideResource): TourGuideResource {
    // convert gender, nationality to number
    const values = {
      ...data,
      gender: data.gender ? Number(data.gender) : undefined,
      nationality: data.nationality ? Number(data.nationality) : undefined,
    };
    return removeUndefinedValuesFromObject(values);
  }
}

export default TourGuideDto;
