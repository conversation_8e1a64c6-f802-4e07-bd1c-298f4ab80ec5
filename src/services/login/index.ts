import type { TourGuideResource } from '@/@types/tourGuide';
import TourGuideDto from './dto';
import STATUS_CODE from '@/constants/statusCode';
import { history } from 'umi';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';

class TourGuideService {
  private tourGuideDto: TourGuideDto;

  constructor() {
    this.tourGuideDto = new TourGuideDto();
  }

  async createTourGuide(data: TourGuideResource) {
    const tourGuide = this.tourGuideDto.createTourGuide(data);

    // try {
    //   const response = await apiCreateTourGuide(tourGuide);
    //   if (response.status === STATUS_CODE.SUCCESSFUL) {
    //     history.goBack();
    //     openNotificationSuccess('新規作成に成功しました');
    //   } else {
    //     openNotificationFail('新規作成に失敗しました');
    //   }
    // } catch (err) {
    //   openNotificationFail('新規作成に失敗しました');
    // }
  }
}

export default new TourGuideService();
