import type { Plan, PlanInfo, UpdatePlan } from '@/@types/plan';
import type { PlanInfoPayload } from '@/apis/plan';
import { getListPlan, updateDuplicatePlan, updatePlan, updatePlanInfo } from '@/apis/plan';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import STATUS_CODE from '@/constants/statusCode';
import { formatDateYMD } from '@/utils/date';
import moment from 'moment';
import React, { createContext, useCallback, useEffect, useState } from 'react';

export type PlanCreateContextProps = {
  plans?: Plan[];
  planInfo?: PlanInfo;
  activePlanTab?: number;
  activeTabOptions?: number;
  planTabs?: PlanTab[];
  setDataPlanTabs?: (data: PlanTab[]) => void;
  isPlanDaysNotAvailable?: boolean;
  fetchPlans?: () => void;
  countDayRange?: number;

  onUpdatePlanInfor?: (data: PlanInfoPayload) => void;
  onPlanUpdate?: (isModelCourse: boolean, callback?: () => void) => void;
  onDuplicatePlan?: (isModelCourse: boolean, callback?: () => void) => void;
  onAddPlan?: (plan: Plan) => void;
  onRemovePlan?: (plan: Plan) => void;
  onChangePlanTab?: (key: number | string) => void;
  onChangeTabOptions?: (key: number | string) => void;
  updatePositionPlans?: (plans: Plan[]) => void;
  onImportOldPlanToNewPlan?: (_plan: PlanTab[]) => void;
  changeDatePlanInfor?: (startDate: string, endDate: string) => void;
};

type PlanCreateProviderProps = {
  children: React.ReactNode;
  planId: string;
};

export type PlanTab = {
  day: number;
  date?: string;
  plans: Plan[];
};

export const PlanCreateContext = createContext<PlanCreateContextProps>({});

function PlanCreateProvider({ children, planId }: PlanCreateProviderProps) {
  const [plans, setPlans] = useState<Plan[]>([]);
  const [planInfo, setPlanInfo] = useState<PlanInfo>(null);
  const [activePlanTab, setActivePlanTab] = useState<number>(1);
  const [activeTabOptions, setActiveTabOptions] = useState<number>(1);
  const [planTabs, setPlanTabs] = useState<PlanTab[]>([]);
  const [isPlanDaysNotAvailable, setIsPlanDaysNotAvailable] = useState<boolean>(true);
  const [countDayRange, setCountDayRange] = useState<number>();

  const fetchPlans = async () => {
    try {
      const response = await getListPlan(planId);

      setPlans(response.data?.data?.plan_items);
      setPlanInfo(response.data?.data?.plan);
    } catch (err) {
      console.error('Failed to fetch plans: ', err);
    }
  };

  // if exists tab day null plan item => disable button save
  const checkIsPlanDayNotAvailable = (planTabRequest: any) => {
    if (countDayRange && planTabRequest.length !== countDayRange) {
      setIsPlanDaysNotAvailable(true);
      return true; // count day !== current tab length
    }

    for (let i = 0; i < planTabRequest.length; i++) {
      if (planTabRequest[i].plans.length > 0) {
        setIsPlanDaysNotAvailable(false);
      } else {
        setIsPlanDaysNotAvailable(true);
        break;
      }
    }

    return isPlanDaysNotAvailable;
  };

  const getListPlanTabs = useCallback(() => {
    if (!planInfo) return;
    const startDate = new Date(planInfo?.start_date);
    const endDate = new Date(planInfo?.end_date);
    const diffTime = Math.abs(endDate.valueOf() - startDate.valueOf());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    setCountDayRange(diffDays + 1);

    const list = Array(diffDays + 1)
      .fill(0)
      .map((_i, index) => {
        const nextDate = new Date(startDate);
        nextDate.setDate(startDate.getDate() + index);
        const date = formatDateYMD(nextDate);

        return {
          day: index + 1,
          date: date,
          plans: plans?.filter((plan) =>
            plan?.day ? plan?.day === index + 1 : plan?.date === date,
          ),
        };
      });

    setPlanTabs(list);
    checkIsPlanDayNotAvailable(list);
  }, [plans, planInfo]);

  const onUpdatePlanInfor = async (data: PlanInfoPayload) => {
    try {
      const { status } = await updatePlanInfo(planInfo.itinerary_id, data, planInfo.id);
      if (status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        fetchPlans();
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const onPlanUpdate = async (isModelCourse: boolean, callback?: () => void) => {
    try {
      // const result = planTabs.reduce((acc, plan) => [...acc, ...plan.plans], []);
      const result = planTabs.reduce((acc, plan) => {
        const planFormated = plan.plans?.map((val, index) => {
          return {
            ...val,
            date: plan.date,
            day: plan.day, // add field day
          };
        });

        return [...acc, ...planFormated];
      }, []);

      const resultSortOrder = result?.map((item, index) => ({
        ...item,
        sort_order: index,
      }));

      const data = {
        item_array: resultSortOrder,
        is_model_course: isModelCourse,
      } as UpdatePlan;
      await updatePlanInfo(planInfo.itinerary_id, { ...planInfo }, planInfo.id);
      await updatePlan(planInfo?.id, data);
      openNotificationSuccess('旅程表作成に成功しました。');
      if (callback) {
        callback();
      }
    } catch (error) {
      console.log('error', error);
      openNotificationFail('旅程表作成に失敗しました。');
    }
  };

  const onDuplicatePlan = async (isModelCourse: boolean, callback?: () => void) => {
    try {
      const result = planTabs.reduce((acc, plan) => {
        const planFormated = plan.plans?.map((val) => {
          return {
            ...val,
            date: plan.date,
            day: plan.day,
          };
        });

        return [...acc, ...planFormated];
      }, []);

      const resultSortOrder = result?.map((item, index) => ({
        ...item,
        sort_order: index,
      }));

      const data = {
        item_array: resultSortOrder,
        is_model_course: isModelCourse,
      } as UpdatePlan;
      await updatePlanInfo(planInfo.itinerary_id, { ...planInfo }, planInfo.id);
      await updateDuplicatePlan(planInfo?.id, data);
      openNotificationSuccess('旅程表作成に成功しました。');
      if (callback) {
        callback();
      }
    } catch (error) {
      console.log('error', error);
      openNotificationFail('旅程表作成に失敗しました。');
    }
  };

  const onChangePlanTab = (key: number | string) => {
    setActivePlanTab(Number(key));
  };

  const onChangeTabOptions = (key: number | string) => {
    setActiveTabOptions(Number(key));
  };

  const onAddPlan = (plan: Plan) => {
    let isDisableBtnAddPlanItem = false;
    const addedData = planTabs.map((tab) => {
      let planData = tab.plans;
      if (tab.day === activePlanTab) {
        const tabPlanItemExits = tab.plans.filter((item) => {
          return (
            item.reference_id == plan.reference_id && item.reference_type == plan.reference_type
          );
        });

        if (!tabPlanItemExits?.length) {
          planData = [...tab.plans, plan];
        } else {
          isDisableBtnAddPlanItem = true;
        }
      }
      return {
        ...tab,
        plans: planData,
      };
    });

    if (isDisableBtnAddPlanItem) {
      openNotificationFail('1日1回しか選択できません。別の項目を選んでください。');
    }

    checkIsPlanDayNotAvailable(addedData);
    setPlanTabs(addedData);
  };

  const onRemovePlan = (planToRemove: Plan) => {
    const updatedData = planTabs.map((tab) => {
      const filteredPlans = tab.plans.filter((item) => {
        if (tab.day === activePlanTab) {
          return item.id !== planToRemove.id;
        }
        return true;
      });

      return {
        ...tab,
        plans: filteredPlans,
      };
    });

    checkIsPlanDayNotAvailable(updatedData);
    setPlanTabs(updatedData);
  };

  const updatePositionPlans = (plansUpdate: Plan[]) => {
    const updatedPosition = planTabs.map((tab) => {
      let planData = tab.plans;
      if (tab.day === activePlanTab) {
        planData = [...plansUpdate];
      }
      return {
        ...tab,
        plans: planData,
      };
    });

    setPlanTabs(updatedPosition);
  };

  // function to update current plantabs to planTabs of booking past
  const onImportOldPlanToNewPlan = (_plan: PlanTab[]) => {
    const currentArrPlanTabs = [...planTabs];
    const pastArrPlanTabs = [..._plan];

    const mergedArr = currentArrPlanTabs?.map((currItem) => {
      const matchingPastItem = pastArrPlanTabs.find((pastItem) => pastItem.day === currItem.day);

      if (matchingPastItem) {
        const currentPlans = currItem.plans;

        const mergedPlans = [
          ...currentPlans,
          ...matchingPastItem?.plans?.filter(
            (pastPlan) =>
              !currentPlans?.some(
                (currentPlan) =>
                  currentPlan.reference_id === pastPlan.reference_id &&
                  currentPlan.reference_type === pastPlan.reference_type,
              ),
          ),
        ];

        return { ...currItem, plans: mergedPlans };
      }

      return currItem;
    });

    const currentDays = currentArrPlanTabs.map((item) => item.day);
    const remainingPastItems = pastArrPlanTabs.filter(
      (pastItem) => !currentDays.includes(pastItem.day),
    );

    const lastDate = moment(currentArrPlanTabs[currentArrPlanTabs.length - 1].date); // Lấy ngày cuối cùng của currentArr
    const dateCounter = lastDate.clone(); // Biến đếm ngày

    remainingPastItems.forEach((item) => {
      dateCounter.add(1, 'day'); // Tăng ngày lên 1
      item.date = dateCounter.format('YYYY-MM-DD'); // Gán ngày mới
      mergedArr.push(item); // Thêm vào mảng kết quả
    });

    const newList = [...mergedArr]?.map((item) => ({
      ...item,
      plans: item?.plans?.map((iPlan) => ({
        ...iPlan,
        date: item.date,
      })),
    }));

    const newPlans = newList?.map((item) => item.plans)?.flatMap((item) => item);

    setPlanTabs(newList);
    setPlanInfo({
      ...planInfo,
      start_date: mergedArr?.[0]?.date,
      end_date: mergedArr?.[mergedArr.length - 1]?.date,
    });
    setPlans(newPlans);
    checkIsPlanDayNotAvailable(newList);
  };

  // format date range array from start_date and end_date
  const getDateRangeArray = (startDate: string, endDate: string) => {
    const start = new Date(startDate);
    const end = new Date(endDate);
    const dateArray = [];

    for (let d = start; d <= end; d.setDate(d.getDate() + 1)) {
      // Push a copy of the date to avoid modifying the original date
      dateArray.push(new Date(d).toISOString().slice(0, 10));
    }

    return dateArray;
  };

  const changeDatePlanInfor = (startDate: string, endDate: string) => {
    setPlanInfo({
      ...planInfo,
      start_date: startDate,
      end_date: endDate,
    });
    const planSortByDate = [...plans].sort((a, b) => {
      return new Date(a.date).getTime() - new Date(b.date).getTime();
    });
    const dateRangeArray = getDateRangeArray(startDate, endDate);
    const countDateRangeArray = dateRangeArray.length;
    let countDateIncrease = 0;
    const newPlans = [];
    let initDate = null;
    planSortByDate?.forEach((item, index) => {
      const dateOfNewPlan = moment(item.date).format('YYYY-MM-DD');
      if (index === 0 || dateOfNewPlan === initDate) {
        initDate = dateOfNewPlan;
        newPlans.push({
          ...item,
          date: dateRangeArray[countDateIncrease],
          day: countDateIncrease + 1,
        });
      } else {
        if (countDateIncrease + 1 < countDateRangeArray) {
          countDateIncrease += 1;
          initDate = dateOfNewPlan;
          newPlans.push({
            ...item,
            date: dateRangeArray[countDateIncrease],
            day: countDateIncrease + 1,
          });
        }
      }
    });
    setPlans(newPlans);
    if (Number(activePlanTab) > countDateRangeArray) {
      setTimeout(() => {
        setActivePlanTab(1);
      }, 100);
    }
  };

  // reset day & date after import & delete plan tab
  const setDayPlanTabs = (data: PlanTab[]) => {
    const dateRangeArray = getDateRangeArray(planInfo?.start_date, planInfo?.end_date);

    const result = data.map((val, index) => {
      let planDate = val.date;

      if (data.length === countDayRange) {
        const dateMapping = dateRangeArray.filter((item, itemIndex) => itemIndex === index);
        planDate = dateMapping[0];
      }

      return {
        ...val,
        day: index + 1,
        date: planDate,
      };
    });

    setPlanTabs(result);
    checkIsPlanDayNotAvailable(result);
  };

  const setDataPlanTabs = (data: PlanTab[]) => {
    setDayPlanTabs(data);
  };

  useEffect(() => {
    fetchPlans();
  }, []);

  useEffect(() => {
    console.log('planInfo', planInfo);
    getListPlanTabs();
  }, [planInfo, plans]);

  return (
    <PlanCreateContext.Provider
      value={{
        plans,
        planInfo,
        activePlanTab,
        activeTabOptions,
        planTabs,
        setDataPlanTabs,
        isPlanDaysNotAvailable,
        fetchPlans,
        countDayRange,
        changeDatePlanInfor,
        onUpdatePlanInfor,
        onPlanUpdate,
        onDuplicatePlan,
        onAddPlan,
        onChangePlanTab,
        onChangeTabOptions,
        onRemovePlan,
        updatePositionPlans,
        onImportOldPlanToNewPlan,
      }}
    >
      {children}
    </PlanCreateContext.Provider>
  );
}

export default PlanCreateProvider;
