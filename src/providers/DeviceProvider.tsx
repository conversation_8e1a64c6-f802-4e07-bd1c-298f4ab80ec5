import { checkDeviceType, type DeviceTypeEnum } from '@/constants/responsiveSize';
import type { ReactNode } from 'react';
import { createContext, useContext, useEffect, useState } from 'react';

const DeviceContext = createContext<DeviceTypeEnum | undefined>(undefined);

export const DeviceProvider = ({ children }: { children: ReactNode }) => {
  const [deviceType, setDeviceType] = useState<DeviceTypeEnum>(checkDeviceType(window.innerWidth));

  useEffect(() => {
    const handleResize = () => {
      setDeviceType(checkDeviceType(window.innerWidth));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return <DeviceContext.Provider value={deviceType}>{children}</DeviceContext.Provider>;
};

export const useDeviceType = () => {
  return useContext(DeviceContext);
};
