export type BusinessPartnerType = {
  [x: string]: any;
  business_partner_code: string;
  business_partner_name: string;
  business_partner_name_kana: string;
  business_partner_name_en: string;
  qualified_invoice_issuer_type: number;
  qualified_invoice_issuer_number: number;
  postal_code: number;
  address: string;
  address_en: string;
  phone_number: string;
  fax: string;
  email: string;
  manager: string;
  memo: string;
  target: number[] | string[] | string;
  r_target_category: number;
  r_rate: number;
  closing_date: string;
  collection_cycle: string;
  payment_cycle: string;
  internal_category: number;
  accounting_unit_code: string;
  profit_loss_unit_code: string;
  internal_department_currency_code: string;
  internal_department_currency_subject: string;
  internal_sales_tax_subject_code: string;
  internal_sales_tax_subject_name: string;
  internal_sales_non_taxable_code: string;
  internal_sales_non_taxable_name: string;
};

export type BusinessPartnerDetailType = {
  id: number;
  status?: boolean | 1 | 0;
} & BusinessPartnerType;
