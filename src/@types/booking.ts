import type { PlanInfo } from './plan';

export enum StatusBookingEnum {
  CANCEL = 'CANCEL',
  COMPLETED = 'COMPLETED',
  HIDDEN = 'HIDDEN',
  PROCESSING = 'PROCESSING',
  SENT = 'SENT',
}

export enum StatusValue {
  PROCESSING = 0,
  SUBMITTED = 1,
  RESERVED = 2,
  DONE = 4,
  CANCEL = 3,
}

export type StatusCountType = {
  [K in keyof typeof StatusBookingEnum]: StatusValue;
};

export type BookingParams = {
  limit: number;
  page: number;
  status?: number;
  keyword?: string;
  inquiry_date_start?: string | Date;
  inquiry_date_end?: string | Date;
  day_go_start?: string | Date;
  day_go_end?: string | Date;
  agency_id?: number;
  representative_id?: number;
  admin_id?: number;
  travel_spot_id?: number;
  model_course?: 0 | 1;
  month?: number;
  prefecture_code?: number;
};

export type BookingPayload = {
  control_number: string;
  title: string;
  agency_id: number;
  representative_id: number;
  admin_id: number;
  inquiry_date: string | Date;
  memo: string;
};

export type ChangeStatusPayload = {
  status: number;
  memo: string;
};

export type BookingResult = {
  bookings: {
    data: BookingItem[];
    total: number;
  };

  countStatus: StatusCountType;
};

export type BookingItem = {
  estimatedAmount: number;
  admin_name?: string;
  agency_name?: string;
  control_number?: string;
  id: number;
  created_at?: string;
  end_date?: string;
  inquiry_date?: string;
  memo?: string;
  representative_name?: string;
  start_date?: string;
  status?: number;
  total_days?: number;
  title: string;
  submission_date: string | Date;
  fee: string | number;
};

export type BookingDetailResult = {
  data: BookingDetail;
  message: string;
};

export type BookingDetail = {
  admin: {
    id: number;
    name: string;
    is_active: boolean;
  };
  admin_id: number;
  agency: {
    id: number;
    agency_name: string;
  };
  agency_id: number;
  control_number: string;
  id: number;
  inquiry_date: string;
  memo: string;
  plans: PlanDetail[];
  representative: {
    id: number;
    name: string;
  };
  representative_id: number;
  status: number;
  title: string;
};

export type PlanDetail = {
  plan_version?: number | string;
  creator: { id: number; name: string };
  booking_id: number;
  id: number;
  is_model_course: boolean;
  plan_description: string;
  plan_status: number;
  start_date: string;
  end_date: string;
  title: string;
  created_at: string;
  estimated_fee: number;
};

// booking past

export type BookingPastItem = {
  admin_name?: string;
  control_number?: string;
  departure_date?: string;
  end_date?: string;
  id?: number;
  memo?: string;
  is_model_course?: boolean;
  plans?: PlanInfo[];
  start_date?: string;
  status?: number;
  total_days?: number;
  travel_id?: string;
  tour_name?: string;
  business_partner_name?: any;
};
