import type { BaseResponse } from './request';

export type TouristSpot = {
  id: number;
  address: string;
  city: {
    id: string | number;
    name_city: number;
    province_id: number;
  };
  control_number: string;
  email: string;
  fax: string;
  images: string[];
  introduce_text: string;
  manager: string;
  memo: string;
  name_en: string;
  name_jp: string;
  phone_number: string;
  prefecture: {
    id: string | number;
    name_province: string;
  };
  tax: number;
};

export type TouristDetailResponse = BaseResponse<TouristSpot>;
