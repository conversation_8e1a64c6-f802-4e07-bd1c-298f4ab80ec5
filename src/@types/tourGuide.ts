import type { CreatedResourceSuccess } from './request';

export type TourGuideResource = {
  id: number;
  control_number: string;
  name_jp: string;
  name_en: string;
  nationality: string | number;
  gender: number | string;
  language: string;
  company: string;
  memo: string;
  address: string;
  manager: string;
  email: string;
  phone_number: string;
  fax: string;
};

export type TourGuide = {
  id: number;
  control_number: string;
  name_jp: string;
  name_en: string;
  nationality: string;
  gender: number;
  company: string;
  language: string;
  phone_number: string;
  email: string;
  memo: string;
  country: TourGuideCoutry;
  fax: string;
  manager: string;
};

type TourGuideCoutry = {
  id: number;
  country_name_en: string;
};

// api
type TourGuidesDataResponse = {
  data: TourGuideResource[];
  current_page: number;
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: number;
  per_page: number;
  to: number;
  total: number;
};

export type TourGuidesResponse = CreatedResourceSuccess & { data: TourGuidesDataResponse };
export type TourGuideDetailResponse = CreatedResourceSuccess & { data: TourGuide };
export type TourGuideRemoveResponse = CreatedResourceSuccess;

export type TourGuidesParams = {
  nationality?: number;
  gender?: string;
  language?: string;
  keyword?: string;
  limit?: number;
  page?: number;
};

export type TourGuidesEditPassParams = {
  tourGuide: TourGuide;
};
