export type Plan = {
  id: number | string;
  day?: number;
  reference_id: number | string;
  reference_type: string;
  date: string;
  name_jp?: string;
  name_en?: string;
  name?: string;
  address?: string;
  home_page?: string;
  phone_number?: string;
  introduce_text?: string;
  pictures?: {
    id?: number | string;
    path?: string;
  };
};

export type PlanInfo = {
  plan_version?: number;
  id: number;
  is_final?: boolean;
  booking_id?: number;
  itinerary_id?: number;
  title: string;
  plan_items?: any[];
  plan_status: number;
  plan_description: string;
  start_date: string;
  end_date: string;
  created_by: number;
  updated_by: number;
  deleted_by: null;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  is_model_course: boolean;
};

export type UpdatePlan = {
  item_array: {
    reference_id: number;
    reference_type: string;
    date: string;
    day?: number;
  }[];
  is_model_course: boolean;
};

// todo: update
export type PlanInfoPayload = {
  title: string;
  plan_status: number;
  plan_description: string;
  start_date: string;
  end_date: string;
};

export type PlansResponse = {
  message: string;
  data: {
    plan_items: Plan[];
    plan: PlanInfo;
  };
};

export type PlanPayloadType = {
  booking_id: number;
  title: string;
  plan_status: number;
  plan_description: string;
  start_date: string;
  end_date: string;
};
