export type CreatedResourceSuccess = {
  status: string;
  message: string;
  data: object;
};

export type RemoveResourceSuccess = {
  status: string;
  message: string;
};

export interface GetListResourceResponse {
  current_page: number;
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: number;
  per_page: number;
  to: number;
  total: number;
}

export type BaseResponse<T> = {
  status: string;
  message: string;
  data: T;
};

export type ResponseList<T> = {
  current_page: number;
  data: T;
  last_page: number;
  per_page: string | number;
  total: number;
  file_url?: string;
};

export type BaseParams = {
  limit?: number | string | 'all';
  page?: number | string;
  sort?: string; //column_name
  order?: 'desc' | 'asc' | string;
  keyword?: string;
  status?: 0 | 1;
} & Record<string, any>;
