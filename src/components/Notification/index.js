import {
  CameraOutlined,
  CheckCircleOutlined,
  CheckOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  IssuesCloseOutlined,
  PlusCircleOutlined,
  RetweetOutlined,
  StopOutlined,
  UnlockOutlined,
  VerticalAlignBottomOutlined,
  VerticalAlignTopOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import { notification } from 'antd';

export const openNotificationApprove = (message, description, color = '#3997C8') => {
  notification.open({
    message: message,
    description: description,

    icon: <CheckOutlined style={{ color: color }} />,
  });
};

export const openNotificationBlock = (message, description, color) => {
  notification.open({
    message: message,
    description: description,

    icon: <IssuesCloseOutlined style={{ color: color }} />,
  });
};

export const openNotificationUnblock = (message, description, color) => {
  notification.open({
    message: message,
    description: description,

    icon: <UnlockOutlined style={{ color: color }} />,
  });
};

export const openNotificationSuccess = (message, description, color = '#3997C8') => {
  notification.open({
    message: message,
    description: description,

    icon: <PlusCircleOutlined style={{ color: color }} />,
  });
};

export const openNotificationWarning = (message, description, color = '#f81d22') => {
  notification.open({
    message: message,
    description: description,
    icon: <WarningOutlined style={{ color: color }} />,
  });
};

export const openNotificationFail = (message, description, color = '#f81d22') => {
  notification.open({
    message: <div className="whitespace-pre-line">{message}</div>,
    description: description,

    icon: <StopOutlined style={{ color: color }} />,
  });
};

export const openNotificationChangeSuccess = (message, description, color) => {
  notification.open({
    message: message,
    description: description,

    icon: <RetweetOutlined style={{ color: color }} />,
  });
};

export const openNotificationTakePhotoSuccess = (message, description, color) => {
  notification.open({
    message: message,
    description: description,

    icon: <CameraOutlined style={{ color: color }} />,
  });
};

export const openNotificationUp = (message, description, color) => {
  notification.open({
    message: message,
    description: description,
    icon: <VerticalAlignTopOutlined style={{ color: color }} />,
  });
};

export const openNotificationDeleteSuccess = (message, description, color = '#3997C8') => {
  notification.open({
    message: message,
    description: description,

    icon: <DeleteOutlined style={{ color: color }} />,
  });
};

export const openNotificationDow = (message, description, color) => {
  notification.open({
    message: message,
    description: description,
    icon: <VerticalAlignBottomOutlined style={{ color: color }} />,
  });
};

export const openNotificationSuccessNG = (message, description, color) => {
  notification.open({
    message: message,
    description: description,
    icon: <CheckCircleOutlined style={{ color: color }} />,
    style: {
      width: 600,
    },
  });
};

export const openNotificationSuccessReject = (message, description, color) => {
  notification.open({
    message: message,
    description: description,
    icon: <CloseCircleOutlined style={{ color: color }} />,
  });
};
