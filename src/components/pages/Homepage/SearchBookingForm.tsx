import React, { forwardRef, useImperativeHandle } from 'react';
import type { FormInstance, FormProps } from 'antd';
import { Form } from 'antd';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import { StatusValue } from '@/@types/booking';
import styles from './index.less';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicInput from '@/components/Commons/BasicInput';
import BasicButton from '@/components/Commons/BasicButton';
import SearchSVG from '@/components/SVG/SearchSVGWhite';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';

export const statusOptions = [
  {
    label: '処理中', //Processing (default)
    value: StatusValue.PROCESSING,
  },
  {
    label: '提出済', // Submitted
    value: StatusValue.SUBMITTED,
  },
  {
    label: '予約済', //Reserved
    value: StatusValue.RESERVED,
  },
  {
    label: '完了', //Done
    value: StatusValue.DONE,
  },
  {
    label: 'キャンセル', //Cancel
    value: StatusValue.CANCEL,
  },
];

type SearchBookingFormProps = {
  onSubmit?: (v: any) => void;
  onResetField?: () => void;
};

export type SearchBookingFormRefType = {
  form: FormInstance<any>;
};

const SearchBookingForm = forwardRef<SearchBookingFormRefType, SearchBookingFormProps>(
  ({ onSubmit, onResetField, ...others }: SearchBookingFormProps & FormProps, ref) => {
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
      form,
    }));

    return (
      <div className={styles.formContainer}>
        <Form {...others} form={form} onFinish={onSubmit} className="">
          <div className="flex flex-wrap gap-x-[22px] [&_.ant-select-selector]:!h-9 [&_.ant-select-selection-search-input]:!h-9">
            <Form.Item label={'出発日'} style={{ marginBottom: 0 }} className="flex items-center">
              <Form.Item style={{ display: 'inline-block' }} name={'departure_date_start'}>
                <BasicDatePicker className="w-[140px]" />
              </Form.Item>
              <span
                style={{
                  display: 'inline-block',
                  padding: '0 10px',
                  lineHeight: '36px',
                }}
              >
                {'>'}
              </span>
              <Form.Item style={{ display: 'inline-block' }} name={'departure_date_end'}>
                <BasicDatePicker className="w-[140px]" />
              </Form.Item>
            </Form.Item>

            <Form.Item
              label={TEXT_TITLE.Date_of_enquiry}
              style={{ marginBottom: 0 }}
              className="flex items-center"
            >
              <Form.Item style={{ display: 'inline-block' }} name={'inquiry_date_start'}>
                <BasicDatePicker className="w-[140px]" />
              </Form.Item>
              <span
                style={{
                  display: 'inline-block',
                  padding: '0 10px',
                  lineHeight: '36px',
                }}
              >
                {'>'}
              </span>
              <Form.Item style={{ display: 'inline-block' }} name={'inquiry_date_end'}>
                <BasicDatePicker className="w-[140px]" />
              </Form.Item>
            </Form.Item>

            <Form.Item name={'status'} label={TEXT_TITLE.Status}>
              <BasicSelect
                options={statusOptions}
                className="min-w-[140px]"
                placeholder={TEXT_PLACEHOLDER.Please_select}
              />
            </Form.Item>

            <div className="flex space-x-[7px]">
              <Form.Item name={'keyword'} label={TEXT_TITLE.keyword}>
                <BasicInput placeholder={TEXT_PLACEHOLDER.Search_by_keyword} className="h-9" />
              </Form.Item>

              <BasicButton
                onClick={() => form.submit()}
                styleType="accept"
                className="flex items-center justify-center border-main-color w-[120px] h-9 space-x-[8px]"
              >
                <SearchSVG />
                {TEXT_ACTION.SEARCH}
              </BasicButton>
              <BasicButton
                onClick={onResetField}
                styleType="back"
                className="flex items-center justify-center border-main-color w-[120px] h-9 space-x-[8px]"
              >
                {TEXT_ACTION.RESET}
              </BasicButton>
            </div>
          </div>
        </Form>
      </div>
    );
  },
);

export default SearchBookingForm;
