import type { BusinessPartnerType } from '@/@types/businessPartner';

export const initialValuesFormBusinessPartner = {
  accounting_unit_code: undefined,
  address: undefined,
  address_en: undefined,
  business_partner_code: undefined,
  business_partner_name: undefined,
  business_partner_name_en: undefined,
  business_partner_name_kana: undefined,
  closing_date: undefined,
  collection_cycle: undefined,
  email: undefined,
  fax: undefined,
  internal_category: 0,
  internal_department_currency_code: undefined,
  internal_department_currency_subject: undefined,
  internal_sales_non_taxable_code: undefined,
  internal_sales_non_taxable_name: undefined,
  internal_sales_tax_subject_code: undefined,
  internal_sales_tax_subject_name: undefined,
  manager: undefined,
  memo: undefined,
  payment_cycle: undefined,
  phone_number: undefined,
  postal_code: undefined,
  profit_loss_unit_code: undefined,
  qualified_invoice_issuer_number: undefined,
  qualified_invoice_issuer_type: 1,
  r_rate: undefined,
  r_target_category: 1,
  target: undefined,
};

export function checkBusinessPartnerFormChange(
  objOld: BusinessPartnerType,
  objNew: BusinessPartnerType,
) {
  const keysObj = Object.keys(objOld);

  for (const key of keysObj) {
    if (objOld[key] !== objNew[key]) {
      return true;
    }
  }

  return false;
}
