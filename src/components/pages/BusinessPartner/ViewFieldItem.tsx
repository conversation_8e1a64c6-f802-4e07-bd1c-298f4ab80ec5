import React from 'react';
import { Checkbox, Col, Radio, Row, Tooltip } from 'antd';
import type { SelectItemType } from '@/@types/common';

interface Props {
  type: 'text' | 'radio' | 'checkbox' | 'textArea';
  value: any;
  label: string;
  options: SelectItemType[] | null;
}
const ViewFieldItem: React.FC<Props> = ({ type, label, value, options }) => {
  const renderItem = () => {
    switch (type) {
      case 'radio':
        return (
          <div>
            {options?.map((item, index) => (
              <Radio key={index} value={item?.value} checked={value === item.value}>
                {item.label}
              </Radio>
            ))}
          </div>
        );

      case 'checkbox':
        return (
          <div>
            {options?.map((item, index) => (
              <Checkbox key={index} checked={value?.includes(item.value)} value={item?.value}>
                {item?.label}
              </Checkbox>
            ))}
          </div>
        );
      case 'text':
        return (
          <Tooltip title={value} placement="topLeft">
            <div className="text-[14px] leading-5 font-medium text-[#363840] truncate">
              {value ?? '-'}
            </div>
          </Tooltip>
        );
      case 'textArea':
        return (
          // <Tooltip title={value} placement="topLeft">
          <p className="text-[14px] leading-5 font-medium text-[#363840] whitespace-pre-line">
            {value ?? '-'}
          </p>
          // </Tooltip>
        );
    }
  };
  return (
    <Row>
      <Col span={12}>
        <div className="flex flex-col">
          <div className="text-xs leading-4 font-medium text-[#606576] mb-2">{label}</div>
          {renderItem()}
        </div>
      </Col>
    </Row>
  );
};

export default ViewFieldItem;
