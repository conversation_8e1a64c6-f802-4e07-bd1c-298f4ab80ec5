import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';
import type { FormProps } from 'antd/es/form';
import { forwardRef, useImperativeHandle, useMemo } from 'react';
import { ListFormItem } from './dataField';

export type BusinessPartnerFormRef = {
  form?: FormInstance<any>;
};

interface Props {
  formProps?: FormProps;
}
const BusinessPartnerForm = forwardRef<BusinessPartnerFormRef, Props>(({ formProps }, ref) => {
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    form,
  }));

  const is_qualified_invoice_issuer_type = Form.useWatch('qualified_invoice_issuer_type', form);
  const is_r_target_category = Form.useWatch('r_target_category', form);
  const is_internal_category = Form.useWatch('internal_category', form);

  const internal_category_item = [
    'accounting_unit_code',
    'profit_loss_unit_code',
    'internal_department_currency_code',
    'internal_department_currency_subject',
    'internal_sales_tax_subject_code',
    'internal_sales_tax_subject_name',
    'internal_sales_non_taxable_code',
    'internal_sales_non_taxable_name',
  ];

  const newFields = useMemo(() => {
    const listFields = ListFormItem?.map((item) => {
      if (item.name === 'qualified_invoice_issuer_number') {
        return {
          ...item,
          disabled: is_qualified_invoice_issuer_type === 0,
          rules: is_qualified_invoice_issuer_type === 0 ? undefined : item?.rules,
        };
      } else if (item.name === 'r_rate') {
        return {
          ...item,
          disabled: is_r_target_category === 0,
          rules: is_r_target_category === 0 ? undefined : item?.rules,
        };
      } else if (internal_category_item?.includes(item.name)) {
        return {
          ...item,
          disabled: is_internal_category === 0,
          rules: is_internal_category === 0 ? undefined : item?.rules,
        };
      } else {
        return item;
      }
    });

    return listFields;
  }, [is_qualified_invoice_issuer_type, is_r_target_category, is_internal_category]);

  return (
    <Form form={form} autoComplete="off" {...formProps}>
      {newFields?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default BusinessPartnerForm;
