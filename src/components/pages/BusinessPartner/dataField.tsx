import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { Array_Date } from '@/constants';
import { TEXT_TITLE } from '@/constants/commonText';
import { targetOptions } from '@/constants/data';
import { rules } from '@/constants/rules';

export const YES_NO_OPTIONS = [
  { label: 'はい', value: 1 },
  { label: 'いいえ', value: 0 },
];

export const ListFormItem: FormItemDetail[] = [
  {
    type: 'numbericInput',
    name: 'business_partner_code',
    title: TEXT_TITLE.Business_Partner_Code,
    isRequired: true,
    placeholder: TEXT_TITLE.Business_Partner_Code,
    rules: rules.integerOnly,
    isNotFormatNumber: true,
  },
  {
    type: 'input',
    name: 'business_partner_name',
    title: TEXT_TITLE.Business_Partner_Name,
    isRequired: true,
    placeholder: TEXT_TITLE.Business_Partner_Name,
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'business_partner_name_kana',
    title: TEXT_TITLE.Business_Partner_Name_Kana,
    isRequired: true,
    placeholder: TEXT_TITLE.Business_Partner_Name_Kana,
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'business_partner_name_en',
    title: TEXT_TITLE.Business_Partner_Name_En,
    // isRequired: true,
    placeholder: TEXT_TITLE.Business_Partner_Name_En,
    // rules: rules.requiredInput,
  },
  {
    type: 'radio',
    name: 'qualified_invoice_issuer_type', // default: YES
    title: TEXT_TITLE.Qualified_Invoice_Issuer_Type,
    // isRequired: true,
    options: YES_NO_OPTIONS,
    defaultValueRadio: YES_NO_OPTIONS[0]?.value,
  },
  {
    type: 'input',
    name: 'qualified_invoice_issuer_number',
    title: TEXT_TITLE.Qualified_Invoice_Issuer_Number,
    isRequired: true,
    placeholder: TEXT_TITLE.Qualified_Invoice_Issuer_Number,
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'postal_code',
    title: TEXT_TITLE.Postal_Code,
    // isRequired: true,
    placeholder: TEXT_TITLE.Postal_Code,
    // rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'address',
    title: TEXT_TITLE.Address,
    placeholder: TEXT_TITLE.Address,
  },
  {
    type: 'input',
    name: 'address_en',
    title: TEXT_TITLE.Address_En,
    placeholder: TEXT_TITLE.Address_En,
  },
  {
    type: 'input',
    name: 'phone_number',
    title: TEXT_TITLE.Phone_Number,
    placeholder: TEXT_TITLE.Phone_Number,
    // rules: rules.isPhoneNumber,
  },
  {
    type: 'input',
    name: 'fax',
    title: TEXT_TITLE.Fax,
    placeholder: TEXT_TITLE.Fax,
    // rules: rules.isFax,
  },
  {
    type: 'input',
    name: 'email',
    title: TEXT_TITLE.Email,
    placeholder: TEXT_TITLE.Email,
    rules: rules.isEmail,
  },
  {
    type: 'input',
    name: 'manager',
    title: TEXT_TITLE.Manager,
    placeholder: TEXT_TITLE.Manager,
  },
  {
    type: 'textArea',
    name: 'memo',
    title: TEXT_TITLE.Memo,
    placeholder: TEXT_TITLE.Memo,
  },
  {
    type: 'checkbox',
    name: 'target',
    title: TEXT_TITLE.Target,
    options: targetOptions,
  },
  {
    type: 'radio',
    name: 'r_target_category', // default: YES
    title: TEXT_TITLE.R_Target_Category,
    // isRequired: true,
    options: YES_NO_OPTIONS,
    defaultValueRadio: YES_NO_OPTIONS[0]?.value,
  },
  {
    type: 'numbericInput',
    name: 'r_rate',
    title: TEXT_TITLE.R_Rate,
    placeholder: TEXT_TITLE.R_Rate,
    isRequired: true,
    rules: rules.requiredInput,
    isPercent: true,
  },
  {
    type: 'select',
    name: 'closing_date',
    title: TEXT_TITLE.Closing_Date,
    placeholder: TEXT_TITLE.Closing_Date,
    options: Array_Date,
  },
  {
    type: 'input',
    name: 'collection_cycle',
    title: TEXT_TITLE.Collection_Cycle,
    placeholder: TEXT_TITLE.Collection_Cycle,
  },
  {
    type: 'input',
    name: 'payment_cycle',
    title: TEXT_TITLE.Payment_Cycle,
    placeholder: TEXT_TITLE.Payment_Cycle,
  },
  {
    type: 'radio',
    name: 'internal_category', // default: NO
    title: TEXT_TITLE.Internal_Category,
    // isRequired: true,
    options: YES_NO_OPTIONS,
    defaultValueRadio: YES_NO_OPTIONS[1]?.value,
  },
  {
    type: 'input',
    name: 'accounting_unit_code',
    title: TEXT_TITLE.Accounting_Unit_Code,
    placeholder: TEXT_TITLE.Accounting_Unit_Code,
    isRequired: true,
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'profit_loss_unit_code',
    title: TEXT_TITLE.Profit_Loss_Unit_Code,
    placeholder: TEXT_TITLE.Profit_Loss_Unit_Code,
    isRequired: true,
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'internal_department_currency_code',
    title: TEXT_TITLE.Internal_Department_Currency_Code,
    placeholder: TEXT_TITLE.Internal_Department_Currency_Code,
    isRequired: true,
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'internal_department_currency_subject',
    title: TEXT_TITLE.Internal_Department_Currency_Subject,
    placeholder: TEXT_TITLE.Internal_Department_Currency_Subject,
    isRequired: true,
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'internal_sales_tax_subject_code',
    title: TEXT_TITLE.Internal_Sales_Tax_Subject_Code,
    placeholder: TEXT_TITLE.Internal_Sales_Tax_Subject_Code,
    isRequired: true,
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'internal_sales_tax_subject_name',
    title: TEXT_TITLE.Internal_Sales_Tax_Subject_Name,
    placeholder: TEXT_TITLE.Internal_Sales_Tax_Subject_Name,
    isRequired: true,
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'internal_sales_non_taxable_code',
    title: TEXT_TITLE.Internal_Sales_Non_Taxable_Code,
    placeholder: TEXT_TITLE.Internal_Sales_Non_Taxable_Code,
    isRequired: true,
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'internal_sales_non_taxable_name',
    title: TEXT_TITLE.Internal_Sales_Non_Taxable_Name,
    placeholder: TEXT_TITLE.Internal_Sales_Non_Taxable_Name,
    isRequired: true,
    rules: rules.requiredInput,
  },
];
