import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { TravelTypeMasterFields } from '../dataField';

interface Props {
  onSubmit: (values) => void;
}

export type TravelTypeMasterFormRef = {
  form: FormInstance<any>;
};

const TravelTypeMasterForm = forwardRef<TravelTypeMasterFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    form,
  }));

  useEffect(() => {
    form?.setFieldsValue({
      status: true,
    });
  }, []);

  return (
    <Form form={form} onFinish={onSubmit}>
      {TravelTypeMasterFields?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default TravelTypeMasterForm;
