import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { rules } from '@/constants/rules';

export const TravelTypeMasterFields: FormItemDetail[] = [
  {
    type: 'input',
    name: 'travel_code',
    title: '種別コード',
    isRequired: true,
    placeholder: '種別コード',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'travel_name',
    title: '種別名',
    isRequired: true,
    placeholder: '種別名',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'switch',
    defaultCheckedSwitch: true,
    name: 'status',
    title: 'アクティブ',
    colSpan: 6,
  },
];
