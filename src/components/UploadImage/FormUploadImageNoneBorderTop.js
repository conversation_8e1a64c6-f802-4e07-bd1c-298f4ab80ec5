import React, { useState, useEffect } from 'react';
import { formatMessage } from 'umi';

import { Upload, Modal, Form, message, Image, Button } from 'antd';
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { openNotificationWarning } from '@/components/Notification';
import Style from '@/pages/Style';
import styles from './index.less';
import ImgCrop from 'antd-img-crop';

import NoImage from '@/assets/noImage.png';
import { deleteImage, uploadImage } from '@/services/uploadImage';
import { isURL } from '../HandleObj';

// eslint-disable-next-line max-lines-per-function
const FormUploadImageNoneBorderTop = ({
  data,
  handleDelete,
  handleSet,
  name,
  required,
  type,
  aspect,
}) => {
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [pass, setPass] = useState(false);
  const [fileList, setFileList] = useState([]);

  const [uploaded, setUploaded] = useState(false);

  function getBase64(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result);
      reader.onerror = (error) => reject(error);
    });
  }

  //handle before Upload
  const beforeUpload = (file) => {
    console.log(file);
    const isJPG = file.type === 'image/jpeg';
    const isJPEG = file.type === 'image/jpeg';
    const isGIF = file.type === 'image/gif';
    const isPNG = file.type === 'image/png';
    if (!(isJPG || isJPEG || isGIF || isPNG)) {
      openNotificationWarning(
        formatMessage({
          id: 'wrongFileFormat',
        }),
        `JPG、JPEG、GIF、PNGファイルをアップロードしてください！`,
        '#f81d22',
      );

      setPass(false);
      return false;
    }
    setPass(true);
    return true;
  };

  //handle if have picture from data
  useEffect(() => {
    if (data) {
      setFileList([
        {
          url: data,
        },
      ]);
      setPreviewImage(data);
    }
  }, [data, setFileList]);

  //handle cancel
  const handleCancel = () => {
    setPreviewVisible(false);
  };

  //handle Preview
  const handlePreview = async (file) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj);
    }
    setPreviewImage(file.url || file.preview);
    setPreviewVisible(true);
    setPreviewTitle(file.name || file.url.substring(file.url.lastIndexOf('/') + 1));
  };

  //handle change
  const handleChange = async (info) => {
    console.log('infoinfo', info);
    let fileList = [...info.fileList];

    if (fileList.length == 0) {
      setFileList([...fileList]);
      setPreviewImage('');
    }
    if (pass) {
      fileList = fileList.slice(-1);
      if (fileList[0].name.split('').length > 40) {
        let array = fileList[0].name.split('').slice(0, 30);
        array.push('… .jpg');
        // console.log('array', array.join(''));
        fileList[0].name = array.join('');
      }

      setFileList(fileList);
    }
  };

  //handle get value in form
  const normFile = (e) => {
    console.log('Upload event:', e);
    if (Array.isArray(e)) {
      return e;
    }
    return e && e.fileList;
  };

  const checkFileList = (_, value) => {
    console.log('value', value);

    if (Array.isArray(value) && !isURL(value) && !uploaded) {
      return Promise.reject(new Error('写真はまだアップロードされていません'));
    }
    if (value && isURL(value)) {
      return Promise.resolve();
    }

    return Promise.reject(new Error(formatMessage({ id: 'pleaseAddAImage' })));
  };
  const uploadImageF = async (options) => {
    setUploaded(false);
    console.log('options', options);
    const { onSuccess, onError, file, onProgress, category } = options;

    const fmData = new FormData();

    fmData.append('file', file);
    fmData.append('type', type);
    try {
      const res = await uploadImage(fmData);
      onSuccess('ok', res);
      handleSet(name, res.response.data.data.path);
      setPreviewImage(res.response.data.data.path);
      setUploaded(true);
    } catch (err) {
      console.log('Eroor: ', err);
      handleSet(name, '');

      setUploaded(false);
      onError({ err });
    }
  };
  return (
    <Form.Item
      style={{ ...Style.styleBorderNoneTop, height: 225 }}
      label={
        <div style={Style.styleLabel}>
          <div>{formatMessage({ id: name })}</div>
          {required && <div style={Style.styleRule}>{'必須'} </div>}
        </div>
      }
      name={name}
    >
      <div style={Style.styleRowAndBetween}>
        <div style={{ display: 'flex', flexDirection: 'column', paddingLeft: 10 }}>
          <div>
            <div>
              {required && (
                <Form.Item
                  name={name}
                  getValueFromEvent={normFile}
                  rules={[
                    {
                      validator: checkFileList,
                    },
                  ]}
                  className={styles.uploadRow}
                >
                  <ImgCrop
                    modalTitle={'画像を編集する'}
                    grid
                    rotate
                    aspect={aspect ? aspect[0] / aspect[1] : 1200 / 900}
                    modalWidth={1200}
                  >
                    <Upload
                      customRequest={uploadImageF}
                      fileList={fileList}
                      beforeUpload={beforeUpload}
                      onPreview={handlePreview}
                      onChange={handleChange}
                      maxCount={1}
                      disabled={previewImage}
                    >
                      {' '}
                      <Button style={{ marginTop: 8 }}>{'画像を選択'}</Button>
                    </Upload>
                  </ImgCrop>
                </Form.Item>
              )}

              {!required && (
                <Form.Item name={name} getValueFromEvent={normFile} className={styles.uploadRow}>
                  <ImgCrop
                    modalTitle={'画像を編集する'}
                    grid
                    rotate
                    aspect={aspect ? aspect[0] / aspect[1] : 1200 / 900}
                    modalWidth={1200}
                  >
                    <Upload
                      customRequest={uploadImageF}
                      fileList={fileList}
                      beforeUpload={beforeUpload}
                      onPreview={handlePreview}
                      onChange={handleChange}
                      maxCount={1}
                      disabled={previewImage}
                    >
                      {' '}
                      <Button style={{ marginTop: 8 }}>{'画像を選択'}</Button>
                    </Upload>
                  </ImgCrop>
                </Form.Item>
              )}
            </div>

            <Modal open={previewVisible} title={previewTitle} footer={null} onCancel={handleCancel}>
              <img alt="example" style={{ width: '100%' }} src={previewImage} />
            </Modal>
          </div>
          <div style={{ fontSize: 12, color: 'rgba(175, 184, 191, 1)' }}>
            <div>{'推奨サイズ：横1,200px × 縦900px（2MB程度）'}</div>
          </div>
        </div>

        {previewImage && (
          <div style={{ display: 'flex', gap: 10, alignItems: 'end' }}>
            <div
              style={{
                alignItems: 'end',
                position: 'absolute',
                zIndex: 1,
                right: 280,
                top: 185,
                cursor: 'pointer',
              }}
              onClick={() => {
                console.log('previewImage', previewImage == data);
                if (!(previewImage == data)) {
                  const formData = new FormData();
                  formData.append('path', previewImage);
                  const { response } = deleteImage(formData);
                  console.log('response deleteimage', response);
                }
                setFileList([]);
                setPreviewImage('');
                handleDelete(name);
              }}
            >
              <DeleteOutlined style={{ paddingBottom: 4 }} />
              {'画像を削除する'}
            </div>
            <Image
              style={{ objectFit: 'cover' }}
              alt="example"
              height={210}
              width={278}
              src={previewImage}
            />
          </div>
        )}
        {!previewImage && (
          <div style={{ display: 'flex', gap: 10 }}>
            <Image preview={false} alt="example" height={210} width={278} src={NoImage} />
          </div>
        )}
      </div>
    </Form.Item>
  );
};

export default FormUploadImageNoneBorderTop;
