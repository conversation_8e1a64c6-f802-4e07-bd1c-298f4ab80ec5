import type { SelectItemType } from '@/@types/common';
import { Col, Form, Row } from 'antd';
import type { Rule } from 'antd/lib/form';
import React from 'react';
import BasicInput from '../../Commons/BasicInput';
import RequiredTag from '../../Commons/RequiredTag/RequiredTag';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTextArea from '@/components/Commons/BasicTextArea';
import BasicCheckboxGroup from '@/components/Commons/BasicCheckboxGroup';
import BasicRadioGroup from '@/components/Commons/BasicRadioGroup';
import BasicNumbericInput from '@/components/Commons/BasicNumbericInput';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicSwitch from '@/components/Commons/BasicSwitch';
import type { UploadImageType } from '@/components/Commons/BasicUploads';
import BasicUploads from '@/components/Commons/BasicUploads';

export type FormItemDetail = {
  type:
    | 'input'
    | 'select'
    | 'checkbox'
    | 'radio'
    | 'textArea'
    | 'customField'
    | 'numbericInput'
    | 'dateTime'
    | 'switch'
    | 'images';
  name: string;
  title?: string;
  options?: SelectItemType[];
  component?: React.ReactNode;
  placeholder?: string;
  rules?: Rule[];
  colSpan?: number;
  isRequired?: boolean;
  allowClear?: boolean;
  defaultValueRadio?: any;
  defaultCheckedSwitch?: boolean;
  disabled?: boolean;
  isPercent?: boolean;
  numbericInputAllowDecimal?: boolean;
  maxLength?: number;
  uploadType?: UploadImageType;
  isNotFormatNumber?: boolean;
};

interface Props {
  field: FormItemDetail;
}

const CommonRenderFormField: React.FC<Props> = ({ field }) => {
  const renderField = () => {
    switch (field.type) {
      case 'input':
        return (
          <BasicInput
            onMouseOver={(e) => e.preventDefault()}
            title={
              <div className="flex items-center gap-[6px]">
                {field.title} {field?.isRequired ? <RequiredTag /> : null}{' '}
              </div>
            }
            autoComplete="off"
            maxLength={field?.maxLength}
            allowClear={field?.allowClear}
            placeholder={field?.placeholder}
            disabled={field?.disabled}
          />
        );

      case 'numbericInput': {
        return (
          <BasicNumbericInput
            title={
              <div className="flex items-center gap-[6px]">
                {field?.title} {field?.isRequired ? <RequiredTag /> : null}{' '}
              </div>
            }
            notAllowNegativeNumber={true}
            isPercent={field?.isPercent}
            maxLength={field?.maxLength}
            allowDecimal={field?.numbericInputAllowDecimal}
            placeholder={field.placeholder}
            disabled={field?.disabled}
            isNotFormatNumber={field?.isNotFormatNumber}
          />
        );
      }
      case 'textArea':
        return (
          <BasicTextArea
            title={
              <div className="flex items-center gap-[6px]">
                {field.title} {field?.isRequired ? <RequiredTag /> : null}{' '}
              </div>
            }
            placeholder={field.placeholder}
            disabled={field?.disabled}
            maxLength={field?.maxLength}
          />
        );
      case 'select':
        return (
          <BasicSelect
            title={
              <div className="flex items-center gap-[6px]">
                {field.title} {field?.isRequired ? <RequiredTag /> : null}{' '}
              </div>
            }
            showSearch
            allowClear={field?.allowClear}
            options={field?.options}
            placeholder={field.placeholder}
            disabled={field?.disabled}
            filterOption={(input, option) => {
              return option?.props?.label?.toLowerCase().indexOf(input?.toLowerCase()) >= 0;
            }}
          />
        );
      case 'checkbox':
        return (
          <BasicCheckboxGroup
            options={field?.options}
            title={
              <div className="flex items-center gap-[6px]">
                {field.title} {field?.isRequired ? <RequiredTag /> : null}{' '}
              </div>
            }
          />
        );
      case 'radio':
        return (
          <BasicRadioGroup
            options={field?.options}
            defaultValue={field?.defaultValueRadio}
            value={field?.defaultValueRadio}
            title={
              <div className="flex items-center gap-[6px]">
                {field.title} {field?.isRequired ? <RequiredTag /> : null}{' '}
              </div>
            }
          />
        );
      case 'dateTime':
        return (
          <BasicDatePicker
            className="h-10"
            title={
              <div className="flex items-center gap-[6px]">
                {field.title} {field?.isRequired ? <RequiredTag /> : null}{' '}
              </div>
            }
            placeholder={field?.placeholder}
            disabled={field?.disabled}
          />
        );
      case 'switch':
        return (
          <BasicSwitch
            defaultChecked={field?.defaultCheckedSwitch}
            style={{
              maxWidth: '44px',
            }}
            title={
              <div className="flex items-center gap-[6px]">
                {field.title} {field?.isRequired ? <RequiredTag /> : null}{' '}
              </div>
            }
          />
        );
      case 'images':
        return (
          <BasicUploads
            type={field?.uploadType}
            maxLength={field?.maxLength}
            title={
              <div className="flex items-center gap-[6px]">
                {field.title} {field?.isRequired ? <RequiredTag /> : null}{' '}
              </div>
            }
          />
        );
      case 'customField':
        return field?.component;
    }
  };

  return (
    <Row>
      <Col span={field?.colSpan ?? 12}>
        <Form.Item name={field?.name} rules={field?.rules} tooltip={null}>
          {renderField()}
        </Form.Item>
      </Col>
    </Row>
  );
};

export default CommonRenderFormField;
