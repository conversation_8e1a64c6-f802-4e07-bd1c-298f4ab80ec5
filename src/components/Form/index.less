@import '~antd/lib/style/themes/default.less';
.emailInput,
.passwordInput {
  height: 43px;
  background-color: #fff;
  border-radius: 8px;
}
.emailInput input::placeholder,
.passwordInput input::placeholder {
  text-align: left;
}

.footerContent {
  margin-top: 3%;
  margin-right: 2%;
  margin-bottom: 2%;
  margin-left: 2%;
  padding-bottom: 20px;
  .footerContentMain {
    padding: 8px;
    background-color: rgba(240, 244, 247, 1);
  }
}

.fieldInSearch {
  :global {
    .ant-form-item-label > label {
      width: 100%;
      text-align: start;
    }
    .ant-form-item-label > label::after {
      content: '';
    }
    .ant-form-item-label {
      // white-space: unset;
    }
  }
}
