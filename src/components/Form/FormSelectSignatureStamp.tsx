import type { DetailInvoiceIssuance } from '@/apis/accounting/invoiceIssuance';
import type { CompanyType } from '@/apis/master/companyMaster';
import { getCompany } from '@/apis/master/companyMaster';
import IconCancelRed from '@/assets/imgs/common-icons/cancel-red.svg';
import IconTickWhite from '@/assets/imgs/common-icons/tick_white.svg';
import BasicButton from '@/components/Commons/BasicButton';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_TITLE } from '@/constants/commonText';
import { Image, Skeleton } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import BasicRadioGroup from '../Commons/BasicRadioGroup';
import { openNotificationFail } from '../Notification';

const FormSelectSignatureStamp = ({
  onClose,
  onSelect,
  detailIssuanceInvoice,
}: {
  onClose: () => void;
  onSelect: (value: {
    seal: string;
    logo: string;
    company: CompanyType;
    language: number;
    optionInvoicePrintf: number;
  }) => void;
  detailIssuanceInvoice: DetailInvoiceIssuance;
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [companyDetailData, setCompanyDetailData] = useState<CompanyType>();
  const [sealSelected, setSealSelected] = useState<number>(0);
  const [language, setLanguage] = useState<number>(detailIssuanceInvoice?.option_language ?? 1);
  const [optionInvoicePrintf, setoptionInvoicePrintf] = useState<number>(
    detailIssuanceInvoice?.option_invoice_print ?? 1,
  );
  const fetchData = async () => {
    setIsLoading(true);
    const resData = await getCompany();
    const detailCompany = resData?.data?.data;
    if (detailCompany) {
      setCompanyDetailData(detailCompany);
      setIsLoading(false);
    } else {
      openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
      setIsLoading(false);
    }
  };

  const optionBanking = useMemo(() => {
    if (companyDetailData?.invoice_print_account_number) {
      return [
        {
          label:
            companyDetailData?.invoice_print_account_name +
            ' - ' +
            companyDetailData?.invoice_print_account_number,
          value: 1,
        },
        {
          label:
            companyDetailData?.invoice_print_account_name_2 +
            ' - ' +
            companyDetailData?.invoice_print_account_number_2,
          value: 2,
        },
      ];
    }
    return [];
  }, [companyDetailData?.invoice_print_account_number]);

  const optionLanguage = [
    {
      label: '日本語',
      value: 1,
    },
    {
      label: 'English',
      value: 2,
    },
  ];

  useEffect(() => {
    fetchData();
  }, []);

  const handleCompleted = () => {
    onSelect?.({
      seal: companyDetailData?.company_seal_estimate_invoice[sealSelected],
      logo: companyDetailData?.logo,
      company: companyDetailData,
      language,
      optionInvoicePrintf,
    });
  };

  return (
    <div className="p-7 flex flex-col gap-6">
      <div className="flex flex-col">
        <p className="font-bold text-base mb-2">社印</p>
        <div className="flex gap-4">
          {!isLoading ? (
            companyDetailData?.company_seal_estimate_invoice?.map((item, index) => {
              return (
                <Image
                  key={item}
                  preview={false}
                  src={item}
                  width={200}
                  height={200}
                  onClick={() => setSealSelected(index)}
                  style={{
                    cursor: 'pointer',
                    borderWidth: index === sealSelected ? 3 : 0,
                    borderColor: '#3997C8',
                    borderStyle: 'solid',
                    borderRadius: 8,
                    padding: index === sealSelected ? 2 : 0,
                  }}
                />
              );
            })
          ) : (
            <div className="flex gap-4">
              <Skeleton.Image active={true} style={{ width: 200, height: 200 }} />
              <Skeleton.Image active={true} style={{ width: 200, height: 200 }} />
              <Skeleton.Image active={true} style={{ width: 200, height: 200 }} />
            </div>
          )}
        </div>
      </div>
      <BasicRadioGroup
        className="!flex !flex-col"
        options={optionBanking}
        value={optionInvoicePrintf}
        title="入金口座"
        onChange={(val) => {
          setoptionInvoicePrintf(val.target.value);
        }}
      />
      <BasicRadioGroup
        options={optionLanguage}
        value={language}
        title="言語"
        onChange={(val) => {
          setLanguage(val.target.value);
        }}
      />
      <div className="flex justify-center mt-6 gap-6">
        <BasicButton
          className="w-[290px] flex items-center justify-center rounded-[4px] shadow-md !bg-white hover:opacity-40 font-medium !text-[#FF3B30] !border-[#DCDEE3]"
          onClick={onClose}
        >
          <Image preview={false} src={IconCancelRed} width={18} height={18} />
          <p className="pl-2 font-medium">{TEXT_ACTION.CANCEL}</p>
        </BasicButton>
        <BasicButton
          styleType="accept"
          className={`!w-[290px] flex justify-center items-center `}
          onClick={handleCompleted}
        >
          <Image preview={false} src={IconTickWhite} width={18} height={18} />
          <p>{TEXT_ACTION.PREVIEW}</p>
        </BasicButton>
      </div>
    </div>
  );
};

export default FormSelectSignatureStamp;
