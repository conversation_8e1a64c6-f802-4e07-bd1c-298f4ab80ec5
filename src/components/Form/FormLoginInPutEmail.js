import { MailOutlined } from '@ant-design/icons';
import { Form, Input } from 'antd';
import { formatMessage } from 'umi';
import style from './index.less';

const FormLoginInPutEmail = ({ disabled }) => {
  return (
    <div>
      <Form.Item
        name="email"
        label={
          <div
            style={{
              fontSize: '14px',
              lineHeight: '21px',
              fontWeight: '500',
            }}
          >
            メールアドレス
          </div>
        }
        rules={[
          { type: 'email', message: '入力したメールアドレスのフォーマットが正しくありません' },
          { required: true, message: 'メールアドレスを入力してください' },
        ]}
      >
        <Input
          prefix={<MailOutlined style={{ marginRight: 6, fontSize: 20 }} />}
          disabled={disabled || false}
          placeholder={'<EMAIL>'}
          className={style.emailInput}
        />
      </Form.Item>
    </div>
  );
};
export default FormLoginInPutEmail;
