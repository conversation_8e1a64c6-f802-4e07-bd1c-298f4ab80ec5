import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import type { ParamsSearchBusinessPartner } from '@/apis/businessPartner';
import { getListBusinessPartner } from '@/apis/businessPartner';
import IconCancelRed from '@/assets/imgs/common-icons/cancel-red.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicInput from '@/components/Commons/BasicInput';
import BasicTable from '@/components/Commons/BasicTable';
import SearchSVG from '@/components/SVG/SearchSVG';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import { ITEM_PER_PAGE } from '@/utils/constants';
import { Image } from 'antd';
import { useEffect, useState } from 'react';
import { openNotificationFail } from '../Notification';

const FormSelectCompany = ({
  onClose,
  onSelect,
  isCompanyActive = false,
  target,
  defaultParams,
}: {
  onClose: () => void;
  onSelect: (value: BusinessPartnerDetailType) => void;
  isCompanyActive?: boolean;
  target?: 1 | 2 | 3 | 4; // 1:売上(sale) | 2:仕入(purchase) | 3:入金(deposit) | 4:支払(payment)
  defaultParams?: ParamsSearchBusinessPartner;
}) => {
  const [total, setTotal] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<BusinessPartnerDetailType[]>([]);
  const [params, setParams] = useState<ParamsSearchBusinessPartner>({
    page: 1,
    limit: ITEM_PER_PAGE,
    ...(defaultParams ?? {}),
  });

  const [searchValue, setSearchValue] = useState<string>('');

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'key',
      key: 'key',
      width: 60,
    },
    {
      title: '取引先コード',
      dataIndex: 'business_partner_code',
      key: 'business_partner_code',
      width: 140,
    },
    {
      title: '取引先名',
      dataIndex: 'business_partner_name',
      key: 'business_partner_name',
      width: 200,
    },
    {
      title: '住所',
      dataIndex: 'address',
      key: 'address',
      width: 260,
      render: (_, record) => (
        <div className="w-[260px]">
          <span className="line-clamp-3">{record?.address}</span>
        </div>
      ),
    },
    {
      title: '電話番号',
      dataIndex: 'phone_number',
      key: 'phone_number',
      width: 160,
    },
    {
      title: '備考',
      dataIndex: 'memo',
      key: 'memo',
      width: 200,
      render: (_, record) => (
        <div className="w-[260px]">
          <span className="line-clamp-3">{record?.memo}</span>
        </div>
      ),
    },
    {
      title: '',
      dataIndex: 'select',
      key: 'select',
      render: (_, record) => (
        <button
          className=" flex items-center justify-center !h-[24px] w-[88px] border-none hover:!bg-[transparent]"
          onClick={() => {
            onSelect(record);
            onClose();
          }}
        >
          <span className="!text-[#225DE0] font-medium">選択</span>
        </button>
      ),
    },
  ];

  const fetchData = async () => {
    setIsLoading(true);
    const resData = await getListBusinessPartner({
      ...params,
      status: isCompanyActive ? 1 : undefined,
      target,
    });
    const listCompany = resData?.data?.data;
    if (listCompany) {
      const dataRender = listCompany.map((item, index) => {
        return { ...item, key: (Number(params.page) - 1) * Number(params.limit) + index + 1 };
      });
      setDataSource(dataRender);
      setTotal(resData.data?.total);
    } else {
      openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    setParams({ ...params, ...defaultParams });
  }, [defaultParams]);

  useEffect(() => {
    fetchData();
  }, [
    params.page,
    params.limit,
    params.is_use_purchase,
    params.is_use_sale,
    params.keyword,
    params.order,
    params.status,
    params.target,
    params.sort,
    target,
  ]);

  return (
    <>
      <div className="flex justify-between px-4">
        <div className="flex gap-4">
          <BasicInput
            style={{
              width: '304px',
              height: '40px',
            }}
            title={TEXT_TITLE.Business_Code_Business_name}
            placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
            onChange={(val) => {
              setSearchValue(val.target.value);
            }}
          />
          <div className="flex items-end">
            <BasicButton
              icon={<SearchSVG colorSvg="white" />}
              className="flex items-center w-[120px]"
              styleType="accept"
              onClick={() => setParams({ ...params, keyword: searchValue, page: 1 })}
            >
              <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
            </BasicButton>
          </div>
        </div>
      </div>

      <div className="p-2 border border-[#DCDEE3] rounded-[12px] mt-6">
        <BasicTable
          className="!mt-0"
          tableProps={{
            scroll: { x: 1080, y: 500 },
            loading: isLoading,
            columns: defaultColumns,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            rowKey: 'id',
          }}
          page={params.page as number}
          pageSize={params.limit as number}
          onChangePage={(p: number) => {
            setParams({ ...params, page: p });
          }}
          total={total}
          onSelectPageSize={(v) => setParams({ ...params, limit: v })}
        />
      </div>
      <div className="flex justify-center mt-6 ">
        <BasicButton
          className="w-[360px] flex items-center justify-center rounded-[4px] shadow-md !bg-white hover:opacity-40 font-medium !text-[#FF3B30] !border-[#DCDEE3]"
          onClick={onClose}
        >
          <Image preview={false} src={IconCancelRed} width={18} height={18} />
          <p className="pl-2 font-medium">{TEXT_ACTION.CLOSE}</p>
        </BasicButton>
      </div>
    </>
  );
};

export default FormSelectCompany;
