import type { AccountingTravelListType, TravelListSearchParams } from '@/apis/accounting/traveList';
import { getListAccountingTravel } from '@/apis/accounting/traveList';
import IconCancelRed from '@/assets/imgs/common-icons/cancel-red.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import BasicTable from '@/components/Commons/BasicTable';
import SearchSVG from '@/components/SVG/SearchSVG';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import { Image } from 'antd';
import type { Moment } from 'moment';
import { useEffect, useState } from 'react';
import { openNotificationFail } from '../Notification';
import { formatMoney } from '@/utils';
import moment from 'moment';
import { ITEM_PER_PAGE } from '@/utils/constants';

const FormSelectTour = ({
  onClose,
  onSelect,
  defaultParams,
}: {
  onClose: () => void;
  onSelect: (data: AccountingTravelListType) => void;
  defaultParams?: TravelListSearchParams;
}) => {
  const [total, setTotal] = useState<number>(1);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<AccountingTravelListType[]>([]);
  const [params, setParams] = useState<TravelListSearchParams>({
    page: 1,
    limit: ITEM_PER_PAGE,
    ...(defaultParams ?? {}),
  });
  const [searchValue, setSearchValue] = useState<string>('');
  const [departureDate, setDepartureDate] = useState<Moment>();
  const [returnDate, setReturnDate] = useState<Moment>();

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      render: (_, record, index) => (
        <>{index + 1 + (Number(params.page) - 1) * Number(params.limit)}</>
      ),
    },
    {
      title: TEXT_TITLE.Travel_Id,
      dataIndex: 'travel_id',
      key: 'travel_id',
      width: 90,
    },
    {
      title: TEXT_TITLE.Tour_Name,
      dataIndex: 'tour_name',
      key: 'tour_name',
      width: 160,
    },
    {
      title: TEXT_TITLE.Departure_Date,
      dataIndex: 'departure_date',
      key: 'departure_date',
      render: (_, { departure_date }) => (
        <>{departure_date && moment(departure_date).format('YYYY/MM/DD')}</>
      ),
      width: 105,
    },
    {
      title: TEXT_TITLE.Return_Date_Trv,
      dataIndex: 'return_date',
      key: 'return_date',
      render: (_, { return_date }) => (
        <>{return_date && moment(return_date).format('YYYY/MM/DD')}</>
      ),
      width: 105,
    },
    {
      title: TEXT_TITLE.Sale,
      dataIndex: 'sale',
      key: 'sale',
      render: (_, record) => (
        <p className="text-right">{record?.sale ? formatMoney(record?.sale) : ''}</p>
      ),
      width: 110,
    },
    {
      title: TEXT_TITLE.Total_Billing_Amount,
      dataIndex: 'total_billing_amount',
      key: 'total_billing_amount',
      render: (_, record) => (
        <p className="text-right">
          {record?.total_billing_amount ? formatMoney(record?.total_billing_amount) : ''}
        </p>
      ),
      width: 110,
    },
    {
      title: TEXT_TITLE.Deposit,
      dataIndex: 'deposit',
      key: 'deposit',
      render: (_, record) => (
        <p className="text-right">{record?.deposit ? formatMoney(record?.deposit) : ''}</p>
      ),
      width: 110,
    },
    {
      title: TEXT_TITLE.Purchase,
      dataIndex: 'purchase',
      key: 'purchase',
      render: (_, record) => (
        <p className="text-right">{record?.purchase ? formatMoney(record?.purchase) : ''}</p>
      ),
      width: 110,
    },
    {
      title: TEXT_TITLE.Payment,
      dataIndex: 'payment',
      key: 'payment',
      render: (_, record) => (
        <p className="text-right">{record?.payment ? formatMoney(record?.payment) : ''}</p>
      ),
      width: 110,
    },
    {
      title: '',
      dataIndex: 'select',
      key: 'select',
      render: (_, record) => (
        <button
          className=" flex items-center justify-center !h-[24px] w-[68px] border-none hover:!bg-[transparent]"
          onClick={() => {
            onSelect(record);
            onClose();
          }}
        >
          <span className="!text-[#225DE0] font-medium">選択</span>
        </button>
      ),
      width: 90,
    },
  ];

  const fetchData = async () => {
    setIsLoading(true);
    const resData = await getListAccountingTravel(params);
    if (resData?.data?.data) {
      setDataSource(resData.data.data);
      setTotal(resData.data.total);
    } else {
      openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    setParams({ ...params, ...defaultParams });
  }, [defaultParams]);

  useEffect(() => {
    fetchData();
  }, [
    params.departure_date,
    params.keyword,
    params.limit,
    params.page,
    params.return_date,
    params.status,
    params.sort,
    params.order,
  ]);

  return (
    <>
      <div className="flex justify-between px-4">
        <div className="flex gap-4">
          <BasicInput
            style={{
              width: '280px',
              height: '40px',
            }}
            title={TEXT_TITLE.Trip_ID_or_Tour_Name}
            placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
            onChange={(val) => {
              setSearchValue(val.target.value);
            }}
          />
          <BasicDatePicker
            title={TEXT_TITLE.Departure_date}
            className="w-[160px] h-10"
            placeholder="YYYY/MM/DD"
            onChange={(value) => setDepartureDate(value)}
          />
          <BasicDatePicker
            title={TEXT_TITLE.Return_Date}
            className="w-[160px] h-10"
            placeholder="YYYY/MM/DD"
            onChange={(value) => setReturnDate(value)}
          />
          <div className="flex items-end">
            <BasicButton
              icon={<SearchSVG colorSvg="white" />}
              className="flex items-center w-[120px]"
              styleType="accept"
              onClick={() =>
                setParams({
                  ...params,
                  keyword: searchValue,
                  departure_date: departureDate?.format('YYYY/MM/DD'),
                  return_date: returnDate?.format('YYYY/MM/DD'),
                })
              }
            >
              <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
            </BasicButton>
          </div>
        </div>
      </div>

      <div className="p-2 border border-[#DCDEE3] rounded-[12px] mt-6">
        <BasicTable
          className="!mt-0"
          tableProps={{
            scroll: { x: 1260, y: 500 },
            loading: isLoading,
            columns: defaultColumns,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            rowKey: 'id',
          }}
          page={Number(params.page)}
          pageSize={params.limit as number}
          onChangePage={(p: number) => {
            setParams({ ...params, page: p });
          }}
          total={total}
          onSelectPageSize={(v) => setParams({ ...params, limit: v })}
        />
      </div>
      <div className="flex justify-center mt-6 ">
        <BasicButton
          className="w-[360px] flex items-center justify-center rounded-[4px] shadow-md !bg-white hover:opacity-40 font-medium !text-[#FF3B30] !border-[#DCDEE3]"
          onClick={onClose}
        >
          <Image preview={false} src={IconCancelRed} width={18} height={18} />
          <p className="pl-2 font-medium">閉じる</p>
        </BasicButton>
      </div>
    </>
  );
};

export default FormSelectTour;
