import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import type { ReportInvoiceParams } from '@/apis/accounting/saleManagement';
import { getListBusinessPartner } from '@/apis/businessPartner';
import IconCancelRed from '@/assets/imgs/common-icons/cancel-red.svg';
import ArrowRight from '@/assets/imgs/svg/arrow-right-02.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicTable from '@/components/Commons/BasicTable';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_PLACEHOLDER } from '@/constants/commonText';
import { DownloadOutlined, FilterOutlined } from '@ant-design/icons';
import { Form, Image, Select } from 'antd';
import type { AxiosResponse } from 'axios';
import type { Moment } from 'moment';
import moment from 'moment';
import type { FieldData } from 'rc-field-form/lib/interface';
import { useEffect, useMemo, useRef, useState } from 'react';
import { CSVLink } from 'react-csv';
import BasicCheckboxGroup from '../Commons/BasicCheckboxGroup';
import BasicDatePicker from '../Commons/BasicDatePicker';
import EditableCell from '../Commons/EditableCell';
import { openNotificationFail, openNotificationSuccess } from '../Notification';

import STATUS_CODE from '@/constants/statusCode';
import { BaseOptionType, filterBusinessPartner } from '@/utils';

const listKeyToDelete = ['key', 'id', 'invoice_id', 0, '0'];
const typeCsv = [
  { type: 'sale', fileName: '売上', issueDepartmentCD: '010535', targetBusinessPartner: 1 },
  { type: 'purchase', fileName: '仕入', issueDepartmentCD: '010535', targetBusinessPartner: 2 },
  { type: 'deposit', fileName: '入金', issueDepartmentCD: '010535', targetBusinessPartner: 3 },
  { type: 'payment', fileName: '支払い', issueDepartmentCD: '010535', targetBusinessPartner: 4 },
];

const labelsColumn = [
  { label: '管理№(自動採番)', formType: 'input', key: 1 },
  { label: '元データ管理№	', formType: 'input', key: 2 },
  { label: '元データ行№	', formType: 'input', key: 3 },
  { label: '発生システム	', formType: 'input', key: 4 },
  { label: 'データ種類	', formType: 'input', key: 5 },
  { label: 'データ発生事業所	', formType: 'input', key: 6 },
  { label: '発行部門	', formType: 'input', key: 7 },
  { label: '記帳日(伝票日付)', formType: 'input', key: 8 },
  { label: '入力管理№	', formType: 'input', key: 9 },
  { label: '入力管理行№	', formType: 'input', key: 10 },
  { label: '伝票№	', formType: 'input', key: 11 },
  { label: '行番号	', formType: 'input', key: 12 },
  { label: '借方 勘定科目コード	', formType: 'input', key: 13 },
  { label: '借方 補助項目コード	', formType: 'input', key: 14 },
  { label: '借方 負担部門コード	', formType: 'input', key: 15 },
  { label: '借方 取引先区分	', formType: 'input', key: 16 },
  { label: '借方 取引先コード	', formType: 'input', key: 17 },
  {
    label: '借方 消費税区分	',
    formType: 'input',
    key: 18,
  },
  {
    label: '借方 消費税計算区分	',
    formType: 'input',
    key: 19,
  },
  { label: '借方 金額	', formType: 'inputNumber', inputProps: { isRightAlign: true }, key: 20 },
  {
    label: '借方 消費税額	',
    formType: 'inputNumber',
    inputProps: { isRightAlign: true },
    key: 21,
  },
  { label: '借方 合計	', formType: 'inputNumber', inputProps: { isRightAlign: true }, key: 22 },
  { label: '借方 摘要	', formType: 'input', key: 23 },
  { label: '貸方 勘定科目コード	', formType: 'input', key: 24 },
  { label: '貸方 補助項目コード	', formType: 'input', key: 25 },
  { label: '貸方 負担部門コード	', formType: 'input', key: 26 },
  { label: '貸方 取引先区分	', formType: 'input', key: 27 },
  { label: '貸方 取引先コード	', formType: 'input', key: 28 },
  {
    label: '貸方 消費税区分	',
    formType: 'input', //task 85 NG cột 貸方 消費税区分 không có dấu phẩy
    inputProps: { isRightAlign: true },
    key: 29,
  },
  {
    label: '貸方 消費税計算区分	',
    formType: 'inputNumber',
    inputProps: { isRightAlign: true },
    key: 30,
  },
  { label: '貸方 金額	', formType: 'inputNumber', inputProps: { isRightAlign: true }, key: 31 },
  {
    label: '貸方 消費税額	',
    formType: 'inputNumber',
    inputProps: { isRightAlign: true },
    key: 32,
  },
  { label: '貸方 合計	', formType: 'inputNumber', inputProps: { isRightAlign: true }, key: 33 },
  { label: '貸方 摘要	', formType: 'input', key: 34 },
  // { label: '工事 工事コード	', formType: 'input' },
  // { label: '工事 工種コード	', formType: 'input' },
  // { label: '工事 工事摘要用業者コード	', formType: 'input' },
  // { label: '工事 工事摘要	', formType: 'input' },
  // { label: '工事 財務摘要用業者コード	', formType: 'input' },
  // { label: '工事 財務摘要	', formType: 'input' },
  // { label: '工事 財務摘要２	', formType: 'input' },
  // { label: '工事 仕入業者コード	', formType: 'input' },
  // { label: '取消元データ管理№	', formType: 'input' },
  // { label: '取消元データ行№	', formType: 'input' },
  // { label: '手形電債区分	', formType: 'input' },
  // { label: '手形：券面番号	', formType: 'input' },
  // { label: '手形：振出日	', formType: 'input' },
  // { label: '手形：満期日	', formType: 'input' },
  // { label: '手形：サイト	', formType: 'input' },
  // { label: '手形：金額	', formType: 'inputNumber', inputProps: { isRightAlign: true } },
  // { label: '手形：取引先区分	', formType: 'input' },
  // { label: '手形：取引先コード	', formType: 'input' },
  // { label: '手形：振出人コード・支払先コード	', formType: 'input' },
  // { label: '手形：振出人名・支払先名	', formType: 'input' },
  // { label: '手形：振出地住所・券面振出人	', formType: 'input' },
  // { label: '手形：支払地	', formType: 'input' },
  // { label: '手形：支払場所　銀行	', formType: 'input' },
  // { label: '手形：支払場所　支店	', formType: 'input' },
  // { label: '手形：手形取引形態	', formType: 'input' },
  // { label: '手形：手形名称コード	', formType: 'input' },
  // { label: '手形：営業所コード	', formType: 'input' },
  // { label: '電債：でんさい記録番号	', formType: 'input' },
  // { label: '電債：振出日	', formType: 'input' },
  // { label: '電債：満期日	', formType: 'input' },
  // { label: '電債：サイト	', formType: 'input' },
  // { label: '電債：金額	', formType: 'inputNumber', inputProps: { isRightAlign: true } },
  // { label: '電債：取引先区分	', formType: 'input' },
  // { label: '電債：取引先コード	', formType: 'input' },
  // { label: '電債：取引先銀行	', formType: 'input' },
  // { label: '電債：取引先支店	', formType: 'input' },
  // { label: '電債：取引先預金種目	', formType: 'input' },
  // { label: '電債：取引先口座番号	', formType: 'input' },
  // { label: '電債；取引先利用者番号	', formType: 'input' },
  // { label: '電債：依頼人Ref№	', formType: 'input' },
  // { label: '電債：譲渡制限有無	', formType: 'input' },
  // { label: '電債：保証随伴有無	', formType: 'input' },
  // { label: '電債：自社取引銀行	', formType: 'input' },
  // { label: '電債：自社利用者番号	', formType: 'input' },
  // { label: '電債：自社請求者名	', formType: 'input' },
  // { label: '電債：管理部門	', formType: 'input' },
  // { label: '電債：備考	', formType: 'input' },
  { label: '登録者	', formType: 'input', key: 82 },
  { label: '登録日時	', formType: 'input', key: 83 },
  { label: '更新者	', formType: 'input', key: 84 },
  { label: '更新日時	', formType: 'input', key: 85 },
  // { label: 'アドオン連携区分	', formType: 'input' },
  // { label: 'アドオン連携日時	', formType: 'input' },
  // { label: 'アドオン連携者', formType: 'input' },
];

const FormAccountingCollaboration = ({
  onClose,
  apiGetList,
  apiMarkInvoiceItem,
  type,
  onReloadData,
}: {
  onClose: () => void;
  apiGetList: (params: ReportInvoiceParams) => Promise<
    AxiosResponse<any> & {
      error?: any;
    }
  >;
  apiMarkInvoiceItem: (body: ReportInvoiceParams) => Promise<
    AxiosResponse<any> & {
      error?: any;
    }
  >;
  type: 'sale' | 'purchase' | 'deposit' | 'payment';
  onReloadData: () => void;
}) => {
  const refExportCsv = useRef(null);
  const [total, setTotal] = useState<number>(1);
  const [data, setData] = useState<any[]>();
  const [allFormData, setAllFormData] = useState<Record<string, any>>({});
  const [isNotIncludeAccountingLinked, setIsNotIncludeAccountingLinked] = useState<boolean>(true);
  const [csvData, setCsvData] = useState<any[]>([]);
  // const [listIdMask, setListIdMask] = useState<number[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [startDate, setStartDate] = useState<Moment | null>(null);
  const [endDate, setEndDate] = useState<Moment | null>(null);
  const [dataSource, setDataSource] = useState<Record<string, any>[]>([]);
  const [isExportAndMarkCSV, setIsExportAndMarkCSV] = useState<boolean>(false);
  const [optionsBusinessPartner, setOptionsBusinessPartner] = useState<BaseOptionType[]>();
  const [params, setParams] = useState<ReportInvoiceParams>({
    page: 1,
    limit: 30,
    include_accounting_linked: isNotIncludeAccountingLinked ? undefined : 1,
  });
  const [firstCodeBusinessPartner, setFirstCodeBusinessPartner] = useState<string>();
  const [secondCodeBusinessPartner, setSecondCodeBusinessPartner] = useState<string>();
  const [firstSearchSelect, setFirstSearchSelect] = useState<string>('');
  const [secondSearchSelect, setSecondSearchSelect] = useState<string>('');

  const [form] = Form.useForm();

  const currentTypeCsv = useMemo(() => {
    const result = typeCsv.find((item) => item.type === type);
    return result || { type: 'sale', fileName: '売上', issueDepartmentCD: '010535' };
  }, [type]);

  const formatOptionBusinessPartner = async () => {
    const targetBusinessPartner = typeCsv.find((item) => item.type === type)?.targetBusinessPartner;
    const resData = await getListBusinessPartner({
      page: 1,
      limit: 'all',
      target: targetBusinessPartner ?? undefined,
      order: 'desc',
      order_by: 'business_partner_code',
    });
    const listCompany = resData?.data?.data;
    if (listCompany) {
      const newList = listCompany?.map((item: BusinessPartnerDetailType) => {
        return {
          label: item.business_partner_name,
          value: item.business_partner_code?.toString(),
          id: item.id,
        };
      });
      newList.sort((a, b) => Number(a.value) - Number(b.value));
      setOptionsBusinessPartner(newList);
      return;
    }
    setOptionsBusinessPartner([]);
  };

  useEffect(() => {
    formatOptionBusinessPartner();
  }, [type]);

  const listIDBusinessPartner = useMemo(() => {
    if (!firstCodeBusinessPartner && !secondCodeBusinessPartner) return [];
    return optionsBusinessPartner
      ?.filter(
        (item) =>
          (!firstCodeBusinessPartner || Number(item.value) >= Number(firstCodeBusinessPartner)) &&
          (!secondCodeBusinessPartner || Number(item.value) <= Number(secondCodeBusinessPartner)),
      )
      ?.map((item) => item.id);
  }, [optionsBusinessPartner, firstCodeBusinessPartner, secondCodeBusinessPartner]);

  const handleSearchByValue = () => {
    setParams({
      ...params,
      page: 1,
      limit: 30,
      start: startDate ? moment(startDate).format('YYYY-MM-DD') : undefined,
      finish: endDate ? moment(endDate).format('YYYY-MM-DD') : undefined,
      include_accounting_linked: isNotIncludeAccountingLinked ? undefined : 1,
      business_partner_ids:
        listIDBusinessPartner?.length > 0 ? [...listIDBusinessPartner] : undefined,
    });
  };

  useEffect(() => {
    if (startDate && endDate && moment(endDate).isBefore(startDate)) {
      setStartDate(endDate);
    }
  }, [endDate]);

  useEffect(() => {
    if (startDate && endDate && moment(startDate).isAfter(endDate)) {
      setEndDate(startDate);
    }
  }, [startDate]);

  const defaultColumns = [
    ...labelsColumn.map((item) => {
      return {
        title: item.label,
        dataIndex: item.key,
        key: item.key,
        width: 120,
        formType: item.formType,
        inputProps: item.inputProps,
      };
    }),
  ];

  const columns = defaultColumns.map((col) => {
    return {
      ...col,
      onCell: (record: Record<string, string>) => {
        const cellProps = {
          record,
          isEditPage: true,
          editable: true,
          dataIndex: col.dataIndex,
          title: col.title,
          formType: col.formType,
          inputProps: col.inputProps,
          form,
        };
        return cellProps;
      },
    };
  });

  const fetchAllData = async () => {
    setIsLoading(true);
    const paramsList = { ...params };
    delete paramsList.page;
    delete paramsList.limit;
    const resData = await apiGetList({ ...paramsList });
    const dataSourceApi = resData?.data?.data?.map((item: any, index) => {
      return { ...item, key: index + 1 };
    });
    setData(dataSourceApi);
    if (dataSourceApi) {
      setTotal(resData?.data?.data?.length);
    } else {
      openNotificationFail(MESSAGE_ALERT.NO_DATA_TO_EXPORT);
    }
  };

  useEffect(() => {
    fetchAllData();
    setIsExportAndMarkCSV(false);
  }, [params.finish, params.start, params.include_accounting_linked, params.business_partner_ids]);

  useEffect(() => {
    if (data) {
      const newFormData: Record<string, any> = {};
      for (let index = 0; index < data.length; index++) {
        const item = data[index];
        for (const key in item) {
          if (item.hasOwnProperty(key) && !listKeyToDelete.includes(key)) {
            const keyForm = `${index + 1}.${key}`;
            newFormData[keyForm] = item[key];
          }
        }
      }
      setAllFormData({ ...newFormData });
    }
  }, [data]);
  useEffect(() => {
    if (dataSource) {
      dataSource.forEach((item, index) => {
        Object.keys(item).forEach((key) => {
          if (!listKeyToDelete.includes(key)) {
            const keyForm = `${
              (Number(params.page) - 1) * Number(params.limit) + index + 1
            }.${key}`;
            if (Object.keys(allFormData).includes(keyForm)) {
              form.setFieldValue(keyForm, allFormData[keyForm]);
            } else {
              form.setFieldValue(keyForm, item[key]);
            }
          }
        });
      });
    }
    setIsLoading(false);
  }, [dataSource]);

  useEffect(() => {
    if (data) {
      setIsLoading(true);
      const keysColumn = labelsColumn.map((item) => item.key);
      const dataSourceApi = [...data];
      if (dataSourceApi) {
        const dataSplice = [...dataSourceApi]
          .splice(((params.page as number) - 1) * (params.limit as number), params.limit as number)
          .map((item) => {
            let sourceFormat: Record<string, any> = {};
            for (const key in item) {
              if (keysColumn.includes(Number(key)) || key === 'key') {
                sourceFormat = { ...sourceFormat, [key]: item[key] };
              }
            }
            return sourceFormat;
          });
        setDataSource([...dataSplice]);
      }
    } else {
      setIsLoading(false);
    }
  }, [params.page, params.limit, data]);

  const onExportCsv = async ({ isPreview }: { isPreview?: boolean }) => {
    setIsLoading(true);

    const resultObj: Record<string, Record<string, any>> = {};
    const keys = Object.keys(allFormData);

    for (let i = 0; i < keys.length; i++) {
      const key = keys[i];
      const [index, property] = key.split('.');
      const value = allFormData[key];

      if (!resultObj[index]) {
        resultObj[index] = {};
      }

      resultObj[index][property] = value;
    }

    const formatDataCsv: any[][] = [];
    const resultObjValues = Object.values(resultObj);

    for (let i = 0; i < resultObjValues.length; i++) {
      const item = resultObjValues[i];
      formatDataCsv.push(Object.values(item));
    }
    setCsvData(formatDataCsv);
    if (!isPreview) {
      const paramsExport = {
        start: startDate ? moment(startDate).format('YYYY-MM-DD') : undefined,
        finish: endDate ? moment(endDate).format('YYYY-MM-DD') : undefined,
        include_accounting_linked: isNotIncludeAccountingLinked ? undefined : 1,
        business_partner_ids:
          listIDBusinessPartner?.length > 0 ? [...listIDBusinessPartner] : undefined,
      };
      const resMark = await apiMarkInvoiceItem(paramsExport);
      if (resMark?.status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.SUCCESSFUL_EXPORT_CSV);
        setIsExportAndMarkCSV(true);
      }
    }
    setTimeout(() => {
      refExportCsv.current.link.click();
    }, 1000);

    if (!isPreview) {
      setTimeout(() => {
        onReloadData?.();
      }, 500);
    }
    setIsLoading(false);
  };

  const handleListItemChange = (changeField: FieldData[]) => {
    const nameFieldChange = changeField?.[0]?.name?.[0];
    const valueChange = changeField?.[0]?.value;
    setAllFormData({ ...allFormData, [nameFieldChange]: valueChange });
  };

  const highlightText = (text: string, search: string) => {
    if (!search) return text;
    const regex = new RegExp(`(${search})`, 'gi');
    try {
      return text?.split?.(regex).map((part, index) =>
        regex.test(part) ? (
          <span key={index} style={{ color: 'red' }}>
            {part}
          </span>
        ) : (
          part
        ),
      );
    } catch (error) {
      return text;
    }
  };

  const secondRenderOption = useMemo(() => {
    if (!optionsBusinessPartner) return [];
    return optionsBusinessPartner.filter(
      (item) => !firstCodeBusinessPartner || Number(item.value) >= Number(firstCodeBusinessPartner),
    );
  }, [optionsBusinessPartner, firstCodeBusinessPartner]);

  return (
    <>
      <div className="formAccountingCollaboration flex justify-between px-4">
        <div className="flex gap-4">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-1">
              <div className="firstSelectBP">
                <div className="mb-2 text-[13px] leading-4 font-medium">取引先コード(取引先名)</div>
                <Select
                  showSearch
                  allowClear
                  className="min-w-[200px] [&_.ant-select-selector]:!rounded-lg
                    [&_.ant-select-selection-search-input]:!min-h-[40px]
                    [&_.ant-select-selector]:!min-h-10 max-w-[350px]
                    [&_.ant-select-selection-placeholder]:flex
                    [&_.ant-select-selection-placeholder]:items-center
                    !h-fit [&_.ant-select-selector]:!h-fit"
                  filterOption={(input, option) =>
                    filterBusinessPartner(input, option as BaseOptionType)
                  }
                  placeholder={TEXT_PLACEHOLDER.Please_select}
                  onChange={setFirstCodeBusinessPartner}
                  onSearch={setFirstSearchSelect}
                  searchValue={firstSearchSelect}
                >
                  {optionsBusinessPartner?.map((item) => (
                    <Select.Option key={item.value} value={item.value} label={item.label}>
                      <span className="text-sm font-semibold">
                        {highlightText(item.value, firstSearchSelect)}
                      </span>
                      <br />
                      <span className="text-xs">
                        {highlightText(item.label, firstSearchSelect)}
                      </span>
                    </Select.Option>
                  ))}
                </Select>
              </div>
              <div className="">
                <div className="h-6" />
                <div className="flex items-center">
                  <Image preview={false} src={ArrowRight} alt="arrow right" />
                </div>
              </div>
              <div className="firstSelectBP">
                <div className="mb-2 text-[13px] leading-4 font-medium">取引先コード(取引先名)</div>
                <Select
                  showSearch
                  allowClear
                  className="min-w-[200px] [&_.ant-select-selector]:!rounded-lg
                    [&_.ant-select-selection-search-input]:!min-h-[40px]
                    [&_.ant-select-selector]:!min-h-10 max-w-[350px]
                    [&_.ant-select-selection-placeholder]:flex
                    [&_.ant-select-selection-placeholder]:items-center
                    !h-fit [&_.ant-select-selector]:!h-fit"
                  filterOption={(input, option) =>
                    filterBusinessPartner(input, option as BaseOptionType)
                  }
                  placeholder={TEXT_PLACEHOLDER.Please_select}
                  onChange={setSecondCodeBusinessPartner}
                  onSearch={setSecondSearchSelect}
                  searchValue={secondSearchSelect}
                >
                  {secondRenderOption?.map((item) => (
                    <Select.Option key={item.value} value={item.value} label={item.label}>
                      <span className="text-sm font-semibold">
                        {highlightText(item.value, secondSearchSelect)}
                      </span>
                      <br />
                      <span className="text-xs">
                        {highlightText(item.label, firstSearchSelect)}
                      </span>
                    </Select.Option>
                  ))}
                </Select>
              </div>
            </div>
            <div className="flex items-center gap-1">
              <BasicDatePicker
                className="!h-10"
                value={startDate}
                onChange={setStartDate}
                title={'集計開始日'}
              />
              <div className="">
                <div className="h-6" />
                <div className="flex items-center">
                  <Image preview={false} src={ArrowRight} alt="arrow right" />
                </div>
              </div>
              <BasicDatePicker
                className="!h-10"
                value={endDate}
                onChange={setEndDate}
                title={'集計終了日'}
              />
            </div>
            <div className="">
              <div className="h-6" />
              <div className="flex items-center">
                <BasicCheckboxGroup
                  value={isNotIncludeAccountingLinked ? [true] : []}
                  onChange={(val) => {
                    if (val.length > 0) {
                      setIsNotIncludeAccountingLinked(true);
                    } else setIsNotIncludeAccountingLinked(false);
                  }}
                  options={[{ label: '未連携のみ', value: true }]}
                />
              </div>
            </div>
          </div>
          <div className="flex flex-col justify-center">
            <div className="h-6" />
            <BasicButton
              icon={<FilterOutlined style={{ color: 'white' }} />}
              className="flex items-center w-[120px]"
              styleType="accept"
              onClick={handleSearchByValue}
            >
              <p className="ml-2">{'絞り込み'}</p>
            </BasicButton>
          </div>
        </div>
      </div>

      <div className="p-2 border border-[#DCDEE3] rounded-[12px] mt-6">
        <Form
          form={form}
          component={false}
          onFieldsChange={(changeField: FieldData[]) => handleListItemChange(changeField)}
        >
          <BasicTable
            className="!mt-0"
            tableProps={{
              scroll: { x: 8000, y: 600 },
              loading: isLoading,
              components: {
                body: {
                  cell: EditableCell,
                },
              },
              columns,
              dataSource: dataSource,
              bordered: false,
              pagination: false,
              rowKey: 'id',
            }}
            page={params.page as number}
            pageSize={params.limit as number}
            onChangePage={(p: number) => {
              setIsLoading(true);
              setParams({ ...params, page: p });
            }}
            total={total}
            onSelectPageSize={(v) => {
              setIsLoading(true);
              setParams({ ...params, limit: v, page: 1 });
            }}
          />
        </Form>
      </div>
      {isExportAndMarkCSV && (
        <div className="text-main-color text-[18px] text-right mt-2 font-bold">
          {MESSAGE_ALERT.SUCCESSFUL_EXPORT_CSV}
        </div>
      )}
      <div className="flex justify-center mt-6 gap-5">
        <BasicButton
          className="w-[290px] flex items-center justify-center rounded-[4px] shadow-md !bg-white hover:opacity-40 font-medium !text-[#FF3B30] !border-[#DCDEE3]"
          onClick={() => {
            setIsExportAndMarkCSV(false);
            onClose();
          }}
        >
          <Image preview={false} src={IconCancelRed} width={18} height={18} />
          <p className="pl-2 font-medium">{TEXT_ACTION.CLOSE}</p>
        </BasicButton>
        <BasicButton
          disabled={isLoading}
          icon={<DownloadOutlined style={{ color: 'white' }} />}
          className="w-[290px] flex items-center !text-white !bg-[#FDAF2E] hover:!bg-[#FDAF2E]"
          styleType="accept"
          onClick={() => {
            onExportCsv({ isPreview: true });
          }}
        >
          {TEXT_ACTION.CSV_Export_for_confirmation}
        </BasicButton>
        <BasicButton
          disabled={isLoading}
          icon={<DownloadOutlined style={{ color: 'white' }} />}
          className="w-[290px] flex items-center !text-white "
          styleType="accept"
          onClick={() => {
            onExportCsv({ isPreview: false });
          }}
        >
          {TEXT_ACTION.CSV_Export}
        </BasicButton>
        <div className="opacity-0 w-0 h-0">
          <CSVLink
            ref={refExportCsv}
            filename={
              '41_' +
              currentTypeCsv.issueDepartmentCD +
              '_' +
              moment().format('YYYYMMDD_HHmmss_') +
              currentTypeCsv.fileName +
              '.csv'
            }
            data={csvData}
          />
        </div>
      </div>
    </>
  );
};

export default FormAccountingCollaboration;
