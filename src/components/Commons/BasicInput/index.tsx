import React from 'react';
import type { InputProps, InputRef } from 'antd';
import { Input } from 'antd';

interface Props extends InputProps {
  title?: string | React.ReactNode | any;
  classNameContainer?: string;
  ref?: React.MutableRefObject<InputRef>;
}
const BasicInput: React.FC<Props> = (props) => {
  return (
    <div className={`[&_.ant-input]:!rounded-lg flex flex-col ${props.classNameContainer}`}>
      {props.title ? (
        <div className="mb-2 text-[13px] leading-4 font-medium">
          {props.title} {props.required && <span className="text-[red]">*</span>}
        </div>
      ) : null}
      <Input ref={props?.ref} {...props} className={`${props.className} h-10 `} title={null} />
    </div>
  );
};

export default BasicInput;
