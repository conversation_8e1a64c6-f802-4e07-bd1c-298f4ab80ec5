import LeftScheduleIcon from '@/assets/imgs/common-icons/arrow-left-tabbar.svg';
import { Image, Tabs } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';

const options = ['left', 'right'];
type Props = {
  items: { label: string; key: string; children: React.ReactNode }[];
  hideMoreButton?: boolean;
  rightButton?: JSX.Element;
  onChange?: (activeKey: string) => void;
  activeKey: string;
  setActiveKey?: (key: string) => void;
};

const BasicTabsHandle: React.FC<Props> = ({
  items,
  hideMoreButton = false,
  rightButton,
  onChange,
  activeKey,
  setActiveKey,
}) => {
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  const tabBarRef = useRef<HTMLDivElement>(null);

  const scrollTabBar = (direction: 'left' | 'right') => {
    if (tabBarRef.current) {
      const tabList = tabBarRef.current.querySelector('.ant-tabs-nav-list') as HTMLDivElement;
      if (tabList) {
        const currentTransform = tabList.style.transform;
        const currentTranslateX = currentTransform
          ? parseInt(currentTransform.match(/translateX\((-?\d+)px\)/)?.[1] || '0', 10)
          : 0;
        const scrollAmount = direction === 'left' ? 100 : -100;
        const newTranslateX = currentTranslateX + scrollAmount;

        // Check boundaries
        const maxScroll = tabList.scrollWidth - tabBarRef.current.clientWidth + 100;
        if (direction === 'left') {
          if (newTranslateX * -1 <= 100) {
            tabList.style.transform = `translateX(0px)`;
            setCanScrollLeft(false);
            setCanScrollRight(true);
          } else {
            tabList.style.transform = `translateX(${newTranslateX}px)`;
            setCanScrollLeft(true);
            setCanScrollRight(true);
          }
        } else {
          if (newTranslateX * -1 > maxScroll) {
            setCanScrollLeft(true);
            setCanScrollRight(false);
          } else {
            tabList.style.transform = `translateX(${newTranslateX}px)`;
            setCanScrollLeft(true);
            setCanScrollRight(true);
          }
        }
      }
    }
  };

  const OperationsSlot = useMemo(() => {
    return {
      left: (
        <div className="flex h-full items-center mr-2">
          <Image
            onClick={() => canScrollLeft && scrollTabBar('left')}
            src={LeftScheduleIcon}
            alt="left icon"
            width={20}
            height={20}
            preview={false}
            className="!w-5 !h-5 cursor-pointer"
          />
        </div>
      ),
      right: (
        <div className="flex h-full items-center ml-2">
          <Image
            onClick={() => canScrollRight && scrollTabBar('right')}
            src={LeftScheduleIcon}
            alt="right icon"
            width={20}
            height={20}
            preview={false}
            className="!w-5 !h-5 cursor-pointer rotate-180"
          />
          {rightButton && rightButton}
        </div>
      ),
    };
  }, [canScrollLeft, canScrollRight]);

  const slot = useMemo(() => {
    if (options.length === 0) return null;

    return options.reduce(
      (acc, direction) => ({ ...acc, [direction]: OperationsSlot[direction] }),
      {},
    );
  }, [options, canScrollLeft, canScrollRight]);

  useEffect(() => {
    const tabBarElement = tabBarRef.current?.querySelector('.ant-tabs-nav-wrap');
    if (tabBarElement) {
      tabBarRef.current = tabBarElement as HTMLDivElement;
    }
  }, []);

  return (
    <div style={{ overflowX: 'auto', flex: 1 }} ref={tabBarRef}>
      <Tabs
        className={`${hideMoreButton ? '[&_.ant-tabs-nav-operations]:!hidden' : ''}`}
        tabBarExtraContent={slot}
        items={items}
        activeKey={activeKey}
        onChange={(key) => {
          setActiveKey?.(key);
          onChange?.(key);
        }}
      />
    </div>
  );
};

export default BasicTabsHandle;
