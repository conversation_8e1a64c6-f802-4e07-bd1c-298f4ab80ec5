import { roundNumber } from '@/utils';
import { Input } from 'antd';
import React, { useEffect, useState } from 'react';

interface NumericInputProps {
  title?: string | React.ReactNode | any;
  style?: React.CSSProperties;
  value?: any;
  onChange?: (value: any) => void;
  maxLength?: number;
  placeholder?: string;
  disabled?: boolean;
  allowDecimal?: boolean;
  isPercent?: boolean;
  required?: boolean;
  isRightAlign?: boolean;
  isRoundNumber?: boolean;
  className?: string;
  notAllowNegativeNumber?: boolean;
  isNotFormatNumber?: boolean;
}

const BasicNumbericInput: React.FC<NumericInputProps> = ({ ...props }) => {
  const {
    value: initialValue,
    onChange,
    maxLength,
    placeholder,
    isRightAlign,
    isPercent = false,
    allowDecimal,
    required,
    isRoundNumber,
    notAllowNegativeNumber = false,
    isNotFormatNumber = false,
  } = props;
  const [value, setValue] = useState<string>();

  const formatPercent = (num: string) => {
    let formattedNumber = num;
    const lastChar = num?.slice(-1);
    if (isPercent && lastChar !== ',' && lastChar !== '.') {
      const number = parseFloat(num);
      if (!isNaN(number)) {
        formattedNumber = (roundNumber(number * 10) / 10).toFixed(1);
      }
      return formattedNumber;
    }
  };

  const toHalfWidth = (str: string) => {
    try {
      return str
        ?.replace(/[\uFF01-\uFF5E]/g, (ch) => String.fromCharCode(ch.charCodeAt(0) - 0xfee0))
        ?.replace(/\u3000/g, ' ');
    } catch (error) {
      console.log('toHalfWidth error', error);
      return str;
    }
  };

  useEffect(() => {
    if (initialValue || initialValue === 0) {
      if (isPercent) {
        const formattedNumber = formatPercent(initialValue.toString());
        setValue?.(formattedNumber);
        return;
      }
      setValue?.(initialValue.toString());
    }
  }, [initialValue]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value: inputValue } = e.target;
    const halfWidthValue = toHalfWidth(inputValue);
    const inputValueNew = halfWidthValue.replaceAll(/,/g, '');

    // Nếu không cho phép số âm, kiểm tra inputValueNew
    if (notAllowNegativeNumber && inputValueNew.startsWith('-')) {
      return; // Không cho phép nhập dấu âm
    }

    const reg = allowDecimal || isPercent ? /^-?\d*\.?\d{0,3}$/ : /^-?\d*$/;
    if (reg.test(inputValueNew) || inputValueNew === '' || inputValueNew === '-') {
      const rawValue = halfWidthValue.replaceAll(/,/g, '');
      if (!isNaN(Number(rawValue)) || rawValue === '-') {
        setValue(rawValue);
        // if (onChange && !isPercent) {
        //   onChange?.(rawValue);
        // }
      }
    }
  };

  const handleBlur = () => {
    let valueTemp = value;
    if (value?.charAt(value?.length - 1) === '.' || value === '-') {
      valueTemp = value?.slice(0, -1);
    }
    if (allowDecimal) {
      valueTemp = valueTemp?.replace(/(\d*\.\d{0,3})\d*/, '$1');
    } else {
      valueTemp = valueTemp?.replace(/0*(\d+)/, '$1');
    }
    if (isPercent) {
      const newPercent = formatPercent(valueTemp);
      setValue?.(newPercent);
    } else if (isNaN(Number(valueTemp))) {
      setValue?.('0');
      onChange?.(0);
      return;
    } else {
      setValue?.(valueTemp);
    }
    onChange?.(valueTemp);
  };

  const formatNumber = (num: string) => {
    const firstChar = num?.[0];
    if (firstChar === '.') {
      return num;
    }
    if (isRoundNumber) {
      if (isNaN(roundNumber(Number(num)))) return 0;
      const roundNum = roundNumber(Number(num)).toString();
      const resultFormat = roundNum?.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
      return resultFormat;
    }
    if (isNotFormatNumber && !isNaN(Number(num))) {
      return Number(num);
    }

    const resultFormat = num?.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    return resultFormat;
  };

  return (
    <div className={`[&_.ant-input]:!rounded-lg flex flex-col`}>
      {props?.title ? (
        <div className="mb-2 text-[13px] leading-4 font-medium">
          {props?.title} {required && <span className="text-[red]">*</span>}
        </div>
      ) : null}
      <Input
        {...props}
        style={{ height: '40px', textAlign: isRightAlign ? 'right' : 'left' }}
        value={formatNumber(value)}
        onChange={handleChange}
        onBlur={handleBlur}
        title={undefined}
        placeholder={placeholder}
        maxLength={maxLength ?? 16}
      />
    </div>
  );
};

export default BasicNumbericInput;
