import type { InputProps } from 'antd';
import { Form } from 'antd';
import React, { useEffect, useState } from 'react';
import BasicInput from '../BasicInput';
import BasicSelect from '../BasicSelect';
import BasicSwitch from '../BasicSwitch';
import BasicCheckboxGroup from '../BasicCheckboxGroup';
import type { Rule } from 'antd/lib/form';
import BasicNumbericInput from '../BasicNumbericInput';
import { formatMoney } from '@/utils';
import BasicDatePicker from '../BasicDatePicker';

type Item = Record<string, string>;

interface EditableCellProps {
  editable: boolean;
  isEditPage?: boolean;
  dataIndex: keyof Item;
  record: Item;
  options?: { label: string; value: string }[];
  formType?: 'input' | 'select' | 'switch' | 'checkbox' | 'inputNumber' | 'textOnly' | 'datePicker';
  inputProps?: InputProps & {
    isPercent?: boolean;
    isRightAlign?: boolean;
    isNumber?: boolean;
    notAllowNegativeNumber: boolean;
  }; // Fix: Modify the type declaration for inputProps
  disable?: boolean;
  ruleFormItem?: Rule[];
  form: any;
  maxLength: number;
  width: number | string;
}

const EditableCell: React.FC<React.PropsWithChildren<EditableCellProps>> = ({
  editable,
  isEditPage,
  children,
  dataIndex,
  record,
  formType,
  disable,
  options = [],
  inputProps,
  ruleFormItem,
  form,
  maxLength,
  width,
  ...restProps
}) => {
  const [editing, setEditing] = useState(false);
  const keyForm = `${record?.key}.${dataIndex}`;
  useEffect(() => {
    setEditing(isEditPage ?? false);
  }, [isEditPage]);

  let childNode = children;

  if (editable) {
    if (formType === 'input' || formType === 'inputNumber' || formType === 'select') {
      childNode = editing ? (
        <Form.Item style={{ margin: 'unset', marginBottom: 0 }} name={keyForm} rules={ruleFormItem}>
          {formType === 'input' ? (
            <BasicInput disabled={disable} {...(inputProps || {})} maxLength={maxLength} />
          ) : formType === 'inputNumber' ? (
            <BasicNumbericInput disabled={disable} {...(inputProps || {})} />
          ) : (
            <BasicSelect
              disabled={disable}
              options={options}
              showSearch
              filterOption={(input, option) => {
                return option?.props?.label?.toLowerCase().indexOf(input?.toLowerCase()) >= 0;
              }}
            />
          )}
        </Form.Item>
      ) : (
        <div
          className="editable-cell-value-wrap"
          style={{ paddingInlineEnd: 24, width: typeof width === 'number' ? `${width}px` : width }}
        >
          {formType === 'select'
            ? options?.find((item) => item?.value === form.getFieldValue(keyForm))?.label
            : children}
        </div>
      );
    } else if (formType === 'switch') {
      childNode = (
        <Form.Item style={{ margin: 'unset', marginBottom: 0 }} name={keyForm} rules={ruleFormItem}>
          <BasicSwitch
            style={{ maxWidth: '44px' }}
            checked={form.getFieldValue(keyForm)}
            disabled={!editing || disable}
          />
        </Form.Item>
      );
    } else if (formType === 'checkbox') {
      childNode = (
        <Form.Item
          style={{ margin: 'unset', marginBottom: 0, height: 40 }}
          name={keyForm}
          valuePropName="checked"
        >
          <BasicCheckboxGroup
            className={`${editing ? 'h-10 ' : ''} `}
            disabled={!editing || disable}
            options={options}
            value={form.getFieldValue(keyForm)}
          />
        </Form.Item>
      );
    } else if (formType === 'datePicker') {
      childNode = (
        <Form.Item
          style={{ margin: 'unset', marginBottom: 0, height: 40 }}
          name={keyForm}
          rules={ruleFormItem}
        >
          <BasicDatePicker
            disabled={!editing || disable}
            className="!h-10"
            placeholder="すべて"
            format={'YYYY/MM/DD'}
            hideSuffixIcon
            hideClearIcon
          />
        </Form.Item>
      );
    } else if (formType === 'textOnly') {
      childNode = (
        <Form.Item name={keyForm} rules={ruleFormItem}>
          <>
            <BasicInput className="hidden" />
            <div
              className="flex items-center h-10"
              style={{ justifyContent: inputProps?.isRightAlign ? 'end' : 'start' }}
            >
              {inputProps?.isNumber
                ? formatMoney(form.getFieldValue(keyForm) ?? 0)
                : form.getFieldValue(keyForm)}
            </div>
          </>
        </Form.Item>
      );
    }
  }

  if (!isEditPage && formType === 'inputNumber' && inputProps?.isPercent) {
    const number = parseFloat(form.getFieldValue(keyForm));
    if (!isNaN(number)) {
      childNode = (Math.round(number * 10) / 10).toFixed(1);
    }
  }
  return (
    <td
      {...restProps}
      style={{
        ...(((restProps as Record<string, any>)?.style as React.CSSProperties) ?? {}),
        ...(editing && formType
          ? { verticalAlign: 'top' }
          : { textAlign: inputProps?.isRightAlign ? 'right' : 'left' }),
      }}
    >
      {childNode}
    </td>
  );
};

export default EditableCell;
