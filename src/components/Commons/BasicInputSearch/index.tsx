import React from 'react';
import { Input } from 'antd';
import type { SearchProps } from 'antd/lib/input';

const { Search } = Input;

interface Props extends SearchProps {
  title?: string | React.ReactNode | any;
  classNameContainer?: string;
  className?: string;
  placeholder?: string;
}
const BasicInputSearch: React.FC<Props> = ({ title, ...props }) => {
  return (
    <div
      className={`[&_.ant-input]:!rounded-[4px] flex flex-col ${props.classNameContainer}
      [&_.ant-input-affix-wrapper]:h-10 [&_.ant-input-affix-wrapper]:!rounded [&_.ant-input-search-button]:h-10
       [&_.ant-input-group-addon]:hidden [&_.ant-input-affix-wrapper]:border-[#DCDEE3]
     `}
    >
      {title ? <div className="mb-2 text-[13px] leading-4 font-medium">{title}</div> : null}
      <Search
        {...props}
        className={`${props.className}`}
        placeholder={props.placeholder}
        allowClear
        // suffix={<div>search</div>}
      />
    </div>
  );
};

export default BasicInputSearch;
