import CalendarSVG from '@/components/SVG/CalendarSVG';
import { DatePicker } from 'antd';
import type { RangePickerProps } from 'antd/lib/date-picker';
import React from 'react';

const { RangePicker } = DatePicker;

interface BasicDateRangePickerProps {
  title?: string;
  required?: boolean;
}
const BasicDateRangePicker: React.FC<RangePickerProps & BasicDateRangePickerProps> = ({
  title,
  required = false,
  ...props
}) => {
  return (
    <div className="flex flex-col">
      {title ? (
        <div className="mb-2 text-[13px] leading-4 font-medium">
          {title} {required ? <span className="text-[red]">*</span> : ''}
        </div>
      ) : null}
      <RangePicker
        {...props}
        placeholder={['YYYY/MM/DD', 'YYYY/MM/DD']}
        style={{
          height: '36px',
          border: '1px solid #EBE9FA',
          borderRadius: '5px',
        }}
        format={'YYYY/MM/DD'}
        separator={<div> ~ </div>}
        suffixIcon={<CalendarSVG />}
      />
    </div>
  );
};

export default BasicDateRangePicker;
