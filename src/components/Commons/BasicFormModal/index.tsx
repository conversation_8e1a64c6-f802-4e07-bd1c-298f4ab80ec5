import CloseSVG from '@/components/SVG/CloseSVG';
import type { ModalProps } from 'antd';
import { Modal, Space } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import type { StyleButtonType } from '../BasicButton';
import BasicButton from '../BasicButton';
import { TEXT_ACTION } from '@/constants/commonText';

type BasicFormModalProps = {
  title?: string;
  content: JSX.Element | any;
  onSubmit?: () => void;
  onCancel?: () => void;
  style?: React.CSSProperties;
  titleStyle?: React.CSSProperties;
  buttonContainerStyle?: React.CSSProperties;
  buttonCloseStyle?: React.CSSProperties;
  buttonSubmitStyle?: React.CSSProperties;
  buttonCloseTitle?: string;
  buttonSubmitTitle?: string;
  isValidate?: boolean;
  iconButtonClose?: React.ReactNode;
  styleTypeButtonClose?: StyleButtonType;
  iconButtonSubmit?: React.ReactNode;
  styleTypeButtonSubmit?: StyleButtonType;
  isLoadingSubmit?: boolean;
  hideListButton?: boolean;
};

type OtherProps = React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>;

type Props = BasicFormModalProps & OtherProps & ModalProps;

export type BasicFormModalRef = {
  open: () => void;
  close: () => void;
};

const BasicFormModal = forwardRef<BasicFormModalRef, Props>(
  (
    {
      title,
      content,
      onSubmit,
      onCancel,
      titleStyle,
      buttonContainerStyle,
      buttonCloseStyle,
      buttonSubmitStyle,
      buttonCloseTitle,
      buttonSubmitTitle,
      isValidate,
      isLoadingSubmit,
      hideListButton = false,
      ...others
    },
    ref,
  ): React.ReactElement => {
    const [isModalOpen, setIsModalOpen] = useState(false);

    const open = () => {
      setIsModalOpen(true);
    };

    const close = () => {
      setIsModalOpen(false);
      onCancel?.();
    };

    useImperativeHandle(ref, () => ({
      open,
      close,
    }));

    const handleOk = () => {
      if (onSubmit) {
        onSubmit();
      }
      if (!isValidate) {
        close();
      }
    };

    return (
      <Modal
        {...others}
        className={`${others.className} [&_.ant-modal-content]:!rounded-xl`}
        open={isModalOpen}
        onCancel={close}
        onOk={handleOk}
        footer={null}
        closable={false}
        centered
        style={{ borderRadius: '12px' }}
        bodyStyle={{ padding: '32px', display: 'flex', flexDirection: 'column' }}
        width={426}
      >
        {title ? (
          <div className="flex justify-between items-center">
            <div />
            <span style={titleStyle} className="text-lg leading-8 font-bold text-[#000000]">
              {title}
            </span>
            <div onClick={close}>
              <CloseSVG />
            </div>
          </div>
        ) : null}

        <div className="py-[22px] modal_content">{content}</div>
        {!hideListButton && (
          <Space style={buttonContainerStyle} className="justify-between">
            <BasicButton
              icon={others?.iconButtonClose}
              styleType={others?.styleTypeButtonClose ?? 'back'}
              onClick={close}
              style={buttonCloseStyle}
              type="default"
              className="w-[164px] hover:!bg-[#CDCED4CC] border-[#CDCED4CC] flex items-center space-x-[8px] justify-center text-main-color"
            >
              {buttonCloseTitle ?? TEXT_ACTION.UNDO}
            </BasicButton>
            <BasicButton
              icon={others?.iconButtonSubmit}
              styleType={others?.styleTypeButtonSubmit ?? 'delete'}
              onClick={handleOk}
              style={buttonSubmitStyle}
              className="w-[164px] flex items-center space-x-[8px] justify-center"
              loading={isLoadingSubmit}
            >
              {buttonSubmitTitle ?? TEXT_ACTION.DELETE}
            </BasicButton>
          </Space>
        )}
      </Modal>
    );
  },
);

export default BasicFormModal;
