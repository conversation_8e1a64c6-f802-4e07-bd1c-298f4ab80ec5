/* eslint-disable max-lines-per-function */
import { Spin } from 'antd';
import { useMemo } from 'react';
import { useModel } from 'umi';

export default () => {
  // eslint-disable-next-line max-lines-per-function
  const { globalLoading } = useModel('common');

  const renderLoading = useMemo(() => {
    if (globalLoading) {
      return (
        <div className="loading">
          <div style={{ opacity: 1 }}>
            <Spin size="large" />
          </div>
        </div>
      );
    }
  }, [globalLoading]);

  return <>{renderLoading}</>;
};
