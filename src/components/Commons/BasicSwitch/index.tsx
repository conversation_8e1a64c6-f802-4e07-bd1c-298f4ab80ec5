import React from 'react';
import type { SwitchProps } from 'antd';
import { Switch } from 'antd';

interface Props extends SwitchProps {
  title?: string | React.ReactNode | any;
  classNameContainer?: string;
}
const BasicSwitch: React.FC<Props> = (props) => {
  return (
    <div className={`[&_.ant-input]:!rounded-lg flex flex-col ${props.classNameContainer}`}>
      {props.title ? (
        <div className="mb-2 text-[13px] leading-4 font-medium">{props.title}</div>
      ) : null}
      <Switch {...props} className={`${props.className} h-10 `} />
    </div>
  );
};

export default BasicSwitch;
