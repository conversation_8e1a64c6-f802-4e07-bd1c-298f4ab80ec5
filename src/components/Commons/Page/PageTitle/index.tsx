import React from 'react';
import styles from './index.less';

type PageTitleProps = {
  title: string;
  className?: string;
  containerStyle?: React.CSSProperties;
  titleStyle?: React.CSSProperties;
};

type OtherProps = React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>;

const PageTitle = ({
  title,
  containerStyle,
  titleStyle,
  ...others
}: PageTitleProps & OtherProps): React.ReactElement => (
  <div {...others} style={containerStyle}>
    <h3 style={titleStyle} className={styles.pageTitle}>
      {title}
    </h3>
  </div>
);

export default PageTitle;
