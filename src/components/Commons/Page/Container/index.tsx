import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { useDeviceType } from '@/providers/DeviceProvider';
import React from 'react';

type PageContainerProps = {
  children: React.ReactNode;
  useBackground?: boolean;
};

type OtherProps = React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>;

const PageContainer = ({
  children,
  useBackground,
  ...others
}: PageContainerProps & OtherProps): React.ReactElement => {
  const deviceType = useDeviceType();

  const className = useBackground
    ? `${others?.className} py-[16px] px-[24px] bg-[#fff] rounded-lg`
    : `${others?.className}`;

  return (
    <div
      {...others}
      className={className}
      style={{
        margin: deviceType === DeviceTypeEnum.MOBILE ? '16px 14px' : '24px 30px',
      }}
    >
      {children}
    </div>
  );
};

export default PageContainer;
