import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import { Tabs } from 'antd';
import TabPane from 'antd/lib/tabs/TabPane';
import React, { useContext, useEffect, useState } from 'react';
import styles from './index.less';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { PlanCreateContext, PlanCreateContextProps } from '@/providers';

export type DataTabType = {
  key: string;
  tabTitle: string | React.ReactElement | JSX.Element;
  tabContent: React.ReactElement | JSX.Element;
};

interface Props {
  data: DataTabType[];
  visibleTabCount?: number;
  handleChangeTab?: (key: number | string) => void;
  currrentTab?: string;
  setCurrentTab?: (key: string) => void;
}

const CustomTabs: React.FC<Props> = ({
  data,
  visibleTabCount = 3,
  handleChangeTab,
  currrentTab,
  setCurrentTab,
}) => {
  const { onChangePlanTab } = useContext<PlanCreateContextProps>(PlanCreateContext);
  const [activeKey, setActiveKey] = useState(data?.[0]?.key ?? '1');
  const [firstVisibleTab, setFirstVisibleTab] = useState(0);
  const deviceType = useDeviceType();

  const ensureTabVisible = (tabKey: string) => {
    const tabIndex = data.findIndex(item => item.key === tabKey);
    if (tabIndex === -1) return;

    if (tabIndex < firstVisibleTab) {
      setFirstVisibleTab(tabIndex);
    }
    else if (tabIndex >= firstVisibleTab + visibleTabCount) {
      setFirstVisibleTab(Math.max(0, tabIndex - visibleTabCount + 1));
    }
  };

  useEffect(() => {
    if (currrentTab) {
      setActiveKey(currrentTab);
      ensureTabVisible(currrentTab);
    }
  }, [currrentTab, data, visibleTabCount]);

  const handlePrevClick = () => {
    const currentIndex = parseInt(activeKey) - 1;
    if (currentIndex > 0) {
      setActiveKey(currentIndex.toString());
      handleChangeTab?.(currentIndex.toString());
      setCurrentTab?.(currentIndex.toString());
      onChangePlanTab?.(currentIndex);

      // Điều chỉnh firstVisibleTab khi tab active nằm ngoài vùng nhìn thấy
      if (currentIndex - 1 < firstVisibleTab) {
        setFirstVisibleTab(Math.max(0, currentIndex - 1));
      }
    }
  };

  const handleNextClick = () => {
    const currentIndex = parseInt(activeKey) - 1;
    if (currentIndex < data.length - 1) {
      setActiveKey((currentIndex + 2).toString());
      handleChangeTab?.((currentIndex + 2).toString());
      setCurrentTab?.((currentIndex + 2).toString());
      onChangePlanTab?.(currentIndex + 2);

      // Điều chỉnh firstVisibleTab khi tab active nằm ngoài vùng nhìn thấy
      if (currentIndex + 2 > firstVisibleTab + visibleTabCount) {
        setFirstVisibleTab(
          Math.min(data.length - visibleTabCount, currentIndex + 2 - visibleTabCount),
        );
      }
    }
  };

  const visibleTabs = data.slice(firstVisibleTab, firstVisibleTab + visibleTabCount);

  const onChangeTab = (key: string) => {
    setActiveKey(key);
    handleChangeTab?.(key);
    setCurrentTab?.(key);
    onChangePlanTab?.(Number(key));
    ensureTabVisible?.(key);
  };

  return (
    <div className={`${styles.customTag} relative flex w-full`}>
      <button
        onClick={handlePrevClick}
        className="absolute left-0 top-[14px] z-10 flex items-center justify-center w-6 h-6 -translate-x-full"
        aria-label="Previous tab"
        disabled={activeKey === '1'}
      >
        <LeftOutlined
          size={16}
          className={activeKey === '1' ? 'text-gray-light' : 'text-gray-dark'}
        />
      </button>

      <Tabs
        activeKey={activeKey}
        onChange={onChangeTab}
        className="w-full custom-tabs"
        animated={{ inkBar: true, tabPane: false }}
      >
        {visibleTabs.map((day) => (
          <TabPane tab={day.tabTitle} key={day.key}>
            {day.tabContent}
          </TabPane>
        ))}
      </Tabs>

      <button
        onClick={handleNextClick}
        className="absolute right-0 top-[14px] z-10 flex items-center justify-center w-6 h-6 translate-x-full"
        aria-label="Next tab"
        disabled={activeKey === data?.length?.toString()}
      >
        <RightOutlined
          className={activeKey === data?.length?.toString() ? 'text-gray-light' : 'text-gray-dark'}
        />
      </button>
    </div>
  );
};

export default CustomTabs;
