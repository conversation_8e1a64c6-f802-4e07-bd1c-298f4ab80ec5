import React from 'react';
import type { SelectProps } from 'antd';
import { Select } from 'antd';
import ArrowDown from '@/components/SVG/ArrowDown';

interface BasicSelectProps {
  title?: string | React.ReactNode;
  required?: boolean;
}
const BasicSelect: React.FC<SelectProps & BasicSelectProps> = ({ title, ...props }) => {
  return (
    <div
      className="flex flex-col [&_.ant-select-selector]:flex [&_.ant-select-selector]:!items-center
  [&_.ant-select-selection-item]:inline-block [&_.ant-select-selection-item]:leading-[38px] [&_.ant-select-selection-item]:truncate"
    >
      {title ? (
        <div className="mb-2 text-[13px] leading-4 font-medium">
          {title}
          {props.required && <span className="text-[red]"> *</span>}
        </div>
      ) : null}
      <Select
        suffixIcon={props.suffixIcon ? props.suffixIcon : <ArrowDown />}
        {...props}
        className={`
        [&_.ant-select-selection-search-input]:!h-[40px] [&_.ant-select-selector]:!h-10
        [&_.ant-select-selector]:!rounded-lg
        [&_.ant-select-selection-placeholder]:flex
        [&_.ant-select-selection-placeholder]:items-center
        ${props.className}
        `}
        tagRender={props.tagRender}
      />
    </div>
  );
};

export default BasicSelect;
