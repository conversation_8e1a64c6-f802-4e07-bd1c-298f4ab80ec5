import { Checkbox } from 'antd';
import type { CheckboxGroupProps } from 'antd/lib/checkbox';
import React from 'react';

interface Props extends CheckboxGroupProps {
  title?: string | React.ReactNode | any;
  classNameContainer?: string;
}

const BasicCheckboxGroup: React.FC<Props> = (props) => {
  return (
    <div className={`flex flex-col ${props.classNameContainer}`}>
      {props.title ? (
        <div className="mb-2 text-[13px] leading-4 font-medium">{props.title}</div>
      ) : null}
      <Checkbox.Group {...props} />
    </div>
  );
};

export default BasicCheckboxGroup;
