import React from 'react';
import styles from './index.less';
import { Image } from 'antd';

interface Props {
  type: 'text' | 'image';
  images?: string[];
  className?: string;
  title: string;
  content?: string | number | JSX.Element;
}

const CustomViewField: React.FC<Props> = ({ type, images, className, title, content }) => {
  switch (type) {
    case 'image':
      return (
        <div className={`${styles.sectionField} ${className}`}>
          <div className={`${styles.title}`}>{title}</div>
          <div className={`${styles.images} flex flex-wrap gap-x-2 gap-y-4`}>
            {images && images.length ? (
              images.map((image, index) => (
                <Image
                  src={image}
                  key={`image-${image}${index}`}
                  width={160}
                  height={120}
                  className="rounded-lg"
                  preview={false}
                />
              ))
            ) : (
              <div className="border border-dashed border-[#ccc] rounded-lg w-[160px] h-[120px] bg-[#F2F3F8]">
                {''}
              </div>
            )}
          </div>
        </div>
      );
    default:
      return (
        <div className={`${styles.sectionField} ${className}`}>
          <div className={`${styles.title}`}>{title}</div>
          <div className={`${styles.content}`}>{content}</div>
        </div>
      );
  }
};

export default CustomViewField;
