import type { ButtonProps } from 'antd';
import { Button } from 'antd';
import React from 'react';

export type StyleButtonType =
  | 'delete'
  | 'accept'
  | 'back'
  | 'outline'
  | 'danger'
  | 'orange'
  | 'noneOutLine';

interface BasicButtonProps {
  width?: number;
  styleType?: StyleButtonType;
}

const BasicButton: React.FC<ButtonProps & BasicButtonProps> = (props) => {
  switch (props.styleType) {
    case 'delete':
      return (
        <Button
          {...props}
          type="default"
          className={`delete !text-[#fff] h-10 text-[14px] leading-5 font-medium border-none bg-[#E04C58] hover:bg-[#E04C58] hover:!text-[#fff] hover:!border-none hover:opacity-85 rounded-lg ${props.className}`}
        >
          {props.children}
        </Button>
      );
    case 'danger':
      return (
        <Button
          {...props}
          className={`h-10 text-[14px] leading-5  bg-white hover:opacity-85 rounded-lg flex items-center justify-center w-[120px] space-x-[8px] ${props.className}`}
          danger
        >
          {props.children}
        </Button>
      );
    case 'orange':
      return (
        <Button
          {...props}
          className={`orange h-10 text-[14px] leading-5 border-none bg-[#FF7648] text-white hover:opacity-85 hover:!bg-[#FF7648] hover:!text-[#fff] rounded-lg flex items-center justify-center space-x-[8px] ${props.className}`}
          danger
        >
          {props.children}
        </Button>
      );

    case 'accept':
      return (
        <Button
          {...props}
          className={`accept text-[#fff] h-10 text-[14px] leading-5 font-medium border-none bg-main-color hover:!bg-main-color hover:!text-[#fff] hover:!border-none hover:opacity-85 rounded-lg flex items-center justify-center space-x-[8px] ${props.className}`}
        >
          {props.children}
        </Button>
      );

    case 'back':
      return (
        <Button
          {...props}
          className={`back h-10 text-[14px] leading-5 font-medium border-none text-[#414244] bg-[#CDCED4CC] hover:bg-[#CDCED4CC] !border-[#CDCED4CC]  hover:!text-[#fff] hover:!border-none hover:opacity-85 rounded-lg ${props.className}`}
        >
          {props.children}
        </Button>
      );
    case 'outline':
      return (
        <Button
          {...props}
          className={`!text-main-color h-10 text-[14px] leading-5  font-medium bg-white !border border-main-color hover:opacity-85 rounded-lg ${props.className}`}
        >
          {props.children}
        </Button>
      );
    case 'noneOutLine':
      return (
        <Button
          {...props}
          className={`text-[#363840] h-10 text-[14px] leading-5  font-medium bg-white !border  hover:opacity-85 rounded-lg ${props.className} !border-[#DCDEE3]`}
        >
          {props.children}
        </Button>
      );
    default:
      return (
        <Button
          {...props}
          className={`text-main-color h-10 text-[14px] leading-5  font-medium bg-white !border border-main-color hover:opacity-85 rounded-lg ${props.className}`}
        >
          {props.children}
        </Button>
      );
  }
};

export default BasicButton;
