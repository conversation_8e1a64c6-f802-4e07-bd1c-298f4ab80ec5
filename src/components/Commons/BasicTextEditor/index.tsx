import 'react-quill/dist/quill.snow.css';
import ImageResize from 'quill-image-resize-module-react';
import React, { useMemo, useRef } from 'react';
import ReactQuill, { Quill } from 'react-quill';
import AutoFormat from 'quill-autoformat';

declare global {
  interface Window {
    Quill: any;
  }
}

window.Quill = Quill;

Quill?.register('modules/imageResize', ImageResize);
Quill?.register('modules/autoformat', AutoFormat);
//thêm danh sách fontSize
const Size = Quill?.import('attributors/style/size');
Size.whitelist = [
  false,
  '10px',
  '12px',
  '14px',
  '16px',
  '18px',
  '20px',
  '22px',
  '24px',
  '26px',
  '28px',
  '32px',
  '36px',
  '40px',
];
Quill?.register(Size, true);

//thêm icon undo/redo
// const icons = Quill?.import('ui/icons');
// icons['undo'] = `<svg viewbox="0 0 18 18">
// <polygon class="ql-fill ql-stroke" points="6 10 4 12 2 10 6 10"></polygon>
// <path class="ql-stroke" d="M8.09,13.91A4.6,4.6,0,0,0,9,14,5,5,0,1,0,4,9"></path>
// </svg>`;
// icons['redo'] = `<svg viewbox="0 0 18 18">
// <polygon class="ql-fill ql-stroke" points="12 10 14 12 16 10 12 10"></polygon>
// <path class="ql-stroke" d="M9.91,13.91A4.6,4.6,0,0,1,9,14a5,5,0,1,1,5-5"></path>
// </svg>`;

const Font = Quill?.import('formats/font');
Font.whitelist = [false, 'Ubuntu', 'Raleway', 'Roboto', 'noto-sans-jp', 'arial'];
Quill?.register(Font, true);

const ReactQuillForm = ({
  title,
  classNameContainer,
  required,
  className,
  name,
  form,
  value,
}: {
  title?: string | React.ReactNode | any;
  classNameContainer?: string;
  required?: boolean;
  className?: string;
  name?: string;
  form: any;
  value?: string;
}) => {
  let quill;
  const quillRef = useRef(null);
  // const myUndo = () => {
  //   const myEditor = quill?.getEditor();
  //   return myEditor?.history?.undo();
  // };

  // const myRedo = () => {
  //   const myEditor = quill?.getEditor();
  //   return myEditor?.history?.redo();
  // };
  const modules = useMemo(() => {
    return {
      imageResize: {
        parchment: Quill?.import('parchment'),
        modules: ['Resize', 'DisplaySize', 'Toolbar'],
      },
      history: {
        delay: 1000,
        maxStack: 100,
        userOnly: false,
      },
      toolbar: {
        container: [
          ['bold', 'italic', 'underline', 'strike'], // toggled buttons
          ['blockquote', 'code-block'],
          ['link'],
          // [{ header: 1 }, { header: 2 }], // custom button values
          [{ list: 'ordered' }, { list: 'bullet' }],
          // [{ script: 'sub' }, { script: 'super' }], // superscript/subscript
          // [{ indent: '-1' }, { indent: '+1' }], // outdent/indent
          // [{ direction: 'rtl' }], // text direction
          [
            {
              size: Size.whitelist,
            },
          ], // custom dropdown
          // [{ color: [] }, { background: [] }], // dropdown with defaults from theme
          // [{ font: Font.whitelist }],
          [{ align: [] }],
          // ['link', 'image', 'video'],
          // ['undo', 'redo'],
        ],
        // handlers: {
        //   undo: myUndo,
        //   redo: myRedo,
        // },
      },

      clipboard: {
        // toggle to add extra line breaks when pasting HTML:
        matchVisual: false,
      },
      autoformat: true,
    };
  }, []);

  // function isQuillEmpty(_, value) {
  //   if (value) {
  //     if (value?.replace(/<(.|\n)*?>/g, '')?.trim()?.length === 0 && !value?.includes('<img')) {
  //       return Promise.reject(new Error('※必須項目が未入力です。'));
  //     }
  //   }
  //   return Promise.resolve();
  // }

  return (
    <div className={`[&_.ant-input]:!rounded-lg flex flex-col ${classNameContainer}`}>
      {title ? (
        <div className="mb-2 text-[13px] leading-4 font-medium">
          {title} {required && <span className="text-[red]">*</span>}
        </div>
      ) : null}
      <ReactQuill
        ref={(el) => {
          quill = el;
        }}
        className={`bg-white ${className}`}
        theme="snow"
        preserveWhitespace={true}
        modules={modules}
        placeholder={'本文を入力してください'}
        onChange={(content, delta, source, editor) => {
          form.setFieldsValue({ [name]: editor.getHTML() });
        }}
        value={value}
      />
    </div>
  );
};

export default ReactQuillForm;
