@import '~antd/lib/style/themes/default.less';
.outline {
  display: flex;
  align-items: center;
  justify-content: center;
  color: @primary-color;
  font-size: 12px;
  border: 1px solid @primary-color;
  border-radius: 30px;
  &:hover {
    // background-color: @primary-color;
    // color: #fff;
    opacity: 0.5;
  }
}

.outlinedisable {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8c8c8c;
  font-size: 12px;
  border: 1px solid #e4e8eb;
  border-radius: 30px;
}

.fill {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 12px;
  background-color: @primary-color;
  border: 1px solid #11c799;
  border-radius: 30px;
  &:hover {
    color: white;
    background-color: @primary-color;
    opacity: 0.5;
  }
}
