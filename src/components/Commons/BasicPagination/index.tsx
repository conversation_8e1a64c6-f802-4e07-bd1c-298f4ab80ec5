import ArrowDown from '@/components/SVG/ArrowDown';
import type { PaginationProps } from 'antd';
import { Pagination, Select } from 'antd';
import React from 'react';
import styles from './index.less';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

interface BasicPaginationProps {
  page: number;
  pageSize: number;
  total: number;
  onChange: (page: number, pageSize?: number) => void;
  onSelectPageSize: (page: number) => void;
}

const BasicPagination: React.FC<PaginationProps & BasicPaginationProps> = (props) => {
  const deviceType = useDeviceType();
  return (
    <div
      className={`${styles.customPagination} flex items-center space-x-[24px] ${
        deviceType === DeviceTypeEnum.MOBILE ? 'flex-col gap-y-2' : ''
      }`}
    >
      <Pagination
        total={props.total}
        pageSize={props.pageSize}
        showQuickJumper={false}
        showSizeChanger={false}
        showTotal={(total) =>
          `全${total}件中 ${total > 0 ? props.pageSize * (props.page - 1) + 1 : 0} 件 〜 ${
            props.pageSize * props.page >= total ? total : props.pageSize * props.page
          }件`
        }
        className="[&_.ant-pagination-total-text]:mr-6 [&_.ant-pagination-next]:hidden [&_.ant-pagination-prev]:hidden"
        current={props.page}
        onChange={(p, pz) => props?.onChange(p, pz)}
      />
      <div className="flex items-center space-x-[15px]">
        <div className="text-sm leading-5 text-[#414244]">表示件数</div>
        <Select
          value={props.pageSize}
          className="w-[70px] h-[32px] [&_.ant-select-selection-item]:!flex [&_.ant-select-selection-item]:!items-center [&_.ant-select-arrow]:mt-[-8px] [&_.ant-select-selector]:!rounded-[6px]"
          onChange={(e) => {
            props?.onSelectPageSize(e);
          }}
          suffixIcon={<ArrowDown width={12} height={12} />}
        >
          <Select.Option value={10}>10</Select.Option>
          <Select.Option value={30}>30</Select.Option>
          <Select.Option value={50}>50</Select.Option>
          <Select.Option value={100}>100</Select.Option>
        </Select>
      </div>
    </div>
  );
};

export default BasicPagination;
