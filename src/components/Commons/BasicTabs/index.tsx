import React, { useEffect, useRef, useState } from 'react';
import type { TabType } from './type';

interface TabProps {
  title: string;
  isActive: boolean;
  onClick: () => void;
}

const Tab: React.FC<TabProps> = ({ title, isActive, onClick }) => {
  const lineRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (isActive && lineRef.current) {
      const line = lineRef.current;
      const button = line.previousSibling as HTMLButtonElement;
      const buttonWidth = button.offsetWidth;
      const buttonLeft = button.offsetLeft;
      const lineOffset = line.offsetLeft;

      line.style.width = `${buttonWidth}px`;
      line.style.transform = `translateX(${buttonLeft - lineOffset}px)`;
    }
  }, [isActive]);

  return (
    <div className="relative">
      <div
        className={`text-[13px] leading-4 cursor-pointer ${
          isActive ? 'text-[#029102] font-bold' : 'text-[#606576] font-medium'
        } `}
        onClick={onClick}
        style={{
          transition: 'all 0.3s ease-in-out',
        }}
      >
        {title}
      </div>
      {isActive && (
        <div
          ref={lineRef}
          className="h-[1px] bg-main-color absolute bottom-[-6px] left-0 transition-transform duration-300"
          style={{
            width: '100%',
            transform: 'translateX(0%)',
            transition: 'all 0.3s ease-in-out',
          }}
        />
      )}
    </div>
  );
};

interface TabsProps {
  tabs: TabType[];
  onChange: (currenTab: number | string) => void;
  tabActive?: number | string;
  defaultActive?: string | number;
}

type OtherProps = React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>;

const BasicTabs: React.FC<TabsProps & OtherProps> = ({
  tabs,
  onChange,
  defaultActive,
  ...others
}) => {
  const [activeTab, setActiveTab] = useState(defaultActive);

  const handleTabClick = (tab: TabType) => {
    setActiveTab(tab.key);
    onChange(tab.key);
  };

  return (
    <div {...others} className={`${others.className} space-x-[16px] flex items-center`}>
      {tabs.map((tab) => {
        return (
          <Tab
            key={tab.key}
            title={tab.label}
            isActive={activeTab === tab.key}
            onClick={() => handleTabClick(tab)}
          />
        );
      })}
    </div>
  );
};

export default BasicTabs;
