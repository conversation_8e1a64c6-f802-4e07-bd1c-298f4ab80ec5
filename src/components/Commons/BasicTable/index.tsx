import type { PaginationProps, TableProps } from 'antd';
import { Table } from 'antd';
import React, { useMemo } from 'react';
import BasicPagination from '../BasicPagination';
import styles from './index.less';

interface Props {
  tableProps?: TableProps<any>;
  paginationProps?: PaginationProps;
  page?: number;
  pageSize?: number;
  total?: number;
  onChangePage?: (page: number) => void;
  hasPagination?: boolean;
  className?: string;
  onSelectPageSize?: (e: number) => void;
}

const BasicTable: React.FC<Props> = ({ hasPagination = true, ...props }) => {
  const tableProps = useMemo(() => {
    try {
      const heightScreen = window.innerHeight;
      const xDefaultTable = 1400;
      const yDefaultTable = heightScreen - 400;
      if (!props.tableProps.scroll) {
        props.tableProps.scroll = {
          x: xDefaultTable,
          y: yDefaultTable,
        };
      } else {
        if (!props.tableProps.scroll.x) {
          props.tableProps.scroll.x = xDefaultTable;
        }
        if (!props.tableProps.scroll.y) {
          props.tableProps.scroll.y = yDefaultTable;
        }
      }
    } catch (error) {
      console.error('error tableProps', error);
    }
    return props.tableProps;
  }, [props.tableProps]);

  return (
    <div className={`mt-6 ${styles.customTable} bg-[#fff] rounded-[12px] ${props.className}`}>
      <Table {...tableProps} />
      {hasPagination ? (
        <div className="flex items-center justify-center pt-6 pb-4">
          <BasicPagination
            onSelectPageSize={props.onSelectPageSize}
            {...props.paginationProps}
            pageSize={props.pageSize}
            page={props.page}
            total={props.total}
            onChange={props.onChangePage}
          />
        </div>
      ) : null}
    </div>
  );
};

export default BasicTable;
