import { deleteImage, uploadImage } from '@/apis/common';
import IconAddImage from '@/assets/imgs/common-icons/gallery-add.svg';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import STATUS_CODE from '@/constants/statusCode';
import { LoadingOutlined } from '@ant-design/icons';
import { Image, Upload } from 'antd';
import React, { useEffect, useState } from 'react';

export enum UploadImageType {
  travel_spot = 'travel_spot',
  hotel = 'hotel',
  agency = 'agency',
  company_master = 'company_master',
  travels = 'travels',
  sgg = 'sgg',
}
interface Props {
  title?: string | React.ReactNode | any;
  type: UploadImageType;
  value?: string[];
  onChange?: (i: any) => void;
  maxLength?: number;
}

const BasicUploads: React.FC<Props> = ({
  title,
  type,
  value: initialValue,
  onChange,
  maxLength,
}) => {
  const [loading, setLoading] = useState(false);
  const [imageUrls, setImageUrls] = useState<string[]>([]);

  useEffect(() => {
    if (initialValue) {
      setImageUrls?.(initialValue);
    }
  }, [initialValue]);

  const handleBeforeUpload = async (file: File) => {
    setLoading(true);
    try {
      const config = {
        headers: { 'Content-Type': 'multipart/form-data' },
      };

      const formData = new FormData();
      formData.append('type', type);
      formData.append('file', file);
      const { data, status, error } = await uploadImage(formData, config);
      if (status === 200) {
        const newList = [...imageUrls, data?.data];
        setImageUrls(newList);
        onChange?.(newList);
        setLoading(false);
      } else {
        const errors = error.data.errors;

        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key][0];
            openNotificationFail(message);
          });
        } else {
          openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
        }
        // openNotificationBlock('画像のアップロードに失敗しました 111', '', '#ff4d4f');
        setLoading(false);
      }
    } catch (error) {
      console.log('upload fail', error);
      setLoading(false);
    }
  };

  const handleDeleteImage = async (url: string) => {
    try {
      const { status } = await deleteImage(url);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const newList = imageUrls.filter((item) => item !== url);
        setImageUrls(newList);
        onChange?.(newList);
      } else {
        openNotificationFail(MESSAGE_ALERT.DELETE_FAILED);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  const renderItemImage = () => {
    const arrImages = imageUrls?.map((imageUrl) => (
      <div key={imageUrl} className="relative">
        <Image
          loading="lazy"
          preview={false}
          className="rounded-lg"
          src={imageUrl}
          alt="avatar"
          style={{ width: '160px', height: '120px' }}
        />
        <div
          onClick={() => handleDeleteImage(imageUrl)}
          className="absolute text-[10px] top-[-6px] right-[-6px] cursor-pointer bg-[#E04C58] text-[#fff] rounded-full border border-[#fff] z-30 w-[20px] h-[20px] flex items-center justify-center"
        >
          x
        </div>
      </div>
    ));

    const itemUpload = (
      <Upload
        name="avatar"
        listType="picture-card"
        className="avatar-uploader [&_.ant-upload-select-picture-card]:!w-[160px] [&_.ant-upload-select-picture-card]:!h-[120px] [&_.ant-upload-select-picture-card]:rounded-lg [&_.ant-upload-picture-card-wrapper]:w-0"
        showUploadList={false}
        beforeUpload={handleBeforeUpload}
        onChange={() => {}}
      >
        <div>
          {loading ? (
            <LoadingOutlined />
          ) : (
            <Image src={IconAddImage} width={24} height={24} preview={false} />
          )}

          <div>アップロード</div>
        </div>
      </Upload>
    );
    if (imageUrls?.length === maxLength) {
      return arrImages;
    }
    return [...arrImages, itemUpload];
  };

  const images = renderItemImage();

  return (
    <div className="flex flex-col">
      {title ? <div className="mb-2 text-[13px] leading-4 font-medium">{title}</div> : null}
      <div className="flex items-center gap-x-[16px] flex-wrap">
        {images.map((item, index) => (
          <div key={`${index}.itemImage`}>{item}</div>
        ))}
      </div>
    </div>
  );
};

export default BasicUploads;
