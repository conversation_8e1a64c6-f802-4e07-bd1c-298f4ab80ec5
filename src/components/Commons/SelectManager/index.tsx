import type { SelectItemType } from '@/@types/common';
import { BaseParams } from '@/@types/request';
import { getListUsers } from '@/apis/users';
import ArrowDown from '@/components/SVG/ArrowDown';
import STATUS_CODE from '@/constants/statusCode';
import useComponentVisible from '@/hooks/useComponentVisible';
import { occupationAccount } from '@/utils/constants';
import { Form, Input, Spin, Tooltip } from 'antd';
import { debounce } from 'lodash';
import React, { useEffect, useRef, useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { connect } from 'umi';

interface Props {
  title?: string | React.ReactNode;
  width?: number;
  height?: number;
  className?: string;
  value?: SelectItemType;
  onChange?: (i: any) => void;
  currentUser?: any;
  allowClear?: boolean;
}
const SelectManager = ({ value: initialValue, allowClear = false, ...props }: Props) => {
  const [options, setOptions] = useState<SelectItemType[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState<boolean>(true);
  const [page, setPage] = useState<number>(1);
  const [inputValue, setInputValue] = useState('');
  const [form] = Form.useForm();
  const valueSearch = Form.useWatch('inputSearch', form);

  const inputRef = useRef(null);

  const [optionSelected, setOptionSelected] = useState<SelectItemType>();

  const [isHoverClear, setIsHoverClear] = useState(false);

  useEffect(() => {
    if (initialValue) {
      setOptionSelected?.(initialValue);
    }
  }, [initialValue]);

  const { ref: optionRef, isComponentVisible, setIsComponentVisible } = useComponentVisible(false);

  const fetchMoreData = async () => {
    setIsLoading(true);
    let paramSearch: BaseParams = {
      page,
      limit: 10,
      keyword: inputValue,
      id: null,
      order: 'asc',
    };
    const user = props?.currentUser;

    if (user && user.occupation == occupationAccount.outernalAdmin) {
      paramSearch = {
        page,
        limit: 10,
        keyword: inputValue,
        id: user.id,
        order: 'asc',
      };
    }

    const { data, status } = await getListUsers(paramSearch);
    if (status === STATUS_CODE.SUCCESSFUL) {
      const dataOptions = data?.data?.map((i) => ({
        label: i.name,
        value: i.id,
      }));
      const newOptions = [...options, ...dataOptions];
      setOptions(newOptions);
      setHasMore(newOptions.length < data?.total);
      setPage(page + 1);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (isComponentVisible && (options?.length === 0 || inputValue?.length >= 0)) {
      fetchMoreData();
    }
  }, [isComponentVisible, inputValue]);

  const onSelectOption = (i: SelectItemType) => {
    setOptionSelected(i);
    props?.onChange?.(i);
    setIsComponentVisible(false);
    setInputValue('');
  };

  const debouncedOnChange = debounce((text: string) => {
    setOptions([]);
    setPage(1);
    setHasMore(false);
    setInputValue(text);
  }, 250);

  useEffect(() => {
    debouncedOnChange(valueSearch);
  }, [valueSearch]);

  const onClearSelect = () => {
    setOptionSelected(undefined);
    props?.onChange?.(undefined);
  };

  return (
    <Form form={form} component={false}>
      <div>
        {props?.title ? (
          <div className="mb-2 text-[13px] leading-4 font-medium">{props?.title}</div>
        ) : null}
        <div
          className={`${props.className}`}
          style={{ width: `${props?.width ?? 140}px`, height: `${props?.height ?? 36}px` }}
          onClick={() => {
            setIsComponentVisible(true);
          }}
          ref={optionRef}
        >
          {isComponentVisible ? (
            <div
              className="relative h-full z-[100] rounded-lg"
              style={{
                boxShadow: '0px 8px 14px 0px rgba(0, 0, 0, 0.15)',
              }}
            >
              <div className="relative h-full">
                <Form.Item name="inputSearch">
                  <Input
                    className=" !outline-0 px-[5px] w-full !h-full !rounded-[8px] border-main-color"
                    autoFocus={isComponentVisible}
                    suffix={<ArrowDown />}
                    ref={inputRef}
                    type="text"
                  />
                </Form.Item>
              </div>

              <div
                className="absolute !top-[40px] bg-white w-full z-[100] "
                style={{ boxShadow: '0px 8px 14px 0px rgba(0, 0, 0, 0.15)' }}
              >
                <Spin spinning={isLoading} className="w-full">
                  <div className="max-h-[200px] w-full overflow-auto" id="scrollableDiv">
                    <InfiniteScroll
                      dataLength={options?.length}
                      className="w-full"
                      next={fetchMoreData}
                      hasMore={hasMore}
                      loader={null}
                      scrollableTarget="scrollableDiv"
                    >
                      {options.map((option) => (
                        <div
                          className={`px-[16px] w-full py-[8px] hover:bg-[#BCE4E3] truncate ${
                            option.value?.toString() == optionSelected?.value?.toString()
                              ? 'bg-[#BCE4E3]'
                              : ''
                          } cursor-pointer`}
                          key={option.value}
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsHoverClear(false);
                            onSelectOption(option);
                          }}
                        >
                          {option.label}
                        </div>
                      ))}
                      {!options?.length && (
                        <p className="flex items-center justify-center my-4 font-medium">
                          データなし
                        </p>
                      )}
                    </InfiniteScroll>
                  </div>
                </Spin>
              </div>
            </div>
          ) : (
            <Tooltip title={optionSelected?.label}>
              <div
                style={{ width: `${props?.width ?? 140}px`, height: `${props?.height ?? 36}px` }}
                className={`${props.className} flex justify-between items-center border-[1px] border-[#E1E6EA] bg-white h-[36px] px-[12px] rounded-[8px] cursor-pointer truncate`}
                onMouseEnter={() => {
                  setIsHoverClear(true);
                }}
                onMouseLeave={() => {
                  setIsHoverClear(false);
                }}
              >
                <div
                  className={`${
                    optionSelected ? '' : 'text-[#000]'
                  } line-clamp-1 flex items-center`}
                >
                  {optionSelected ? (
                    optionSelected?.label
                  ) : (
                    <div className="text-[#c3c3c3] text-[14px] font-light">すべて</div>
                  )}
                </div>
                <div>
                  {allowClear && optionSelected && isHoverClear ? (
                    <div
                      onClick={(e) => {
                        onClearSelect();
                        e.stopPropagation();
                      }}
                    >
                      <span className="w-[12px] h-[12px] text-[8px] rounded-full hover:opacity-90 text-white bg-[#9b9b9bcc] flex items-center justify-center">
                        x
                      </span>
                    </div>
                  ) : (
                    <ArrowDown />
                  )}
                </div>
              </div>
            </Tooltip>
          )}
        </div>
      </div>
    </Form>
  );
};

export default connect(({ user }) => ({
  currentUser: user.currentUser,
}))(SelectManager);
