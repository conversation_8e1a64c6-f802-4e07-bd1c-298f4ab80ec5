import CloseSVG from '@/components/SVG/CloseSVG';
import type { ModalProps } from 'antd';
import { Modal, Space } from 'antd';
import React, { forwardRef, useImperativeHandle, useState } from 'react';
import type { StyleButtonType } from '../BasicButton';
import BasicButton from '../BasicButton';
import { TEXT_ACTION } from '@/constants/commonText';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

type BasicModalProps = {
  title: string;
  content: React.ReactNode | string | any;
  onSubmit: () => void;
  onClose?: () => void;
  style?: React.CSSProperties;
  titleStyle?: React.CSSProperties;
  buttonContainerStyle?: React.CSSProperties;
  buttonCloseStyle?: React.CSSProperties;
  iconButtonClose?: React.ReactNode;
  styleTypeButtonClose?: StyleButtonType;
  buttonSubmitStyle?: React.CSSProperties;
  iconButtonSubmit?: React.ReactNode;
  styleTypeButtonSubmit?: StyleButtonType;
  buttonCloseTitle?: string;
  buttonSubmitTitle?: string;
  children?: React.ReactNode;
  hideCloseButton?: boolean;
  hideSubmitButton?: boolean;
};

type OtherProps = React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>;

type Props = BasicModalProps & OtherProps & ModalProps;

export type BasicModalRef = {
  open: () => void;
  close: () => void;
  isModalOpen: boolean;
};

const BasicModal = forwardRef<BasicModalRef, Props>(
  (
    {
      title,
      content,
      onSubmit,
      onClose,
      titleStyle,
      buttonContainerStyle,
      buttonCloseStyle,
      buttonSubmitStyle,
      buttonCloseTitle,
      buttonSubmitTitle,
      children,
      hideCloseButton = false,
      hideSubmitButton = false,
      ...others
    },
    ref,
  ): React.ReactElement => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const deviceType = useDeviceType();
    const open = () => {
      setIsModalOpen(true);
    };

    const close = () => {
      onClose?.();
      setIsModalOpen(false);
    };

    useImperativeHandle(ref, () => ({
      open,
      close,
      isModalOpen,
    }));

    const handleOk = () => {
      if (onSubmit) {
        onSubmit();
      } else {
        close();
      }
    };

    return (
      <Modal
        {...others}
        className={`${others.className} [&_.ant-modal-content]:!rounded-xl`}
        open={isModalOpen}
        onCancel={close}
        onOk={handleOk}
        footer={null}
        closable={false}
        centered
        style={{ borderRadius: '12px' }}
        bodyStyle={{
          padding: deviceType !== DeviceTypeEnum.DESKTOP ? '24px 12px' : '32px',
          display: 'flex',
          flexDirection: 'column',
        }}
        width={426}
      >
        <div className="flex justify-between items-center">
          <div />
          <span style={titleStyle} className="text-lg leading-8 font-bold text-[#000000]">
            {title}
          </span>
          <div onClick={close}>
            <CloseSVG />
          </div>
        </div>
        <div className="py-[22px] h-full modal_content">{children ? children : content}</div>
        <div
          style={buttonContainerStyle}
          className={`w-full flex space-x-[6px] ${
            hideCloseButton || hideSubmitButton ? 'justify-center' : 'justify-between'
          } `}
        >
          {!hideCloseButton && (
            <BasicButton
              icon={others?.iconButtonClose}
              onClick={close}
              style={buttonCloseStyle}
              type="default"
              styleType={others?.styleTypeButtonClose ?? 'back'}
              className={`${
                deviceType !== DeviceTypeEnum.DESKTOP ? 'flex-1 min-w-[50%]' : 'w-[164px]'
              } flex items-center space-x-[8px] justify-center`}
            >
              {buttonCloseTitle ?? TEXT_ACTION.CANCEL}
            </BasicButton>
          )}
          {!hideSubmitButton && (
            <BasicButton
              icon={others?.iconButtonSubmit}
              onClick={handleOk}
              style={buttonSubmitStyle}
              styleType={others?.styleTypeButtonSubmit ?? 'delete'}
              className={`${
                deviceType !== DeviceTypeEnum.DESKTOP ? 'flex-1 min-w-[50%]' : 'w-[164px]'
              }flex items-center space-x-[8px] justify-center`}
            >
              {buttonSubmitTitle ?? TEXT_ACTION.DELETE}
            </BasicButton>
          )}
        </div>
      </Modal>
    );
  },
);

export default BasicModal;
