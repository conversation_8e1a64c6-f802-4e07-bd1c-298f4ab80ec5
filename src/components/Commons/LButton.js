import { Button } from 'antd';
import styles from './LButton.less';
import classNames from 'classnames';

export const LButtonOutline = ({
  childrent,
  onClick = () => {},
  txt = '',
  className,
  preIcon = null,
  loading,
  ...restProps
}) => {
  const clsString = classNames(styles.outline, className);

  return (
    <Button className={clsString} onClick={onClick} {...restProps} loading={loading}>
      {preIcon ? preIcon : null}
      {txt}
    </Button>
  );
};

export const LButtonOutlineDisable = ({
  childrent,
  onClick = () => {},
  txt = '',
  className,
  preIcon = null,
  loading,
  ...restProps
}) => {
  const clsString = classNames(styles.outlinedisable, className);

  return (
    <Button className={clsString} onClick={onClick} {...restProps} loading={loading}>
      {preIcon ? preIcon : null}
      {txt}
    </Button>
  );
};

export const LButtonFill = ({
  childrent,
  onClick = () => {},
  txt = '',
  disabled = false,
  className,
  preIcon = null,
  loading,
  ...restProps
}) => {
  const clsString = classNames(styles.fill, className);

  return (
    <Button
      className={clsString}
      disabled={disabled}
      onClick={onClick}
      {...restProps}
      loading={loading}
    >
      {preIcon ? preIcon : null}
      {txt}
    </Button>
  );
};
