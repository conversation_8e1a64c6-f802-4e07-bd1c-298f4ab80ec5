/* eslint-disable @typescript-eslint/consistent-type-imports */
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import BasicSelect from '../BasicSelect';
import { PREFECTURES_OPTIONS, OptionType } from '@/constants/prefectures';
import { getCity } from '@/apis/common';
import RequiredTag from '../RequiredTag/RequiredTag';

type DataType = {
  prefectures: OptionType;
  municipalities: OptionType;
};
interface Props {
  value?: DataType;
  onChange?: (v: DataType) => void;
  className?: string;
  layout?: 'vertical' | 'horizontal';
  hasMunicipalities?: boolean;
  isRequired?: boolean;
  placeHolder?: {
    placeHolderPrefecture: string;
    placeHolderMunicipalities: string;
  };
}

const CustomSelectPrefectures: React.FC<Props> = ({
  value: initialValue,
  onChange,
  className,
  layout = 'horizontal',
  hasMunicipalities = true,
  isRequired = false,
  placeHolder,
}) => {
  const [value, setValue] = useState<DataType>({
    municipalities: undefined,
    prefectures: undefined,
  });

  const [municipalitiesOptions, setMunicipalitiesOptions] = useState<OptionType[]>([]);
  useEffect(() => {
    if (initialValue) {
      setValue?.(initialValue);
    }
  }, [initialValue]);

  useEffect(() => {
    const getInitCity = async () => {
      const { data, status } = await getCity(initialValue?.prefectures?.value);
      if (status === 200) {
        const newData = data?.data?.map((item: any) => ({
          value: item.id,
          label: item.name_city,
        }));
        setMunicipalitiesOptions(newData);
      } else {
        setMunicipalitiesOptions([]);
      }
    };
    if (initialValue?.prefectures?.value) {
      getInitCity();
    }
  }, [initialValue?.prefectures?.value]);

  const onChangePrefecture = async (v: number, option: any) => {
    const { data, status } = await getCity(v);
    if (status === 200) {
      const newData = data?.data?.map((item: any) => ({
        value: item.id,
        label: item.name_city,
      }));
      setMunicipalitiesOptions(newData);
    } else {
      setMunicipalitiesOptions([]);
    }
    setValue?.({ prefectures: option, municipalities: null });
    onChange?.({ prefectures: option, municipalities: null });
  };

  const onChangeMunicipalities = (v: number, option: any) => {
    setValue?.({ ...value, municipalities: option });
    onChange?.({ ...value, municipalities: option });
  };

  return (
    <div
      className={`${className} ${
        styles.container
      } [&_.ant-select-selection-item]:flex [&_.ant-select-selection-item]:items-center  ${
        layout === 'vertical' ? 'flex-col space-y-[16px]' : 'space-x-[16px] '
      }`}
    >
      <div className="flex-1">
        <div className={`${styles.title} flex items-center gap-[6px]`}>
          都道府県 {isRequired ? <RequiredTag /> : null}{' '}
        </div>
        <BasicSelect
          showSearch
          value={value?.prefectures?.value}
          placeholder={placeHolder?.placeHolderPrefecture ?? '都道府県'}
          options={PREFECTURES_OPTIONS}
          onChange={onChangePrefecture}
          allowClear
          filterOption={(input, option) => {
            return option?.props?.label?.toLowerCase().indexOf(input?.toLowerCase()) >= 0;
          }}
        />
      </div>
      {hasMunicipalities ? (
        <div className="flex-1">
          <div className={`${styles.title} flex items-center gap-[6px]`}>
            市区町村 {isRequired ? <RequiredTag /> : null}
          </div>
          <BasicSelect
            showSearch
            value={value?.municipalities?.value}
            placeholder={placeHolder?.placeHolderMunicipalities ?? '市区町村'}
            options={municipalitiesOptions}
            onChange={onChangeMunicipalities}
            allowClear
            filterOption={(input, option) => {
              return option?.props?.label?.toLowerCase().indexOf(input?.toLowerCase()) >= 0;
            }}
          />
        </div>
      ) : null}
    </div>
  );
};

export default CustomSelectPrefectures;
