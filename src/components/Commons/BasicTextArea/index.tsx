import React from 'react';
import { Input } from 'antd';
import type { TextAreaProps } from 'antd/lib/input';

const { TextArea } = Input;
interface Props extends TextAreaProps {
  title?: string | any;
  row?: number;
}
const BasicTextArea: React.FC<Props> = (props) => {
  return (
    <div className="[&_.ant-input]:!rounded-lg flex flex-col">
      {props.title ? (
        <div className="mb-2 text-[13px] leading-4 font-medium">
          {props.title} {props?.required && <span className="text-[red]">*</span>}
        </div>
      ) : null}
      <TextArea {...props} rows={props.row ?? 6} className={`${props.className}`} title={null} />
    </div>
  );
};

export default BasicTextArea;
