import type { SelectItemType } from '@/@types/common';
import type { RadioGroupProps } from 'antd';
import { Radio } from 'antd';
import React from 'react';

interface Props extends RadioGroupProps {
  title?: string | React.ReactNode | any;
  classNameContainer?: string;
  options: SelectItemType[];
}

const BasicRadioGroup: React.FC<Props> = (props) => {
  return (
    <div className={`[&_.ant-input]:!rounded-lg flex flex-col ${props.classNameContainer}`}>
      {props.title ? (
        <div className="mb-2 text-[13px] leading-4 font-medium">{props.title}</div>
      ) : null}
      <Radio.Group {...props}>
        {props?.options?.map((item, index) => (
          <Radio value={item.value} key={`${item?.value}-${index}`}>
            {item.label}
          </Radio>
        ))}
      </Radio.Group>
    </div>
  );
};

export default BasicRadioGroup;
