import CalendarSVG from '@/components/SVG/CalendarSVG';
import { CloseCircleOutlined } from '@ant-design/icons';
import type { DatePickerProps } from 'antd';
import { DatePicker } from 'antd';

import React, { useEffect, useState } from 'react';

const dateFormat = 'YYYY/MM/DD';
const monthFormat = 'YYYY/MM';
const yearFormat = 'YYYY';

interface BasicDatePickerProps {
  title?: string | React.ReactNode | any;
  value?: moment.Moment;
  onChange?: (i: any) => void;
  required?: boolean;
  hideSuffixIcon?: boolean;
  hideClearIcon?: boolean;
}

const BasicDatePicker: React.FC<DatePickerProps & BasicDatePickerProps> = ({
  value: initialValue,
  required = false,
  onChange,
  title,
  hideSuffixIcon,
  hideClearIcon,
  ...props
}) => {
  const [date, setDate] = useState<moment.Moment>();
  useEffect(() => {
    if (initialValue) {
      setDate?.(initialValue);
    }
  }, [initialValue]);

  const handleChangeDate = (value: moment.Moment) => {
    if (value) {
      setDate(value);
      onChange?.(value);
    }
  };

  const handleClearDate = () => {
    if (date) {
      setDate(undefined);
      onChange?.(undefined);
    }
  };

  return (
    <div className="flex flex-col">
      {title ? (
        <div className="mb-2 text-[13px] leading-4 font-medium">
          {title} {required ? <span className="text-[red]">*</span> : ''}
        </div>
      ) : null}
      <DatePicker
        format={
          props?.picker === 'month'
            ? monthFormat
            : props?.picker === 'year'
            ? yearFormat
            : dateFormat
        }
        {...props}
        value={date}
        onChange={handleChangeDate}
        style={{
          border: '1px solid #d9d9d9',
          borderRadius: '8px',
        }}
        suffixIcon={hideSuffixIcon ? null : <CalendarSVG />}
        clearIcon={
          hideClearIcon ? null : <CloseCircleOutlined onClick={handleClearDate} color="#DCDEE3" />
        }
      />
    </div>
  );
};

export default BasicDatePicker;
