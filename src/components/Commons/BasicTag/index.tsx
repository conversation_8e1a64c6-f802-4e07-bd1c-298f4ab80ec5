import React from 'react';
import type { SelectProps } from 'antd';
import { Tag } from 'antd';
import styles from './index.less';

interface BasicTagProps {
  title?: string;
  color?: string;
}
const BasicTag: React.FC<SelectProps & BasicTagProps> = (props) => {
  return (
    <div className={`${styles.customTag}`}>
      <Tag className={`${props.className}  rounded-full`} color={`${props.color}`}>
        {props.title}
      </Tag>
    </div>
  );
};

export default BasicTag;
