let CURRENT = 'NULL';

/**
 * Use authority or getAuthority
 *
 * @param {string|()=>String} currentAuthority
 */
const renderAuthorize = (Authorized) => (currentAuthority) => {
  if (currentAuthority) {
    if (typeof currentAuthority === 'function') {
      CURRENT = currentAuthority();
    }
    // //console.log('renderAuthorize', Object.prototype.toString.call(currentAuthority));
    if (
      Object.prototype.toString.call(currentAuthority) === '[object String]' ||
      Array.isArray(currentAuthority)
    ) {
      CURRENT = currentAuthority;
    } else if (Object.prototype.toString.call(currentAuthority) === '[object Object]') {
      CURRENT = currentAuthority?.data?.roles;
    }
  } else {
    CURRENT = 'NULL';
    // CURRENT = 'admin';
  }

  return Authorized;
};

export { CURRENT };
export default (Authorized) => renderAuthorize(Authorized);
