import React from 'react';

type Props = {
  width?: number;
  height?: number;
};
const AreaSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div {...props}>
      <svg
        width={props?.width ?? '18'}
        height={props?.height ?? '19'}
        viewBox="0 0 18 19"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M1.71753 6.23684V13.5343C1.71753 14.9593 2.73003 15.5443 3.96003 14.8393L5.72253 13.8343C6.10503 13.6168 6.74253 13.5943 7.14003 13.7968L11.0775 15.7693C11.475 15.9643 12.1125 15.9493 12.495 15.7318L15.7425 13.8718C16.155 13.6318 16.5 13.0468 16.5 12.5668V5.26934C16.5 3.84434 15.4875 3.25934 14.2575 3.96434L12.495 4.96934C12.1125 5.18684 11.475 5.20934 11.0775 5.00684L7.14003 3.04184C6.74253 2.84684 6.10503 2.86184 5.72253 3.07934L2.47503 4.93934C2.05503 5.17934 1.71753 5.76434 1.71753 6.23684Z"
          stroke="#88889D"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M6.41992 3.40137V13.1514"
          stroke="#88889D"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.7976 5.36621V15.4012"
          stroke="#88889D"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};

export default AreaSVG;
