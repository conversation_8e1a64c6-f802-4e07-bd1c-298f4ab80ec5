import React from 'react';

type Props = {
  width?: number;
  height?: number;
};

const ArrowLeftSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div>
      <svg
        width={props?.width ?? '18'}
        height={props?.height ?? '18'}
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M6.38016 3.95312L2.3335 7.99979L6.38016 12.0465"
          stroke="#414244"
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.6668 8H2.44678"
          stroke="#414244"
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};

export default ArrowLeftSVG;
