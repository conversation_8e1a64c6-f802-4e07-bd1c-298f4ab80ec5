import React from 'react';

type Props = {
  width?: number;
  height?: number;
};

const OverwriteSaveSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div {...props}>
      <svg
        width="14"
        height="16"
        viewBox="0 0 14 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4.33301 14.6667V12.6667C4.33301 11.4096 4.33301 10.781 4.72353 10.3905C5.11406 10 5.7426 10 6.99967 10C8.25675 10 8.88529 10 9.27582 10.3905C9.66634 10.781 9.66634 11.4096 9.66634 12.6667V14.6667"
          stroke="#3997C8"
          strokeWidth="1.25"
          strokeLinejoin="round"
        />
        <path
          d="M5.66699 4.66797H8.33366"
          stroke="#3997C8"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M1 7.90628C1 4.85531 1 3.32982 1.92449 2.36328C1.95864 2.32757 1.99359 2.29263 2.0293 2.25847C2.99584 1.33398 4.52133 1.33398 7.5723 1.33398C8.29534 1.33398 8.64366 1.33649 8.97521 1.46023C9.29448 1.57938 9.56191 1.80225 10.0968 2.24798L11.5607 3.46793C12.2685 4.05772 12.6224 4.35262 12.8112 4.75576C13 5.1589 13 5.61954 13 6.54082V8.66732C13 11.1671 13 12.4171 12.3634 13.2933C12.1578 13.5763 11.9089 13.8251 11.626 14.0307C10.7497 14.6673 9.49982 14.6673 7 14.6673C4.50018 14.6673 3.25027 14.6673 2.37405 14.0307C2.09107 13.8251 1.84221 13.5763 1.63661 13.2933C1 12.4171 1 11.1671 1 8.66732V7.90628Z"
          stroke="#3997C8"
          strokeWidth="1.5"
        />
      </svg>
    </div>
  );
};

export default OverwriteSaveSVG;
