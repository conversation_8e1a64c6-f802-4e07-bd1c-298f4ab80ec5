import React from 'react';

type Props = {
  width?: number;
  height?: number;
};
const CoinSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div {...props}>
      <svg
        width={props?.width ?? '25'}
        height={props?.height ?? '24'}
        viewBox="0 0 25 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.49 17.98C14.9028 17.98 18.48 14.4028 18.48 9.99C18.48 5.57724 14.9028 2 10.49 2C6.07724 2 2.5 5.57724 2.5 9.99C2.5 14.4028 6.07724 17.98 10.49 17.98Z"
          fill="white"
        />
        <path
          d="M22.4701 15.9901C22.4701 19.2901 19.7901 21.9701 16.4901 21.9701C14.4501 21.9701 12.6601 20.9501 11.5801 19.4001C15.9401 18.9101 19.4101 15.4401 19.9001 11.0801C21.4501 12.1601 22.4701 13.9501 22.4701 15.9901Z"
          fill="white"
        />
        <path
          d="M11.95 9.70991L9.55 8.86991C9.31 8.78991 9.26 8.76991 9.26 8.41991C9.26 8.15991 9.44 7.94991 9.67 7.94991H11.17C11.49 7.94991 11.75 8.23991 11.75 8.59991C11.75 9.00991 12.09 9.34991 12.5 9.34991C12.91 9.34991 13.25 9.00991 13.25 8.59991C13.25 7.44991 12.36 6.50991 11.25 6.45991V6.40991C11.25 5.99991 10.91 5.65991 10.5 5.65991C10.09 5.65991 9.75 5.98991 9.75 6.40991V6.45991H9.66C8.61 6.45991 7.75 7.33991 7.75 8.42991C7.75 9.37991 8.17 9.98991 9.04 10.2899L11.45 11.1299C11.69 11.2099 11.74 11.2299 11.74 11.5799C11.74 11.8399 11.56 12.0499 11.33 12.0499H9.83C9.51 12.0499 9.25 11.7599 9.25 11.3999C9.25 10.9899 8.91 10.6499 8.5 10.6499C8.09 10.6499 7.75 10.9899 7.75 11.3999C7.75 12.5499 8.64 13.4899 9.75 13.5399V13.5999C9.75 14.0099 10.09 14.3499 10.5 14.3499C10.91 14.3499 11.25 14.0099 11.25 13.5999V13.5499H11.34C12.39 13.5499 13.25 12.6699 13.25 11.5799C13.25 10.6299 12.82 10.0199 11.95 9.70991Z"
          fill="#3CC53C"
        />
      </svg>
    </div>
  );
};

export default CoinSVG;
