import React from 'react';

type Props = {
  width?: number;
  height?: number;
};

const CalendarSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div {...props}>
      <svg
        width={props?.width ?? '16'}
        height={props?.height ?? '16'}
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.6667 2.82909L11.1206 2.87112C11.9273 2.94582 12.4721 3.21667 12.831 3.59776C13.1609 3.9479 13.3743 4.43406 13.4551 5.05992H2.54489C2.62574 4.43406 2.83912 3.9479 3.16895 3.59776C3.52792 3.21667 4.07265 2.94582 4.87943 2.87112L5.33333 2.82909V2.83325H5.83333H10.1667H10.6667V2.82909Z"
          fill="#414244"
          stroke="#414244"
        />
        <path
          d="M13.3333 6.56006H2.66667C2.3 6.56006 2 6.86006 2 7.22673V11.3334C2 13.3334 3 14.6667 5.33333 14.6667H10.6667C13 14.6667 14 13.3334 14 11.3334V7.22673C14 6.86006 13.7 6.56006 13.3333 6.56006ZM6.14 12.1401C6.10667 12.1667 6.07333 12.2001 6.04 12.2201C6 12.2467 5.96 12.2667 5.92 12.2801C5.88 12.3001 5.84 12.3134 5.8 12.3201C5.75333 12.3267 5.71333 12.3334 5.66667 12.3334C5.58 12.3334 5.49333 12.3134 5.41333 12.2801C5.32667 12.2467 5.26 12.2001 5.19333 12.1401C5.07333 12.0134 5 11.8401 5 11.6667C5 11.4934 5.07333 11.3201 5.19333 11.1934C5.26 11.1334 5.32667 11.0867 5.41333 11.0534C5.53333 11.0001 5.66667 10.9867 5.8 11.0134C5.84 11.0201 5.88 11.0334 5.92 11.0534C5.96 11.0667 6 11.0867 6.04 11.1134C6.07333 11.1401 6.10667 11.1667 6.14 11.1934C6.26 11.3201 6.33333 11.4934 6.33333 11.6667C6.33333 11.8401 6.26 12.0134 6.14 12.1401ZM6.14 9.80673C6.01333 9.92673 5.84 10.0001 5.66667 10.0001C5.49333 10.0001 5.32 9.92673 5.19333 9.80673C5.07333 9.68006 5 9.50673 5 9.33339C5 9.16006 5.07333 8.98673 5.19333 8.86006C5.38 8.67339 5.67333 8.61339 5.92 8.72006C6.00667 8.75339 6.08 8.80006 6.14 8.86006C6.26 8.98673 6.33333 9.16006 6.33333 9.33339C6.33333 9.50673 6.26 9.68006 6.14 9.80673ZM8.47333 12.1401C8.34667 12.2601 8.17333 12.3334 8 12.3334C7.82667 12.3334 7.65333 12.2601 7.52667 12.1401C7.40667 12.0134 7.33333 11.8401 7.33333 11.6667C7.33333 11.4934 7.40667 11.3201 7.52667 11.1934C7.77333 10.9467 8.22667 10.9467 8.47333 11.1934C8.59333 11.3201 8.66667 11.4934 8.66667 11.6667C8.66667 11.8401 8.59333 12.0134 8.47333 12.1401ZM8.47333 9.80673C8.44 9.83339 8.40667 9.86006 8.37333 9.88673C8.33333 9.91339 8.29333 9.93339 8.25333 9.94673C8.21333 9.96673 8.17333 9.98006 8.13333 9.98672C8.08667 9.99339 8.04667 10.0001 8 10.0001C7.82667 10.0001 7.65333 9.92673 7.52667 9.80673C7.40667 9.68006 7.33333 9.50673 7.33333 9.33339C7.33333 9.16006 7.40667 8.98673 7.52667 8.86006C7.58667 8.80006 7.66 8.75339 7.74667 8.72006C7.99333 8.61339 8.28667 8.67339 8.47333 8.86006C8.59333 8.98673 8.66667 9.16006 8.66667 9.33339C8.66667 9.50673 8.59333 9.68006 8.47333 9.80673ZM10.8067 12.1401C10.68 12.2601 10.5067 12.3334 10.3333 12.3334C10.16 12.3334 9.98667 12.2601 9.86 12.1401C9.74 12.0134 9.66667 11.8401 9.66667 11.6667C9.66667 11.4934 9.74 11.3201 9.86 11.1934C10.1067 10.9467 10.56 10.9467 10.8067 11.1934C10.9267 11.3201 11 11.4934 11 11.6667C11 11.8401 10.9267 12.0134 10.8067 12.1401ZM10.8067 9.80673C10.7733 9.83339 10.74 9.86006 10.7067 9.88673C10.6667 9.91339 10.6267 9.93339 10.5867 9.94673C10.5467 9.96673 10.5067 9.98006 10.4667 9.98672C10.42 9.99339 10.3733 10.0001 10.3333 10.0001C10.16 10.0001 9.98667 9.92673 9.86 9.80673C9.74 9.68006 9.66667 9.50673 9.66667 9.33339C9.66667 9.16006 9.74 8.98673 9.86 8.86006C9.92667 8.80006 9.99333 8.75339 10.08 8.72006C10.2 8.66673 10.3333 8.65339 10.4667 8.68006C10.5067 8.68673 10.5467 8.70006 10.5867 8.72006C10.6267 8.73339 10.6667 8.75339 10.7067 8.78006C10.74 8.80673 10.7733 8.83339 10.8067 8.86006C10.9267 8.98673 11 9.16006 11 9.33339C11 9.50673 10.9267 9.68006 10.8067 9.80673Z"
          fill="#414244"
        />
      </svg>
    </div>
  );
};

export default CalendarSVG;
