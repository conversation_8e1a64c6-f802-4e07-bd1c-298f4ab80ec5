import React from 'react';

type Props = {
  width?: number;
  height?: number;
};
const SendSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div {...props}>
      <svg
        width={props?.width ?? '20'}
        height={props?.height ?? '20'}
        viewBox="0 0 20 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.5399 2.54476C15.7249 0.59012 2.07223 5.37831 2.0835 7.12649C2.09628 9.10888 7.41523 9.71875 8.88952 10.1324C9.77607 10.3811 10.0135 10.636 10.2179 11.5657C11.1438 15.776 11.6086 17.8702 12.668 17.917C14.3566 17.9916 19.3113 4.45232 17.5399 2.54476Z"
          stroke="#363840"
          strokeWidth="1.5"
        />
        <path
          d="M9.5835 10.4167L12.5002 7.5"
          stroke="#141B34"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};

export default SendSVG;
