import React from 'react';

type Props = {
  width?: number;
  height?: number;
};
const MoveSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div>
      <svg
        width={props?.width ?? '24'}
        height={props?.height ?? '24'}
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <circle cx="7.5" cy="4.5" r="1.5" fill="#383B46" />
        <circle cx="7.5" cy="12" r="1.5" fill="#383B46" />
        <circle cx="7.5" cy="19.5" r="1.5" fill="#383B46" />
        <circle cx="16.5" cy="4.5" r="1.5" fill="#383B46" />
        <circle cx="16.5" cy="12" r="1.5" fill="#383B46" />
        <circle cx="16.5" cy="19.5" r="1.5" fill="#383B46" />
      </svg>
    </div>
  );
};

export default MoveSVG;
