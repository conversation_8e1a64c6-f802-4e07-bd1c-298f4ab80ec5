import React from 'react';

type Props = {
  styleType?: 'default' | 'white';
  width?: number;
  height?: number;
};
const LocationSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div {...props}>
      <svg
        width="12"
        height="14"
        viewBox="0 0 12 14"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.99987 1.71886C8.939 0.647054 7.50016 0.0449219 5.99987 0.0449219C4.49958 0.0449219 3.06074 0.647054 1.99987 1.71886C0.939006 2.79066 0.343018 4.24434 0.343018 5.76009C0.343018 7.27585 0.939006 8.72953 1.99987 9.80133L5.51321 13.3576C5.57518 13.4207 5.64891 13.4709 5.73015 13.5051C5.81139 13.5392 5.89853 13.5569 5.98654 13.5569C6.07455 13.5569 6.16168 13.5392 6.24292 13.5051C6.32416 13.4709 6.3979 13.4207 6.45987 13.3576L9.99987 9.76765C11.0563 8.70031 11.6498 7.25269 11.6498 5.74326C11.6498 4.23382 11.0563 2.7862 9.99987 1.71886ZM9.04654 8.80449L5.99987 11.896L2.9532 8.80449C2.3513 8.19583 1.94152 7.42053 1.77567 6.57662C1.60981 5.7327 1.69533 4.85805 2.02141 4.06323C2.34749 3.26841 2.89948 2.5891 3.60763 2.11118C4.31577 1.63325 5.14826 1.37817 5.99987 1.37817C6.85148 1.37817 7.68398 1.63325 8.39212 2.11118C9.10026 2.5891 9.65226 3.26841 9.97833 4.06323C10.3044 4.85805 10.3899 5.7327 10.2241 6.57662C10.0582 7.42053 9.64844 8.19583 9.04654 8.80449ZM3.99987 3.69233C3.46168 4.23774 3.15947 4.97648 3.15947 5.74662C3.15947 6.51677 3.46168 7.25551 3.99987 7.80092C4.39971 8.20557 4.90893 8.48192 5.46364 8.59529C6.01835 8.70866 6.59384 8.65401 7.11792 8.43819C7.642 8.22237 8.09133 7.85499 8.40952 7.38214C8.72772 6.9093 8.90063 6.35205 8.90654 5.7803C8.90954 5.39854 8.83675 5.02005 8.69247 4.6672C8.54819 4.31435 8.33535 3.99431 8.06654 3.726C7.80232 3.45294 7.48726 3.23536 7.13949 3.08578C6.79171 2.93621 6.4181 2.85759 6.04014 2.85446C5.66218 2.85133 5.28734 2.92375 4.93719 3.06754C4.58703 3.21134 4.26849 3.42367 3.99987 3.69233ZM7.12654 6.84449C6.8739 7.10363 6.54001 7.26606 6.18196 7.30402C5.8239 7.34197 5.46391 7.25309 5.16354 7.05257C4.86317 6.85205 4.64107 6.55234 4.53521 6.20469C4.42935 5.85703 4.4463 5.48302 4.58318 5.1466C4.72005 4.81018 4.96834 4.53224 5.28559 4.3603C5.60285 4.18836 5.96937 4.1331 6.3225 4.20396C6.67562 4.27482 6.99341 4.46741 7.22154 4.7488C7.44968 5.03018 7.57398 5.38289 7.5732 5.74662C7.56351 6.16238 7.39087 6.55725 7.0932 6.84449H7.12654Z"
          fill="#88889D"
        />
      </svg>
    </div>
  );
};

export default LocationSVG;
