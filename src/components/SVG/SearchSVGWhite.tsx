import React from 'react';

const SearchSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>,
) => {
  return (
    <div {...props}>
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.66536 14C11.1632 14 13.9987 11.1645 13.9987 7.66671C13.9987 4.1689 11.1632 1.33337 7.66536 1.33337C4.16756 1.33337 1.33203 4.1689 1.33203 7.66671C1.33203 11.1645 4.16756 14 7.66536 14Z"
          stroke="#fff"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M14.6654 14.6667L13.332 13.3334"
          stroke="#fff"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};
export default SearchSVG;
