import React from 'react';

type Props = {
  styleType?: 'default' | 'white';
  width?: number;
  height?: number;
};
const EditSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div>
      <svg
        width={props?.width ?? '16'}
        height={props?.height ?? '16'}
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.83958 2.4L3.36624 8.19333C3.15958 8.41333 2.95958 8.84667 2.91958 9.14667L2.67291 11.3067C2.58624 12.0867 3.14624 12.62 3.91958 12.4867L6.06624 12.12C6.36624 12.0667 6.78624 11.8467 6.99291 11.62L12.4662 5.82667C13.4129 4.82667 13.8396 3.68667 12.3662 2.29334C10.8996 0.913336 9.78624 1.4 8.83958 2.4Z"
          stroke={props.styleType === 'white' ? '#fff' : '#3997C8'}
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M7.92578 3.36667C8.21245 5.20667 9.70578 6.61333 11.5591 6.8"
          stroke={props.styleType === 'white' ? '#fff' : '#3997C8'}
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M2 14.6667H14"
          stroke={props.styleType === 'white' ? '#fff' : '#3997C8'}
          strokeWidth="1.5"
          strokeMiterlimit="10"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};

export default EditSVG;
