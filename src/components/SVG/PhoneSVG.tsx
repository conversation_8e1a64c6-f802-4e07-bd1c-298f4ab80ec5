import React from 'react';

type Props = {
  width?: number;
  height?: number;
};
const PhoneSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div {...props}>
      <svg
        width={props?.width ?? '17'}
        height={props?.height ?? '17'}
        viewBox="0 0 17 17"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M15.5622 12.9841C15.5622 13.2391 15.5055 13.5012 15.3851 13.7562C15.2647 14.0112 15.1088 14.252 14.9034 14.4787C14.5563 14.8612 14.1738 15.1374 13.7417 15.3145C13.3167 15.4916 12.8563 15.5837 12.3605 15.5837C11.638 15.5837 10.8659 15.4137 10.0513 15.0666C9.23675 14.7195 8.42216 14.252 7.61466 13.6641C6.80008 13.0691 6.028 12.4103 5.29133 11.6807C4.56175 10.9441 3.903 10.172 3.31508 9.36449C2.73425 8.55699 2.26675 7.74949 1.92675 6.94908C1.58675 6.14158 1.41675 5.36949 1.41675 4.63283C1.41675 4.15116 1.50175 3.69074 1.67175 3.26574C1.84175 2.83366 2.11091 2.43699 2.48633 2.08283C2.93966 1.63658 3.4355 1.41699 3.95966 1.41699C4.158 1.41699 4.35633 1.45949 4.53341 1.54449C4.71758 1.62949 4.8805 1.75699 5.008 1.94116L6.65133 4.25741C6.77883 4.43449 6.87091 4.59741 6.93466 4.75324C6.99841 4.90199 7.03383 5.05074 7.03383 5.18533C7.03383 5.35533 6.98425 5.52533 6.88508 5.68824C6.793 5.85116 6.65841 6.02116 6.48841 6.19116L5.95008 6.75074C5.87216 6.82866 5.83675 6.92074 5.83675 7.03408C5.83675 7.09074 5.84383 7.14033 5.858 7.19699C5.87925 7.25366 5.9005 7.29616 5.91466 7.33866C6.04216 7.57241 6.26175 7.87699 6.57341 8.24533C6.89216 8.61366 7.23216 8.98908 7.6005 9.36449C7.983 9.73991 8.35133 10.087 8.72675 10.4057C9.09508 10.7174 9.39966 10.9299 9.6405 11.0574C9.67591 11.0716 9.71841 11.0928 9.768 11.1141C9.82466 11.1353 9.88133 11.1424 9.94508 11.1424C10.0655 11.1424 10.1576 11.0999 10.2355 11.022L10.7738 10.4907C10.9509 10.3137 11.1209 10.1791 11.2838 10.0941C11.4467 9.99491 11.6097 9.94533 11.7867 9.94533C11.9213 9.94533 12.063 9.97366 12.2188 10.0374C12.3747 10.1012 12.5376 10.1932 12.7147 10.3137L15.0592 11.9782C15.2434 12.1057 15.3709 12.2545 15.4488 12.4316C15.5197 12.6087 15.5622 12.7857 15.5622 12.9841Z"
          stroke="#88889D"
          strokeWidth="1.5"
          strokeMiterlimit="10"
        />
      </svg>
    </div>
  );
};

export default PhoneSVG;
