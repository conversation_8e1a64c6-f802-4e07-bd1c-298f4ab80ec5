import React from 'react';

type Props = {
  width?: number;
  height?: number;
};
const NotificationStatus = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div {...props}>
      <svg
        width={props?.width ?? '25'}
        height={props?.height ?? '24'}
        viewBox="0 0 25 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M19.5 8C21.1569 8 22.5 6.65685 22.5 5C22.5 3.34315 21.1569 2 19.5 2C17.8431 2 16.5 3.34315 16.5 5C16.5 6.65685 17.8431 8 19.5 8Z"
          fill="white"
        />
        <path
          d="M19.5 9.5C17.02 9.5 15 7.48 15 5C15 4.28 15.19 3.61 15.49 3H8.02C4.57 3 2.5 5.06 2.5 8.52V16.47C2.5 19.94 4.57 22 8.02 22H15.97C19.43 22 21.49 19.94 21.49 16.48V9.01C20.89 9.31 20.22 9.5 19.5 9.5Z"
          fill="white"
        />
        <path
          d="M12.25 14H7.25C6.84 14 6.5 13.66 6.5 13.25C6.5 12.84 6.84 12.5 7.25 12.5H12.25C12.66 12.5 13 12.84 13 13.25C13 13.66 12.66 14 12.25 14Z"
          fill="#BF83FF"
        />
        <path
          d="M16.25 18H7.25C6.84 18 6.5 17.66 6.5 17.25C6.5 16.84 6.84 16.5 7.25 16.5H16.25C16.66 16.5 17 16.84 17 17.25C17 17.66 16.66 18 16.25 18Z"
          fill="#BF83FF"
        />
      </svg>
    </div>
  );
};

export default NotificationStatus;
