import React from 'react';

const FileDollarIcon = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>,
) => {
  return (
    <div {...props}>
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M13.3334 6.60573V5.21179C13.3334 4.08297 13.3334 3.51855 13.1547 3.06777C12.8675 2.34307 12.2602 1.77144 11.4902 1.50114C11.0112 1.33301 10.4115 1.33301 9.21209 1.33301C7.11323 1.33301 6.06375 1.33301 5.22557 1.62724C3.87809 2.10026 2.81521 3.10062 2.31262 4.36884C2 5.15771 2 6.14544 2 8.12087V9.81787C2 11.8641 2 12.8872 2.56513 13.5977C2.72705 13.8013 2.91908 13.9821 3.13538 14.1345C3.89031 14.6663 4.97737 14.6663 7.15149 14.6663H7.66669C8.02309 14.6663 8.68916 14.6663 9.00003 14.6645M2 7.99967C2 6.77234 2.99492 5.77745 4.22222 5.77745C4.66607 5.77745 5.18935 5.85523 5.62091 5.73959C6.00434 5.63685 6.30384 5.33735 6.40658 4.95391C6.52221 4.52237 6.44444 3.99909 6.44444 3.55523C6.44444 2.32793 7.43936 1.33301 8.66669 1.33301"
          stroke="#3997C8"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.8328 10.5401C13.9027 9.69709 12.6129 9.00877 11.6272 9.47058C10.3959 10.0475 10.5032 11.5056 12.024 11.5827C12.7007 11.617 13.2927 11.5439 13.6949 11.9799C14.0972 12.4159 14.2715 13.5261 13.0857 13.9029C11.9 14.2796 10.6641 13.6077 10.6641 12.7881M12.3114 8.66602V9.31779M12.3114 14.1463V14.666"
          stroke="#3997C8"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};

export default FileDollarIcon;
