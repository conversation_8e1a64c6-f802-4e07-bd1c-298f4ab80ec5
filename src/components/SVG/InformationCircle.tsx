import React from 'react';

const InformationCircle = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>,
) => {
  return (
    <div {...props}>
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M14.6654 7.99935C14.6654 4.31745 11.6806 1.33268 7.9987 1.33268C4.3168 1.33268 1.33203 4.31745 1.33203 7.99935C1.33203 11.6812 4.3168 14.666 7.9987 14.666C11.6806 14.666 14.6654 11.6812 14.6654 7.99935Z"
          stroke="#FDAF2E"
          strokeWidth="1.25"
        />
        <path
          d="M8.15885 11.333V7.99967C8.15885 7.6854 8.15885 7.52827 8.06122 7.43064C7.96359 7.33301 7.80646 7.33301 7.49219 7.33301"
          stroke="#FDAF2E"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M7.99206 5.33301H7.99805"
          stroke="#FDAF2E"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};

export default InformationCircle;
