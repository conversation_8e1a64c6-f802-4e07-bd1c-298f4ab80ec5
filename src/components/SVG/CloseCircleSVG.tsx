import React from 'react';

type Props = {
  styleType?: 'default' | 'white';
  width?: number;
  height?: number;
};
const CloseCircleSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div {...props}>
      <svg
        width={props?.width ?? '16'}
        height={props?.height ?? '16'}
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M7.9987 14.6667C11.6654 14.6667 14.6654 11.6667 14.6654 8.00001C14.6654 4.33334 11.6654 1.33334 7.9987 1.33334C4.33203 1.33334 1.33203 4.33334 1.33203 8.00001C1.33203 11.6667 4.33203 14.6667 7.9987 14.6667Z"
          stroke={props.styleType === 'white' ? '#fff' : '#3997C8'}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M6.11328 9.88668L9.88661 6.11334"
          stroke={props.styleType === 'white' ? '#fff' : '#3997C8'}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M9.88661 9.88668L6.11328 6.11334"
          stroke={props.styleType === 'white' ? '#fff' : '#3997C8'}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};

export default CloseCircleSVG;
