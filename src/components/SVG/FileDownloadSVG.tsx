import React from 'react';

type Props = {
  width?: number;
  height?: number;
};

const FileDownloadSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div {...props}>
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M8.33301 1.33398H8.51483C10.689 1.33398 11.776 1.33398 12.531 1.86587C12.7473 2.01827 12.9393 2.199 13.1012 2.40258C13.6663 3.1131 13.6663 4.13622 13.6663 6.18247V7.87944C13.6663 9.85489 13.6663 10.8426 13.3537 11.6315C12.8511 12.8997 11.7883 13.9001 10.4408 14.3731C9.60259 14.6673 8.55313 14.6673 6.45422 14.6673C5.25484 14.6673 4.65515 14.6673 4.17619 14.4992C3.4062 14.2289 2.79884 13.6573 2.51165 12.9326C2.33301 12.4818 2.33301 11.9174 2.33301 10.7885V8.00065"
          stroke="#225DE0"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.6667 8C13.6667 9.2273 12.6717 10.2222 11.4444 10.2222C11.0006 10.2222 10.4773 10.1444 10.0458 10.2601C9.66232 10.3628 9.36282 10.6623 9.26008 11.0458C9.14445 11.4773 9.22222 12.0006 9.22222 12.4444C9.22222 13.6717 8.2273 14.6667 7 14.6667"
          stroke="#225DE0"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M3 5.0013C3.32769 5.33843 4.19985 6.66797 4.66667 6.66797M6.33333 5.0013C6.00565 5.33843 5.13349 6.66797 4.66667 6.66797M4.66667 6.66797L4.66667 1.33464"
          stroke="#225DE0"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};

export default FileDownloadSVG;
