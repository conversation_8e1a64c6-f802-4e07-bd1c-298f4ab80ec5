import React from 'react';

const FileIcon = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>,
) => {
  return (
    <div {...props}>
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.6641 11.3333L5.9974 11.3333"
          stroke="#3997C8"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10.6641 8.6665L8.66406 8.6665"
          stroke="#3997C8"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.6641 9.33317C13.6641 11.8473 13.6641 13.1044 12.8342 13.8855C12.0043 14.6665 10.6687 14.6665 7.9974 14.6665H7.48224C5.30811 14.6665 4.22104 14.6665 3.46611 14.1346C3.24981 13.9822 3.05778 13.8015 2.89586 13.5979C2.33073 12.8874 2.33073 11.8643 2.33073 9.81802V8.12105C2.33073 6.1456 2.33073 5.15788 2.64335 4.369C3.14594 3.10079 4.20882 2.10043 5.5563 1.62741C6.39448 1.33317 7.44394 1.33317 9.54285 1.33317C10.7422 1.33317 11.3419 1.33317 11.8209 1.5013C12.5909 1.7716 13.1982 2.34324 13.4854 3.06793C13.6641 3.51872 13.6641 4.08313 13.6641 5.21196V9.33317Z"
          stroke="#3997C8"
          strokeWidth="1.5"
          strokeLinejoin="round"
        />
        <path
          d="M2.33333 8C2.33333 6.7727 3.32826 5.77778 4.55556 5.77778C4.99941 5.77778 5.52269 5.85555 5.95424 5.73992C6.33768 5.63718 6.63718 5.33768 6.73992 4.95424C6.85555 4.52269 6.77778 3.99941 6.77778 3.55556C6.77778 2.32826 7.7727 1.33333 9 1.33333"
          stroke="#3997C8"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};

export default FileIcon;
