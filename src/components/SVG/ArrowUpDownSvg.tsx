import React from 'react';

type Props = {
  width?: number;
  height?: number;
};

const ArrowUpDownSvg = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div>
      <svg
        width={props?.width ?? '16'}
        height={props?.height ?? '16'}
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4.66699 2.66602V13.3327"
          stroke="white"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M11.334 12.666L11.334 2.66602"
          stroke="white"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M6.66699 4.666C6.66699 4.666 5.19401 2.66602 4.66698 2.66602C4.13994 2.66601 2.66699 4.66602 2.66699 4.66602"
          stroke="white"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13.334 11.332C13.334 11.332 11.861 13.332 11.334 13.332C10.8069 13.332 9.33398 11.332 9.33398 11.332"
          stroke="white"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
};

export default ArrowUpDownSvg;
