import React from 'react';

type Props = {
  width?: number;
  height?: number;
};
const EyeSVG = (
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) => {
  return (
    <div {...props}>
      <svg
        width={props?.width ?? '25'}
        height={props?.height ?? '24'}
        viewBox="0 0 25 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M21.75 9.14993C19.44 5.51993 16.06 3.42993 12.5 3.42993C10.72 3.42993 8.99 3.94993 7.41 4.91993C5.83 5.89993 4.41 7.32993 3.25 9.14993C2.25 10.7199 2.25 13.2699 3.25 14.8399C5.56 18.4799 8.94 20.5599 12.5 20.5599C14.28 20.5599 16.01 20.0399 17.59 19.0699C19.17 18.0899 20.59 16.6599 21.75 14.8399C22.75 13.2799 22.75 10.7199 21.75 9.14993ZM12.5 16.0399C10.26 16.0399 8.46 14.2299 8.46 11.9999C8.46 9.76993 10.26 7.95993 12.5 7.95993C14.74 7.95993 16.54 9.76993 16.54 11.9999C16.54 14.2299 14.74 16.0399 12.5 16.0399Z"
          fill="white"
        />
        <path
          d="M12.4999 9.13989C10.9299 9.13989 9.6499 10.4199 9.6499 11.9999C9.6499 13.5699 10.9299 14.8499 12.4999 14.8499C14.0699 14.8499 15.3599 13.5699 15.3599 11.9999C15.3599 10.4299 14.0699 9.13989 12.4999 9.13989Z"
          fill="white"
        />
      </svg>
    </div>
  );
};

export default EyeSVG;
