import React from 'react';

type Props = {
  color?: string;
  width?: number;
  height?: number;
};

export default function BadgeCheckSVG(
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement> & Props,
) {
  return (
    <div {...props}>
      <svg
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M9.00012 12.0006L11.0001 14.0006L15.0001 10.0006M7.83486 4.69766C8.55239 4.6404 9.23358 4.35824 9.78144 3.89136C11.0599 2.80184 12.9403 2.80184 14.2188 3.89136C14.7667 4.35824 15.4478 4.6404 16.1654 4.69766C17.8398 4.83128 19.1695 6.16092 19.3031 7.83535C19.3603 8.55288 19.6425 9.23407 20.1094 9.78193C21.1989 11.0604 21.1989 12.9408 20.1094 14.2193C19.6425 14.7672 19.3603 15.4483 19.3031 16.1659C19.1695 17.8403 17.8398 19.1699 16.1654 19.3036C15.4479 19.3608 14.7667 19.643 14.2188 20.1099C12.9403 21.1994 11.0599 21.1994 9.78144 20.1099C9.23358 19.643 8.55239 19.3608 7.83486 19.3036C6.16043 19.1699 4.83079 17.8403 4.69717 16.1659C4.63991 15.4483 4.35775 14.7672 3.89087 14.2193C2.80135 12.9408 2.80135 11.0604 3.89087 9.78193C4.35775 9.23407 4.63991 8.55288 4.69717 7.83535C4.83079 6.16092 6.16043 4.83128 7.83486 4.69766Z"
          stroke={props.color ? props.color : '#545F71'}
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
}
