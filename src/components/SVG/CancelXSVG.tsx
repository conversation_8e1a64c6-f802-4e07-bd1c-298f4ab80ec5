import React from 'react';

type CancelXSVGProps = React.DetailedHTMLProps<
  React.HTMLAttributes<HTMLDivElement>,
  HTMLDivElement
> & {
  width?: string;
  height?: string;
};

export default function CancelXSVG(props: CancelXSVGProps) {
  return (
    <div {...props}>
      <svg
        width="16"
        height="16"
        viewBox="0 0 16 16"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M12.667 3.33398L3.33362 12.6673M3.33362 3.33398L12.667 12.6673"
          stroke="#FF3B30"
          strokeWidth="1.25"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
}
