import React from 'react';

type CloseSVGProps = React.DetailedHTMLProps<
  React.HTMLAttributes<HTMLDivElement>,
  HTMLDivElement
> & {
  width?: string;
  height?: string;
};

export default function CloseSVG(props: CloseSVGProps) {
  return (
    <div {...props}>
      <svg
        width={props?.width ?? '22'}
        height={props?.height ?? '22'}
        viewBox="0 0 22 22"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M4.58203 4.58331L17.4145 17.4158"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M4.58159 17.4158L17.4141 4.58331"
          stroke="black"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
}
