import React from 'react';

export default function AddCircleSVG(
  props: React.DetailedHTMLProps<React.HTMLAttributes<HTMLDivElement>, HTMLDivElement>,
) {
  return (
    <div {...props}>
      <svg
        width="21"
        height="20"
        viewBox="0 0 21 20"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M10.5001 18.3332C15.0834 18.3332 18.8334 14.5832 18.8334 9.99984C18.8334 5.4165 15.0834 1.6665 10.5001 1.6665C5.91675 1.6665 2.16675 5.4165 2.16675 9.99984C2.16675 14.5832 5.91675 18.3332 10.5001 18.3332Z"
          stroke="white"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M7.16675 10H13.8334"
          stroke="white"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M10.5 13.3332V6.6665"
          stroke="white"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </svg>
    </div>
  );
}
