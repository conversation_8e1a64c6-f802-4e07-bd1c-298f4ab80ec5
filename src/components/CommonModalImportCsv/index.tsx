import STATUS_CODE from '@/constants/statusCode';
import { DownloadOutlined, UploadOutlined } from '@ant-design/icons';
import { Modal, Upload } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';
import BasicButton from '../Commons/BasicButton';
import { openNotificationFail, openNotificationSuccess } from '../Notification';

import { importTravelList } from '@/apis/accounting/traveList';
import { importCsvBusinessPartner } from '@/apis/businessPartner';
import { importCsvAccountSubjectMaster } from '@/apis/master/accountSubject';
import { importCsvAggregationItem } from '@/apis/master/AggregationItem';
import { importCsvConsumptionTax } from '@/apis/master/consumptionTax';
import { importCsvCurrencyTypeMaster } from '@/apis/master/currencyType';
import { importCsvSubjectMaster } from '@/apis/master/subjectMaster';
import { importCsvTaxCategory } from '@/apis/master/taxCategory';
import { importCsvTravelTypeMaster } from '@/apis/travelTypeMaster';
import type { ErrorResponse } from '@/constants/errorCode';
import { getFieldCsvErrorMessage } from '@/constants/errorCode';
import { EntityCancelFeeEnum, EntityEnum } from '@/apis/purchase-and-material-management/tariff';
import {
  importCsvCancelFeeTouristSpot,
  importCsvPriceTouristSpot,
} from '@/apis/purchase-and-material-management/touristDestinations';
import {
  importCsvCancelFeeHotel,
  importCsvPriceHotel,
} from '@/apis/purchase-and-material-management/hotel';
import {
  importCsvCancelFeeRestaurant,
  importCsvPriceRestaurant,
} from '@/apis/purchase-and-material-management/restaurant';
import {
  importCsvCancelFeeBus,
  importCsvPriceBus,
} from '@/apis/purchase-and-material-management/buses';
import {
  importCsvCancelFeeHireCar,
  importCsvPriceHireCar,
} from '@/apis/purchase-and-material-management/hireCar';
import {
  importCsvCancelFeeRailway,
  importCsvPriceRailway,
} from '@/apis/purchase-and-material-management/railway';
import {
  importCsvCancelFeeAirline,
  importCsvPriceAirline,
} from '@/apis/purchase-and-material-management/airline';
import {
  importCsvCancelFeeShip,
  importCsvPriceShip,
} from '@/apis/purchase-and-material-management/ship';
import {
  importCsvCancelFeeGuide,
  importCsvPriceGuide,
} from '@/apis/purchase-and-material-management/guides';
import {
  importCsvCancelFeeDelivery,
  importCsvPriceDelivery,
} from '@/apis/purchase-and-material-management/delivery';
import {
  importCsvCancelFeeOther,
  importCsvPriceOther,
} from '@/apis/purchase-and-material-management/other';
import { importCsvSaleInvoice } from '@/apis/accounting/saleManagement';
import { importCsvPurchaseInvoice } from '@/apis/accounting/purchaseManagement';
import { importCsvPaymentInvoice } from '@/apis/accounting/paymentManagement';
import { importCsvDepositInvoice } from '@/apis/accounting/depositManagement';

export enum EMenuType {
  //master data
  'tax-category-master' = 'tax-category',
  'consumption-tax-master' = 'consumption-tax',
  'aggregate-item-master' = 'summary-item',
  'accounting-subject-master' = 'account-master',
  'currency-type-master' = 'currency-type',
  'item-master' = 'account-subject',
  'travel-type-master' = 'travel-master',
  'business-partner' = 'business_partner',

  //accounting
  'travel-list' = 'travel',
  'sale-invoice' = 'sale-invoice',
  'purchase-invoice' = 'purchase-invoice',
  'deposit-invoice' = 'deposit-invoice',
  'payment-invoice' = 'payment-invoice',
}

type Props = {
  type: EMenuType | EntityEnum | EntityCancelFeeEnum;
  _entity_id?: string;
  refetch?: () => void;
  confirm?: () => void;
};

export type ImportCsvCommonRef = {
  open: () => void;
  close: () => void;
};

const csvMap = {
  //master data csv
  [EMenuType['tax-category-master']]: {
    exportUrl: `/master/${EMenuType['tax-category-master']}_template.csv`,
    importFun: importCsvTaxCategory,
  },
  [EMenuType['consumption-tax-master']]: {
    exportUrl: `/master/${EMenuType['consumption-tax-master']}_template.csv`,
    importFun: importCsvConsumptionTax,
  },
  [EMenuType['aggregate-item-master']]: {
    exportUrl: `/master/${EMenuType['aggregate-item-master']}_template.csv`,
    importFun: importCsvAggregationItem,
  },
  [EMenuType['accounting-subject-master']]: {
    exportUrl: `/master/${EMenuType['accounting-subject-master']}_template.csv`,
    importFun: importCsvAccountSubjectMaster,
  },
  [EMenuType['currency-type-master']]: {
    exportUrl: `/master/${EMenuType['currency-type-master']}_template.csv`,
    importFun: importCsvCurrencyTypeMaster,
  },
  [EMenuType['item-master']]: {
    exportUrl: `/master/${EMenuType['item-master']}_template.csv`,
    importFun: importCsvSubjectMaster,
  },
  [EMenuType['travel-type-master']]: {
    exportUrl: `/master/${EMenuType['travel-type-master']}_template.csv`,
    importFun: importCsvTravelTypeMaster,
  },
  [EMenuType['business-partner']]: {
    exportUrl: `/master/${EMenuType['business-partner']}_template.csv`,
    importFun: importCsvBusinessPartner,
  },

  //accounting data csv
  [EMenuType['travel-list']]: {
    exportUrl: `/file-upload/${EMenuType['travel-list']}_template.csv`,
    importFun: importTravelList,
  },

  //purchase-and-management data csv
  //=====price
  [EntityEnum['travel-spot']]: {
    exportUrl: `/master/${EntityEnum['travel-spot']}_template.csv`,
    importFun: importCsvPriceTouristSpot,
  },
  [EntityEnum.hotel]: {
    exportUrl: `/master/${EntityEnum.hotel}_template.csv`,
    importFun: importCsvPriceHotel,
  },
  [EntityEnum.restaurant]: {
    exportUrl: `/master/${EntityEnum.restaurant}_template.csv`,
    importFun: importCsvPriceRestaurant,
  },
  [EntityEnum.bus]: {
    exportUrl: `/master/${EntityEnum.bus}_template.csv`,
    importFun: importCsvPriceBus,
  },
  [EntityEnum['hire-car']]: {
    exportUrl: `/master/${EntityEnum['hire-car']}_template.csv`,
    importFun: importCsvPriceHireCar,
  },
  [EntityEnum['bullet-train']]: {
    exportUrl: `/master/${EntityEnum['bullet-train']}_template.csv`,
    importFun: importCsvPriceRailway,
  },
  [EntityEnum.airplane]: {
    exportUrl: `/master/${EntityEnum.airplane}_template.csv`,
    importFun: importCsvPriceAirline,
  },
  [EntityEnum.ship]: {
    exportUrl: `/master/${EntityEnum.ship}_template.csv`,
    importFun: importCsvPriceShip,
  },
  [EntityEnum.guide]: {
    exportUrl: `/master/${EntityEnum.guide}_template.csv`,
    importFun: importCsvPriceGuide,
  },
  [EntityEnum.delivery]: {
    exportUrl: `/master/${EntityEnum.delivery}_template.csv`,
    importFun: importCsvPriceDelivery,
  },
  [EntityEnum['service-other']]: {
    exportUrl: `/master/${EntityEnum['service-other']}_template.csv`,
    importFun: importCsvPriceOther,
  },

  //=====cancellation-fee
  [EntityCancelFeeEnum['travel-spot-cancel-fee']]: {
    exportUrl: `/master/cancel_fee_template.csv`,
    importFun: importCsvCancelFeeTouristSpot,
  },
  [EntityCancelFeeEnum['hotel-cancel-fee']]: {
    exportUrl: `/master/cancel_fee_template.csv`,
    importFun: importCsvCancelFeeHotel,
  },
  [EntityCancelFeeEnum['restaurant-cancel-fee']]: {
    exportUrl: `/master/cancel_fee_template.csv`,
    importFun: importCsvCancelFeeRestaurant,
  },
  [EntityCancelFeeEnum['bus-cancel-fee']]: {
    exportUrl: `/master/cancel_fee_template.csv`,
    importFun: importCsvCancelFeeBus,
  },
  [EntityCancelFeeEnum['hire-car-cancel-fee']]: {
    exportUrl: `/master/cancel_fee_template.csv`,
    importFun: importCsvCancelFeeHireCar,
  },
  [EntityCancelFeeEnum['bullet-train-cancel-fee']]: {
    exportUrl: `/master/cancel_fee_template.csv`,
    importFun: importCsvCancelFeeRailway,
  },
  [EntityCancelFeeEnum['airplane-cancel-fee']]: {
    exportUrl: `/master/cancel_fee_template.csv`,
    importFun: importCsvCancelFeeAirline,
  },
  [EntityCancelFeeEnum['ship-cancel-fee']]: {
    exportUrl: `/master/cancel_fee_template.csv`,
    importFun: importCsvCancelFeeShip,
  },
  [EntityCancelFeeEnum['guide-cancel-fee']]: {
    exportUrl: `/master/cancel_fee_template.csv`,
    importFun: importCsvCancelFeeGuide,
  },
  [EntityCancelFeeEnum['delivery-cancel-fee']]: {
    exportUrl: `/master/cancel_fee_template.csv`,
    importFun: importCsvCancelFeeDelivery,
  },
  [EntityCancelFeeEnum['service-other-cancel-fee']]: {
    exportUrl: `/master/cancel_fee_template.csv`,
    importFun: importCsvCancelFeeOther,
  },

  //sale invoice
  [EMenuType['sale-invoice']]: {
    exportUrl: `/import/sale_invoice_template.csv`,
    importFun: importCsvSaleInvoice,
  },
  //purchase invoice
  [EMenuType['purchase-invoice']]: {
    exportUrl: `/import/purchase_invoice_template.csv`,
    importFun: importCsvPurchaseInvoice,
  },

  //deposit invoice
  [EMenuType['deposit-invoice']]: {
    exportUrl: `/import/deposit_invoice_template.csv`,
    importFun: importCsvDepositInvoice,
  },
  //payment invoice
  [EMenuType['payment-invoice']]: {
    exportUrl: `/import/payment_invoice_template.csv`,
    importFun: importCsvPaymentInvoice,
  },
};

const CommonModalImportCsv = forwardRef<ImportCsvCommonRef, Props>(
  ({ type, refetch, confirm, _entity_id }, ref) => {
    const [isVisble, setIsVisible] = useState(false);
    const [isUploading, setIsUploading] = useState<boolean>(false);
    const [messageError, setMessageError] = useState<string>();

    const open = () => {
      setIsVisible(true);
    };

    const close = () => {
      setIsVisible(false);
    };

    useImperativeHandle(ref, () => ({ open, close }));

    const onUploadFile = async (file: File) => {
      const formData = new FormData();
      formData.append('file', file);
      try {
        setIsUploading(true);
        const fnc = _entity_id
          ? csvMap[type].importFun(formData, _entity_id)
          : csvMap[type].importFun(formData, undefined); // Pass undefined as the second argument
        const { status, error } = await fnc;
        setIsUploading(false);
        if (status === STATUS_CODE.SUCCESSFUL) {
          confirm?.();
          refetch?.();
          openNotificationSuccess('インポートファイルが成功しました');
          setMessageError(undefined);
          close();
        } else {
          if ((error as ErrorResponse)?.data?.errors?.fields) {
            const messageCsvError = getFieldCsvErrorMessage(
              (error as ErrorResponse)?.data?.errors?.fields,
            );
            const lineError = (error as ErrorResponse)?.data?.errors?.line;
            openNotificationFail(`Line ${lineError}: \n${messageCsvError}`);
            setMessageError(`Line ${lineError}: \n${messageCsvError}`);
          } else {
            openNotificationFail('インポートファイルが失敗しました');
          }
        }
      } catch (error) {
        console.log('upload error', error);
        setIsUploading(false);
        openNotificationFail('インポートファイルが失敗しました');
      }
    };

    const beforeUpload = async (file: File) => {
      await onUploadFile(file);
    };

    const onExportTemplateCsv = async () => {
      try {
        const urlCsv = `${process.env.FILE_URL}${csvMap[type].exportUrl}`;

        if (urlCsv) {
          const a = document.createElement('a');
          a.href = urlCsv;
          a.click();
          window.URL.revokeObjectURL(urlCsv);
        }
        close();
      } catch (error) {
        console.error('error', error);
        close();
        openNotificationFail('サーバーエラー');
      }
    };
    return (
      <Modal open={isVisble} footer={null} onCancel={close} centered>
        <div className="flex flex-col">
          <span className="text-[18px] font-bold">CSV取込</span>
          <span className="mt-1">CSVデータをアップロードしてください。</span>
          {messageError && (
            <span className="text-[red] text-sm whitespace-pre-line">{messageError}</span>
          )}
          <div className="flex flex-col justify-center items-center my-4">
            <Upload
              showUploadList={false}
              beforeUpload={beforeUpload}
              accept=".csv"
              className="flex flex-col items-center"
            >
              <BasicButton
                className="flex items-center justify-center space-x-[8px] w-[200px] mb-2"
                icon={<UploadOutlined width={16} height={16} />}
                styleType={'accept'}
                loading={isUploading}
              >
                ファイルを選択
              </BasicButton>
            </Upload>
            <span className="text-sm text-[#AAAAAA]">ファイル形式：.csvのみ</span>
          </div>
        </div>
        <div className="flex flex-col">
          <span className="text-[18px] font-bold">CSVテンプレートダウンロード</span>
          <div className="flex justify-center mt-4">
            <BasicButton
              className="flex items-center justify-center space-x-[8px] w-[200px] text-white"
              icon={<DownloadOutlined width={16} height={16} />}
              styleType={'back'}
              onClick={onExportTemplateCsv}
            >
              CSV入稿をダウンロード
            </BasicButton>
          </div>
        </div>
      </Modal>
    );
  },
);

export default CommonModalImportCsv;
