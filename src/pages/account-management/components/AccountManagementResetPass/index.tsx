import React, { useRef } from 'react';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import BasicModal from '@/components/Commons/BasicModal';
import BasicButton from '@/components/Commons/BasicButton';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { sendNewPassword } from '@/apis/account';
import STATUS_CODE from '@/constants/statusCode';

interface Props {
  accountId: number;
  onEmitEventLoading: (loading: boolean) => void;
}
const AccountManagementResetPass: React.FC<Props> = ({ accountId, onEmitEventLoading }) => {
  const refResetPasswordModal = useRef<BasicModalRef>(null);

  const openModalResetPassword = () => {
    if (refResetPasswordModal) {
      refResetPasswordModal.current.open();
    }
  };

  const onClickResetPassword = async () => {
    onEmitEventLoading(true);
    try {
      const { status } = await sendNewPassword(accountId);

      if (status === STATUS_CODE.SUCCESSFUL) {
        onEmitEventLoading(false);
        openNotificationSuccess(MESSAGE_ALERT.SENT_NEW_PASSWORD_SUCCESS);
      } else {
        onEmitEventLoading(false);
        openNotificationFail(MESSAGE_ALERT.SENT_NEW_PASSWORD_FAIL);
      }
      refResetPasswordModal.current.close();
    } catch (error: any) {
      onEmitEventLoading(false);
      openNotificationFail(MESSAGE_ALERT.SENT_NEW_PASSWORD_FAIL);
      refResetPasswordModal.current.close();
    }
  };

  const btnSubmitModalStyle: React.CSSProperties = {
    backgroundColor: '#3997C8',
  };

  return (
    <div>
      <BasicButton styleType="danger" onClick={openModalResetPassword}>
        PWリセット
      </BasicButton>

      <BasicModal
        ref={refResetPasswordModal}
        title="パスワードリセット"
        buttonCloseTitle="いいえ"
        buttonSubmitTitle="はい"
        buttonSubmitStyle={btnSubmitModalStyle}
        content={'該当ユーザーのパスワードをリセットしますか？'}
        onSubmit={onClickResetPassword}
      />
    </div>
  );
};

export default AccountManagementResetPass;
