import React, { useEffect, useState } from 'react';
import BasicInput from '@/components/Commons/BasicInput';
import type { FormInstance } from 'antd';
import { Col, Form, Row, Spin } from 'antd';
import styles from '../../index.less';
import BasicSelect from '@/components/Commons/BasicSelect';
import { ROLE_OPTIONS } from '@/utils/constants';
import { rules } from '@/constants/rules';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import RequiredTag from '@/components/Commons/RequiredTag/RequiredTag';
import { getDetailAccount } from '@/apis/account';
import STATUS_CODE from '@/constants/statusCode';

interface Props {
  form: FormInstance<any>;
  onSubmit: (v: any) => void;
  accountId?: number;
}
const AccountManagementForm: React.FC<Props> = ({ form, onSubmit, accountId }) => {
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchData = async () => {
      if (accountId) {
        setIsLoading(true);
        const { status, data } = await getDetailAccount(accountId);

        form.setFieldsValue({ ...data.data, occupation: Number(data.data.occupation) });
        if (status == STATUS_CODE.SUCCESSFUL) {
          setIsLoading(false);
        } else {
          openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
          setIsLoading(false);
        }
      }
    };
    fetchData();
  }, [form]);
  return (
    <div className="w-full">
      <Spin spinning={isLoading}>
        <Form form={form} onFinish={onSubmit}>
          <Row>
            <Col span={24} className={styles.sectionField}>
              <Form.Item rules={rules.requiredInput} name={'name'}>
                <BasicInput
                  title={
                    <div className="flex items-center gap-[6px]">
                      名前 <RequiredTag />{' '}
                    </div>
                  }
                  placeholder="Elizabeth Lopez"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24} className={styles.sectionField}>
              <Form.Item rules={[...rules.requiredEmail, ...rules.isEmail]} name={'email'}>
                <BasicInput
                  title={
                    <div className="flex items-center gap-[6px]">
                      メールアドレス <RequiredTag />{' '}
                    </div>
                  }
                  placeholder="<EMAIL>"
                />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item rules={rules?.requiredSelect} name={'occupation'}>
                <BasicSelect
                  title={
                    <div className="flex items-center gap-[6px]">
                      権限 <RequiredTag />{' '}
                    </div>
                  }
                  className="min-w-[140px] [&_.ant-select-selector]:flex [&_.ant-select-selector]:items-center"
                  options={ROLE_OPTIONS}
                  placeholder="メンバー"
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Spin>
    </div>
  );
};

export default AccountManagementForm;
