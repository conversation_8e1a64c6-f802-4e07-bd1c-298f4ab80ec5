import React, { useRef } from 'react';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import BasicButton from '@/components/Commons/BasicButton';
import AddSVG from '@/components/SVG/AddSVG';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import AccountManagementForm from '../AccountManagementForm';
import { Form } from 'antd';
import STATUS_CODE from '@/constants/statusCode';
import { createAccount } from '@/apis/account';

interface Props {
  onEmitEventLoading: (loading: boolean) => void;
  onEmitEventReRenderData: () => void;
}
const AccountManagementAddNew: React.FC<Props> = ({
  onEmitEventLoading,
  onEmitEventReRenderData,
}) => {
  const refModal = useRef<BasicModalRef>(null);
  const [form] = Form.useForm();

  const openModal = () => {
    if (refModal) {
      refModal.current.open();
    }
  };

  const onClickSubmitBtn = async () => {
    await form.validateFields();
    const values = form.getFieldsValue();
    // emit event loading to parent component
    const payload = {
      ...values,
      email: values.email,
      name: values.name,
      occupation: values.occupation,
    };
    const { status, error } = await createAccount(payload);

    if (status === STATUS_CODE.SUCCESSFUL || status === STATUS_CODE.CREATED) {
      openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);

      // emit event to parent component re-render listing
      onEmitEventReRenderData();
      refModal.current.close();
      form.resetFields();
    } else if (status === STATUS_CODE.INVALID) {
      const errors = error.data.errors;
      if (errors && Object.keys(errors).length) {
        Object.keys(errors).forEach((key) => {
          const message = errors[key][0];
          openNotificationFail(message);
        });
      }
    }
  };

  const handleSubmitAddNew = () => {};

  const btnSubmitModalStyle: React.CSSProperties = {
    backgroundColor: '#3997C8',
  };

  return (
    <div>
      <BasicButton
        styleType="accept"
        className="flex items-center justify-center border-main-color color space-x-[15px] pr-[3rem] font-bold text-base"
        onClick={openModal}
      >
        <AddSVG />
        追加
      </BasicButton>

      <BasicFormModal
        ref={refModal}
        content={<AccountManagementForm form={form} onSubmit={handleSubmitAddNew} />}
        title="アカウント発行"
        buttonCloseTitle="閉じる"
        buttonSubmitTitle="アカウント発行"
        buttonSubmitStyle={btnSubmitModalStyle}
        onSubmit={onClickSubmitBtn}
        isValidate={true}
      />
    </div>
  );
};

export default AccountManagementAddNew;
