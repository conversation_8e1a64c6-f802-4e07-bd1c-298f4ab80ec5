import React, { useRef, useState } from 'react';
import { Switch } from 'antd';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import BasicModal from '@/components/Commons/BasicModal';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { occupationAccount } from '@/utils/constants';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { disableAccount } from '@/apis/account';
import STATUS_CODE from '@/constants/statusCode';

interface Props {
  isActive: boolean;
  accountId: number;
  occupation: number;
  onEmitEventLoading: (loading: boolean) => void;
}
const AccountManagementDisable: React.FC<Props> = ({
  isActive,
  accountId,
  occupation,
  onEmitEventLoading,
}) => {
  const refDisableModal = useRef<BasicModalRef>(null);
  const isDisable = occupation == occupationAccount.adminOwner ? true : false;
  const [isSwitchChecked, setIsSwitchChecked] = useState(isActive);

  const closeModalDisable = () => {
    if (isSwitchChecked && refDisableModal) {
      refDisableModal.current.close();
    }
  };

  const onClickDisableBtn = async () => {
    // emit event loading to parent component
    onEmitEventLoading(true);
    try {
      const { status } = await disableAccount(accountId);

      if (status === STATUS_CODE.SUCCESSFUL) {
        onEmitEventLoading(false);
        openNotificationSuccess(MESSAGE_ALERT.CHANGE_STATUS_SUCCESS);
        setIsSwitchChecked(!isSwitchChecked);
        // emit event loading to parent component
        onEmitEventLoading(false);
      } else {
        onEmitEventLoading(false);
        openNotificationFail(MESSAGE_ALERT.CHANGE_STATUS_FAIL);
      }
    } catch (error: any) {
      onEmitEventLoading(false);
      openNotificationFail(MESSAGE_ALERT.CHANGE_STATUS_FAIL);
    } finally {
      closeModalDisable();
    }
  };

  const openModalDisable = () => {
    if (isSwitchChecked && refDisableModal) {
      refDisableModal.current.open();
    } else {
      // active -> not show model confirm
      onClickDisableBtn();
    }
  };

  const btnSubmitModalStyle: React.CSSProperties = {
    backgroundColor: '#3997C8',
  };

  return (
    <div>
      <div className="text-[15px] leading-5 font-medium text-[#383B46]">
        <Switch
          defaultChecked={isActive}
          checked={isSwitchChecked}
          onClick={openModalDisable}
          disabled={isDisable}
        />
      </div>
      <BasicModal
        ref={refDisableModal}
        title="アカウント無効"
        buttonCloseTitle="いいえ"
        buttonSubmitTitle="はい"
        buttonSubmitStyle={btnSubmitModalStyle}
        content={'このアカウントを無効にしてもよろしいですか？'}
        onSubmit={onClickDisableBtn}
      />
    </div>
  );
};

export default AccountManagementDisable;
