import React, { useRef } from 'react';
import { Form } from 'antd';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import BasicButton from '@/components/Commons/BasicButton';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import AccountManagementForm from '../AccountManagementForm';
import { openNotificationFail, openNotificationApprove } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { updateAccount } from '@/apis/account';
import STATUS_CODE from '@/constants/statusCode';

interface Props {
  accountId: number;
  onEmitEventLoading: (loading: boolean) => void;
  onEmitEventReRenderData: () => void;
}
const AccountManagementEdit: React.FC<Props> = ({
  accountId,
  onEmitEventLoading,
  onEmitEventReRenderData,
}) => {
  const [form] = Form.useForm();
  const refEditModal = useRef<BasicModalRef>(null);

  const btnSubmitModalStyle: React.CSSProperties = {
    backgroundColor: '#3997C8',
  };

  const openModalEdit = () => {
    if (refEditModal) {
      refEditModal.current.open();
    }
  };

  const onClickEditBtn = async () => {
    await form.validateFields();
    const values = form.getFieldsValue();
    // emit event loading to parent component
    const payload = {
      ...values,
      email: values.email,
      name: values.name,
      occupation: values.occupation,
    };
    const { status, error } = await updateAccount(accountId, payload);

    if (status === STATUS_CODE.SUCCESSFUL || status === STATUS_CODE.CREATED) {
      openNotificationApprove(MESSAGE_ALERT.EDIT_SUCCESS);

      // emit event to parent component re-render listing
      onEmitEventReRenderData();
      refEditModal.current.close();
    } else if (status === STATUS_CODE.INVALID) {
      const errors = error.data.errors;
      if (errors && Object.keys(errors).length) {
        Object.keys(errors).forEach((key) => {
          const message = errors[key][0];
          openNotificationFail(message);
        });
      }
    }
  };

  const handleOnSubmitEdit = () => {};

  return (
    <div>
      <BasicButton
        type="default"
        className="flex items-center justify-center border-main-color w-[120px] space-x-[4px]"
        onClick={openModalEdit}
      >
        編集
      </BasicButton>

      <BasicFormModal
        ref={refEditModal}
        content={
          <AccountManagementForm form={form} onSubmit={handleOnSubmitEdit} accountId={accountId} />
        }
        title="アカウント編集"
        buttonCloseTitle="閉じる"
        buttonSubmitTitle="保存"
        buttonSubmitStyle={btnSubmitModalStyle}
        onSubmit={onClickEditBtn}
        isValidate={true}
      />
    </div>
  );
};

export default AccountManagementEdit;
