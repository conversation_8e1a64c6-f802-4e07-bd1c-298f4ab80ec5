import BasicButton from '@/components/Commons/BasicButton';
import BasicInput from '@/components/Commons/BasicInput';
import BasicTable from '@/components/Commons/BasicTable';
import PageContainer from '@/components/Commons/Page/Container';
import SearchSVGWhite from '@/components/SVG/SearchSVGWhite';
import { Form } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import { useEffect, useState } from 'react';
import styles from './index.less';
import type { AccountManagementType, ParamsListAccount } from '@/apis/account';
import { getList } from '@/apis/account';
import { openNotificationFail } from '@/components/Notification';
import AccountManagementResetPass from './components/AccountManagementResetPass';
import AccountManagementEdit from './components/AccountManagementEdit';
import AccountManagementDisable from './components/AccountManagementDisable';
import BasicTag from '@/components/Commons/BasicTag';
import { occupationAccount } from '@/utils/constants';
import AccountManagementAddNew from './components/AccountManagementAddNew';

const ITEM_PER_PAGE: number = 10;

const AccountManagement = () => {
  const [form] = Form.useForm();
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [pageSize, setPageSize] = useState(ITEM_PER_PAGE);
  const [isLoading, setIsLoading] = useState(false);
  const [listAccount, setListAccount] = useState<any[]>([]);
  const [searchValues, setSearchValues] = useState<ParamsListAccount>({});

  const handleGetList = async (params: ParamsListAccount) => {
    setIsLoading(true);
    try {
      const { data, status } = await getList(params);
      if (status === 200) {
        setTotal(data?.total);
        setListAccount(data?.data);
        setIsLoading(false);
      } else {
        setIsLoading(false);
        openNotificationFail('サーバーエラー');
      }
    } catch (error) {
      setIsLoading(false);
      openNotificationFail('サーバーエラー');
    }
  };

  const handleOnEmitEventReRenderData = () => {
    const paramsSearch = {
      limit: pageSize,
      page: page,
      ...searchValues,
    };

    handleGetList(paramsSearch);
  };

  // const columns: ColumnsType<AccountManagementType> = accountManagementColumn({ page });
  const getRoleLabel = (roleId: number) => {
    if (roleId == occupationAccount.adminOwner) {
      return <BasicTag className="text-[10px]" color="#3997C8" title="管理者" />;
    } else if (roleId == occupationAccount.internalAdmin) {
      return <BasicTag className="text-[10px]" color="#0095FF" title="内部スタッフ" />;
    }
    return <BasicTag className="text-[10px]" color="#FF7648" title="外部スタッフ" />;
  };

  const handleOnEmitEventLoading = (loading: boolean) => {
    setIsLoading(loading);
  };

  const columns: ColumnsType<AccountManagementType> = [
    {
      title: <div className="font-[400] text-[13px] leading-4 text-center">ID</div>,
      dataIndex: 'id',
      key: 'id',
      width: '80px',
      render: (_value, _data, index) => (
        <div className="text-[15px] leading-5 font-sm text-center">{page * index + 1}</div>
      ),
    },
    {
      title: <div className="font-[400] text-[13px] leading-4">名前</div>,
      dataIndex: 'name',
      key: 'name',
      render: (_, { name }) => (
        <div className="text-[15px] leading-5 font-sm text-[#383B46]">{name}</div>
      ),
    },
    {
      title: <div className="font-[400] text-[13px] leading-4">メールアドレス</div>,
      dataIndex: 'email',
      key: 'email',
      render: (_, { email }) => (
        <div className="text-[15px] leading-5 font-sm text-[#383B46]">{email}</div>
      ),
    },
    {
      title: <div className="font-[400] text-[13px] leading-4">権限</div>,
      dataIndex: 'occupation',
      key: 'occupation',
      render: (_, { occupation }) => (
        <div className="text-[10px] leading-5 font-sm text-[#383B46]">
          {getRoleLabel(occupation)}
        </div>
      ),
    },
    {
      title: <div className="font-[400] text-[13px] leading-4">最終ログイン</div>,
      dataIndex: 'last_login_at',
      key: 'last_login_at',
      render: (_, { last_login_at }) => (
        <div className="text-[15px] leading-5 font-sm text-[#383B46]">{last_login_at}</div>
      ),
    },
    {
      title: <div className="font-[400] text-[13px] leading-4">有効</div>,
      dataIndex: 'is_active',
      key: 'is_active',
      render: (_value, _data) => (
        <AccountManagementDisable
          isActive={_data.is_active}
          accountId={_data.id}
          occupation={_data.occupation}
          onEmitEventLoading={handleOnEmitEventLoading}
        />
      ),
    },
    {
      title: null,
      dataIndex: 'id',
      key: 'id',
      render: (_, { id }) => (
        <div className="text-[15px] flex space-x-4 font-medium text-[#383B46]">
          <AccountManagementEdit
            accountId={id}
            onEmitEventLoading={handleOnEmitEventLoading}
            onEmitEventReRenderData={handleOnEmitEventReRenderData}
          />
          <AccountManagementResetPass
            accountId={id}
            onEmitEventLoading={handleOnEmitEventLoading}
          />
        </div>
      ),
    },
  ];

  useEffect(() => {
    const paramsSearch = {
      limit: pageSize,
      page: page,
      ...searchValues,
    };

    handleGetList(paramsSearch);
  }, [page, searchValues, pageSize]);

  const handleSearch = () => {
    form.submit();
  };

  const handleFinishSearch = (values: any) => {
    const params = {
      ...values,
    };

    setPage(1);
    setSearchValues(params);
  };

  return (
    <PageContainer>
      <div className="mt-[32px] flex items-center space-x-4 bg-[#fff] p-5 rounded-xl">
        <div className="keyword-title font-medium">キーワード</div>
        <Form form={form} onFinish={handleFinishSearch}>
          <div className="flex">
            <div className="flex space-x-4 items-end flex-1">
              <Form.Item name={'keyword'} noStyle>
                <BasicInput
                  placeholder={'検索キーワード'}
                  className="!w-[300px] !rounded-[0.5rem]"
                  allowClear
                />
              </Form.Item>
              <BasicButton
                styleType="accept"
                className="flex items-center justify-center border-main-color w-[120px] space-x-[8px]"
                onClick={handleSearch}
              >
                <SearchSVGWhite />
                検索
              </BasicButton>
            </div>
          </div>
        </Form>
      </div>

      <div className="mt-[32px] flex items-center justify-start space-x-4 bg-[#fff] p-5 rounded-t-xl">
        <AccountManagementAddNew
          onEmitEventLoading={handleOnEmitEventLoading}
          onEmitEventReRenderData={handleOnEmitEventReRenderData}
        />
      </div>

      <BasicTable
        className={`${styles.table} !mt-0`}
        hasPagination={true}
        tableProps={{
          columns,
          loading: isLoading,
          dataSource: listAccount,
          bordered: false,
          pagination: false,
          rowKey: 'id',
        }}
        page={page}
        pageSize={pageSize}
        onChangePage={(p: number) => setPage(p)}
        total={total}
        onSelectPageSize={(v) => setPageSize(v)}
      />
    </PageContainer>
  );
};

export default AccountManagement;
