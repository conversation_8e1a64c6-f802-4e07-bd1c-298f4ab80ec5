import type { UserItem } from '@/apis/users';
import { getDetailUser } from '@/apis/users';
import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import STATUS_CODE from '@/constants/statusCode';
import { Form, type FormInstance } from 'antd';
import { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { UserItemFields } from './UserItemFields';

interface Props {
  onSubmit: (values) => void;
  id?: number;
  currentUser?: any;
}

export type UserFormRef = {
  form: FormInstance<any>;
};

const FormUpdateOrCreateUserItem = forwardRef<UserFormRef, Props>(
  ({ onSubmit, id, currentUser }, ref) => {
    const [form] = Form.useForm();
    const [userSelect, setUserSelect] = useState<UserItem>();
    useImperativeHandle(ref, () => ({
      form,
    }));

    const onFetchDataUser = async (idUser: number) => {
      const response = await getDetailUser(idUser);
      if (response?.status === STATUS_CODE.SUCCESSFUL) {
        const data = response?.data?.data;
        setUserSelect(data);
        form.setFieldsValue({
          email: data?.email,
          name: data?.name,
          role: data?.roles?.[0]?.id,
        });
      }
    };

    useEffect(() => {
      if (id) {
        onFetchDataUser(id);
      } else {
        form.resetFields();
        setUserSelect(undefined);
      }
    }, [id]);

    const UserItemFieldForCurrentUser = useMemo(() => {
      return UserItemFields?.map((item) => {
        if (item?.name === 'role') {
          const newOptions = item?.options?.map((option) => {
            return {
              ...option,
              disabled:
                option?.value === 1 ||
                (!currentUser?.permissions?.includes('create_account_admin') &&
                  option?.value === 2),
            };
          });
          return {
            ...item,
            disabled: userSelect?.roles?.[0]?.name === 'admin_owner',
            options: newOptions,
          };
        }
        if (item?.name === 'email' && id) {
          return { ...item, disabled: true };
        }
        return { ...item };
      });
    }, [currentUser, id, userSelect?.roles?.[0]?.name]);

    const onFinish = (values) => {
      const valueSubmit = { ...values, id };
      if (userSelect?.roles?.[0]?.name === 'admin_owner') {
        delete valueSubmit.role;
      }
      onSubmit({ ...valueSubmit });
    };

    return (
      <Form form={form} onFinish={onFinish}>
        {UserItemFieldForCurrentUser?.map((item) => (
          <CommonRenderFormField key={item?.name} field={item} />
        ))}
      </Form>
    );
  },
);

export default FormUpdateOrCreateUserItem;
