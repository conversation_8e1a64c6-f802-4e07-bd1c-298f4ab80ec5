import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { optionRole } from '@/constants/data';
import { rules } from '@/constants/rules';

export const UserItemFields: FormItemDetail[] = [
  {
    type: 'input',
    name: 'name',
    title: '名前',
    isRequired: true,
    placeholder: '名前',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'email',
    title: 'メールアドレス',
    isRequired: true,
    placeholder: 'メールアドレス',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'select',
    name: 'role',
    title: '権限',
    isRequired: true,
    placeholder: '権限',
    rules: rules.requiredSelect,
    options: optionRole,
    colSpan: 24,
  },
];
