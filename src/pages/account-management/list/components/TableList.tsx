import type { BaseParams } from '@/@types/request';
import type { BodyUpdateUserType, DataGetMe } from '@/apis/users';
import { updateUser, type UserItem } from '@/apis/users';
import IconEdit from '@/assets/imgs/common-icons/edit-blue.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicTable from '@/components/Commons/BasicTable';
import EditableCell from '@/components/Commons/EditableCell';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { optionRole } from '@/constants/data';
import STATUS_CODE from '@/constants/statusCode';
import type { FormInstance } from 'antd';
import { Form, Image, Modal } from 'antd';
import moment from 'moment';
import type { FieldData } from 'rc-field-form/lib/interface';
import type { AccountManagementListProps } from '..';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  isLoading,
  dataSource,
  paramSearch,
  setParamSearch,
  total,
  form,
  setIdUserSelected,
  currentUser,
  onFetchData,
}: {
  isLoading: boolean;
  dataSource: AccountManagementListProps[];
  paramSearch?: BaseParams;
  setParamSearch?: (val: BaseParams) => void;
  total: number;
  form: FormInstance<any>;
  setIdUserSelected: (val: number) => void;
  currentUser: DataGetMe;
  onFetchData: () => void;
}) => {
  const { confirm } = Modal;
  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'key',
      key: 'key',
      width: 60,
    },
    {
      title: '名前',
      dataIndex: 'name',
      key: 'name',
      width: 160,
    },
    {
      title: 'メールアドレス',
      dataIndex: 'email',
      key: 'email',
      width: 260,
    },
    {
      title: '権限',
      dataIndex: 'role_id',
      key: 'role_id',
      width: 120,
      render: (_, record) => (
        <span>
          {record?.role_id ? optionRole.find((role) => role.value === record.role_id)?.label : ''}
        </span>
      ),
    },
    {
      title: '最終ログイン',
      dataIndex: 'last_login_at',
      key: 'last_login_at',
      width: 180,
      render: (_, record) => (
        <span>
          {record?.last_login_at ? moment(record?.last_login_at).format('YYYY/MM/DD HH:mm:ss') : ''}
        </span>
      ),
    },
    {
      title: '',
      dataIndex: 'is_active',
      editable: true,
      key: 'is_active',
      formType: 'switch',
      width: 60,
    },

    {
      title: '',
      dataIndex: 'editItem',
      key: 'editItem',
      width: 80,
      render: (_, record) => {
        let isDisable = false;
        if (currentUser.role.name !== 'admin_owner' && currentUser.role.name !== 'admin') {
          isDisable = true;
        } else if (
          currentUser.role.name === 'admin' &&
          (record?.roles?.[0]?.name === 'admin' || record?.roles?.[0]?.name === 'admin_owner')
        ) {
          isDisable = true;
        }
        return (
          <BasicButton
            disabled={isDisable}
            onClick={() => setIdUserSelected(record.id)}
            styleType="danger"
            className="!h-[24px] w-[76px] !border-[transparent] hover:!border-[#225DE0] !text-[#225DE0]"
          >
            <Image preview={false} src={IconEdit} width={12} height={13} />
            <p className="text-xs !ml-1">編集</p>
          </BasicButton>
        );
      },
    },
  ];

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return { ...col, width: col.width || 250 };
    }
    const checkEditTable = (record: UserItem) => {
      if (col.key === 'is_active') {
        if (record?.roles?.[0]?.name === 'admin_owner') {
          return false;
        } else if (currentUser.role.name !== 'admin_owner' && currentUser.role.name !== 'admin') {
          return false;
        } else if (currentUser.role.name === 'admin' && record?.roles?.[0]?.name === 'admin') {
          return false;
        }
      }
      return true;
    };

    return {
      ...col,
      width: col.width || 250,
      onCell: (record: UserItem) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        isEditPage: checkEditTable(record),
        title: col.title,
        formType: col.formType,
        form,
      }),
    };
  });
  const updateStatusUser = async (data: BodyUpdateUserType) => {
    const resUpdateUser = await updateUser(data);
    if (resUpdateUser.status === STATUS_CODE.SUCCESSFUL) {
      openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
    } else if (resUpdateUser.status === STATUS_CODE.FORBIDDEN) {
      openNotificationFail(MESSAGE_ALERT.Permission_Denied);
    } else {
      onFetchData?.();
      openNotificationFail(
        resUpdateUser?.error?.data?.errors?.message ?? MESSAGE_ALERT.EDIT_FAILED,
      );
    }
  };

  const showConfirm = (user, valueChange) => {
    confirm({
      title: 'このアカウントを無効しますか ?',
      onOk() {
        updateStatusUser({ id: user.id, is_active: valueChange ? 1 : 0 });
      },
      onCancel() {
        onFetchData?.();
      },
      okText: '保存',
      cancelText: 'キャンセル',
    });
  };

  // Usage example
  const handleChangeStatus = (user, valueChange) => {
    showConfirm(user, valueChange);
  };

  const handleListItemChange = async (changeField: FieldData[]) => {
    const nameFieldChange = changeField?.[0]?.name?.[0];
    const keyChange = nameFieldChange.split('.')[0];
    const valueChange = changeField?.[0]?.value;
    if (nameFieldChange.includes('is_active')) {
      const user = dataSource.find((item) => item.key === Number(keyChange));
      if (!valueChange) {
        handleChangeStatus(user, valueChange);
        return;
      }
      updateStatusUser({ id: user.id, is_active: valueChange ? 1 : 0 });
    }
  };

  return (
    <>
      <Form
        form={form}
        component={false}
        onFieldsChange={(changeField: FieldData[]) => handleListItemChange(changeField)}
      >
        <BasicTable
          className="!mt-0"
          tableProps={{
            scroll: { x: 1100 },
            loading: isLoading,
            components: {
              body: {
                cell: EditableCell,
              },
            },
            columns,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            rowKey: 'id',
          }}
          page={Number(paramSearch?.page) ?? 1}
          pageSize={Number(paramSearch?.limit) ?? 10}
          onChangePage={(p: number) => {
            setParamSearch({
              ...paramSearch,
              page: Number(p),
            });
          }}
          total={total}
          onSelectPageSize={(v) => setParamSearch({ ...paramSearch, limit: v, page: 1 })}
        />
      </Form>
    </>
  );
};

export default TableList;
