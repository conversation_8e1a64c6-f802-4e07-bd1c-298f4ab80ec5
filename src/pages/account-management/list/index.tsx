import type { BaseParams } from '@/@types/request';
import type { UserItem } from '@/apis/users';
import { createUser, getListUsers, updateUser } from '@/apis/users';
import PageContainer from '@/components/Commons/Page/Container';
import STATUS_CODE from '@/constants/statusCode';
import { connect } from 'umi';
import { Form } from 'antd';
import { useEffect, useRef, useState } from 'react';
import HeaderAction from './components/HeaderAction';
import TableListDeposit from './components/TableList';
import type { BasicFormModalRef } from '@/components/Commons/BasicFormModal';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import type { UserFormRef } from './components/FormUpdateOrCreateUserItem';
import FormUpdateOrCreateUserItem from './components/FormUpdateOrCreateUserItem';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_TITLE } from '@/constants/commonText';
import { ITEM_PER_PAGE } from '@/utils/constants';

//# 旅行ID 売上伝票ID ツアー名 得意先 売上金額 請求書発行

export interface AccountManagementListProps extends UserItem {
  role_id: number;
  key: number;
}

const AccountManagementList = ({ currentUser }) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<AccountManagementListProps[]>([]);
  const [total, setTotal] = useState(0);
  const [paramSearch, setParamSearch] = useState<BaseParams>({
    limit: ITEM_PER_PAGE,
    page: 1,
  });
  const [form] = Form.useForm();
  const refModalCreateUser = useRef<BasicFormModalRef>();
  const refFormAddNewItem = useRef<UserFormRef>(null);
  const [idUserSelected, setIdUserSelected] = useState<number | null>(null);

  const onFetchData = async () => {
    setIsLoading(true);
    try {
      const resGetListUsers = await getListUsers(paramSearch);
      if (resGetListUsers.status === STATUS_CODE.SUCCESSFUL) {
        const resData = resGetListUsers.data;
        const dataRender = resData.data.map((item, index) => {
          return {
            ...item,
            role_id: item.roles?.[0]?.id,
            key: (Number(paramSearch.page) - 1) * Number(paramSearch.limit) + index + 1,
          };
        });
        for (let i = 0; i < dataRender.length; i++) {
          form.setFieldValue(`${dataRender[i].key}.is_active`, dataRender[i].is_active);
        }

        setDataSource(dataRender);
        setTotal(resData.total);
      }
    } catch (error) {
      console.log('error', error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    onFetchData();
  }, [paramSearch]);

  useEffect(() => {
    if (idUserSelected) {
      refModalCreateUser?.current?.open();
    }
  }, [idUserSelected]);

  const handleCreateOrUpdateUser = async (values) => {
    try {
      const handleActionApi = values?.id ? updateUser : createUser;
      const responseCreate = await handleActionApi({
        ...values,
      });
      if (
        responseCreate.status === STATUS_CODE.SUCCESSFUL ||
        responseCreate.status === STATUS_CODE.CREATED
      ) {
        if (values?.id) {
          openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
          if (values?.id === currentUser?.user?.id) {
            window.location.reload();
          }
        } else {
          openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
        }
        refModalCreateUser?.current?.close();
        onFetchData();
      } else {
        if (values?.id) {
          openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
        } else {
          openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
        }
      }
    } catch (error) {
      console.log('error', error);
      if (values?.id) {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      } else {
        openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
      }
    }
  };

  return (
    <PageContainer>
      <HeaderAction
        refModalCreateUser={refModalCreateUser}
        paramSearch={paramSearch}
        setParamSearch={setParamSearch}
      />
      <div className="p-2 rounded-xl bg-white mt-6">
        <TableListDeposit
          form={form}
          total={total}
          isLoading={isLoading}
          dataSource={dataSource}
          paramSearch={paramSearch}
          setParamSearch={setParamSearch}
          setIdUserSelected={setIdUserSelected}
          currentUser={currentUser}
          onFetchData={onFetchData}
        />
      </div>
      <BasicFormModal
        ref={refModalCreateUser}
        content={
          <FormUpdateOrCreateUserItem
            id={idUserSelected}
            ref={refFormAddNewItem}
            onSubmit={handleCreateOrUpdateUser}
            currentUser={currentUser}
          />
        }
        title={TEXT_TITLE.Add_Account}
        buttonCloseTitle={TEXT_ACTION.CANCEL}
        buttonSubmitTitle={TEXT_ACTION.SAVE}
        buttonSubmitStyle={{ backgroundColor: '#3997C8' }}
        onSubmit={() => refFormAddNewItem?.current?.form?.submit()}
        onCancel={() => {
          setIdUserSelected(null);
        }}
        isValidate={true}
      />
    </PageContainer>
  );
};

export default connect(({ user }) => ({
  currentUser: user.currentUser,
}))(AccountManagementList);
