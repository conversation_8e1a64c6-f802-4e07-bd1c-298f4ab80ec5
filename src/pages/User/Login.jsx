import { login } from '@/apis/login';
import { checkEmailExist } from '@/apis/user';
import FormLoginInPutEmail from '@/components/Form/FormLoginInPutEmail';
import {
  openNotificationApprove,
  openNotificationBlock,
  openNotificationFail,
} from '@/components/Notification';
import { Button, Checkbox, Form, Image, Input } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { connect, formatMessage, history } from 'umi';
import { instance } from '@/utils/request';
import { reloadAuthorized } from '@/utils/Authorized';
import { setToken } from '@/utils/authority';
import styles from './Login.less';
import STATUS_CODE from '@/constants/statusCode';
import { TEXT_ACTION, TEXT_TITLE } from '@/constants/commonText';
import IconPassword from '@/assets/ic_square-lock.png';

const LoginPage = (props) => {
  const ref = useRef();
  const [submitting, setSubmitting] = useState(false);
  const { dispatch } = props;

  const [form] = Form.useForm();
  const [isCorrectEmail, setIsCorrectEmail] = useState(false);

  useEffect(() => {
    const token = localStorage.getItem('authority');
    if (token) {
      history.replace('/');
    }
  }, []);

  const initData = async () => {
    try {
      dispatch({
        type: 'user/fetchCurrent',
      });
    } catch (error) {
      console.error('ERROR GET CITY AND COUNTRY IN LOGIN SUCCESS: ' + error);
    }
  };

  const handleSubmitCheckMail = async (v) => {
    setSubmitting(true);
    try {
      if (v.email) {
        let formData = new FormData();
        formData.append('email', v.email);
        const { status } = await checkEmailExist(formData);
        if (status === STATUS_CODE.SUCCESSFUL) {
          setIsCorrectEmail(true);
          form.setFieldValue('email', v.email);
        } else if (status === STATUS_CODE.NOT_FOUND) {
          openNotificationBlock(
            '入力したメールアドレスはシステムに登録されていません。',
            '',
            '#ff4d4f',
          );
        } else if (status === STATUS_CODE.FORBIDDEN) {
          openNotificationBlock(
            'アカウントはもうアクティブではありません。管理者にお問い合わせください。',
            '',
            '#ff4d4f',
          );
        } else {
          openNotificationBlock(formatMessage({ id: 'FailedConnectServer' }), '', '#ff4d4f');
        }

        setSubmitting(false);
      }
    } catch (error) {
      console.log('error');
      setSubmitting(false);
    }
  };

  const handleLogin = async (v) => {
    setSubmitting(true);
    try {
      if (v.email && v.password) {
        let formData = new FormData();
        formData.append('email', v.email);
        formData.append('password', v.password);
        const { data, status } = await login(formData);

        if (status === STATUS_CODE.SUCCESSFUL) {
          openNotificationApprove('ログインしました！', '', '#3997C8');
          reloadAuthorized(data);
          setToken(data.data.access_token);
          instance.defaults.headers.common['Authorization'] = `Bearer ${data.data.access_token}`;
          dispatch({
            type: 'user/saveAccessToken',
            payload: data.data.access_token,
          });
          await initData();
          setTimeout(() => {
            history.replace('/');
          }, 500);
        } else if (status === STATUS_CODE.NOT_FOUND) {
          openNotificationFail(
            'メールアドレス又は、パスワードが正しくありません。入力情報をご確認のうえ、もう一度お試しください。',
            '',
            '#ff4d4f',
          );
        } else {
          openNotificationFail('パスワードが正しくありません', '', '#ff4d4f');
        }

        setSubmitting(false);
      }
    } catch (error) {
      console.log('error', error);
      setSubmitting(false);
      openNotificationBlock(formatMessage({ id: 'FailedConnectServer' }), '', '#ff4d4f');
    }
  };

  return (
    <div className={styles.main}>
      <div className={styles.container}>
        <div className={styles.logoWidget}>
          <Image
            preview={false}
            alt="logo"
            src="/imgs/showa-logo.jpg"
            style={{
              maxHeight: '220px',
              minHeight: '100px',
            }}
          />
        </div>
        <div
          style={{
            minWidth: '400px',
          }}
        >
          <Form
            name="login"
            layout="vertical"
            onFinish={isCorrectEmail ? handleLogin : handleSubmitCheckMail}
            form={form}
            ref={ref}
            validateTrigger={['onSubmit', 'onChange']}
          >
            <div
              style={{
                fontSize: '32px',
                lineHeight: '52px',
                fontWeight: '700',
                textAlign: 'center',
              }}
            >
              生産性向上システム
            </div>
            <div
              style={{
                marginTop: '42px',
              }}
            >
              <FormLoginInPutEmail disabled={isCorrectEmail} />
              {isCorrectEmail ? (
                <div>
                  <Form.Item
                    className="!mb-1"
                    label={<span>{TEXT_TITLE.Password}</span>}
                    name="password"
                    rules={[{ required: true, message: 'パスワードを入力してください。' }]}
                  >
                    <Input.Password
                      prefix={
                        <div className="mr-[6px] flex justify-center items-center">
                          <Image
                            preview={false}
                            alt="password"
                            src={IconPassword}
                            style={{ height: 20 }}
                          />
                        </div>
                      }
                      placeholder="パスワードを入力してください"
                      className={styles.passwordInput}
                    />
                  </Form.Item>
                  <div style={{ display: 'flex', justifyContent: 'right' }}>
                    <Link className={styles.forgotPassword} to={'/user/forgot'}>
                      {TEXT_TITLE.Forgot_password}
                    </Link>
                  </div>
                  <Form.Item name="remember" valuePropName="checked">
                    <Checkbox>{TEXT_TITLE.Remember_login_info}</Checkbox>
                  </Form.Item>
                </div>
              ) : null}
            </div>
          </Form>
          <div style={{ paddingTop: '12px' }}>
            <Button
              block
              loading={submitting}
              type="primary"
              className={styles.submitButton}
              onClick={() => {
                form.submit();
              }}
            >
              {isCorrectEmail ? TEXT_ACTION.LOGIN : TEXT_ACTION.NEXT}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default connect(({ login }) => ({
  login,
}))(LoginPage);
