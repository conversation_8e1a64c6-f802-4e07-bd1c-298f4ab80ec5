import FormLoginInPutEmail from '@/components/Form/FormLoginInPutEmail';
import { openNotificationBlock, openNotificationFail } from '@/components/Notification';
import { Button, Form, Image } from 'antd';
import { connect } from 'dva';
import { useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { formatMessage, history } from 'umi';
import styles from './FotgotPassword.less';
import { sendEmailForgotPassword } from '@/apis/login';
import STATUS_CODE from '@/constants/statusCode';
import IconMainSend from '@/assets/ic_mail-send.png';

function ForgotPassword(props) {
  const ref = useRef();
  const [submitted, setSubmitted] = useState(false);

  const [form] = Form.useForm();
  const [submitting, setSubmitting] = useState(false);

  const handleSubmit = async (values) => {
    setSubmitting(true);
    try {
      if (values.email) {
        let formData = new FormData();
        formData.append('email', values.email);
        const { data, status } = await sendEmailForgotPassword(formData);
        if (status === STATUS_CODE.SUCCESSFUL) {
          history.replace('/user/forgot/pass');
        } else if (status === STATUS_CODE.NOT_FOUND) {
          openNotificationFail(
            '入力したメールアドレスはシステムに登録されていません。',
            '',
            '#ff4d4f',
          );
        } else {
          openNotificationFail(formatMessage({ id: 'FailedConnectServer' }), '', '#ff4d4f');
        }

        setSubmitting(false);
      }
    } catch (error) {
      setSubmitting(false);
      openNotificationBlock(formatMessage({ id: 'FailedConnectServer' }), '', '#ff4d4f');
    }
  };

  return (
    <>
      <div className={styles.main}>
        <div className={styles.container}>
          <div className={styles.logoWidget}>
            <Image
              preview={false}
              alt="logo"
              src="/imgs/showa-travel-logo.png"
              style={{ height: 100 }}
            />
          </div>
          <div
            style={{
              minWidth: '400px',
            }}
          >
            <Form
              name="basic"
              layout="vertical"
              initialValues={{}}
              onFinish={handleSubmit}
              onChange={() => {}}
              form={form}
              ref={ref}
              validateTrigger={submitted ? ['onSubmit', 'onChange'] : ['onSubmit']}
            >
              <div
                style={{
                  fontSize: '20px',
                  lineHeight: '32px',
                  fontWeight: '500',
                  textAlign: 'center',
                }}
              >
                アカウントを探す
              </div>
              <div
                style={{
                  marginTop: '4px',
                  fontSize: '14px',
                  lineHeight: '20px',
                  color: '#7F8493',
                  fontWeight: '400',
                  textAlign: 'center',
                }}
              >
                登録したメールアドレスを入力してください。
              </div>

              <div
                style={{
                  marginTop: '40px',
                }}
              >
                <FormLoginInPutEmail />
              </div>
            </Form>
            <div style={{ paddingTop: '12px' }}>
              <Button
                block
                onClick={() => {
                  setSubmitted(true);
                  form.submit();
                }}
                type="primary"
                disabled={submitting}
                loading={submitting}
                className={styles.submitButton}
              >
                <div className="flex items-center justify-center">
                  <Image
                    preview={false}
                    alt="mail-send"
                    src={IconMainSend}
                    style={{ height: 16 }}
                  />
                  <div className="ml-2">メール送信</div>
                </div>
              </Button>
            </div>
            <div style={{ textAlign: 'center' }}>
              <Link className={styles.forgotPassword} to={'/user/login'}>
                ログインに戻る
              </Link>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default connect(({ login, loading }) => ({
  userLogin: login,
  ...login,
  submitting: loading.effects['login/login'],
}))(ForgotPassword);
