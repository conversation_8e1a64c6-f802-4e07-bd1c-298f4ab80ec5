import { resetPassword } from '@/apis/login';
import { openNotificationBlock } from '@/components/Notification';
import { rules } from '@/constants/rules';
import { Button, Form, Image, Input } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { formatMessage, history } from 'umi';
import { useUrlSearchParams } from 'use-url-search-params';
import styles from './ClickLinkResetPassword.less';
import STATUS_CODE from '@/constants/statusCode';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';

const style = {
  styleCenter: {
    display: 'flex',
    textAlign: 'center',
    justifyContent: 'center',
  },
  styleLabelInput: {
    fontWeight: 500,
    fontSize: '14px',
    lineHeight: '21px',
    color: '#181818',
    display: 'block',
  },
};

const passwordStatus = {
  incorrectFormatPassword: 'correct',
  weakPassword: 'Weak password',
  averagePassword: 'Average password',
  goodPassword: (
    <div
      style={{
        color: '#3997C8',
      }}
    >
      良いパスワード
    </div>
  ),
  shortPassword: 'は8文字以上である必要があります',
  emptyPassword: 'パスワード を入力してください',
};

const ClickLinkResetPassword = () => {
  const [form] = Form.useForm();
  const [parameter] = useUrlSearchParams({});
  const ref = useRef();

  const [completed, setCompleted] = useState(false);
  const [formSubmited, setFormSubmited] = useState(false);
  const [passwordStrength, setPasswordStrength] = useState('');
  const [showPasswordAlert, setShowPasswordAlert] = useState(false);

  const textPassword = Form.useWatch('password', form);

  useEffect(() => {
    if (formSubmited && !textPassword) {
      setPasswordStrength(passwordStatus.emptyPassword);
      setShowPasswordAlert(true);
    }
  }, [formSubmited, textPassword]);

  const handleSubmit = async (values) => {
    setFormSubmited(true);
    try {
      const formData = new FormData();
      formData.append('token', parameter.access_token);
      formData.append('password', values.password);
      const { data, status, error } = await resetPassword(formData);
      if (status === STATUS_CODE.SUCCESSFUL) {
        setCompleted(true);
      } else if (error.data?.errors?.message === 'ERROR_TOKEN_IS_INVALID') {
        openNotificationBlock(
          'パスワード再設定は24時間以内に有効です。\n有効期限を過ぎた場合は、再度リクエストを行ってください。',
          '',
          '#ff4d4f',
        );
      } else {
        openNotificationBlock(formatMessage({ id: 'FailedConnectServer' }), '', '#ff4d4f');
      }
    } catch (error) {
      console.log('error: ', error);
    } finally {
      setFormSubmited(false);
    }
  };

  const submitForm = () => {
    form.submit();
  };

  const validateConfirmPassword = (_, value) => {
    const password = form.getFieldValue('password');
    if (value && value !== password) {
      return Promise.reject('パスワードが一致していません');
    }
    return Promise.resolve();
  };

  return (
    <div className={styles.main}>
      <div className={styles.container}>
        <div className={styles.logoWidget}>
          <Image
            preview={false}
            alt="logo"
            src="/imgs/showa-travel-logo.png"
            style={{ height: 200 }}
          />
        </div>
        <div
          style={{
            minWidth: '384px',
            marginLeft: '116px',
          }}
        >
          {!completed ? (
            <>
              <div>
                <h4
                  style={{
                    textAlign: 'center',
                    fontSize: '24px',
                    lineHeight: '24px',
                    color: '#181818',
                    fontWeight: '500',
                    marginBottom: '32px',
                  }}
                >
                  {'パスワード再設定'}
                </h4>
                <div style={{ fontSize: '13px', lineHeight: '22px', color: '#676767' }}>
                  <div>{TEXT_PLACEHOLDER.Please_enter_a_new_password}</div>
                </div>
              </div>
              <div className="[&_.ant-form-item-explain-error]:!max-w-[384px] [&_.ant-form-item-explain-error]:break-words">
                <Form
                  name="basic"
                  layout="vertical"
                  initialValues={{}}
                  onFinish={handleSubmit}
                  onChange={() => {
                    setFormSubmited(false);
                  }}
                  form={form}
                  ref={ref}
                >
                  <Form.Item
                    name={'password'}
                    label={
                      <span
                        className={{
                          fontWeight: 500,
                          fontSize: '14px',
                          lineHeight: '21px',
                          color: '#181818',
                          display: 'block',
                        }}
                      >
                        新しいパスワード
                      </span>
                    }
                    rules={[
                      {
                        required: true,
                        message:
                          '半角英小文字大文字数字をそれぞれ1種類以上含む8文字以上でパスワードを設定ください。',
                        validateTrigger: ['onSubmit', 'onChange'],
                      },
                      ...rules.correctPasswordFormat,
                    ]}
                    validateTrigger={['onSubmit', 'onChange']}
                  >
                    <Input.Password
                      placeholder="新しいパスワードを入力"
                      // onChange={handlePasswordChange}
                      className={styles.passwordInput}
                    />
                  </Form.Item>
                  <Form.Item
                    name={'confirmPassword'}
                    label={<span className={style.styleLabelInput}>新しいパスワード（確認）</span>}
                    dependencies={['password']}
                    rules={[
                      { required: true, message: 'パスワード（確認用）を入力してください' },
                      { validator: validateConfirmPassword },
                    ]}
                  >
                    <Input.Password
                      // placeholder={formatMessage({ id: name })}
                      placeholder="新しいパスワードを再入力"
                      className={styles.passwordInput}
                    />
                  </Form.Item>
                </Form>
              </div>
              <div>
                <Button
                  block
                  loading={formSubmited}
                  htmlType="submit"
                  onClick={submitForm}
                  type={'primary'}
                  className={styles.submitButton}
                >
                  {TEXT_ACTION.SETTING}
                </Button>
              </div>
            </>
          ) : (
            <div>
              <div
                style={{
                  textAlign: 'center',
                }}
              >
                <h1
                  style={{
                    fontSize: '24px',
                    lineHeight: '48px',
                    fontWeight: '500',
                  }}
                >
                  {TEXT_TITLE.Setting_complete}
                </h1>
                <h4
                  style={{
                    margin: '52px 0',
                    fontSize: '14px',
                    lineHeight: '22px',
                  }}
                >
                  新しいパスワードが設定できました。
                </h4>
              </div>

              <div style={{}}>
                <Button
                  block
                  type="primary"
                  className={styles.submitButton}
                  onClick={() => {
                    history.push('/user/login');
                  }}
                >
                  {TEXT_ACTION.LOGIN}
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ClickLinkResetPassword;
