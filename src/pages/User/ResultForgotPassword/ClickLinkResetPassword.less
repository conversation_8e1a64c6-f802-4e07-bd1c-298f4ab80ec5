@import '~antd/lib/style/themes/default.less';

.main {
  :global {
    .ant-input-affix-wrapper > input.ant-input {
      background-color: #fcfdfe;
      background-color: #fff !important;
    }
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      margin: 0;
      content: '';
    }
  }

  display: flex;
  align-items: center;
  justify-content: center;
  color: #181818;

  @media screen and (max-width: @screen-sm) {
    width: 95%;
  }
  .logoField {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 30px;
  }
  .emailInput,
  .passwordInput {
    height: 43px;
    border-radius: 8px;
  }
  .submitButton {
    height: 44px;
    margin-bottom: 24px;
    font-size: 15px;
    line-height: 23px;
    border-radius: 8px;
  }
  .alertMessage {
    padding: 8px;
    color: red;
    background-color: @alert-color;
    border-radius: 5px;
  }
  .container {
    display: flex;
    align-items: center;
    justify-content: end;
    width: 1000px;
    padding-top: 60px;
    padding-right: 64px;
    padding-bottom: 63px;
    background-color: #fff;
    border-radius: 24px;
  }
}
