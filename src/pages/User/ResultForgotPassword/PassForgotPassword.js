import React from 'react';

import styles from './PassForgotPassword.less';
import { Button, Image } from 'antd';
import { Link } from 'react-router-dom';
import { history } from 'umi';
import { TEXT_ACTION } from '@/constants/commonText';

function PassForgotPassword() {
  const handleRedirectLogin = () => {
    history.push('/user/login');
  };
  return (
    <>
      <div className={styles.main}>
        <div className={styles.container}>
          <div className={styles.logoWidget}>
            <Image
              preview={false}
              alt="logo"
              src="/imgs/showa-travel-logo.png"
              style={{ height: 200 }}
            />
          </div>

          <div
            style={{
              minWidth: '384px',
              marginLeft: '116px',
            }}
          >
            <div
              style={{
                fontSize: '24px',
                lineHeight: '48px',
                fontWeight: 500,
                textAlign: 'center',
              }}
            >
              送信完了
            </div>
            <div
              style={{
                fontSize: '14px',
                lineHeight: '22px',
                margin: '52px 0',
                textAlign: 'center',
              }}
            >
              パスワードを再設定するメールを送信しました。
            </div>
            <div style={{ paddingTop: '12px' }}>
              <Button
                block
                type="primary"
                className={styles.submitButton}
                onClick={handleRedirectLogin}
              >
                {TEXT_ACTION.LOGIN}
              </Button>
              <div style={{ display: 'flex', justifyContent: 'center' }}>
                <Link className={styles.forgotPassword} to={'/user/forgot'}>
                  {TEXT_ACTION.RESEND_EMAIL}
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default PassForgotPassword;
