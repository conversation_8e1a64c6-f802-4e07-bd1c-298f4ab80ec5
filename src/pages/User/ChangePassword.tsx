import { changePassword } from '@/apis/user';
import BasicButton from '@/components/Commons/BasicButton';
import { PageTitle } from '@/components/Commons/Page';
import PageContainer from '@/components/Commons/Page/Container';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { Form, Input } from 'antd';
import { useState } from 'react';

const ChangePassword = () => {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);

  const onSubmit = async (values) => {
    setIsLoading(true);
    try {
      const body = {
        ...values,
      };

      const { status, error } = await changePassword(body);
      setIsLoading(false);
      if (status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.CHANGE_STATUS_SUCCESS);
      } else if (status === STATUS_CODE.INVALID) {
        if (error?.data?.data?.errors?.newPassword) {
          openNotificationFail(
            error?.data?.data?.errors?.newPassword?.map((item) => item).join('\n'),
          );
        }
      } else {
        openNotificationFail(MESSAGE_ALERT.CHANGE_STATUS_FAIL);
      }
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT.CHANGE_STATUS_FAIL);
    }
  };

  return (
    <PageContainer
      useBackground
      style={{
        minHeight: 'calc(100vh - 130px)',
      }}
    >
      <PageTitle title={TEXT_TITLE.Reset_password} className="justify-center flex w-full" />
      <div className="flex flex-col">
        <div className="flex w-full justify-center my-4">
          {TEXT_PLACEHOLDER.Please_enter_a_new_password}
        </div>
        <Form form={form} onFinish={onSubmit} className="w-full flex flex-col items-center">
          <Form.Item
            name={'oldPassword'}
            className="w-[20%] [&_.ant-input]:!rounded-lg"
            rules={[{ required: true, message: '現在のパスワードを入力してください。' }]}
          >
            <Input.Password placeholder={TEXT_TITLE.Password_current} className="h-10" />
          </Form.Item>
          <Form.Item
            name={'confirmPassword'}
            className="w-[20%] [&_.ant-input]:!rounded-lg"
            rules={[{ required: true, message: 'パスワードを入力してください。' }]}
          >
            <Input.Password placeholder={TEXT_TITLE.Password} className="h-10" />
          </Form.Item>
          <Form.Item
            name={'newPassword'}
            className="w-[20%] [&_.ant-input]:!rounded-lg"
            rules={[{ required: true, message: 'パスワード（確認用）を入力してください。' }]}
          >
            <Input.Password placeholder={TEXT_TITLE.Password_confirm} className="h-10" />
          </Form.Item>
          <BasicButton
            loading={isLoading}
            onClick={() => form.submit()}
            styleType="accept"
            className="w-[20%]"
          >
            {TEXT_ACTION.SET_PASSWORD}
          </BasicButton>
        </Form>
      </div>
    </PageContainer>
  );
};

export default ChangePassword;
