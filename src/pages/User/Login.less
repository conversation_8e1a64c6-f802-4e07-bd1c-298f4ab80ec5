@import '~antd/lib/style/themes/default.less';

.main {
  :global {
    .ant-input-affix-wrapper > input.ant-input {
      background-color: #fcfdfe;
    }
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      margin: 0;
      content: '';
    }
  }

  display: flex;
  align-items: center;
  justify-content: center;
  color: #181818;
  @media screen and (max-width: @screen-sm) {
    width: 95%;
  }
  .title {
    display: flex;
    justify-content: center;
    color: #11c799;
    font-size: 32px;
    line-height: 30px;
    letter-spacing: 0.01em;
  }
  .logo {
    height: 20px;
    padding-right: 10px;
  }
  .logoField {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 30px;
  }
  .icon {
    margin-left: 16px;
    color: rgba(0, 0, 0, 0.2);
    font-size: 24px;
    vertical-align: middle;
    cursor: pointer;
    transition: color 0.3s;

    .inputFields {
      height: 30px;
      border-radius: 50vh;
    }

    &:hover {
      color: @primary-color;
    }
  }

  .other {
    margin-top: 24px;
    line-height: 22px;
    text-align: left;

    .register {
      float: right;
    }
  }
  .emailInput,
  .passwordInput {
    height: 43px;
    border-radius: 8px;
  }
  button {
    width: 100%;
    height: 48px;
    color: white;
    border: none;
  }
  .submitButton {
    height: 44px;
    margin-bottom: 24px;
    font-size: 15px;
    line-height: 23px;
    border-radius: 8px;
  }
  .alertMessage {
    padding: 8px;
    color: red;
    text-align: center;
    background-color: @alert-color;
    border-radius: 5px;
  }
  .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: end;
    width: 520px;
    padding-top: 60px;
    padding-bottom: 60px;
    background-color: #fff;
    border-radius: 12px;
  }
}

.forgotPassword {
  color: #363840;
  font-weight: 700;
  font-size: 12px;
  line-height: 16px;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
}
