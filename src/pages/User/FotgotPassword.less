@import '~antd/lib/style/themes/default.less';

.main {
  :global {
    .ant-input-affix-wrapper > input.ant-input {
      background-color: #fcfdfe;
    }
    .ant-form-item-label
      > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
      margin: 0;
      content: '';
    }
  }

  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  padding: 30px 50px;
  border-radius: 5px;
  @media screen and (max-width: @screen-sm) {
    width: 95%;
  }
  .title {
    display: flex;
    justify-content: center;
    color: #00b050;
    font-size: 32px;
    line-height: 30px;
    letter-spacing: 0.01em;
  }
  .logo {
    height: 20px;
    padding-right: 10px;
  }
  .logoField {
    display: flex;
    align-items: center;
    justify-content: center;
    padding-bottom: 30px;
  }
  .icon {
    margin-left: 16px;
    color: rgba(0, 0, 0, 0.2);
    font-size: 24px;
    vertical-align: middle;
    cursor: pointer;
    transition: color 0.3s;

    .inputFields {
      height: 30px;
      border-radius: 50vh;
    }

    &:hover {
      color: @primary-color;
    }
  }

  .other {
    margin-top: 24px;
    line-height: 22px;
    text-align: left;

    .register {
      float: right;
    }
  }
  .emailInput,
  .passwordInput {
    height: 40px;
    padding: 6px 12px;
    background-color: #fcfdfe;
    border-radius: 4px;
  }
  button {
    width: 100%;
    height: 48px;
    color: white;
    border: none;
  }
  .submitButton {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 44px;
    margin-bottom: 24px;
    color: white !important;
    font-weight: 700;
    font-size: 14px;
    line-height: 20px;
    background-color: #3997c8 !important;
    border-radius: 8px;
  }

  .forgotPassword {
    color: #3997c8;
    font-size: 14px;
    line-height: 22px;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
  }
  .alertMessage {
    padding: 8px;
    color: red;
    background-color: @alert-color;
    border-radius: 5px;
  }
  .container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: end;
    width: 520px;
    padding-top: 60px;
    padding-bottom: 60px;
    background-color: #fff;
    border-radius: 12px;
  }
}
