// @ts-ignore
import type { ItineraryItemType } from '@/apis/itineraries';
import { createItinerary, getListItinerary } from '@/apis/itineraries';
import BasicButton from '@/components/Commons/BasicButton';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import BasicModal from '@/components/Commons/BasicModal';
import PageContainer from '@/components/Commons/Page/Container';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import FolderAddSVG from '@/components/SVG/FolderAddSVG';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import STATUS_CODE from '@/constants/statusCode';
import { ITEM_PER_PAGE } from '@/utils/constants';
import type { TabsProps } from 'antd';
import { Badge, Form, Spin, Tabs } from 'antd';
import moment from 'moment';
import { useEffect, useRef, useState } from 'react';
import { useUrlSearchParams } from 'use-url-search-params';
import CreateBookingForm from './components/CreateBookingForm';
import HeaderInformation from './components/HeaderInformation';
import TableList from './components/TableList';
import type { StatusCountType } from './type';
import { statusOptions, StatusValue, StatusValueLabel } from './type';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

export interface DetailRowItemSaleInvoiceType {
  key: number;
  subject_id: string;
  product_name: string;
  sale_date: string;
  unit_price: number;
  quantity: number;
  amount_including_tax: number;
  id: number;
  memo?: string;
  subjectName?: string;
  categoryTaxName?: string;
  sale_invoice_item_id?: string;
  consolidated_invoice_item_id?: number;
  tax_rate?: number;
  is_exists?: number;
}

const initSearchParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};

const InvoicesPage = () => {
  const isEditPage = true;
  const [form] = Form.useForm();
  const [formHeaderInformation] = Form.useForm();
  const [countStatus, setCountStatus] = useState<StatusCountType>();
  const [activeTab, setActiveTab] = useState<string>('all');
  const refCreateBookingForm = useRef<any>(null);
  const refCreateBookingModal = useRef<BasicModalRef>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);

  const [dataSource, setDataSource] = useState<ItineraryItemType[]>([]);
  const [total, setTotal] = useState(0);

  const deviceType = useDeviceType();

  const fetchData = async () => {
    setIsLoading(true);
    try {
      const paramsSearch = {
        ...parameter,
        admin_id: parameter?.admin_id
          ? JSON.parse(parameter?.admin_id as string)?.value
          : undefined,
        business_partner_id: parameter?.business_partner_id
          ? JSON.parse(parameter?.business_partner_id as string)?.value
          : undefined,
        status:
          parameter?.status || parameter?.status === 0
            ? JSON.parse(parameter?.status as string)?.value
              ? JSON.parse(parameter?.status as string)?.value
              : parameter?.status
            : undefined,
      };
      const { data, status } = await getListItinerary(paramsSearch);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dataListBooking: ItineraryItemType[] = data?.bookings?.data?.map((i, index) => ({
          ...i,
          key: Number(parameter.limit) * (Number(parameter.page) - 1) + index + 1,
        }));
        setCountStatus(data?.countStatus);
        setDataSource(dataListBooking);
        setTotal(data?.bookings?.total);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    fetchData();
    formHeaderInformation.setFieldsValue({
      keyword: parameter?.keyword,
      admin_id: parameter?.admin_id ? JSON.parse(parameter?.admin_id as string) : undefined,
      business_partner_id: parameter?.business_partner_id
        ? JSON.parse(parameter?.business_partner_id as string)
        : undefined,
      status: parameter?.status ? JSON.parse(parameter?.status as string) : undefined,
      day_go_start: parameter?.day_go_start ? moment(parameter?.day_go_start as string) : undefined,
      day_go_end: parameter?.day_go_end ? moment(parameter?.day_go_end as string) : undefined,
    });
    if (parameter?.status && !isNaN(Number(parameter?.status))) {
      formHeaderInformation.setFieldsValue({
        status: statusOptions?.find((item) => item.value === Number(parameter.status)),
      });
      setActiveTab(parameter?.status.toString());
    } else {
      const tabNumber = parameter?.status ? JSON.parse(parameter?.status as string)?.value : 'all';
      setActiveTab(tabNumber.toString());
    }
  }, [
    parameter?.limit,
    parameter?.page,
    parameter?.keyword,
    parameter?.status,
    parameter?.day_go_start,
    parameter?.day_go_end,
    parameter?.admin_id,
    parameter?.business_partner_id,
  ]);

  const deleteParameter = (key) => {
    const searchParams = new URLSearchParams(window.location.search);
    searchParams.delete(key);
    const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
    window.history.pushState({}, '', newUrl);
  };

  const onSearch = async () => {
    try {
      const valueSearch = await formHeaderInformation.validateFields();
      if (!valueSearch?.status) {
        deleteParameter('status');
      }
      setParameter({
        ...valueSearch,
        page: 1,
        limit: parameter?.limit,
        day_go_start: valueSearch?.day_go_start?.format('YYYY-MM-DD'),
        day_go_end: valueSearch?.day_go_end?.format('YYYY-MM-DD'),
      });
      if (!valueSearch?.status) {
        setActiveTab('all');
      }
    } catch (error) {
      console.log('error onSearch', error);
    }
  };
  const handleCreateBookings = async (values) => {
    try {
      const payload = {
        ...values,
        day_go_start: values?.day_go_start
          ? moment(values?.day_go_start).format('YYYY-MM-DD')
          : null,
        admin_id: values?.admin_id?.value,
        business_partner_id: values?.business_partner_id?.value,
        status: StatusValue.ESTIMATED,
      };

      const { status, error } = await createItinerary(payload);

      if (status === STATUS_CODE.CREATED) {
        refCreateBookingForm?.current?.form?.resetFields();
        refCreateBookingModal?.current?.close();
        setParameter({ ...parameter, page: 1 });
        fetchData();
        openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
      } else if (status === STATUS_CODE.INVALID) {
        const errors = error.data.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key][0];
            openNotificationFail(message);
          });
        }
      } else {
        openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleChangeTab = (e) => {
    if (e !== 'all') {
      setActiveTab(e.toString());
      setParameter({ ...parameter, status: Number(e) });
    } else {
      setActiveTab('all');
      const newSearchValues = { ...parameter, status: undefined };

      setParameter({ ...newSearchValues });
    }
  };

  const renderBadgeCount = (label: string, count: number) => {
    return (
      <div className="flex items-center">
        <p>{label}</p>
        <div className="bg-[#FFE3E3] py-[4px] px-[7px] text-[#FF3B30] ml-1 rounded">
          <p className="font-semibold text-[10px] leading-3">
            {count ? (count > 99 ? '99+' : count) : 0}
          </p>
        </div>
      </div>
    );
  };

  const items: TabsProps['items'] = [
    {
      key: 'all',
      label: <Badge count={0}>すべて</Badge>,
    },
    {
      key: StatusValue.ESTIMATED.toString(),
      label: renderBadgeCount(StatusValueLabel.ESTIMATED.toString(), countStatus?.ESTIMATED),
    },
    {
      key: StatusValue.PROCESSING.toString(),
      label: renderBadgeCount(StatusValueLabel.PROCESSING.toString(), countStatus?.PROCESSING),
    },
    {
      key: StatusValue.BOOKED.toString(),
      label: renderBadgeCount(StatusValueLabel.BOOKED.toString(), countStatus?.BOOKED),
    },
    {
      key: StatusValue.ACTIVE.toString(),
      label: renderBadgeCount(StatusValueLabel.ACTIVE.toString(), countStatus?.ACTIVE),
    },
    {
      key: StatusValue.CANCELLED.toString(),
      label: renderBadgeCount(StatusValueLabel.CANCELLED.toString(), countStatus?.CANCELLED),
    },
    {
      key: StatusValue.COMPLETED.toString(),
      label: renderBadgeCount(StatusValueLabel.COMPLETED.toString(), countStatus?.COMPLETED),
    },
  ];

  return (
    <Spin spinning={isLoading}>
      <PageContainer>
        <HeaderInformation form={formHeaderInformation} onSearch={onSearch} />
        <div
          className={`px-[22px] py-6 mt-6 bg-[#fff] rounded-xl border
         border-[#3F3F661F] [&_.ant-tabs-nav]:before:!border-none
          [&_.ant-scroll-number]:!top-[8px] [&_.ant-scroll-number]:!right-[-22px]
           [&_.ant-tabs-tab_.ant-badge]:text-[#7D7E82] [&_.ant-tabs-tab_.ant-badge]:font-medium
          [&_.ant-tabs-tab-active_.ant-badge]:!text-[#000000]
          `}
        >
          <div className="flex justify-between">
            <Tabs
              activeKey={activeTab}
              items={items}
              className="max-w-[70%] overflow-x-auto scroll-smooth touch-pan-x"
              onChange={handleChangeTab}
            />

            <BasicButton
              styleType="accept"
              onClick={() => refCreateBookingModal?.current?.open()}
              className="flex items-center space-x-[10px]"
            >
              <FolderAddSVG />
              新規作成
            </BasicButton>
          </div>
          <TableList
            form={form}
            isEditPage={isEditPage}
            isLoading={isLoading}
            dataSource={dataSource}
            total={total}
            paramSearch={parameter}
            setParamSearch={setParameter}
            fetchData={fetchData}
          />
        </div>
        <BasicModal
          ref={refCreateBookingModal}
          title="旅程作成"
          content={<CreateBookingForm ref={refCreateBookingForm} onSubmit={handleCreateBookings} />}
          onSubmit={() => {
            refCreateBookingForm?.current?.form?.submit();
          }}
          className="!w-[600px] [&_.ant-modal-content]:!max-h-[90vh] [&_.ant-modal-content]:overflow-y-auto"
          buttonCloseTitle="キャンセル"
          styleTypeButtonSubmit="accept"
          buttonSubmitTitle="保存"
          onClose={() => {
            refCreateBookingForm?.current?.form?.resetFields();
          }}
        />
      </PageContainer>
    </Spin>
  );
};

export default InvoicesPage;
