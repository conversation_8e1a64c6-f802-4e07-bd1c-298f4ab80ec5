import BasicButton from '@/components/Commons/BasicButton';
import BasicInput from '@/components/Commons/BasicInput';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import SearchSVG from '@/components/SVG/SearchSVG';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { DownloadOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { UploadImageType } from '@/components/Commons/BasicUploads';
import { uploadImage } from '@/apis/common';
import { notification, Upload } from 'antd';
import AttachmentTableList from './AttachmentTableList';
import type { ItemFileItineraryType } from '@/apis/itineraries';
import {
  createAttachedFileItinerary,
  deleteAttachedFileItinerary,
  getListAttachedFileItinerary,
  sortAttachedFileItinerary,
} from '@/apis/itineraries';
import { useDeviceType } from '@/providers/DeviceProvider';

interface ListDocumentProps {
  // Define the props for your component here
  travelId: string;
}
const MAX_FILE_SIZE_MB = 10;
const acceptTypes = 'image/*,application/pdf';
const ListDocument: React.FC<ListDocumentProps> = ({ travelId }) => {
  const [searchValue, setSearchValue] = useState<string>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [listAttachedFile, setListAttachedFile] = useState<ItemFileItineraryType[]>([]);
  const [listAttachedOrigin, setListAttachedOrigin] = useState<ItemFileItineraryType[]>([]);

  const fetchListAttachedFile = async () => {
    const resGetList = await getListAttachedFileItinerary(travelId);
    if (resGetList.status === STATUS_CODE.SUCCESSFUL) {
      const list = resGetList.data.data;
      setListAttachedFile(list);
      setListAttachedOrigin(list);
    }
  };

  useEffect(() => {
    if (travelId) {
      fetchListAttachedFile();
    }
  }, [travelId]);

  const handleBeforeUpload = async (file: File) => {
    const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

    if (file.size > MAX_FILE_SIZE_BYTES) {
      notification.error({
        message: `※1ファイル${MAX_FILE_SIZE_MB}MBまでとすること`,
      });
      return;
    }
    setIsLoading(true);
    try {
      const config = {
        headers: { 'Content-Type': 'multipart/form-data' },
      };

      const formData = new FormData();
      formData.append('type', UploadImageType.travels);
      formData.append('file', file);

      const { data: dataFile, status, error } = await uploadImage(formData, config);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const urlFile = dataFile.data;
        const resCreate = await createAttachedFileItinerary({ id: travelId, file_link: urlFile });
        if (resCreate.status === STATUS_CODE.SUCCESSFUL) {
          fetchListAttachedFile();
          openNotificationSuccess(MESSAGE_ALERT.UPLOAD_SUCCESS);
        } else {
          openNotificationFail(MESSAGE_ALERT.UPLOAD_FAILED);
        }
      } else {
        const errors = error.data.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key][0];
            openNotificationFail(message);
          });
        } else {
          openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
        }
      }
      setIsLoading(false);
    } catch (error) {
      console.log('upload fail', error);
      setIsLoading(false);
    }
  };
  const handleSearchByValue = () => {
    if (!searchValue) {
      setListAttachedFile(listAttachedOrigin);
      return;
    }
    const listAttachedFileTemp = listAttachedOrigin.filter((item) => {
      return item.file_name.toLowerCase().includes(searchValue.toLowerCase());
    });
    setListAttachedFile(listAttachedFileTemp);
  };

  const handleDeleteAttachedFile = async (id: number) => {
    setIsLoading(true);
    try {
      const resDelete = await deleteAttachedFileItinerary(id);
      if (resDelete.status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.DELETE_SUCCESS);
        fetchListAttachedFile();
      } else {
        openNotificationFail(MESSAGE_ALERT.DELETE_FAILED);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };
  const handleChangeSort = async (newIndex: number, id: number) => {
    try {
      const resDelete = await sortAttachedFileItinerary({
        idFile: id,
        idItinerary: travelId,
        newIndex: newIndex,
      });
      if (resDelete.status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.DELETE_SUCCESS);
        fetchListAttachedFile();
      } else {
        openNotificationFail(MESSAGE_ALERT.DELETE_FAILED);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };
  return (
    <div className="flex flex-col mt-11 bg-white p-4 rounded-xl">
      {/* Render your component UI here */}
      <p className="mb-4 text-lg font-bold">3. 資料</p>
      <div className="flex justify-between flex-wrap  gap-4">
        <div className="flex flex-col flex-1">
          <p className="font-medium text-xs text-[#363840]">ファイル名</p>
          <div className="flex gap-4 mt-2 flex-wrap">
            <BasicInput
              style={{
                width: '280px',
                height: '40px',
              }}
              value={searchValue}
              placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
              onChange={(val) => {
                setSearchValue(val.target.value);
              }}
            />
            <BasicButton
              icon={<SearchSVG colorSvg="white" />}
              className="flex items-center w-[120px] !bg-[#3997C8] hover:!bg-[#3997C8]"
              styleType="accept"
              onClick={handleSearchByValue}
            >
              <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
            </BasicButton>
          </div>
        </div>
        <div className="flex flex-col  justify-end items-end">
          <span className="text-xs text-[#363840] mb-2">
            ※1ファイル{MAX_FILE_SIZE_MB}MBまでとすること
          </span>
          <Upload
            accept={acceptTypes}
            name="attachedFile"
            showUploadList={false}
            beforeUpload={handleBeforeUpload}
          >
            <BasicButton
              icon={<DownloadOutlined style={{ color: '#3997C8' }} />}
              className="flex items-center !border-[#3997C8] hover:!bg-[#BCE4E3]"
              styleType="outline"
            >
              <p className="!text-[#3997C8]">ファイルアップロード</p>
            </BasicButton>
          </Upload>
        </div>
      </div>
      <div className="flex w-full mt-6">
        <AttachmentTableList
          handleDeleteAttachedFile={handleDeleteAttachedFile}
          handleChangeSort={handleChangeSort}
          listAttachedFile={listAttachedFile}
          loading={isLoading}
        />
      </div>
    </div>
  );
};

export default ListDocument;
