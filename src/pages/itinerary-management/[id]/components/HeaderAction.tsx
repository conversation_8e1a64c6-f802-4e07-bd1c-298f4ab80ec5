import { DetailItineraryDataResponseType } from '@/apis/itineraries';
import BasicButton from '@/components/Commons/BasicButton';
import { TEXT_ACTION } from '@/constants/commonText';
import { PlusOutlined } from '@ant-design/icons';
import React from 'react';
import { history } from 'umi';

interface HeaderActionProps {
  // Define the props for your component here
  onCreatePlan: () => void;
  isDisableCreatePlane: boolean;
  dataDetail: DetailItineraryDataResponseType;
}

const HeaderAction: React.FC<HeaderActionProps> = ({
  onCreatePlan,
  isDisableCreatePlane,
  dataDetail,
}) => {
  // Implement your component logic here

  return (
    <div className="flex justify-between px-2 py-3">
      <div className="flex flex-1">
        <p className="mb-4 text-lg font-bold">2. 行程表</p>
      </div>
      <div className="flex gap-4">
        <BasicButton
          icon={<PlusOutlined />}
          className="flex items-center"
          styleType="accept"
          onClick={() => {
            history.push(
              `/accounting-management/sales-management/invoices?tourId=${dataDetail.travel_id}&businessId=${dataDetail.businessPartner?.business_partner_code}`,
            );
          }}
        >
          {TEXT_ACTION.CREATE_SALES_SLIPS}
        </BasicButton>
        {!isDisableCreatePlane && (
          <BasicButton
            icon={<PlusOutlined />}
            className="flex items-center"
            styleType="accept"
            onClick={onCreatePlan}
          >
            {TEXT_ACTION.CREATE_ITINERARIES}
          </BasicButton>
        )}
      </div>
    </div>
  );
};

export default HeaderAction;
