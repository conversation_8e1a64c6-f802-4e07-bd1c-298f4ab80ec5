import type { DetailItineraryDataResponseType } from '@/apis/itineraries';
import BasicDateRangePicker from '@/components/Commons/BasicDateRangePicker';
import BasicInput from '@/components/Commons/BasicInput';
import RequiredTag from '@/components/Commons/RequiredTag/RequiredTag';
import { rules } from '@/constants/rules';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import type { FormProps } from 'antd/es/form';
import moment from 'moment';
import { forwardRef, useEffect, useImperativeHandle } from 'react';

type CreatePlanFormProps = {
  onSubmit?: (v: any) => void;
  initialData?: DetailItineraryDataResponseType;
  currentMaxPlanVersion: number;
};

export type CreatePlanFormRefType = {
  form: FormInstance<any>;
};

const CreatePlanForm = forwardRef<CreatePlanFormRefType, CreatePlanFormProps>(
  (
    { onSubmit, initialData, currentMaxPlanVersion, ...others }: CreatePlanFormProps & FormProps,
    ref,
  ) => {
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
      form,
    }));

    useEffect(() => {
      console.log('initialData', initialData);
      if (initialData) {
        form.setFieldsValue({
          title: 'v' + (currentMaxPlanVersion + 1) + '_' + initialData?.tour_name,
          time_period: [
            initialData?.departure_date ? moment(initialData?.departure_date) : undefined,
            undefined,
          ],
        });
      }
    }, [initialData]);

    return (
      <div>
        <Form {...others} form={form} onFinish={onSubmit} className="">
          <Form.Item name="title" rules={rules?.requiredInput}>
            <BasicInput
              title={
                <div className="flex items-center gap-[6px]">
                  旅程表タイトル
                  <RequiredTag isSymbol />
                </div>
              }
              disabled
              placeholder="ツアー名を入力"
            />
          </Form.Item>
          <Form.Item
            name="time_period"
            rules={[
              { required: true, message: '※必須項目が未選択です。' },
              {
                validator: (_, value) =>
                  value && value[0] && value[1]
                    ? Promise.resolve()
                    : Promise.reject(new Error('※必須項目が未選択です。')),
              },
            ]}
          >
            <BasicDateRangePicker title="出発日" className="!h-10" required />
          </Form.Item>
          {/* <Form.Item name="plan_description">
            <BasicInput title="区間" placeholder="区間" />
          </Form.Item> */}
        </Form>
      </div>
    );
  },
);
export default CreatePlanForm;
