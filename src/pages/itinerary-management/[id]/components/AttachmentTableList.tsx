import type { ItemFileItineraryType } from '@/apis/itineraries';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import IconEye from '@/assets/imgs/common-icons/view-eye.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicTable from '@/components/Commons/BasicTable';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { useDeviceType } from '@/providers/DeviceProvider';
import { MenuOutlined } from '@ant-design/icons';
import { Image, Popconfirm } from 'antd';
import { arrayMoveImmutable } from 'array-move';
import { useEffect, useState } from 'react';
import type { SortableContainerProps, SortEnd } from 'react-sortable-hoc';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';

interface Props {
  listAttachedFile: ItemFileItineraryType[];
  handleDeleteAttachedFile: (id: number) => void;
  handleChangeSort: (newIndex: number, id: number) => void;
  loading: boolean;
}

const DragHandle = SortableHandle(() => <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />);

const SortableItem = SortableElement((props: React.HTMLAttributes<HTMLTableRowElement>) => (
  <tr {...props} />
));
const SortableBody: React.ReactNode = SortableContainer(
  (props: React.HTMLAttributes<HTMLTableSectionElement>) => <tbody {...props} />,
);

const AttachmentTableList: React.FC<Props> = ({
  loading,
  listAttachedFile,
  handleDeleteAttachedFile,
  handleChangeSort,
}) => {
  const [dataSource, setDataSource] = useState<any>([]);
  const deviceType = useDeviceType();

  const defaultColumns = [
    {
      title: '',
      dataIndex: 'sort',
      width: 60,
      className: 'drag-visible',
      // @ts-ignore
      render: () => <DragHandle />,
    },
    {
      title: 'ファイル名',
      dataIndex: 'file_name',
      key: 'file_name',
      maxWidth: 200,
      render: (_, { file_name }) => {
        const textFileName = file_name ?? 'file';
        return <>{textFileName}</>;
      },
    },
    {
      title: '',
      dataIndex: 'preview',
      key: 'preview',
      width: deviceType === DeviceTypeEnum.DESKTOP ? 120 : 60,
      render: (_, record) => {
        return (
          <div className="flex items-center justify-center">
            <BasicButton
              style={{
                width: deviceType === DeviceTypeEnum.DESKTOP ? '112px' : '40px',
              }}
              styleType="outline"
              className=" flex items-center justify-center !h-[24px] !bg-[transparent] !shadow-none !border-none text-main-color"
              onClick={() => {
                window.open(record?.file_link, '_blank');
              }}
            >
              <Image preview={false} src={IconEye} width={16} height={16} />
              {deviceType === DeviceTypeEnum.DESKTOP ? (
                <p className="text-xs !ml-1">{TEXT_ACTION.PREVIEW}</p>
              ) : null}
            </BasicButton>
          </div>
        );
      },
    },
    {
      title: '',
      dataIndex: 'deleteFile',
      key: 'deleteFile',
      width: deviceType === DeviceTypeEnum.DESKTOP ? 120 : 60,
      render: (_, record) => (
        <Popconfirm
          title={TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
          onConfirm={() => handleDeleteAttachedFile(record?.id)}
          okText={TEXT_ACTION.DELETE}
          cancelText={TEXT_ACTION.CANCEL}
          placement="topRight"
        >
          <div className="flex items-center justify-center">
            <BasicButton
              style={{
                width: deviceType === DeviceTypeEnum.DESKTOP ? '84px' : '40px',
              }}
              styleType="danger"
              className="!h-[24px] hover:shadow-md hover:opacity-70 !border-none !shadow-none !bg-[transparent]"
            >
              <Image preview={false} src={IconDelete} width={15} height={14} />
              {deviceType === DeviceTypeEnum.DESKTOP ? (
                <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
              ) : null}
            </BasicButton>
          </div>
        </Popconfirm>
      ),
    },
  ];

  useEffect(() => {
    if (listAttachedFile) {
      const newDataSource = listAttachedFile?.map((item, index) => ({
        ...item,
        index: item.sort_order + 1 + index,
      }));
      setDataSource(newDataSource);
    }
  }, [listAttachedFile]);

  const onSortEnd = ({ oldIndex, newIndex }: SortEnd) => {
    console.log('Sorted items: ', oldIndex, newIndex);
    if (oldIndex !== newIndex) {
      const newData = arrayMoveImmutable(dataSource.slice(), oldIndex, newIndex).filter(
        (el: any) => !!el,
      );
      console.log('Sorted items: ', newData);
      // setDataSource(newData);
      handleChangeSort(newIndex, dataSource[oldIndex].id);
    }
  };

  const DraggableContainer = (props: SortableContainerProps) => (
    // @ts-ignore
    <SortableBody
      useDragHandle
      disableAutoscroll
      helperClass="row-dragging"
      onSortEnd={onSortEnd}
      {...props}
    />
  );

  const DraggableBodyRow: React.FC<any> = ({ className, style, ...restProps }) => {
    // function findIndex base on Table rowKey props and should always be a right array index
    const index = dataSource.findIndex((x) => x.id === restProps['data-row-key']);
    // @ts-ignore
    return <SortableItem index={index} {...restProps} />;
  };

  return (
    <BasicTable
      className="!mt-0 w-full"
      tableProps={{
        scroll: { x: 800, y: 900 },
        columns: defaultColumns,
        dataSource: dataSource,
        components: {
          body: {
            wrapper: DraggableContainer,
            row: DraggableBodyRow,
          },
        },
        bordered: false,
        pagination: false,
        loading: loading,
        rowKey: 'id',
      }}
      hasPagination={false}
    />
  );
};

export default AttachmentTableList;
