import type { BaseParams } from '@/@types/request';
import BadgeCheck from '@/assets/imgs/common-icons/badge-check.svg';
import IconCall from '@/assets/imgs/common-icons/call-orange.svg';
import IconEdit from '@/assets/imgs/common-icons/edit-blue.svg';
import IconEstimate from '@/assets/imgs/common-icons/money-blue.svg';
import IconEye from '@/assets/imgs/common-icons/view-eye.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicCheckboxGroup from '@/components/Commons/BasicCheckboxGroup';
import BasicTable from '@/components/Commons/BasicTable';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { useDeviceType } from '@/providers/DeviceProvider';
import { formatMoney } from '@/utils';
import { MoreOutlined } from '@ant-design/icons';
import { Dropdown, Image, Menu } from 'antd';
import React, { useMemo } from 'react';
import { history } from 'umi';

interface TableListProps {
  isLoading: boolean;
  dataSource: any[];
  total: number;
  paramSearch?: BaseParams;
  setParamSearch?: (val: BaseParams) => void;
  handleChangeFinalPlan: (val: number) => void;
}

const TableList: React.FC<TableListProps> = ({
  isLoading,
  dataSource,
  total,
  paramSearch,
  setParamSearch,
  handleChangeFinalPlan,
}) => {
  const deviceType = useDeviceType();

  const isMobile = useMemo(() => {
    return deviceType === DeviceTypeEnum.MOBILE;
  }, [deviceType]);
  const isTablet = useMemo(() => {
    return deviceType === DeviceTypeEnum.TABLET;
  }, [deviceType]);
  const isDesktop = useMemo(() => {
    return deviceType === DeviceTypeEnum.DESKTOP;
  }, [deviceType]);

  const ActionColumn = {
    title: '',
    dataIndex: 'action',
    key: 'action',
    width: isMobile || isTablet ? 60 : 420,
    fixed: 'right',
    render: (_, record) => {
      const menu = (
        <Menu>
          <Menu.Item key="preview">
            <div
              className="flex items-center gap-2"
              onClick={() =>
                history.push(`/itinerary-management/${record.itinerary_id}/preview/${record.id}`)
              }
            >
              <Image preview={false} src={IconEye} width={16} height={16} />
              <span className="text-main-color">プレビュー</span>
            </div>
          </Menu.Item>
          <Menu.Item key="edit">
            <div
              className="flex items-center gap-2"
              onClick={() =>
                history.push(`/itinerary-management/${record.itinerary_id}/create/${record.id}`)
              }
            >
              <Image preview={false} src={IconEdit} width={16} height={16} />
              <span className="text-[#225DE0]">編集</span>
            </div>
          </Menu.Item>
          <Menu.Item key="estimate">
            <div
              className="flex items-center gap-2"
              onClick={() =>
                history.push(`/itinerary-management/${record.itinerary_id}/estimate/${record.id}`)
              }
            >
              <Image preview={false} src={IconEstimate} width={16} height={16} />
              <span className="text-[#0692BC]">見積書</span>
            </div>
          </Menu.Item>
          <Menu.Item key="arrangement">
            <div
              className="flex items-center gap-2"
              onClick={() =>
                history.push(
                  `/itinerary-management/${record.itinerary_id}/plan/${record.id}/arrangement`,
                )
              }
            >
              <Image preview={false} src={IconCall} width={16} height={16} />
              <span className="text-[#FDAF2E]">手配状況</span>
            </div>
          </Menu.Item>
        </Menu>
      );

      if (isMobile || isTablet) {
        return (
          <Dropdown overlay={menu} trigger={['click']}>
            <BasicButton
              styleType="outline"
              className="flex items-center justify-center !h-[24px] w-[40px] !border-none hover:border-main-color"
            >
              <MoreOutlined className="text-lg" />
            </BasicButton>
          </Dropdown>
        );
      }

      // Trên tablet và desktop: Hiển thị buttons
      return (
        <div className={`flex gap-2 ${isTablet ? 'flex-wrap' : ''}`}>
          <BasicButton
            styleType="outline"
            className="flex items-center justify-center !h-[24px] w-fit !border-none hover:border-main-color text-main-color text-xs px-2 py-1"
            onClick={() =>
              history.push(`/itinerary-management/${record.itinerary_id}/preview/${record.id}`)
            }
          >
            <Image preview={false} src={IconEye} width={16} height={16} />
            <span className="ml-1 text-main-color">プレビュー</span>
          </BasicButton>
          <BasicButton
            styleType="outline"
            className="flex items-center justify-center !h-[24px] w-fit !border-none hover:border-[#225DE0] text-[#225DE0] text-xs px-2 py-1"
            onClick={() =>
              history.push(`/itinerary-management/${record.itinerary_id}/create/${record.id}`)
            }
          >
            <Image preview={false} src={IconEdit} width={16} height={16} />
            <span className="ml-1 text-[#225DE0]">編集</span>
          </BasicButton>
          <BasicButton
            styleType="outline"
            className="flex items-center justify-center !h-[24px] w-fit !border-none hover:border-[#0692BC] text-[#0692BC] text-xs px-2 py-1"
            onClick={() =>
              history.push(`/itinerary-management/${record.itinerary_id}/estimate/${record.id}`)
            }
          >
            <Image preview={false} src={IconEstimate} width={16} height={16} />
            <span className="ml-1 text-[#0692BC]">見積書</span>
          </BasicButton>
          <BasicButton
            styleType="outline"
            className="flex items-center justify-center !h-[24px] w-fit !border-none hover:border-[#FDAF2E] text-[#FDAF2E] text-xs px-2 py-1"
            onClick={() =>
              history.push(
                `/itinerary-management/${record.itinerary_id}/plan/${record.id}/arrangement`,
              )
            }
          >
            <Image preview={false} src={IconCall} width={16} height={16} />
            <span className="ml-1 text-[#FDAF2E]">手配状況</span>
          </BasicButton>
        </div>
      );
    },
  };

  const defaultColumns = useMemo(() => {
    return [
      {
        title: '#',
        dataIndex: 'key',
        key: 'key',
        width: 40,
      },
      {
        title: '催行',
        dataIndex: 'event',
        key: 'event',
        width: 50,
        render: (_, record) => (
          <BasicCheckboxGroup
            options={[{ value: record?.id, label: '' }]}
            value={record?.is_final ? [record?.id] : undefined}
            onChange={(checked) => {
              if (checked?.[0]) handleChangeFinalPlan(checked?.[0] as number);
            }}
          />
        ),
      },
      {
        title: 'ツアー名',
        dataIndex: 'tour_name',
        key: 'tour_name',
        width: deviceType === DeviceTypeEnum.MOBILE ? 220 : undefined,
      },
      {
        title: '出発日',
        dataIndex: 'departure_date',
        key: 'departure_date',
        width: 100,
      },
      {
        title: '日数',
        dataIndex: 'number_of_days',
        key: 'number_of_days',
        width: 50,
        render: (_, { total_days }) => <div className="text-right">{total_days}</div>,
      },
      {
        title: (
          <>
            モデル
            {/* <br /> */}
            コース
          </>
        ),
        dataIndex: 'is_model_course',
        key: 'is_model_course',
        width: 80,
        render: (_, record) => {
          if (record.is_model_course)
            return (
              <div className="flex justify-center">
                <Image src={BadgeCheck} preview={false} width={24} height={24} />
              </div>
            );
          else return '';
        },
      },
      {
        title: '見積金額',
        dataIndex: 'estimated_amount',
        key: 'estimated_amount',
        width: 80,
        render: (_, { estimated_amount }) => (
          <div className="text-right">{formatMoney(estimated_amount)}</div>
        ),
      },
      {
        title: '作成日',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 80,
      },
      {
        title: '作成者',
        dataIndex: 'created_by',
        key: 'created_by',
        width: 100,
      },
      {
        ...ActionColumn,
      },
    ];
  }, [dataSource]);

  const scrollConfig = useMemo(
    () => ({
      x: isMobile ? 600 : isTablet ? 900 : 1100,
      y: isMobile ? 400 : 700, // Giảm chiều cao trên mobile
    }),
    [isMobile, isTablet],
  );

  return (
    <>
      <BasicTable
        className="!mt-0 w-full"
        tableProps={{
          scroll: scrollConfig,
          loading: isLoading,
          columns: defaultColumns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'key',
          size: deviceType === DeviceTypeEnum.MOBILE ? 'small' : 'middle',
        }}
        page={paramSearch.page as number}
        pageSize={paramSearch.limit as number}
        onChangePage={(p: number) => {
          setParamSearch({ ...paramSearch, page: p });
        }}
        total={total}
        onSelectPageSize={(v) => setParamSearch({ ...paramSearch, limit: v })}
      />
    </>
  );
};

export default TableList;
