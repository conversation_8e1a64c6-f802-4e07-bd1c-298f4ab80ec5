import React from 'react';
import IconEdit from '@/assets/imgs/common-icons/edit-blue.svg';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import BasicButton from '@/components/Commons/BasicButton';
import { Image, Popconfirm } from 'antd';
import type { DetailItineraryDataResponseType } from '@/apis/itineraries';
import moment from 'moment';
import { paymentStatusTravel } from '@/constants/data';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

interface HeaderInformationProps {
  dataDetail: DetailItineraryDataResponseType;
  handleUpdate: () => void;
  handleDelete: () => void;
}

const HeaderInformation: React.FC<HeaderInformationProps> = ({
  dataDetail,
  handleUpdate,
  handleDelete,
}) => {
  const deviceType = useDeviceType();

  return (
    <div className="flex bg-white p-4 rounded-xl">
      <div className="flex flex-col flex-1">
        <p className="mb-4 text-lg font-bold">1. 概要</p>
        <div
          className={`${
            deviceType === DeviceTypeEnum.DESKTOP
              ? 'flex gap-4 flex-wrap'
              : 'grid grid-cols-2 gap-4'
          }`}
        >
          <div className="flex flex-col gap-4 min-w-[100px]">
            <p className="font-medium text-[#6B7180]">旅行ID</p>
            <p className="font-medium text-[#363840]">{dataDetail?.travel_id}</p>
          </div>
          <div className="flex flex-col gap-4 min-w-[100px]">
            <p className="font-medium text-[#6B7180]">ツアー名</p>
            <p className="font-medium text-[#363840]">{dataDetail?.tour_name}</p>
          </div>
          <div className="flex flex-col gap-4 min-w-[100px]">
            <p className="font-medium text-[#6B7180]">得意先</p>
            <p className="font-medium text-[#363840]">
              {dataDetail?.businessPartner?.business_partner_name}
            </p>
          </div>
          <div className="flex flex-col gap-4 min-w-[100px]">
            <p className="font-medium text-[#6B7180]">得意先の担当者</p>
            <p className="font-medium text-[#363840]">{dataDetail?.representative_name}</p>
          </div>
          <div className="flex flex-col gap-4 min-w-[100px]">
            <p className="font-medium text-[#6B7180]">出発日</p>
            <p className="font-medium text-[#363840]">
              {dataDetail?.departure_date
                ? moment(dataDetail.departure_date).format('YYYY/MM/DD')
                : ''}
            </p>
          </div>
          <div className="flex flex-col gap-4 min-w-[80px]">
            <p className="font-medium text-[#6B7180]">顧客数</p>
            <p className="font-medium text-[#363840] text-right">{dataDetail?.customer_count}</p>
          </div>
          <div className="flex flex-col gap-4 min-w-[100px]">
            <p className="font-medium text-[#6B7180]">担当者</p>
            <p className="font-medium text-[#363840]">{dataDetail?.admin?.name}</p>
          </div>
          <div className="flex flex-col gap-4 min-w-[100px]">
            <p className="font-medium text-[#6B7180]">支払状況</p>
            <p className="font-medium text-[#363840] line-clamp-1">
              {dataDetail?.payment_status || dataDetail?.payment_status === 0
                ? paymentStatusTravel.find((item) => item.value === dataDetail.payment_status)
                    ?.label
                : ''}
            </p>
          </div>
          <div className="flex flex-col gap-4 min-w-[100px]">
            <p className="font-medium text-[#6B7180]">メモ</p>
            <p className="font-medium text-[#363840] max-w-[400px] line-clamp-3">
              {dataDetail?.memo}
            </p>
          </div>
        </div>
      </div>
      <div className="flex flex-col gap-4 min-w-[100px]">
        <BasicButton
          styleType="outline"
          className=" flex items-center justify-center !h-[40px] w-[112px] !bg-white !border-[#DCDEE3] hover:!border-[#225DE0] "
          onClick={handleUpdate}
        >
          <Image preview={false} src={IconEdit} width={16} height={16} />
          <p className="text-xs !ml-1 !text-[#225DE0]">編集</p>
        </BasicButton>
        <Popconfirm
          title={TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
          onConfirm={handleDelete}
          okText={TEXT_ACTION.DELETE}
          cancelText={TEXT_ACTION.CANCEL}
          placement="topLeft"
        >
          <BasicButton
            styleType="outline"
            className=" flex items-center justify-center !h-[40px] w-[112px] !bg-white !border-[#DCDEE3] hover:!border-[#FF3B30] "
          >
            <Image preview={false} src={IconDelete} width={16} height={16} />
            <p className="text-xs !ml-1 !text-[#FF3B30]">削除</p>
          </BasicButton>
        </Popconfirm>
      </div>
    </div>
  );
};

export default HeaderInformation;
