import type { DetailItineraryDataResponseType } from '@/apis/itineraries';
import { deleteItinerary, getDetailItinerary, updateItinerary } from '@/apis/itineraries';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import BasicModal from '@/components/Commons/BasicModal';
import PageContainer from '@/components/Commons/Page/Container';
import Exception from '@/components/Exception';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import STATUS_CODE from '@/constants/statusCode';
import { ITEM_PER_PAGE } from '@/utils/constants';
import { Spin } from 'antd';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Link, history } from 'umi';
import { useUrlSearchParams } from 'use-url-search-params';
import CreateBookingForm from '../components/CreateBookingForm';
import HeaderAction from './components/HeaderAction';
import HeaderInformation from './components/HeaderInformation';
import ListDocument from './components/ListDocument';
import TableList from './components/TableList';
import {
  changeFinalPlanItinerary,
  createPlanItinerary,
  getListPlanItinerary,
} from '@/apis/itineraries/plan';
import CreatePlanForm from './components/CreatePlanForm';
import CancelXSVG from '@/components/SVG/CancelXSVG';

const initSearchParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};

const DetailItineraryManagement = () => {
  const paramUrl = useParams();
  const travelId = (paramUrl as { id: string })?.id;
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);
  const refUpdateBookingModal = useRef<BasicModalRef>(null);
  const refCreatePlanModal = useRef<BasicModalRef>(null);
  const refUpdateBookingForm = useRef<any>(null);
  const refCreatePlanForm = useRef<any>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [total, setTotal] = useState(0);
  const [dataDetail, setDataDetail] = useState<DetailItineraryDataResponseType>();

  const onFetchListPlanData = async () => {
    try {
      const resListPlan = await getListPlanItinerary({ idItinerary: travelId });
      if (resListPlan.status === STATUS_CODE.SUCCESSFUL) {
        const dataRender = resListPlan.data?.data?.map((item, index) => {
          return {
            key: index + 1,
            id: item.id,
            tour_name: 'v' + item.plan_version + '_' + item?.title,
            departure_date: item.start_date,
            end_date: item.end_date,
            total_days: item?.total_days,
            number_of_days: moment(item.end_date).diff(moment(item.start_date), 'days') + 1,
            is_model_course: item.is_model_course,
            created_at: item.created_at,
            created_by: item.creator?.name,
            itinerary_id: item.itinerary_id,
            plan_version: item.plan_version,
            is_final: item.is_final,
            estimated_amount: item.estimated_amount,
          };
        });

        setTotal(resListPlan.data?.total);
        setDataSource(dataRender);
      } else {
        setDataSource([]);
      }
    } catch (error) {
      console.log('onFetchListPlanData error', error);
    }
  };

  const currentMaxPlanVersion = useMemo(() => {
    if (!dataSource?.length) return 0;
    const itemWithMaxPlanVersion = dataSource.reduce((prev, current) => {
      return prev.plan_version > current.plan_version ? prev : current;
    });
    return itemWithMaxPlanVersion?.plan_version;
  }, [dataSource]);

  const onFetchData = async () => {
    setIsLoading(true);
    try {
      const resDetail = await getDetailItinerary(travelId);
      if (resDetail.status === STATUS_CODE.SUCCESSFUL) {
        setDataDetail(resDetail.data?.data);
      } else {
        setDataDetail(null);
      }
    } catch (error) {
      console.log('error onFetchData', error);
    }

    setIsLoading(false);
  };

  useEffect(() => {
    if (travelId) {
      onFetchData();
      onFetchListPlanData();
    }
  }, [travelId]);

  const handleUpdateBookings = async (values) => {
    try {
      await refUpdateBookingForm?.current?.form?.validateFields();
      const payload = {
        ...values,
        id: travelId,
        departure_date: values?.departure_date
          ? moment(values?.departure_date).format('YYYY-MM-DD')
          : null,
        admin_id: values?.admin_id?.value,
        business_partner_id: values?.business_partner_id?.value ?? dataDetail?.businessPartner?.id,
        // status: 0,
      };

      const { status, error } = await updateItinerary(payload);

      if (status === STATUS_CODE.SUCCESSFUL) {
        refUpdateBookingForm?.current?.form?.resetFields();
        refUpdateBookingModal?.current?.close();
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        onFetchData();
      } else if (status === STATUS_CODE.INVALID) {
        const errors = error.data.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key][0];
            openNotificationFail(message);
          });
        }
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleCreatePlan = async (values) => {
    try {
      const payload = {
        title: values?.title?.split('v1_')?.[1],
        start_date: values?.time_period?.[0]?.format('YYYY-MM-DD'),
        end_date: values?.time_period?.[1]?.format('YYYY-MM-DD'),
        plan_description: values?.plan_description,
        idItinerary: travelId,
      };

      const { status, error, data } = await createPlanItinerary(payload);

      if (status === STATUS_CODE.CREATED) {
        refCreatePlanForm?.current?.form?.resetFields();
        refCreatePlanModal?.current?.close();
        openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
        // onFetchData();
        // onFetchListPlanData();

        // navigate to itinerary-management/:id/create/:planId
        history.push(`/itinerary-management/${travelId}/create/${data?.data?.id}`);
      } else if (status === STATUS_CODE.INVALID) {
        const errors = error.data.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key][0];
            openNotificationFail(message);
          });
        }
      } else {
        openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleDeleteBooking = async () => {
    try {
      const resDelete = await deleteItinerary(travelId);
      if (resDelete.status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.DELETE_SUCCESS);
        history.replace('/itinerary-management');
      } else {
        openNotificationFail(resDelete.error?.data?.errors?.id?.[0] || MESSAGE_ALERT.DELETE_FAILED);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.DELETE_FAILED);
    }
  };

  const handleChangeFinalPlan = async (idPlan: number) => {
    try {
      const resChangeFinalPlan = await changeFinalPlanItinerary(idPlan);
      if (resChangeFinalPlan.status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        onFetchListPlanData();
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
    }
  };

  return (
    <PageContainer>
      <Spin spinning={isLoading}>
        {dataDetail === null ? (
          // @ts-ignore: TS2786
          <Exception type="404" style={{ minHeight: 500, height: '100%' }} linkElement={Link} />
        ) : (
          <div>
            <HeaderInformation
              dataDetail={dataDetail}
              handleUpdate={() => refUpdateBookingModal?.current?.open()}
              handleDelete={handleDeleteBooking}
            />
            <div className="flex flex-col bg-white p-2 rounded-xl mt-11">
              <HeaderAction
                isDisableCreatePlane={currentMaxPlanVersion > 0}
                onCreatePlan={() => refCreatePlanModal?.current?.open()}
                dataDetail={dataDetail}
              />
              <TableList
                isLoading={isLoading}
                dataSource={dataSource}
                total={total}
                paramSearch={parameter}
                setParamSearch={setParameter}
                handleChangeFinalPlan={handleChangeFinalPlan}
              />
            </div>
            <ListDocument travelId={travelId} />
            <div className="flex items-center justify-center my-6">
              <div
                className="h-[40px] w-[162px] gap-x-2 border rounded border-[#DCDEE3] hover:opacity-90 bg-[#FFF] flex items-center justify-center text-[#FF3B30] cursor-pointer"
                onClick={() => history.goBack()}
              >
                <CancelXSVG /> キャンセル
              </div>
            </div>
          </div>
        )}
      </Spin>
      {dataDetail && (
        <BasicModal
          ref={refUpdateBookingModal}
          title="旅程編集"
          content={
            <CreateBookingForm
              ref={refUpdateBookingForm}
              onSubmit={handleUpdateBookings}
              initialData={dataDetail}
            />
          }
          onSubmit={() => {
            refUpdateBookingForm?.current?.form?.submit();
          }}
          className="!w-[600px] [&_.ant-modal-content]:!max-h-[90vh] [&_.ant-modal-content]:overflow-y-auto"
          buttonCloseTitle="キャンセル"
          styleTypeButtonSubmit="accept"
          buttonSubmitTitle="保存"
        />
      )}
      {dataDetail && (
        <BasicModal
          ref={refCreatePlanModal}
          title="旅程表作成"
          content={
            <CreatePlanForm
              ref={refCreatePlanForm}
              onSubmit={handleCreatePlan}
              initialData={dataDetail}
              currentMaxPlanVersion={currentMaxPlanVersion}
            />
          }
          onSubmit={() => {
            refCreatePlanForm?.current?.form?.submit();
          }}
          className="!w-[600px] [&_.ant-modal-content]:!max-h-[90vh] [&_.ant-modal-content]:overflow-y-auto"
          buttonCloseTitle="キャンセル"
          styleTypeButtonSubmit="accept"
          buttonSubmitTitle="次へ"
        />
      )}
    </PageContainer>
  );
};

export default DetailItineraryManagement;
