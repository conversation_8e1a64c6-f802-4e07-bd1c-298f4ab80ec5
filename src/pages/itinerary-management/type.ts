import { FormInstance } from 'antd';

// itinerary management type
export type BookingOverViewType = {
  id: number;
  controlnumber: string;
  status: { label: string; value: string | number };
  departureDate: string | Date;
  numberOfDays: number;
  itineraryName: string;
  manager: string;
  localAgent: string;
  localAgentPerson: string;
  inquiryDate: string | Date;
  quotationSubmissionDate: string | Date;
  estimatedAmount: number;
  memo: string;
  country_name?: string;
};

export type ItineraryDetailType = {
  id: number;
  title: string;
  period: {
    startDate: string;
    endDate: string;
  };
  modalCourse: boolean;
  estimatedAmount?: number;
  createdDate: string;
  author: string;
  comment: string;
  plan_version?: number;
};

//  estimate plan type

export type EstimateItemRef = {
  form?: FormInstance<any>;
  getFormData?: any;
};
export enum PlanEnum {
  touristSport = '観光地',
  hotel = '宿泊施設',
  bus = 'バス',
  hireCar = 'ハイヤー',
  bulletTrain = '新幹線',
  publicTransportation = '公共交通',
  delivery = '宅配',
  specialArrangement = '特別手配',
  guide = 'ガイド',
}

export type EstimateTableType = {
  id: number;
  plan_id: number;
  date: string;
  type: PlanEnum;
  title: string;
  amount: number;
  item_fee?: number; //fee of item has one price
  reference_id: number;
  reference_type: string;
  available_data: any[];
  additional_data: any[];
};
export enum StatusBookingEnum {
  ESTIMATED = 'ESTIMATED',
  PROCESSING = 'PROCESSING',
  BOOKED = 'BOOKED ',
  ACTIVE = 'ACTIVE',
  CANCELLED = 'CANCELLED',
  COMPLETED = 'COMPLETED',
}

export type StatusCountType = {
  [K in keyof typeof StatusBookingEnum]: StatusValue;
};

export enum StatusValue {
  ESTIMATED = 0,
  PROCESSING = 1,
  BOOKED = 2,
  ACTIVE = 3,
  CANCELLED = 4,
  COMPLETED = 5,
}
export enum StatusValueLabel {
  ESTIMATED = '見積中',
  PROCESSING = '手配中',
  BOOKED = '予約済 ',
  ACTIVE = '催行中',
  CANCELLED = 'キャンセル',
  COMPLETED = '完了',
}

export const TagPlan = [
  {
    name: PlanEnum.touristSport,
    color: '#3997C8',
  },
  {
    name: PlanEnum.hotel,
    color: '#FF7648',
  },
  {
    name: PlanEnum.bus,
    color: '#0095FF',
  },
  {
    name: PlanEnum.hireCar,
    color: '#FF7648',
  },
  {
    name: PlanEnum.bulletTrain,
    color: '#fff27a',
  },
  {
    name: PlanEnum.publicTransportation,
    color: '#25095a',
  },
  {
    name: PlanEnum.delivery,
    color: '#e23a08',
  },
  {
    name: PlanEnum.specialArrangement,
    color: '#99A3A4',
  },
  {
    name: PlanEnum.guide,
    color: '#99A3A4',
  },
];

export const statusOptions = [
  {
    label: StatusValueLabel.ESTIMATED,
    value: StatusValue.ESTIMATED,
  },
  {
    label: StatusValueLabel.PROCESSING,
    value: StatusValue.PROCESSING,
  },
  {
    label: StatusValueLabel.BOOKED,
    value: StatusValue.BOOKED,
  },
  {
    label: StatusValueLabel.ACTIVE,
    value: StatusValue.ACTIVE,
  },
  {
    label: StatusValueLabel.CANCELLED,
    value: StatusValue.CANCELLED,
  },
  {
    label: StatusValueLabel.COMPLETED,
    value: StatusValue.COMPLETED,
  },
];
