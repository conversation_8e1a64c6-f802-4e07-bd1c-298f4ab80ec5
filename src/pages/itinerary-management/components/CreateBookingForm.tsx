import type { DetailItineraryDataResponseType } from '@/apis/itineraries';
import { getListTravelTypeMaster } from '@/apis/travelTypeMaster';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import BasicNumbericInput from '@/components/Commons/BasicNumbericInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTextArea from '@/components/Commons/BasicTextArea';
import RequiredTag from '@/components/Commons/RequiredTag/RequiredTag';
import SelectLocalAgent from '@/components/Commons/SelectLocalAgent';
import SelectManager from '@/components/Commons/SelectManager';
import { TEXT_TITLE } from '@/constants/commonText';
import { paymentStatusTravel } from '@/constants/data';
import { rules } from '@/constants/rules';
import STATUS_CODE from '@/constants/statusCode';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import type { FormProps } from 'antd/es/form';
import moment from 'moment';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

type CreateBookingFormProps = {
  onSubmit?: (v: any) => void;
  initialData?: DetailItineraryDataResponseType;
};

export type CreateBookingFormRefType = {
  form: FormInstance<any>;
};

const CreateBookingForm = forwardRef<CreateBookingFormRefType, CreateBookingFormProps>(
  ({ onSubmit, initialData, ...others }: CreateBookingFormProps & FormProps, ref) => {
    const [form] = Form.useForm();
    const [optionTravelTypeMaster, setOptionTravelTypeMaster] = useState<any[]>([]);

    useImperativeHandle(ref, () => ({
      form,
    }));

    useEffect(() => {
      if (initialData) {
        form.setFieldsValue({
          tour_name: initialData?.tour_name,
          business_partner_id: {
            label: initialData?.businessPartner?.business_partner_name,
            value: initialData?.businessPartner?.id,
          },
          representative_name: initialData?.representative_name,
          departure_date: initialData?.departure_date ? moment(initialData?.departure_date) : null,
          admin_id: {
            label: initialData?.admin?.name,
            value: initialData?.admin?.id,
          },
          memo: initialData?.memo,
          adult_count: initialData?.adult_count,
          children_count: initialData?.children_count,
          infants_count: initialData?.infants_count,
          travel_type: initialData?.travelMaster?.id,
          payment_status: initialData?.payment_status,
        });
      }
    }, [initialData]);
    const handleGetTravelTypeMaster = async () => {
      const res = await getListTravelTypeMaster({
        limit: 'all',
        order: 'asc',
        sort: 'travel_code',
      });
      if (res.status === STATUS_CODE.SUCCESSFUL) {
        const options = res?.data?.data?.map((item) => {
          return {
            value: item.id,
            label: item.travel_name,
            disabled: item.status === 0,
          };
        });
        setOptionTravelTypeMaster(options);
      }
    };

    useEffect(() => {
      handleGetTravelTypeMaster();
    }, []);

    return (
      <div>
        <Form
          {...others}
          form={form}
          onFinish={onSubmit}
          className=""
          onFieldsChange={(v) => {
            if (v?.[0]?.name?.[0] === 'localAgent') {
              form.resetFields(['localAgentPerson']);
            }
          }}
        >
          <Form.Item name="tour_name" rules={rules?.requiredInput}>
            <BasicInput
              title={
                <div className="flex items-center gap-[6px]">
                  ツアー名
                  <RequiredTag isSymbol />
                </div>
              }
              placeholder="ツアー名を入力"
            />
          </Form.Item>
          <Form.Item name="business_partner_id" rules={rules.requiredSelect}>
            <SelectLocalAgent
              title={
                <div className="flex items-center gap-[6px]">
                  得意先
                  <RequiredTag isSymbol />
                </div>
              }
              className="!w-full"
            />
          </Form.Item>
          <Form.Item name="representative_name" rules={rules.requiredInput}>
            <BasicInput
              title={
                <div className="flex items-center gap-[6px]">
                  得意先の担当者
                  <RequiredTag isSymbol />
                </div>
              }
              placeholder="得意先の担当者を入力"
            />
          </Form.Item>
          <Form.Item
            name="departure_date"
            rules={[{ required: true, message: '※必須項目が未入力です。' }]}
          >
            <BasicDatePicker
              title={
                <div className="flex items-center gap-[6px]">
                  出発日
                  <RequiredTag isSymbol />
                </div>
              }
              className="!h-10"
            />
          </Form.Item>

          <Form.Item name="adult_count">
            <BasicNumbericInput
              maxLength={14}
              title={<div className="flex items-center gap-[6px]">人数（大人）</div>}
              placeholder="人数（大人）を入力"
            />
          </Form.Item>
          <Form.Item name="children_count">
            <BasicNumbericInput
              maxLength={14}
              title={<div className="flex items-center gap-[6px]">人数（子供）</div>}
              placeholder="人数（子供）を入力"
            />
          </Form.Item>
          <Form.Item name="infants_count">
            <BasicNumbericInput
              maxLength={14}
              title={<div className="flex items-center gap-[6px]">人数（幼児）</div>}
              placeholder="人数（幼児）を入力"
            />
          </Form.Item>
          <Form.Item name="travel_type">
            <BasicSelect
              className=""
              title={TEXT_TITLE.Trip_type}
              placeholder={'選択してください'}
              allowClear
              options={optionTravelTypeMaster}
            />
          </Form.Item>
          <Form.Item name="admin_id" rules={rules.requiredSelect}>
            <SelectManager
              title={
                <div className="flex items-center gap-[6px]">
                  {TEXT_TITLE.Salesperson}
                  <RequiredTag isSymbol />
                </div>
              }
              className="!w-full"
            />
          </Form.Item>
          <Form.Item name="payment_status" initialValue={0}>
            <BasicSelect
              className=""
              title="支払状況"
              placeholder={'支払状況'}
              allowClear
              options={paymentStatusTravel}
            />
          </Form.Item>
          <Form.Item name="memo">
            <BasicTextArea title="メモ" placeholder="メモ" maxLength={1000} />
          </Form.Item>
        </Form>
      </div>
    );
  },
);
export default CreateBookingForm;
