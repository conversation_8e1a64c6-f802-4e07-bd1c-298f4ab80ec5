import ArrowRight from '@/assets/imgs/svg/arrow-right-02.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import SearchSVG from '@/components/SVG/SearchSVG';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import type { FormInstance } from 'antd';
import { Form, Image } from 'antd';
import moment from 'moment';
import { useEffect } from 'react';
import { statusOptions } from '../type';
import SelectManager from '@/components/Commons/SelectManager';
import SelectLocalAgent from '@/components/Commons/SelectLocalAgent';

const HeaderInformation = ({
  onSearch,
  form,
}: {
  onSearch: () => void;
  form: FormInstance<any>;
}) => {
  const startDate = Form.useWatch('day_go_start', form);
  const endDate = Form.useWatch('day_go_end', form);

  useEffect(() => {
    if (moment(endDate).isBefore(startDate)) {
      form.setFieldValue('day_go_start', endDate);
    }
  }, [endDate]);

  useEffect(() => {
    if (endDate && moment(startDate).isAfter(endDate)) {
      form.setFieldValue('day_go_end', startDate);
    }
  }, [startDate]);

  return (
    <Form form={form} component={false}>
      <div className="grid grid-cols-4 md:grid-cols-8 gap-6 max-w-[1000px]">
        <div className="col-span-4 flex gap-1 items-center">
          <Form.Item className="!mb-0 flex-1" name="day_go_start">
            <BasicDatePicker title="出発日" className="!h-10" placeholder="すべて" />
          </Form.Item>
          <div className="">
            <div className="h-6" />
            <div className="flex items-center">
              <Image preview={false} src={ArrowRight} alt="arrow right" />
            </div>
          </div>

          <Form.Item className="!mb-0 !mt-[24px] flex-1" name="day_go_end">
            <BasicDatePicker title="" className="!h-10" placeholder="すべて" />
          </Form.Item>
        </div>
        <div className="col-span-2">
          <Form.Item className="!mb-0" name="status" initialValue={0}>
            <BasicSelect
              allowClear
              title={TEXT_TITLE.Status}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              options={statusOptions}
            />
          </Form.Item>
        </div>
        <div className="col-span-0 md:col-span-2 " />
        <div className="col-span-2">
          <Form.Item className="!mb-0" name="admin_id">
            <SelectManager
              allowClear
              title={TEXT_TITLE.Contact_person}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              className="!w-full"
            />
          </Form.Item>
        </div>
        <div className="col-span-2">
          <Form.Item className="!mb-0" name="business_partner_id">
            <SelectLocalAgent allowClear title={TEXT_TITLE.Customer} className="!w-full" />
          </Form.Item>
        </div>
        <div className="col-span-3">
          <Form.Item className="!mb-0" name="keyword">
            <BasicInput
              title="ツアー名または旅行ID"
              placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
            />
          </Form.Item>
        </div>
        <div className="col-span-1 flex items-end">
          <BasicButton
            icon={<SearchSVG colorSvg="white" />}
            className="flex items-center w-[120px]"
            styleType="accept"
            onClick={onSearch}
          >
            <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
          </BasicButton>
        </div>
      </div>
    </Form>
  );
};

export default HeaderInformation;
