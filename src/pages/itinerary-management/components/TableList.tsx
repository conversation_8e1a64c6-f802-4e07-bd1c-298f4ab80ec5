import type { BaseParams } from '@/@types/request';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTable from '@/components/Commons/BasicTable';
import { Image, Tooltip, type FormInstance } from 'antd';
import moment from 'moment';
import React from 'react';
import IconEye from '@/assets/imgs/common-icons/view-eye.svg';
import { statusOptions } from '../type';
import BasicButton from '@/components/Commons/BasicButton';
import { history } from 'umi';
import { paymentStatusTravel } from '@/constants/data';
import { updateStatusItinerary } from '@/apis/itineraries';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

interface TableListProps {
  isEditPage: boolean;
  isLoading: boolean;
  form: FormInstance<any>;
  dataSource: any[];
  total: number;
  paramSearch?: BaseParams;
  setParamSearch?: (val: BaseParams) => void;
  fetchData: () => void;
}

const TableList: React.FC<TableListProps> = ({
  isEditPage,
  isLoading,
  form,
  dataSource,
  total,
  paramSearch,
  setParamSearch,
  fetchData,
}) => {
  const heightScreen = window.innerHeight;

  const deviceType = useDeviceType();

  const changeStatusTravel = async ({ id, value }: { id: number; value: number }) => {
    const resUpdate = await updateStatusItinerary({ id, status: value });
    if (resUpdate.status === 200) {
      openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
      fetchData?.();
    } else {
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
    }
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'key',
      key: 'key',
      width: 65,
    },
    {
      title: '旅行ID',
      dataIndex: 'travel_id',
      key: 'travel_id',
      width: 90,
    },
    {
      title: 'ツアー名',
      dataIndex: 'tour_name',
      key: 'tour_name',
      width: 220,
      render: (_, { tour_name }) => {
        return (
          <Tooltip title={tour_name ?? '-'}>
            <span className="break-words block">{tour_name}</span>
          </Tooltip>
        );
      },
    },
    {
      title: 'ステータス',
      dataIndex: 'status',
      key: 'status',
      width: 130,
      render: (_, { status, id }) => {
        return (
          <BasicSelect
            options={statusOptions}
            defaultValue={status}
            onChange={(value) => changeStatusTravel({ id, value })}
          />
        );
      },
    },
    {
      title: '得意先',
      dataIndex: 'business_partner_name',
      key: 'business_partner_name',
      width: 120,
      render: (_, { business_partner_name }) => {
        return (
          <Tooltip title={business_partner_name ?? '-'}>
            <p className="truncate">{business_partner_name}</p>
          </Tooltip>
        );
      },
    },
    {
      title: '担当者',
      dataIndex: 'admin_name',
      key: 'admin_name',
      width: 120,
      render: (_, { admin_name }) => {
        return (
          <Tooltip title={admin_name ?? '-'}>
            <p className="truncate">{admin_name}</p>
          </Tooltip>
        );
      },
    },
    {
      title: '出発日',
      dataIndex: 'departure_date',
      key: 'departure_date',
      width: 100,
      render: (_, { departure_date }) => (
        <p>{departure_date ? moment(departure_date).format('YYYY/MM/DD') : ''}</p>
      ),
    },
    {
      title: '支払状況',
      dataIndex: 'payment_status',
      key: 'payment_status',
      width: 100,
      render: (_, { payment_status }) => {
        const paymentStatusTitle =
          payment_status || payment_status === 0
            ? paymentStatusTravel?.find((item) => item.value === payment_status)?.label
            : '';
        return (
          <Tooltip title={paymentStatusTitle ?? '-'}>
            <p className="truncate">{paymentStatusTitle}</p>
          </Tooltip>
        );
      },
    },
    {
      title: 'メモ',
      dataIndex: 'memo',
      key: 'memo',
      width: 120,
      render: (_, { memo }) => <p className="line-clamp-2">{memo}</p>,
    },
    {
      title: '',
      dataIndex: 'action',
      key: 'action',
      width: deviceType === DeviceTypeEnum.DESKTOP ? 140 : 60,
      fixed: 'right',
      render: (_, record) => (
        <BasicButton
          styleType="outline"
          className=" flex items-center justify-center !h-[24px] hover:border-main-color text-main-color"
          onClick={() => {
            history.push(`/itinerary-management/${record.id}`);
          }}
        >
          <Image preview={false} src={IconEye} width={16} height={16} />
          {deviceType === DeviceTypeEnum.DESKTOP ? (
            <p className="text-xs !ml-1">旅程詳細を見る</p>
          ) : null}
        </BasicButton>
      ),
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1400, y: heightScreen - 500 },
          loading: isLoading,
          columns: defaultColumns as any,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
        }}
        page={Number(paramSearch.page)}
        pageSize={Number(paramSearch.limit)}
        onChangePage={(p: number) => {
          setParamSearch({ ...paramSearch, page: p });
        }}
        total={total}
        onSelectPageSize={(v) => setParamSearch({ ...paramSearch, limit: v, page: 1 })}
      />
    </>
  );
};

export default TableList;
