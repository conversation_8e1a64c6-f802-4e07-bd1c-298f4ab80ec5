import { ResponseListPreviewPlan } from '@/apis/itineraries/plan';
import BasicInput from '@/components/Commons/BasicInput';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { useDeviceType } from '@/providers/DeviceProvider';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import React from 'react';

interface HeaderInformationProps {
  // Define the props for your component here
  dataDetail: ResponseListPreviewPlan;
  form: FormInstance<any>;
}

const HeaderInformation: React.FC<HeaderInformationProps> = ({ dataDetail, form }) => {
  const deviceType = useDeviceType();
  const noWhitespaceValidator = (_: any, value: string) => {
    if (!value || value.trim() === '') {
      return Promise.reject(new Error('この項目は必須です'));
    }
    return Promise.resolve();
  };

  return (
    <Form form={form} component={false}>
      <div className="p-4 rounded-lg bg-white">
        <p className="font-bold text-[#363840] mb-4">概要 </p>
        <div className="flex gap-4 flex-wrap">
          <div className={`${deviceType === DeviceTypeEnum.MOBILE ? 'w-full' : 'min-w-[360px]'}`}>
            <p className="mb-2 text-xs font-medium">
              ツアー名 <span className="text-[red]">*</span>
            </p>
            <div className="flex h-10 items-center">
              <p className="text-sm">{dataDetail?.overview?.title}</p>
            </div>
          </div>
          <div className={`${deviceType === DeviceTypeEnum.MOBILE ? 'w-full' : 'min-w-[200px]'}`}>
            <p className="mb-2 text-xs font-medium">
              フライト情報 <span className="text-[red]">*</span>
            </p>
            <Form.Item
              rules={[
                {
                  validator: noWhitespaceValidator,
                },
              ]}
              name="flight_info"
              className="!mb-0"
            >
              <BasicInput />
            </Form.Item>
          </div>
          <div className={`${deviceType === DeviceTypeEnum.MOBILE ? 'w-full' : 'min-w-[160px]'}`}>
            <p className="mb-2 text-xs font-medium">
              顧客数 <span className="text-[red]">*</span>
            </p>
            <Form.Item
              rules={[
                {
                  validator: noWhitespaceValidator,
                },
              ]}
              name="pax_unit"
              className="!mb-0"
            >
              <BasicInput />
            </Form.Item>
          </div>
          <div className={`${deviceType === DeviceTypeEnum.MOBILE ? 'w-full' : 'min-w-[360px]'}`}>
            <p className="mb-2 text-xs font-medium">
              TL/TG <span className="text-[red]">*</span>
            </p>
            <Form.Item
              rules={[
                {
                  validator: noWhitespaceValidator,
                },
              ]}
              name="tl_tg"
              className="!mb-0"
            >
              <BasicInput />
            </Form.Item>
          </div>
        </div>
      </div>
    </Form>
  );
};

export default HeaderInformation;
