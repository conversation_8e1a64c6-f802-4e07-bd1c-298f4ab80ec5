import BasicInput from '@/components/Commons/BasicInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTable from '@/components/Commons/BasicTable';
import BasicTextArea from '@/components/Commons/BasicTextArea';
import { optionPaymentOnHotel } from '@/constants/data';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { useDeviceType } from '@/providers/DeviceProvider';
import moment from 'moment';

const MealTable = ({
  dataSource,
  handleChangeDataSchedule,
  setOverView,
  overView,
}: {
  dataSource: any[];
  handleChangeDataSchedule: (key: number, keyChange: string, valueChange: any) => void;
  setOverView: (value: Record<string, string>) => void;
  overView: Record<string, string>;
}) => {
  const deviceType = useDeviceType();
  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'stt',
      key: 'stt',
      width: 60,
      render: (_, record, index) => {
        return <p className="text-center">{index + 1}</p>;
      },
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (_, record) => <>{record?.date ? moment(record?.date)?.format('YYYY/MM/DD') : ''}</>,
    },
    {
      title: 'Time',
      dataIndex: 'time',
      key: 'time',
      width: 80,
      render: (_, record) => <>{record.time?.[0] ? record.time?.[0] : ''}</>,
    },
    {
      title: 'Area ',
      dataIndex: 'meal_area',
      key: 'meal_area',
      width: 180,
      render: (_, record) => (
        <BasicInput
          name="meal_area"
          defaultValue={record.meal_area}
          onChange={(event) =>
            handleChangeDataSchedule(record.key, 'meal_area', event.target.value)
          }
        />
      ),
    },
    {
      title: 'Restaurant ',
      dataIndex: 'Restaurant',
      key: 'Restaurant',
      width: 180,
      render: (_, record) => (
        <div>
          <p className="text-base font-medium">{record.name_jp}</p>
          <p className="text-xs font-medium">{record.name_en}</p>
        </div>
      ),
    },
    {
      title: 'Address & TEL',
      dataIndex: 'AddressTEL',
      key: 'AddressTEL',
      render: (_, record) => (
        <div>
          <p className="text-base font-medium">{record.address}</p>
          <p className="text-xs font-medium">{record.phone_number}</p>
        </div>
      ),
    },
    {
      title: 'Payment',
      dataIndex: 'meal_payment',
      key: 'meal_payment',
      width: 100,
      render: (_, record) => (
        <BasicSelect
          className="!w-[100px]"
          defaultValue={record.meal_payment}
          options={optionPaymentOnHotel}
          onChange={(value) => {
            handleChangeDataSchedule(record.key, 'meal_payment', value);
          }}
        />
      ),
    },
    {
      title: 'Menu',
      dataIndex: 'meal_menu',
      key: 'meal_menu',
      width: 200,
      render: (_, record) => (
        <BasicInput
          name="meal_menu"
          defaultValue={record.meal_menu}
          onChange={(event) =>
            handleChangeDataSchedule(record.key, 'meal_menu', event.target.value)
          }
        />
      ),
    },
  ];

  return (
    <div>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1200 },
          columns: defaultColumns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
          size: deviceType === DeviceTypeEnum.MOBILE ? 'small' : 'middle',
        }}
        hasPagination={false}
      />
      <div className="h-10" />
      <BasicTextArea
        title={'Note'}
        // required
        name="meal_note"
        defaultValue={overView?.meal_note}
        onChange={(event) => setOverView({ ...overView, meal_note: event.target.value })}
      />
    </div>
  );
};

export default MealTable;
