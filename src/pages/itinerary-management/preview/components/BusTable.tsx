import BasicInput from '@/components/Commons/BasicInput';
import BasicTable from '@/components/Commons/BasicTable';
import BasicTextArea from '@/components/Commons/BasicTextArea';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { useDeviceType } from '@/providers/DeviceProvider';

const BusTable = ({
  dataSource,
  handleChangeDataSchedule,
  setOverView,
  overView,
}: {
  dataSource: any[];
  handleChangeDataSchedule: (key: number, keyChange: string, valueChange: any) => void;
  setOverView: (value: Record<string, string>) => void;
  overView: Record<string, string>;
}) => {
  const deviceType = useDeviceType();
  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'stt',
      key: 'stt',
      width: 60,
      render: (_, record, index) => {
        return <p className="text-center">{index + 1}</p>;
      },
    },
    {
      title: 'Bus',
      dataIndex: 'Bus',
      key: 'Bus',
      width: 180,
      render: (_, record) => (
        <div>
          <p className="text-base font-medium">{record.name_jp}</p>
          <p className="text-xs font-medium">{record.name_en}</p>
        </div>
      ),
    },
    {
      title: 'Period of use',
      dataIndex: 'bus_period_of_use',
      key: 'bus_period_of_use',
      width: 140,
      render: (_, record) => (
        <BasicInput
          name="bus_period_of_use"
          defaultValue={record.bus_period_of_use}
          onBlur={(event) =>
            handleChangeDataSchedule(record.key, 'bus_period_of_use', event.target.value)
          }
        />
      ),
    },
    {
      title: 'Address & TEL',
      dataIndex: 'AddressTEL',
      key: 'AddressTEL',
      width: 180,
      render: (_, record) => (
        <div>
          <p className="text-base font-medium">{record.address}</p>
          <p className="text-xs font-medium">{record.phone_number}</p>
        </div>
      ),
    },
    {
      title: 'Bus type',
      dataIndex: 'bus_type',
      key: 'bus_type',
      width: 140,
      render: (_, record) => (
        <BasicInput
          name="bus_type"
          defaultValue={record.bus_type}
          onBlur={(event) => handleChangeDataSchedule(record.key, 'bus_type', event.target.value)}
        />
      ),
    },
    {
      title: 'Driver’s infomation',
      dataIndex: 'bus_driver_info',
      key: 'bus_driver_info',
      width: 140,
      render: (_, record) => (
        <BasicInput
          name="bus_driver_info"
          defaultValue={record.bus_driver_info}
          onBlur={(event) =>
            handleChangeDataSchedule(record.key, 'bus_driver_info', event.target.value)
          }
        />
      ),
    },
  ];

  return (
    <div>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1300 },
          columns: defaultColumns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
          size: deviceType === DeviceTypeEnum.MOBILE ? 'small' : 'middle',
        }}
        hasPagination={false}
      />
      <div className="h-10" />
      <BasicTextArea
        title={'Note'}
        // required
        name="bus_note"
        defaultValue={overView?.bus_note}
        onChange={(event) => setOverView({ ...overView, bus_note: event.target.value })}
      />
    </div>
  );
};

export default BusTable;
