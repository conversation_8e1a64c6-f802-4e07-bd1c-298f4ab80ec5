import type { BaseParams } from '@/@types/request';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicCheckboxGroup from '@/components/Commons/BasicCheckboxGroup';
import BasicInput from '@/components/Commons/BasicInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTable from '@/components/Commons/BasicTable';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import { optionMealOnHotel } from '@/constants/data';
import { MenuOutlined } from '@ant-design/icons';
import { Form, FormInstance, Image, Popconfirm, TimePicker } from 'antd';
import { arrayMoveImmutable } from 'array-move';
import type { SortableContainerProps, SortEnd } from 'react-sortable-hoc';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';
import type { FieldData } from 'rc-field-form/lib/interface';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

const DragHandle = SortableHandle(() => <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />);

const SortableItem = SortableElement((props: React.HTMLAttributes<HTMLTableRowElement>) => (
  <tr {...props} />
));
const SortableBody: React.ReactNode = SortableContainer(
  (props: React.HTMLAttributes<HTMLTableSectionElement>) => <tbody {...props} />,
);

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  isLoading,
  dataSource,
  handleChangeCategory,
  setDataSchedule,
  form,
  handleChangeDataSchedule,
  dataSchedule,
  deleteItem,
}: {
  isLoading: boolean;
  dataSchedule: any[];
  dataSource: any[];
  setDataSchedule: (val: any) => void;
  deleteItem: (key: number) => void;
  handleChangeCategory: (key: number, type: string, value: boolean) => void;
  form: FormInstance<any>;
  handleChangeDataSchedule: (key: number, keyChange: string, valueChange: any) => void;
}) => {
  const deviceType = useDeviceType();

  const noWhitespaceValidator = (_: any, value: string) => {
    if (!value || value.trim() === '') {
      return Promise.reject(new Error('この項目は必須です'));
    }
    return Promise.resolve();
  };
  const onSortEnd = ({ oldIndex, newIndex }: SortEnd) => {
    if (oldIndex !== newIndex) {
      const newData = arrayMoveImmutable(dataSource.slice(), oldIndex, newIndex).filter(
        (el: any) => !!el,
      );
      const newDataSource = newData.map((item, index) => {
        return { ...item, sort_order: index + 1 };
      });
      const newSchedule = dataSchedule.map((item) => {
        const itemSource = newDataSource.find((x) => x.id === item.id);
        return itemSource ?? item;
      });
      setDataSchedule(newSchedule);
    }
  };

  const DraggableContainer = (props: SortableContainerProps) => (
    // @ts-ignore
    <SortableBody
      useDragHandle
      disableAutoscroll
      helperClass="row-dragging"
      onSortEnd={onSortEnd}
      {...props}
    />
  );

  const DraggableBodyRow: React.FC<any> = ({ className, style, ...restProps }) => {
    // function findIndex base on Table rowKey props and should always be a right array index
    const index = dataSource.findIndex((x) => x.id === restProps['data-row-key']);
    // @ts-ignore
    return <SortableItem index={index} {...restProps} />;
  };

  const handleListItemChange = (changeField: FieldData[]) => {
    const nameFieldChange = changeField?.[0]?.name?.[0];
    const keyChange = nameFieldChange.split('.')[0];
    const typeChange = nameFieldChange.split('.')[1];
    const valueChange = changeField?.[0]?.value;
    if (
      typeChange === 'is_checked_hotel' ||
      typeChange === 'is_checked_meal' ||
      typeChange === 'is_checked_facility' ||
      typeChange === 'is_checked_bus'
    ) {
      handleChangeCategory(keyChange, typeChange, valueChange?.length > 0);
    }
    if (typeChange === 'time') {
      handleChangeDataSchedule(keyChange, typeChange, [
        valueChange[0]?.format('HH:mm'),
        valueChange[1]?.format('HH:mm'),
      ]);
    }
  };

  const defaultColumns = [
    {
      title: '',
      dataIndex: 'sort',
      width: 40,
      className: 'drag-visible',
      // @ts-ignore
      render: () => <DragHandle />,
    },
    {
      title: '#',
      dataIndex: 'stt',
      key: 'stt',
      width: 40,
      render: (_, record, index) => {
        return <p className="text-center">{index + 1}</p>;
      },
    },
    {
      title: 'Hotel',
      dataIndex: 'is_checked_hotel',
      key: 'is_checked_hotel',
      width: 60,
      render: (_, record) => (
        <div className="flex justify-center ">
          <Form.Item className="!mb-0" name={`${record.key}.is_checked_hotel`}>
            <BasicCheckboxGroup options={[{ value: record?.id, label: '' }]} />
          </Form.Item>
        </div>
      ),
    },
    {
      title: 'Meal',
      dataIndex: 'is_checked_meal',
      key: 'is_checked_meal',
      width: 60,
      render: (_, record) => (
        <div className="flex justify-center ">
          <Form.Item className="!mb-0" name={`${record.key}.is_checked_meal`}>
            <BasicCheckboxGroup options={[{ value: record?.id, label: '' }]} />
          </Form.Item>
        </div>
      ),
    },
    {
      title: 'Facility',
      dataIndex: 'is_checked_facility',
      key: 'is_checked_facility',
      width: 70,
      render: (_, record) => (
        <div className="flex justify-center ">
          <Form.Item className="!mb-0" name={`${record.key}.is_checked_facility`}>
            <BasicCheckboxGroup options={[{ value: record?.id, label: '' }]} />
          </Form.Item>
        </div>
      ),
    },
    {
      title: 'Bus',
      dataIndex: 'is_checked_bus',
      key: 'is_checked_bus',
      width: 55,
      render: (_, record) => (
        <div className="flex justify-center ">
          <Form.Item className="!mb-0" name={`${record.key}.is_checked_bus`}>
            <BasicCheckboxGroup options={[{ value: record?.id, label: '' }]} />
          </Form.Item>
        </div>
      ),
    },
    {
      title: 'Time',
      dataIndex: 'time',
      key: 'time',
      width: 140,
      render: (_, record) => (
        <Form.Item
          rules={[
            {
              required: true,
              message: 'この項目は必須です',
            },
          ]}
          className="!mb-0"
          name={`${record.key}.time`}
        >
          <TimePicker.RangePicker format="HH:mm" />
        </Form.Item>
      ),
    },
    {
      title: 'English',
      dataIndex: 'name_en',
      key: 'name_en',
      width: 140,
      render: (_, record) => (
        <Form.Item
          rules={[
            {
              validator: noWhitespaceValidator,
            },
          ]}
          className="!mb-0"
          name={`${record.key}.name_en`}
        >
          <BasicInput
            onBlur={(event) =>
              handleChangeDataSchedule(record.key, 'hotel_area', event.target.value)
            }
          />
        </Form.Item>
      ),
    },
    {
      title: '日本語',
      dataIndex: 'name_jp',
      key: 'name_jp',
      width: 140,
      render: (_, record) => (
        <Form.Item
          rules={[
            {
              required: true,
              message: 'この項目は必須です',
            },
            {
              validator: noWhitespaceValidator,
            },
          ]}
          className="!mb-0"
          name={`${record.key}.name_jp`}
        >
          <BasicInput
            onBlur={(event) =>
              handleChangeDataSchedule(record.key, 'hotel_area', event.target.value)
            }
          />
        </Form.Item>
      ),
    },
    {
      title: 'Breakfast',
      dataIndex: 'breakfast',
      key: 'breakfast',
      width: 100,
      render: (_, record) => (
        <Form.Item className="!mb-0" name={`${record.key}.breakfast`}>
          <BasicSelect options={optionMealOnHotel} />
        </Form.Item>
      ),
    },
    {
      title: 'Lunch',
      dataIndex: 'lunch',
      key: 'lunch',
      width: 100,
      render: (_, record) => (
        <Form.Item className="!mb-0" name={`${record.key}.lunch`}>
          <BasicSelect options={optionMealOnHotel} />
        </Form.Item>
      ),
    },
    {
      title: 'Dinner',
      dataIndex: 'dinner',
      key: 'dinner',
      width: 100,
      render: (_, record) => (
        <Form.Item className="!mb-0" name={`${record.key}.dinner`}>
          <BasicSelect options={optionMealOnHotel} />
        </Form.Item>
      ),
    },
    {
      title: '',
      dataIndex: 'groupAction',
      key: 'groupAction',
      width: 80,
      render: (_, record) => (
        <div className="flex gap-4">
          <Popconfirm
            title={TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
            onConfirm={() => deleteItem(Number(record.key))}
            okText={TEXT_ACTION.DELETE}
            cancelText={TEXT_ACTION.CANCEL}
            placement="topRight"
          >
            <BasicButton
              styleType="danger"
              className="!h-[24px] w-[84px] hover:shadow-md hover:opacity-70 !border-none !shadow-none !bg-[transparent]"
            >
              <Image preview={false} src={IconDelete} width={15} height={14} />
              {deviceType !== DeviceTypeEnum.DESKTOP ? (
                <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
              ) : null}
            </BasicButton>
          </Popconfirm>
        </div>
      ),
    },
  ];

  return (
    <>
      <Form
        form={form}
        component={false}
        onFieldsChange={(changeField: FieldData[]) => handleListItemChange(changeField)}
      >
        <BasicTable
          className="!mt-0"
          tableProps={{
            components: {
              body: {
                wrapper: DraggableContainer,
                row: DraggableBodyRow,
              },
            },
            size: deviceType === DeviceTypeEnum.MOBILE ? 'small' : 'middle',
            scroll: { x: 1400, y: 600 },
            loading: isLoading,
            columns: defaultColumns,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            rowKey: 'id',
          }}
          hasPagination={false}
        />
      </Form>
    </>
  );
};

export default TableList;
