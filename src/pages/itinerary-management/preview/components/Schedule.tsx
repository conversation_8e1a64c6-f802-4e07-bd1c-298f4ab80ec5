import { ResponseListPreviewPlan } from '@/apis/itineraries/plan';
import BasicButton from '@/components/Commons/BasicButton';
import BasicTabsHandle from '@/components/Commons/BasicTabsHandle';
import { TEXT_ACTION } from '@/constants/commonText';
import { PlusOutlined } from '@ant-design/icons';
import { FormInstance } from 'antd';
import moment from 'moment';
import React, { useEffect, useMemo, useState } from 'react';
import ScheduleTable from './ScheduleTable';

type Props = {
  isLoading: boolean;
  setDataSchedule: (val: any) => void;
  dataSchedule: any[];
  onCreateItem: (date: string) => void;
  formTable: FormInstance<any>;
  dataDetail: ResponseListPreviewPlan;
  handleChangeCategory: (key: number, type: string, value: boolean) => void;
  handleChangeDataSchedule: (key: number, keyChange: string, valueChange: any) => void;
  deleteItem: (key: number) => void;
  keyActive: string;
  setKeyActive: (val: string) => void;
};
const Schedule: React.FC<Props> = ({
  setDataSchedule,
  isLoading,
  dataSchedule,
  onCreateItem,
  formTable,
  dataDetail,
  handleChangeCategory,
  handleChangeDataSchedule,
  deleteItem,
  keyActive,
  setKeyActive,
}) => {
  const [listDays, setListDays] = useState<{ label: string; key: string; children: JSX.Element }[]>(
    [],
  );

  useEffect(() => {
    if (dataDetail) {
      const startDate = new Date(dataDetail.overview.start_date);
      const endDate = new Date(dataDetail.overview.end_date);
      const items = [];
      setKeyActive(moment(startDate).format('YYYY-MM-DD'));
      for (let date = startDate; date <= endDate; date.setDate(date.getDate() + 1)) {
        items.push({
          label: moment(date).format('YYYY/MM/DD'),
          key: moment(date).format('YYYY-MM-DD'),
          children: <></>,
        });
      }
      setListDays(items);
    }
  }, [dataDetail]);

  const dataSourceWithDate = useMemo(() => {
    return dataSchedule
      .filter((item) => {
        return moment(item.date).format('YYYY-MM-DD') === keyActive;
      })
      .sort((a, b) => a.sort_order - b.sort_order);
  }, [dataSchedule, keyActive]);

  return (
    <div>
      <div className="flex gap-10">
        <BasicTabsHandle
          items={listDays}
          hideMoreButton
          onChange={setKeyActive}
          activeKey={keyActive}
        />
        <BasicButton
          icon={<PlusOutlined />}
          className="flex items-center justify-center"
          styleType="accept"
          onClick={() => {
            onCreateItem(keyActive);
          }}
        >
          1行追加
        </BasicButton>
      </div>
      <ScheduleTable
        isLoading={isLoading}
        dataSchedule={dataSchedule}
        dataSource={dataSourceWithDate}
        setDataSchedule={setDataSchedule}
        form={formTable}
        deleteItem={deleteItem}
        handleChangeCategory={handleChangeCategory}
        handleChangeDataSchedule={handleChangeDataSchedule}
      />
    </div>
  );
};

export default Schedule;
