import BasicInput from '@/components/Commons/BasicInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTable from '@/components/Commons/BasicTable';
import BasicTextArea from '@/components/Commons/BasicTextArea';
import { optionPaymentOnHotel } from '@/constants/data';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { useDeviceType } from '@/providers/DeviceProvider';
import { Form } from 'antd';
import moment from 'moment';

const HotelTable = ({
  dataSource,
  handleChangeDataSchedule,
  setOverView,
  overView,
}: {
  dataSource: any[];
  handleChangeDataSchedule: (key: number, keyChange: string, valueChange: any) => void;
  setOverView: (value: Record<string, string>) => void;
  overView: Record<string, string>;
}) => {
  const deviceType = useDeviceType();
  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'stt',
      key: 'stt',
      width: 60,
      render: (_, record, index) => {
        return <p className="text-center">{index + 1}</p>;
      },
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (_, record) => <>{record?.date ? moment(record?.date)?.format('YYYY/MM/DD') : ''}</>,
    },
    {
      title: 'Area',
      dataIndex: 'hotel_area',
      key: 'hotel_area',
      width: 180,
      render: (_, record) => (
        <>
          <BasicInput
            name="hotel_area"
            defaultValue={record.hotel_area}
            onBlur={(event) =>
              handleChangeDataSchedule(record.key, 'hotel_area', event.target.value)
            }
          />
        </>
      ),
    },
    {
      title: 'Hotel ',
      dataIndex: 'Name',
      key: 'Name',
      width: 180,
      render: (_, record) => (
        <div>
          <p className="text-base font-medium">{record.name_jp}</p>
          <p className="text-xs font-medium">{record.name_en}</p>
        </div>
      ),
    },
    {
      title: 'Address & TEL',
      dataIndex: 'AddressTEL',
      key: 'AddressTEL',
      width: 180,
      render: (_, record) => (
        <div>
          <p className="text-base font-medium">{record.address}</p>
          <p className="text-xs font-medium">{record.phone_number}</p>
        </div>
      ),
    },
    {
      title: 'Payment',
      dataIndex: 'hotel_payment',
      key: 'hotel_payment',
      width: 100,
      render: (_, record) => (
        <BasicSelect
          className="!w-[100px]"
          defaultValue={record.hotel_payment}
          options={optionPaymentOnHotel}
          onChange={(value) => {
            handleChangeDataSchedule(record.key, 'hotel_payment', value);
          }}
        />
      ),
    },
    {
      title: 'Room',
      dataIndex: 'hotel_breakfast',
      key: 'hotel_room',
      width: 140,
      render: (_, record) => (
        <BasicInput
          name="hotel_room"
          defaultValue={record.hotel_room}
          onBlur={(event) => handleChangeDataSchedule(record.key, 'hotel_room', event.target.value)}
        />
      ),
    },
    {
      title: 'Breakfast',
      dataIndex: 'hotel_breakfast',
      key: 'hotel_breakfast',
      width: 140,
      render: (_, record) => (
        <BasicInput
          name="hotel_breakfast"
          defaultValue={record.hotel_breakfast}
          onBlur={(event) =>
            handleChangeDataSchedule(record.key, 'hotel_breakfast', event.target.value)
          }
        />
      ),
    },
  ];

  return (
    <div>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1300 },
          columns: defaultColumns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
          size: deviceType === DeviceTypeEnum.MOBILE ? 'small' : 'middle',
        }}
        hasPagination={false}
      />
      <div className="h-10" />
      <BasicTextArea
        title={'Note'}
        // required
        name="hotel_note"
        defaultValue={overView?.hotel_note}
        onChange={(event) => setOverView({ ...overView, hotel_note: event.target.value })}
      />
    </div>
  );
};

export default HotelTable;
