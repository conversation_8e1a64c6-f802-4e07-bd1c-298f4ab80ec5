import BasicInput from '@/components/Commons/BasicInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTable from '@/components/Commons/BasicTable';
import BasicTextArea from '@/components/Commons/BasicTextArea';
import { optionPaymentOnHotel } from '@/constants/data';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { useDeviceType } from '@/providers/DeviceProvider';
import { Form } from 'antd';
import moment from 'moment';

const FacilityTable = ({
  dataSource,
  handleChangeDataSchedule,
  setOverView,
  overView,
}: {
  dataSource: any[];
  handleChangeDataSchedule: (key: number, keyChange: string, valueChange: any) => void;
  setOverView: (value: Record<string, string>) => void;
  overView: Record<string, string>;
}) => {
  const deviceType = useDeviceType();
  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'stt',
      key: 'stt',
      width: 60,
      render: (_, record, index) => {
        return <p className="text-center">{index + 1}</p>;
      },
    },
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: 120,
      render: (_, record) => <>{record?.date ? moment(record?.date)?.format('YYYY/MM/DD') : ''}</>,
    },
    {
      title: 'Time',
      dataIndex: 'time',
      key: 'time',
      width: 100,
      render: (_, record) => <>{record.time?.[0] ? record.time?.[0] : ''}</>,
    },
    {
      title: 'Area ',
      dataIndex: 'facility_area',
      key: 'facility_area',
      width: 180,
      render: (_, record) => (
        <BasicInput
          name="facility_area"
          defaultValue={record.facility_area}
          onBlur={(event) =>
            handleChangeDataSchedule(record.key, 'facility_area', event.target.value)
          }
        />
      ),
    },
    {
      title: 'Facility',
      dataIndex: 'Facility',
      key: 'Facility',
      width: 180,
      render: (_, record) => (
        <div>
          <p className="text-base font-medium">{record.name_jp}</p>
          <p className="text-xs font-medium">{record.name_en}</p>
        </div>
      ),
    },
    {
      title: 'Address & TEL',
      dataIndex: 'AddressTEL',
      key: 'AddressTEL',
      width: 240,
      render: (_, record) => (
        <div>
          <p className="text-base font-medium">{record.address}</p>
          <p className="text-xs font-medium">{record.phone_number}</p>
        </div>
      ),
    },
    {
      title: 'Payment',
      dataIndex: 'facility_payment',
      key: 'facility_payment',
      width: 120,
      render: (_, record) => (
        <BasicSelect
          className="!w-[100px]"
          defaultValue={record.facility_payment}
          options={optionPaymentOnHotel}
          onChange={(value) => {
            handleChangeDataSchedule(record.key, 'facility_payment', value);
          }}
        />
      ),
    },
    {
      title: 'Remarks',
      dataIndex: 'facility_remark',
      key: 'facility_remark',
      width: 180,
      render: (_, record) => (
        <BasicInput
          name="facility_remark"
          defaultValue={record.facility_remark}
          onBlur={(event) =>
            handleChangeDataSchedule(record.key, 'facility_remark', event.target.value)
          }
        />
      ),
    },
  ];

  return (
    <div>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1200 },
          columns: defaultColumns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
          size: deviceType === DeviceTypeEnum.MOBILE ? 'small' : 'middle',
        }}
        hasPagination={false}
      />
      <div className="h-10" />
      <BasicTextArea
        title={'Note'}
        // required
        name="facility_note"
        defaultValue={overView?.facility_note}
        onChange={(event) => setOverView({ ...overView, facility_note: event.target.value })}
      />
    </div>
  );
};

export default FacilityTable;
