import { exportPreviewPlan } from '@/apis/itineraries/plan';
import IconBack from '@/assets/imgs/common-icons/back-btn.svg';
import IconSave from '@/assets/imgs/common-icons/save-white-btn.svg';
import BasicButton from '@/components/Commons/BasicButton';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION } from '@/constants/commonText';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import STATUS_CODE from '@/constants/statusCode';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DownloadOutlined, DownOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Dropdown, Image } from 'antd';
import { useState } from 'react';
import { history } from 'umi';

const ListButtonAction = ({ isLoading, setIsLoading, onSave, planId }) => {
  const [typeExport, setTypeExport] = useState<{ label: string; key: string }>();
  const deviceType = useDeviceType();

  const exportPDF = async () => {
    try {
      setIsLoading(true);
      const res = await exportPreviewPlan({
        id: planId,
        // waiting for backend to update
        // params: { type: typeExport.key ?? 'international' },
        params: { type: 'international' },
      });
      if (res.status === STATUS_CODE.SUCCESSFUL) {
        setIsLoading(false);
        window.open(res.data?.data?.file_url);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
        setIsLoading(false);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      console.log('onHandleDelete error', error);
      setIsLoading(false);
    }
  };

  const items: { label: string; key: string }[] = [
    {
      label: 'インバウンド',
      key: '1',
    },
    {
      label: '国内',
      key: '2',
    },
  ];

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    const itemSelected = items.find((item) => item.key === e.key);
    if (itemSelected) setTypeExport(itemSelected);
  };

  const menuProps = {
    items,
    onClick: handleMenuClick,
  };

  return (
    <div
      className={`flex ${
        deviceType === DeviceTypeEnum.MOBILE ? 'flex-col' : ''
      } gap-6 justify-center mt-10`}
    >
      <BasicButton
        styleType="noneOutLine"
        disabled={isLoading}
        className={`${
          deviceType === DeviceTypeEnum.MOBILE ? 'w-full' : '!w-[84px]'
        }  !bg-white flex justify-center items-center`}
        onClick={() => {
          history.goBack();
        }}
      >
        <Image preview={false} src={IconBack} width={8} />
        <p className="ml-2">{TEXT_ACTION.RETURN}</p>
      </BasicButton>

      <Dropdown menu={menuProps}>
        <BasicButton
          disabled={isLoading}
          styleType="accept"
          className={`${
            deviceType === DeviceTypeEnum.MOBILE ? 'w-full' : '!w-[186px]'
          } flex justify-center items-center`}
        >
          <p>{typeExport?.label ?? '旅程表出力 (EXCEL)'} </p>
          <DownOutlined className="!text-sm" />
        </BasicButton>
      </Dropdown>
      <BasicButton
        icon={<DownloadOutlined style={{ color: 'white' }} />}
        disabled={isLoading}
        styleType="accept"
        className={`${
          deviceType === DeviceTypeEnum.MOBILE ? 'w-full' : '!w-[216px]'
        } flex justify-center items-center`}
        onClick={exportPDF}
      >
        {/* <Image preview={false} src={IconTickWhite} width={18} height={18} /> */}
        <p>Confirmation出力 (EXCEL) </p>
      </BasicButton>
      <BasicButton
        disabled={isLoading}
        styleType="accept"
        className={`${
          deviceType === DeviceTypeEnum.MOBILE ? 'w-full' : '!w-[84px]'
        } flex justify-center items-center`}
        onClick={onSave}
      >
        <Image preview={false} src={IconSave} width={18} height={18} />
        <p>{TEXT_ACTION.SAVE}</p>
      </BasicButton>
    </div>
  );
};

export default ListButtonAction;
