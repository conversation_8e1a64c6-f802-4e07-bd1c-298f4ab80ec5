import type { BaseParams } from '@/@types/request';
import type { BodyUpdatePreviewPlan, ResponseListPreviewPlan } from '@/apis/itineraries/plan';
import { getListPreviewPlan, updatePreviewPlan } from '@/apis/itineraries/plan';
import PageContainer from '@/components/Commons/Page/Container';
import STATUS_CODE from '@/constants/statusCode';
import { ITEM_PER_PAGE } from '@/utils/constants';
import type { TabsProps } from 'antd';
import { Badge, Form, Spin, Tabs } from 'antd';
import moment from 'moment';
import React, { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';
import HeaderInformation from './components/HeaderInformation';
import HotelTable from './components/HotelTable';
import ListButtonAction from './components/ListButtonAction';
import Schedule from './components/Schedule';
import MealTable from './components/MealTable';
import FacilityTable from './components/FacilityTable';
import BusTable from './components/BusTable';
import BasicTextArea from '@/components/Commons/BasicTextArea';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';

const ItineraryPreview: React.FC = () => {
  const paramUrl = useParams();
  const travelId = (paramUrl as { id: string })?.id;
  const planId = (paramUrl as { plan_id: string })?.plan_id;
  const [formHeader] = Form.useForm();
  const [formTable] = Form.useForm();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataDetail, setDataDetail] = useState<ResponseListPreviewPlan>();
  const [activeTab, setActiveTab] = useState<string>('Schedule');
  const [dataSchedule, setDataSchedule] = useState<any[]>([]);
  const [keyHotel, setKeyHotel] = useState<number[]>([]);
  const [keyMeal, setKeyMeal] = useState<number[]>([]);
  const [keyFacility, setKeyFacility] = useState<number[]>([]);
  const [keyBus, setKeyBus] = useState<number[]>([]);
  const [overView, setOverView] = useState<Record<string, string>>({});
  const [countDelete, setCountDelete] = useState<number>(0);
  const [listIdDelete, setListIdDelete] = useState<number[]>([]);
  const [keyActive, setKeyActive] = useState<string>('');

  const formatTime = (time: string) => {
    if (!time) return '';
    if (time.length < 6) return time;
    const [hours, minutes] = time.split(':');
    return `${hours}:${minutes}`;
  };

  const formatSchedule = (schedule: any[]) => {
    if (schedule) {
      const dataForm = {};
      const listHotelKey = [];
      const listMealKey = [];
      const listFacilityKey = [];
      const listBusKey = [];
      const data = schedule.map((item, index) => {
        dataForm[`${index + 1}.time`] = [
          item.start_time ? moment(item.start_time, 'HH:mm') : undefined,
          item.end_time ? moment(item.end_time, 'HH:mm') : undefined,
        ];
        dataForm[`${index + 1}.name_en`] = item.name_en;
        dataForm[`${index + 1}.name_jp`] = item.name_jp;
        dataForm[`${index + 1}.breakfast`] = item.breakfast;
        dataForm[`${index + 1}.lunch`] = item.lunch;
        dataForm[`${index + 1}.dinner`] = item.dinner;
        dataForm[`${index + 1}.is_checked_hotel`] = item.is_checked_hotel ? [item.id] : undefined;
        dataForm[`${index + 1}.is_checked_meal`] = item.is_checked_meal ? [item.id] : undefined;
        dataForm[`${index + 1}.is_checked_facility`] = item.is_checked_facility
          ? [item.id]
          : undefined;
        dataForm[`${index + 1}.is_checked_bus`] = item.is_checked_bus ? [item.id] : undefined;
        if (item.is_checked_hotel) {
          listHotelKey.push(index + 1);
        }
        if (item.is_checked_meal) {
          listMealKey.push(index + 1);
        }
        if (item.is_checked_facility) {
          listFacilityKey.push(index + 1);
        }
        if (item.is_checked_bus) {
          listBusKey.push(index + 1);
        }
        return {
          key: index + 1,
          index: index + 1,
          id: item.id,
          time: [item.start_time, item.end_time],
          is_checked_hotel: item.is_checked_hotel,
          is_checked_meal: item.is_checked_meal,
          is_checked_facility: item.is_checked_facility,
          is_checked_bus: item.is_checked_bus,
          name_en: item.name_en,
          name_jp: item.name_jp,
          breakfast: item.breakfast,
          lunch: item.lunch,
          dinner: item.dinner,
          date: item.date,
          address: item.address,
          phone_number: item.phone_number,
          hotel_area: item.hotel_area,
          hotel_payment: item.hotel_payment,
          hotel_room: item.hotel_room,
          hotel_breakfast: item.hotel_breakfast,
          meal_area: item.meal_area,
          meal_payment: item.meal_payment,
          meal_menu: item.meal_menu,
          facility_payment: item.facility_payment,
          facility_remark: item.facility_remark,
          facility_area: item.facility_area,
          bus_period_of_use: item.bus_period_of_use,
          bus_type: item.bus_type,
          bus_driver_info: item.bus_driver_info,
        };
      });
      setKeyHotel(listHotelKey);
      setKeyMeal(listMealKey);
      setKeyFacility(listFacilityKey);
      setKeyBus(listBusKey);
      setDataSchedule(data);
      formTable.setFieldsValue(dataForm);
    }
  };

  const onFetchDataPlanPreview = async () => {
    setIsLoading(true);
    setListIdDelete([]);
    setCountDelete(0);
    setActiveTab('Schedule');
    try {
      const resDetail = await getListPreviewPlan({
        id: planId,
        params: { limit: 10, page: 1, order: 'asc', sort: 'sort_order' },
      });
      if (resDetail.status === STATUS_CODE.SUCCESSFUL) {
        formHeader.setFieldsValue({
          flight_info: resDetail.data?.overview?.flight_info,
          pax_unit: resDetail.data?.overview?.pax_unit,
          title: resDetail.data?.overview?.title,
          tl_tg: resDetail.data?.overview?.tl_tg,
        });
        setKeyActive(moment(resDetail.data?.overview?.start_date).format('YYYY-MM-DD'));
        setOverView(resDetail.data?.overview);
        formatSchedule(resDetail.data?.schedule);
        setDataDetail(resDetail.data);
      } else {
        setDataDetail(null);
      }
    } catch (error) {
      console.log('error onFetchDataTravel', error);
    }

    setIsLoading(false);
  };

  const handleChangeCategory = (key: number, type: string, value: boolean) => {
    if (value) {
      switch (type) {
        case 'is_checked_hotel':
          setKeyHotel([...keyHotel, Number(key)]);
          break;
        case 'is_checked_meal':
          setKeyMeal([...keyMeal, Number(key)]);
          break;
        case 'is_checked_facility':
          setKeyFacility([...keyFacility, Number(key)]);
          break;
        case 'is_checked_bus':
          setKeyBus([...keyBus, Number(key)]);
          break;
        default:
          break;
      }
    } else {
      switch (type) {
        case 'is_checked_hotel':
          setKeyHotel([...keyHotel].filter((item) => item !== Number(key)));
          break;
        case 'is_checked_meal':
          setKeyMeal([...keyMeal].filter((item) => item !== Number(key)));
          break;
        case 'is_checked_facility':
          setKeyFacility([...keyFacility].filter((item) => item !== Number(key)));
          break;
        case 'is_checked_bus':
          setKeyBus([...keyBus].filter((item) => item !== Number(key)));
          break;
        default:
      }
    }
  };

  const onSave = async () => {
    try {
      setIsLoading(true);
      const valueHeader = await formHeader.validateFields();
      const resultValidateTable = await formTable.validateFields();
      console.log('resultValidateTable', resultValidateTable);
      const valueTable = formTable.getFieldsValue();
      const resultObj = Object.keys(valueTable).reduce((acc, key) => {
        const [index, property] = key.split('.');
        const value = valueTable[key];

        if (!acc[index]) {
          acc[index] = {};
        }

        acc[index][property] = value;

        return acc;
      }, {});

      const newData = [...dataSchedule];

      Object.keys(resultObj).forEach((key) => {
        const index = dataSchedule.findIndex((item) => item.key === Number(key));
        newData[index] = { ...dataSchedule[index], ...resultObj[key] };
      });

      // Filter data change
      const dataChange = newData.map((item) => {
        return {
          ...item,
        };
      });
      const valueSubmit: BodyUpdatePreviewPlan = {
        overview: {
          flight_info: valueHeader.flight_info,
          pax_unit: valueHeader.pax_unit,
          tl_tg: valueHeader.tl_tg,
          bus_note: overView?.bus_note,
          facility_note: overView?.facility_note,
          hotel_note: overView?.hotel_note,
          meal_note: overView?.meal_note,
          other_note: overView?.other_note,
        },
        schedule: dataChange.map((item, index) => {
          return {
            ...item,
            is_checked_meal: item.is_checked_meal ? true : false,
            is_checked_facility: item.is_checked_facility ? true : false,
            is_checked_bus: item.is_checked_bus ? true : false,
            is_checked_hotel: item.is_checked_hotel ? true : false,
            start_time:
              typeof item.time?.[0] === 'string'
                ? formatTime(item.time?.[0])
                : item.time?.[0]?.format('HH:mm'),
            end_time:
              typeof item.time?.[1] === 'string'
                ? formatTime(item.time?.[1])
                : item.time?.[1]?.format('HH:mm'),
            breakfast: item.breakfast ?? 0,
            lunch: item.lunch ?? 0,
            dinner: item.dinner ?? 0,
            sort_order: item.sort_order ?? index + 1,
          };
        }),
        schedule_delete: listIdDelete,
      };
      const res = await updatePreviewPlan({ id: planId, body: valueSubmit });
      if (res.status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        onFetchDataPlanPreview();
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      setActiveTab('Schedule');
      console.log('error', error);
    }
  };

  const handleChangeTab = (e) => {
    if (e !== 'Schedule') {
      setActiveTab(e.toString());
    } else {
      setActiveTab('Schedule');
    }
  };

  const onCreateItem = (date: string) => {
    setDataSchedule([
      ...dataSchedule,
      {
        id: 'schedule' + dataSchedule.length + 1,
        key: dataSchedule.length + 1,
        index: dataSchedule.length + 1,
        date,
      },
    ]);
  };

  const dataHotel = useMemo(() => {
    return [...dataSchedule]
      .filter((item) => keyHotel.includes(item.key))
      .map((item) => {
        return { ...item, id: `hotel_${item.id}` };
      });
  }, [keyHotel, dataSchedule]);

  const dataMeal = useMemo(() => {
    return [...dataSchedule]
      .filter((item) => keyMeal.includes(item.key))
      .map((item) => {
        return { ...item, id: `meal_${item.id}` };
      });
  }, [keyMeal, dataSchedule]);

  const dataFacility = useMemo(() => {
    return [...dataSchedule]
      .filter((item) => keyFacility.includes(item.key))
      .map((item) => {
        return { ...item, id: `facility_${item.id}` };
      });
  }, [keyFacility, dataSchedule]);

  const dataBus = useMemo(() => {
    return [...dataSchedule]
      .filter((item) => keyBus.includes(item.key))
      .map((item) => {
        return { ...item, id: `bus_${item.id}` };
      });
  }, [keyBus, dataSchedule]);

  useEffect(() => {
    if (planId) {
      onFetchDataPlanPreview();
    }
  }, [travelId, planId]);

  const handleChangeDataSchedule = (key: number, keyChange: string, value: any) => {
    const newData = [...dataSchedule];
    const index = dataSchedule.findIndex((item) => item.key === Number(key));
    if (index < 0) return;
    newData[index] = { ...newData[index], [keyChange]: value };
    setDataSchedule(newData);
  };

  const deleteItem = (key: number) => {
    const newData = [...dataSchedule].filter((item) => item.key !== key);
    const itemDelete = dataSchedule.find(
      (item) =>
        item.key === key && typeof item?.id === 'number' && !item?.id?.includes?.('schedule'),
    );
    if (itemDelete) {
      setListIdDelete([...listIdDelete, itemDelete.id]);
    }
    formTable.resetFields([
      `${key}.time`,
      `${key}.name_en`,
      `${key}.name_jp`,
      `${key}.is_checked_hotel`,
      `${key}.is_checked_meal`,
      `${key}.is_checked_facility`,
      `${key}.is_checked_bus`,
      `${key}.breakfast`,
      `${key}.lunch`,
      `${key}.dinner`,
    ]);
    setCountDelete(countDelete + 1);
    setDataSchedule(newData);
  };

  const items: TabsProps['items'] = [
    {
      key: 'Schedule',
      label: <Badge>Schedule</Badge>,
      children: (
        <Schedule
          formTable={formTable}
          isLoading={isLoading}
          keyActive={keyActive}
          setKeyActive={setKeyActive}
          dataSchedule={dataSchedule}
          setDataSchedule={setDataSchedule}
          onCreateItem={onCreateItem}
          dataDetail={dataDetail}
          deleteItem={deleteItem}
          handleChangeCategory={handleChangeCategory}
          handleChangeDataSchedule={handleChangeDataSchedule}
        />
      ),
    },
    {
      key: 'Hotel',
      label: 'Hotel',
      children: (
        <HotelTable
          dataSource={dataHotel}
          handleChangeDataSchedule={handleChangeDataSchedule}
          overView={overView}
          setOverView={setOverView}
        />
      ),
    },
    {
      key: 'Meal',
      label: 'Meal',
      children: (
        <MealTable
          dataSource={dataMeal}
          handleChangeDataSchedule={handleChangeDataSchedule}
          overView={overView}
          setOverView={setOverView}
        />
      ),
    },
    {
      key: 'Facility',
      label: 'Facility',
      children: (
        <FacilityTable
          dataSource={dataFacility}
          handleChangeDataSchedule={handleChangeDataSchedule}
          overView={overView}
          setOverView={setOverView}
        />
      ),
    },
    {
      key: 'Bus',
      label: 'Bus',
      children: (
        <BusTable
          dataSource={dataBus}
          handleChangeDataSchedule={handleChangeDataSchedule}
          overView={overView}
          setOverView={setOverView}
        />
      ),
    },
    {
      key: 'Other',
      label: 'Other',
      children: (
        <BasicTextArea
          title={'Note'}
          required
          name="note_other"
          defaultValue={overView?.other_note}
          onChange={(event) => setOverView({ ...overView, other_note: event.target.value })}
        />
      ),
    },
  ];

  return (
    <PageContainer>
      <Spin spinning={isLoading}>
        {dataDetail && <HeaderInformation form={formHeader} dataDetail={dataDetail} />}
        <div className="p-2 py-4 rounded-lg bg-white mt-6">
          <div className="px-4">
            <Tabs
              activeKey={activeTab}
              items={items}
              className="flex-1"
              onChange={handleChangeTab}
            />
          </div>
        </div>
        <ListButtonAction
          isLoading={isLoading}
          setIsLoading={setIsLoading}
          onSave={onSave}
          planId={planId}
        />
      </Spin>
    </PageContainer>
  );
};

export default ItineraryPreview;
