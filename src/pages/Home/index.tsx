import BasicButton from '@/components/Commons/BasicButton';
import BasicTable from '@/components/Commons/BasicTable';
import EditableCell from '@/components/Commons/EditableCell';
import PageContainer from '@/components/Commons/Page/Container';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import { Form, Image, Popconfirm } from 'antd';
import { useEffect, useState } from 'react';
import { FieldData } from 'rc-field-form/lib/interface';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import { ITEM_PER_PAGE } from '@/utils/constants';

const options = [
  { value: '1', label: '売上' },
  { value: '2', label: '入金' },
  { value: '3', label: '支払' },
  { value: '4', label: '仕入' },
];

const dataExample = Array.from({ length: 12 }, (_, i) => ({
  key: i,
  managementNumber: `管理番号 ${i}`,
  accommodation: `宿泊施設 ${i}`,
  rank: `ランク ${i}`,
  prefecture: `都道府県 ${i}`,
  city: `市区町村 ${i}`,
  parking: `駐車場 ${i}`,
  parkingFee: `駐車料金 ${i}`,
  memo: `メモ ${i}`,
  priceList: `料金表 ${i}`,
  cancellationFee: `取消料情報 ${i}`,
  active: i % 2 === 0,
}));

type DataType = {
  key: number;
  managementNumber: string;
  accommodation: string;
  rank: string;
  prefecture: string;
  city: string;
  parking: string;
  parkingFee: string;
  memo: string;
  priceList: string;
  cancellationFee: string;
};

// eslint-disable-next-line max-lines-per-function
const HomePage = (props) => {
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(Number(ITEM_PER_PAGE));
  const [total, setTotal] = useState(dataExample.length);
  const [listItemChange, setListItemChange] = useState<string[]>([]);
  const [isEditPage, setIsEditPage] = useState(false);
  const [dataSource, setDataSource] = useState<DataType[]>(dataExample);
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleListItemChange = (changeField: FieldData[]) => {
    const nameFieldChange = changeField?.[0]?.name?.[0];
    const keyChange = nameFieldChange.split('.')[0];
    setListItemChange([...listItemChange, keyChange]);
  };

  const removeListItemChange = () => {
    setListItemChange([]);
  };

  const confirmDeleteRow = (key) => {
    setIsLoading(true);
    setDataSource([...dataSource].filter((item) => item.key !== key));
    setListItemChange(listItemChange.filter((item) => item !== key));
    setIsLoading(false);
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'key',
      key: 'key',
      width: 60,
    },
    {
      title: '',
      dataIndex: 'active',
      editable: true,
      key: 'active',
      formType: 'switch',
      width: 60,
    },
    { title: '課税区分コード', dataIndex: 'rank', key: 'rank', editable: true, formType: 'input' },
    {
      title: '課税区分名',
      dataIndex: 'prefecture',
      key: 'prefecture',
      editable: true,
      formType: 'input',
    },
    { title: '税区分略称', dataIndex: 'city', key: 'city', editable: true, formType: 'input' },
    { title: '税率(%)', dataIndex: 'parking', key: 'parking', formType: 'input', editable: true },
    {
      title: '対象',
      dataIndex: 'parkingFee',
      key: 'parkingFee',
      editable: true,
      formType: 'checkbox',
      width: 400,
      options: options,
    },
    {
      title: '',
      dataIndex: 'deleteItem',
      key: 'deleteItem',
      width: 80,
      render: (_, record) => (
        <Popconfirm
          title={TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
          onConfirm={() => confirmDeleteRow(record.key)}
          okText={TEXT_ACTION.DELETE}
          cancelText={TEXT_ACTION.CANCEL}
        >
          <BasicButton styleType="danger" className="!h-[24px] w-[76px]">
            <Image preview={false} src={IconDelete} width={12} height={13} />
            <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
          </BasicButton>
        </Popconfirm>
      ),
    },
  ];

  const columns = defaultColumns.map((col) => {
    if (!col.editable && !isEditPage) {
      return { ...col, width: col.width || 250 };
    }
    return {
      ...col,
      width: col.width || 250,
      onCell: (record: { [key: string]: string }) => ({
        record,
        editable: col.editable,
        isEditPage,
        dataIndex: col.dataIndex,
        title: col.title,
        formType: col.formType,
        options: col.options,
        form,
      }),
    };
  });

  const save = async () => {
    setIsLoading(true);
    try {
      const values = await form.validateFields();
      const resultObj = Object.keys(values).reduce((acc, key) => {
        const [index, property] = key.split('.');
        const value = values[key];

        if (!acc[index]) {
          acc[index] = {};
        }

        acc[index][property] = value;

        return acc;
      }, {});

      const newData = [...dataSource];

      Object.keys(resultObj).forEach((key) => {
        const index = dataSource.findIndex((item) => item.key === Number(key));
        newData[index] = { ...dataSource[index], ...resultObj[key] };
      });

      setDataSource([...newData]);
      // Filter data change
      const dataChange = newData.filter((item) => listItemChange.includes(item.key.toString()));
      console.log({ dataChange });
      // => Call API here
      setTimeout(() => {
        setIsLoading(false);
        setIsEditPage(false);
      }, 2000);
      // Reset
      removeListItemChange();
    } catch (errInfo) {
      setIsLoading(false);
      console.log('Save failed:', errInfo);
    }
  };

  useEffect(() => {
    dataSource.forEach((item) => {
      Object.keys(item).forEach((key) => {
        const keyForm = `${item.key}.${key}`;
        form.setFieldValue(keyForm, item[key]);
      });
    });
    removeListItemChange();
  }, [dataSource]);

  return (
    <>
      <PageContainer useBackground>
        <h1>BASE COMPONENT</h1>
        <div className="space-y-[17px]">
          <BasicButton
            loading={isLoading}
            onClick={() => (isEditPage ? save() : setIsEditPage(true))}
          >
            {isEditPage ? 'SAVE' : 'EDIT'}
          </BasicButton>
        </div>

        <Form
          form={form}
          component={false}
          onFieldsChange={(changeField: FieldData[]) => handleListItemChange(changeField)}
        >
          <BasicTable
            className="!mt-0"
            tableProps={{
              scroll: { x: 1500 },
              loading: isLoading,
              components: {
                body: {
                  cell: EditableCell,
                },
              },
              columns,
              dataSource: dataSource,
              bordered: false,
              pagination: false,
              rowKey: 'id',
            }}
            page={page}
            pageSize={pageSize}
            onChangePage={(p: number) => setPage(p)}
            total={total}
            onSelectPageSize={(v) => setPageSize(v)}
          />
        </Form>
      </PageContainer>
    </>
  );
};

export default HomePage;
