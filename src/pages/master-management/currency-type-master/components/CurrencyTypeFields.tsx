import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { targetOptions } from '@/constants/data';
import { rules } from '@/constants/rules';

export const CurrencyTypeFields: FormItemDetail[] = [
  {
    type: 'input',
    name: 'currency_type_code',
    title: '金種コード',
    isRequired: true,
    placeholder: '金種コード',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'currency_type_name',
    title: '金種名',
    isRequired: true,
    placeholder: '金種名',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'business_partner_type',
    title: '取引先区分',
    // isRequired: true,
    placeholder: '取引先区分',
    // rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'account_code',
    title: '口座コード',
    // isRequired: true,
    placeholder: '口座コード',
    // rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'checkbox',
    name: 'target',
    title: '対象',
    isRequired: true,
    rules: rules.isRequired,
    options: targetOptions,
    colSpan: 24,
  },
  {
    type: 'switch',
    name: 'status',
    title: 'アクティブ',
    defaultCheckedSwitch: true,
    colSpan: 6,
  },
];
