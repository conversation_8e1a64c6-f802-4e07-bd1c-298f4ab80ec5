import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { CurrencyTypeFields } from './CurrencyTypeFields';

interface Props {
  onSubmit: (values) => void;
}

export type CurrencyTypeFormRef = {
  form: FormInstance<any>;
};

const FormCreateCurrencyType = forwardRef<CurrencyTypeFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));

  useEffect(() => {
    form?.setFieldsValue({
      status: true,
    });
  }, []);

  return (
    <Form form={form} onFinish={onSubmit}>
      {CurrencyTypeFields?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default FormCreateCurrencyType;
