import type { SelectItemType } from '@/@types/common';
import type { BaseParams } from '@/@types/request';
import { getListAggregationItem } from '@/apis/master/AggregationItem';
import type { SubjectMasterDetailType } from '@/apis/master/subjectMaster';
import {
  createOrUpdateSubjectMaster,
  createSubjectMaster,
  exportSubjectMaster,
  getListSubjectMaster,
} from '@/apis/master/subjectMaster';
import { getListTaxCategory } from '@/apis/master/taxCategory';
import type { ImportCsvCommonRef } from '@/components/CommonModalImportCsv';
import CommonModalImportCsv, { EMenuType } from '@/components/CommonModalImportCsv';
import BasicButton from '@/components/Commons/BasicButton';
import type { BasicFormModalRef } from '@/components/Commons/BasicFormModal';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import BasicInputSearch from '@/components/Commons/BasicInputSearch';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE, TEXT_WARNING } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { ITEM_PER_PAGE } from '@/utils/constants';
import {
  DownloadOutlined,
  PlusOutlined,
  SaveOutlined,
  SearchOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { Form } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { useHistory } from 'umi';
import type { ItemMasterFormRef } from './components/FormCreateItemMaster';
import FormCreateItemMaster from './components/FormCreateItemMaster';
import TableItemMaster from './components/TableItemMaster';
import { useUrlSearchParams } from 'use-url-search-params';

const initSearchParams: BaseParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};

const ItemMaster = () => {
  const [form] = Form.useForm();
  const [formSearch] = Form.useForm();

  const refModalCreateMaster = useRef<BasicFormModalRef>(null);
  const refFormAddNewItem = useRef<ItemMasterFormRef>(null);
  const refImportCsv = useRef<ImportCsvCommonRef>(null);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<SubjectMasterDetailType[]>([]);
  const [isEditPage, setIsEditPage] = useState(false);
  const [listItemChange, setListItemChange] = useState<string[]>([]);
  const [total, setTotal] = useState(0);
  // const [paramSearch, setParamSearch] = useState<BaseParams>(initSearchParams);
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);

  const [optionTaxCategory, setOptionTaxCategory] = useState<SelectItemType[]>([]);
  const [optionAggregationMaster, setOptionAggregationMaster] = useState<SelectItemType[]>([]);

  const history = useHistory();

  useEffect(() => {
    const unblock =
      listItemChange.length > 0 ? history.block(TEXT_WARNING.leave_page_and_lose_changes) : null;

    return () => {
      if (unblock) {
        unblock();
      }
    };
  }, [listItemChange, history]);

  const onFetchData = async () => {
    setIsLoading(true);
    try {
      const payload: BaseParams = {
        ...parameter,
        sort: 'subject_code',
        order: 'asc',
      };
      const resGetListMaster = await getListSubjectMaster(payload);
      if (resGetListMaster.status === STATUS_CODE.SUCCESSFUL) {
        const resData = resGetListMaster.data;
        setDataSource(
          resData.data.map((item, index) => ({
            ...item,
            key: item?.id,
            numericalOrder: (Number(parameter?.page) - 1) * Number(parameter?.limit) + index + 1,
            summary_item_id: Number(item?.summary_item_id),
            tax_category_id: Number(item?.tax_category_id),
            unit_price: item?.unit_price?.toString(),
            target: item.target ? JSON.parse(item.target as string) : [],
          })),
        );
        setTotal(resData.total);
      }
    } catch (error) {
      console.log('error', error);
    }
    setIsLoading(false);
  };

  const handleGetTaxCategoryMaster = async () => {
    const res = await getListTaxCategory({ limit: 'all', sort: 'tax_category_code', order: 'asc' });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      const options = res?.data?.data?.map((item) => {
        return {
          value: item.id,
          label: item.tax_category_abbreviation,
          disabled: item.status === 0,
        };
      });
      setOptionTaxCategory(options);
    }
  };

  const handleGetAggregationMaster = async () => {
    const res = await getListAggregationItem({
      limit: 'all',
      sort: 'summary_item_code',
      order: 'asc',
    });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      const options = res?.data?.data?.map((item) => {
        return { value: item.id, label: item.summary_item_name, disabled: item.status === 0 };
      });
      setOptionAggregationMaster(options);
    }
  };

  useEffect(() => {
    handleGetTaxCategoryMaster();
    handleGetAggregationMaster();
  }, []);

  useEffect(() => {
    onFetchData();
    if (parameter?.keyword) {
      formSearch.setFieldValue('keyword', parameter?.keyword);
    }
  }, [parameter?.keyword, parameter?.limit, parameter?.page]);

  const removeListItemChange = () => {
    setListItemChange([]);
  };

  useEffect(() => {
    dataSource.forEach((item) => {
      Object.keys(item).forEach((key) => {
        const keyForm = `${item.key}.${key}`;
        form.setFieldValue(keyForm, item[key]);
      });
    });
    removeListItemChange();
  }, [dataSource]);

  const onImportCsv = () => {
    if (refImportCsv) {
      refImportCsv.current.open();
    }
  };

  const onExportCsv = async () => {
    const cloneParams = { ...parameter };
    const responseExport = await exportSubjectMaster({ ...cloneParams, limit: 'all', page: 1 });
    if (responseExport.status === STATUS_CODE.SUCCESSFUL) {
      const urlCsv = responseExport?.data?.file_link;
      if (urlCsv) {
        const a = document.createElement('a');
        a.href = urlCsv;
        a.download = 'csv_item_master.csv';
        a.click();
        window.URL.revokeObjectURL(urlCsv);
      }
    }
  };

  const saveEdit = async () => {
    setIsLoading(true);
    try {
      if (listItemChange.length === 0) {
        setIsLoading(false);
        setIsEditPage(false);
        return;
      }
      const values = await form.validateFields();
      const resultObj = Object.keys(values).reduce((acc, key) => {
        const [index, property] = key.split('.');
        const value = values[key];

        if (!acc[index]) {
          acc[index] = {};
        }

        acc[index][property] = value;

        return acc;
      }, {});

      const newData = [...dataSource];

      Object.keys(resultObj).forEach((key) => {
        const index = dataSource.findIndex((item) => item.key === Number(key));
        newData[index] = { ...dataSource[index], ...resultObj[key] };
      });

      // Filter data change
      const dataChange = newData.filter((item) => listItemChange.includes(item.key.toString()));

      const dataUpdate = dataChange.map((item) => {
        delete item.created_at;
        delete item.updated_at;
        item.status = item.status ? 1 : 0;
        return item;
      });
      // => Call API here
      const responseUpdate = await createOrUpdateSubjectMaster(dataUpdate);
      if (
        responseUpdate.status === STATUS_CODE.SUCCESSFUL ||
        responseUpdate.status === STATUS_CODE.CREATED
      ) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        onFetchData();
        setIsEditPage(false);
        setIsLoading(false);

        // Reset when update successfully
        removeListItemChange();
      } else if (responseUpdate.status === STATUS_CODE.INVALID) {
        const errors = responseUpdate?.error?.data?.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const nameField = key.split('.').slice(1).join('.');
            const message = errors[key];
            form.setFields([
              {
                name: nameField,
                errors: message,
              },
            ]);
          });
        }
        setIsLoading(false);
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
        setIsLoading(false);
      }
    } catch (errInfo) {
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      setIsLoading(false);
      console.log('Save failed:', errInfo);
    }
  };

  const handleSearchByValue = (value: string) => {
    setParameter({ ...parameter, page: 1, keyword: value });
  };

  const handleAddNew = async (values) => {
    try {
      const responseCreate = await createSubjectMaster({
        ...values,
        target: JSON.stringify(values.target),
        status: values?.status ? 1 : 0,
      });
      if (
        responseCreate.status === STATUS_CODE.SUCCESSFUL ||
        responseCreate.status === STATUS_CODE.CREATED
      ) {
        openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
        refModalCreateMaster?.current?.close();
        setParameter(initSearchParams);
        onFetchData();
        refFormAddNewItem?.current?.form?.resetFields();
      } else if (responseCreate.status === STATUS_CODE.INVALID) {
        const errors = responseCreate?.error?.data?.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key];
            refFormAddNewItem?.current?.form.setFields([
              {
                name: key,
                errors: message,
              },
            ]);
          });
        }
      } else {
        openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const openModal = () => {
    refFormAddNewItem?.current?.form?.resetFields();
    if (refModalCreateMaster) {
      refFormAddNewItem?.current?.form?.setFieldsValue({
        status: true,
      });
      refModalCreateMaster.current.open();
    }
  };

  return (
    <PageContainer>
      <div className="flex items-end justify-between">
        <Form
          form={formSearch}
          onFinish={(value) => {
            handleSearchByValue(value?.keyword);
          }}
        >
          <div className="flex items-end gap-x-[24px]">
            <Form.Item name={'keyword'} noStyle>
              <BasicInputSearch
                style={{
                  width: '280px',
                  height: '40px',
                }}
                title={TEXT_TITLE.keyword}
                placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
                onSearch={(value) => {
                  handleSearchByValue(value);
                }}
              />
            </Form.Item>
            <BasicButton
              onClick={() => formSearch.submit()}
              icon={<SearchOutlined />}
              styleType="accept"
              className="flex items-center min-w-[120px]"
            >
              {TEXT_ACTION.SEARCH}
            </BasicButton>
          </div>
        </Form>

        <div className="flex gap-x-[12px]">
          <BasicButton
            icon={<SaveOutlined style={{ color: '#225DE0' }} />}
            className="flex items-center !text-[#225DE0]"
            styleType="noneOutLine"
            onClick={() => (isEditPage ? saveEdit() : setIsEditPage(true))}
          >
            {isEditPage ? TEXT_ACTION.SAVE : TEXT_ACTION.EDIT}
          </BasicButton>
          <BasicButton
            icon={<UploadOutlined style={{ color: '#EC980C' }} />}
            className="flex items-center !text-[#EC980C]"
            styleType="noneOutLine"
            onClick={onImportCsv}
          >
            {TEXT_ACTION.CSV_Import}
          </BasicButton>
          <BasicButton
            icon={<DownloadOutlined style={{ color: '#3997C8' }} />}
            className="flex items-center !text-main-color"
            styleType="noneOutLine"
            onClick={onExportCsv}
          >
            {TEXT_ACTION.CSV_Export}
          </BasicButton>
          <BasicButton
            icon={<PlusOutlined />}
            className="flex items-center"
            styleType="accept"
            onClick={openModal}
          >
            {TEXT_ACTION.CREATE_NEW}
          </BasicButton>
        </div>
      </div>
      <div className="p-2 rounded-xl bg-white mt-6">
        <TableItemMaster
          total={total}
          isEditPage={isEditPage}
          isLoading={isLoading}
          form={form}
          dataSource={dataSource}
          setDataSource={setDataSource}
          listItemChange={listItemChange}
          setIsLoading={setIsLoading}
          setListItemChange={setListItemChange}
          paramSearch={parameter}
          setParamSearch={setParameter}
          listAggregationMasterCode={optionAggregationMaster}
          listTaxMasterCode={optionTaxCategory}
          refetch={onFetchData}
        />
      </div>
      <BasicFormModal
        ref={refModalCreateMaster}
        content={
          <FormCreateItemMaster
            ref={refFormAddNewItem}
            onSubmit={handleAddNew}
            listAggregationMasterCode={optionAggregationMaster}
            listTaxMasterCode={optionTaxCategory}
          />
        }
        title="科目作成"
        buttonCloseTitle={TEXT_ACTION.CANCEL}
        buttonSubmitTitle={TEXT_ACTION.SAVE}
        buttonSubmitStyle={{ backgroundColor: '#3997C8' }}
        onSubmit={() => refFormAddNewItem?.current?.form?.submit()}
        isValidate={true}
      />
      <CommonModalImportCsv
        confirm={onFetchData}
        ref={refImportCsv}
        type={EMenuType['item-master']}
      />
    </PageContainer>
  );
};

export default ItemMaster;
