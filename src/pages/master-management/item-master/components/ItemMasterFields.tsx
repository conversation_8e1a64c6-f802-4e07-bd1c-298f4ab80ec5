import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { rules } from '@/constants/rules';
import { targetOptions } from '@/constants/data';

export const ItemMasterFields: FormItemDetail[] = [
  {
    type: 'input',
    name: 'subject_code',
    title: '科目コード',
    isRequired: true,
    placeholder: '科目コード',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'select',
    name: 'summary_item_id',
    title: '集計科目コード',
    isRequired: true,
    placeholder: '集計科目コード',
    rules: rules.requiredSelect,
    options: [],
    colSpan: 24,
  },
  {
    type: 'select',
    name: 'tax_category_id',
    title: '課税区分コード',
    isRequired: true,
    placeholder: '課税区分コード',
    rules: rules.requiredSelect,
    options: [],
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'subject_name',
    title: '科目名',
    isRequired: true,
    placeholder: '科目名',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'subject_name_en',
    title: '科目名(英字)',
    isRequired: true,
    placeholder: '科目名(英字)',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'unit_price',
    title: '単価',
    isRequired: true,
    placeholder: '単価',
    rules: rules.integerOnly,
    colSpan: 24,
  },
  {
    type: 'checkbox',
    name: 'target',
    title: '対象',
    isRequired: true,
    rules: rules.isRequired,
    options: targetOptions,
    colSpan: 24,
  },
  {
    type: 'switch',
    name: 'status',
    title: 'アクティブ',
    defaultCheckedSwitch: true,
    colSpan: 6,
  },
];
