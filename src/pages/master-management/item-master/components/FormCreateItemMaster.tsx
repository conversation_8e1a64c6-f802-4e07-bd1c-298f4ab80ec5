import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { ItemMasterFields } from './ItemMasterFields';
import type { SelectItemType } from '@/@types/common';

interface PropsUmi {
  listTaxMasterCode?: SelectItemType[];
  listAggregationMasterCode?: SelectItemType[];
}

interface Props extends PropsUmi {
  onSubmit: (values) => void;
}

export type ItemMasterFormRef = {
  form: FormInstance<any>;
};

const FormCreateItemMaster = forwardRef<ItemMasterFormRef, Props>(
  ({ onSubmit, listTaxMasterCode, listAggregationMasterCode }, ref) => {
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
      form,
    }));

    useEffect(() => {
      form?.setFieldsValue({
        status: true,
      });
    }, []);

    const newItemFields = ItemMasterFields?.map((item) => {
      if (item?.name === 'summary_item_id') {
        return {
          ...item,
          options: listAggregationMasterCode,
        };
      } else if (item?.name === 'tax_category_id') {
        return {
          ...item,
          options: listTaxMasterCode,
        };
      } else {
        return item;
      }
    });

    return (
      <Form form={form} onFinish={onSubmit}>
        {newItemFields?.map((item) => (
          <CommonRenderFormField key={item?.name} field={item} />
        ))}
      </Form>
    );
  },
);

export default FormCreateItemMaster;
