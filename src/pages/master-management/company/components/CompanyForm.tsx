import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance, FormProps } from 'antd';
import { Form } from 'antd';
import { forwardRef, useImperativeHandle } from 'react';
import { CompanyFormItem } from './companyFieldsForm';

export type CompanyFormRef = {
  form?: FormInstance<any>;
};

interface Props {
  formProps?: FormProps;
}

const CompanyForm = forwardRef<CompanyFormRef, Props>(({ formProps }, ref) => {
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} autoComplete="off" {...formProps}>
      {CompanyFormItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default CompanyForm;
