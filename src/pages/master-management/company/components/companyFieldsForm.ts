import { UploadImageType } from '@/components/Commons/BasicUploads';
import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { rules } from '@/constants/rules';

export const CompanyFormItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'company_name',
    title: '社名',
    isRequired: true,
    placeholder: '社名',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'company_name_en',
    title: '社名（英字）',
    isRequired: true,
    placeholder: '社名（英字）',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'business_name',
    title: '事業所名',
    isRequired: true,
    placeholder: '事業所名',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'business_name_en',
    title: '事業所名（英字）',
    isRequired: true,
    placeholder: '事業所名（英字）',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'postal_code',
    title: '郵便番号',
    isRequired: true,
    placeholder: '郵便番号',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'address',
    title: '住所',
    isRequired: true,
    placeholder: '住所',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'address_en',
    title: '住所（英字)',
    isRequired: true,
    placeholder: '住所（英字)',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'corporate_number',
    title: '法人番号',
    isRequired: true,
    placeholder: '法人番号',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'qualified_invoice_issuer_number',
    title: '適格請求書発行事業所番号',
    isRequired: true,
    placeholder: '適格請求書発行事業所番号',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'phone_number',
    title: 'TEL',
    isRequired: true,
    placeholder: 'TEL',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'fax',
    title: 'FAX',
    isRequired: true,
    placeholder: 'FAX',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'email',
    title: 'メールアドレス',
    isRequired: true,
    placeholder: 'メールアドレス',
    rules: rules.isEmail,
  },
  {
    type: 'input',
    name: 'travel_agency_license_number',
    title: '登録旅行業許可番号',
    isRequired: true,
    placeholder: '登録旅行業許可番号',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'manager',
    title: '取扱管理者',
    isRequired: true,
    placeholder: '取扱管理者',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'invoice_print_account_number',
    title: '請求書印字口座番号1',
    isRequired: true,
    placeholder: '請求書印字口座番号1',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'invoice_print_account_name',
    title: '請求書印字口座名義1',
    isRequired: true,
    placeholder: '請求書印字口座名義1',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'invoice_print_account_number_2',
    title: '請求書印字口座番号2',
    isRequired: true,
    placeholder: '請求書印字口座番号2',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'invoice_print_account_name_2',
    title: '請求書印字口座名義2',
    isRequired: true,
    placeholder: '請求書印字口座名義2',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'company_name_beneficiary',
    title: '社名（ACCOUNT NAME）',
    isRequired: true,
    placeholder: '社名（ACCOUNT NAME）',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'swift_code',
    title: 'SWIFT CODE',
    isRequired: true,
    placeholder: 'SWIFT CODE',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'financial_institution_name',
    title: '金融機関名称（PAYING BANK AND BRANCH）',
    isRequired: true,
    placeholder: '金融機関名称（PAYING BANK AND BRANCH）',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'financial_institution_address',
    title: '金融機関住所（BANK ADDRESS）',
    isRequired: true,
    placeholder: '金融機関住所（BANK ADRESS）',
    rules: rules.requiredInput,
  },
  {
    type: 'input',
    name: 'account_number',
    title: '口座番号（ACCOUNT NO）',
    isRequired: true,
    placeholder: '口座番号（ACCOUNT NO）',
    rules: rules.requiredInput,
  },
  {
    type: 'images',
    name: 'company_seal_estimate_invoice', //company_seal_estimate_invoice
    title: '社印（見積・請求）',
    maxLength: 4,
    uploadType: UploadImageType.company_master,
  },
  {
    type: 'images',
    name: 'logo',
    title: 'ロゴ',
    maxLength: 1,
    uploadType: UploadImageType.company_master,
  },
];
