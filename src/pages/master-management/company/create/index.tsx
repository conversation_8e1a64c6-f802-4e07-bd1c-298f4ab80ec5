import { createCompany, getCompany } from '@/apis/master/companyMaster';
import BasicButton from '@/components/Commons/BasicButton';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import IconCancelRed from '@/assets/imgs/common-icons/cancel-red.svg';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { SaveOutlined } from '@ant-design/icons';
import { Col, Row, Spin, Image } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { history } from 'umi';
import type { CompanyFormRef } from '../components/CompanyForm';
import CompanyForm from '../components/CompanyForm';

const CreateCompany = () => {
  const refFormCreate = useRef<CompanyFormRef>();

  const [isloading, setIsLoading] = useState<boolean>(false);

  const [formChange, setFormChange] = useState(false);

  const hasData = !!refFormCreate?.current?.form?.getFieldValue('company_name');

  useEffect(() => {
    const unblock = formChange ? history.block(TEXT_WARNING.leave_page_and_lose_changes) : null;

    return () => {
      if (unblock) {
        unblock();
      }
    };
  }, [formChange, history]);

  const fetchDetailCompany = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getCompany();
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dataFields = {
          ...data.data,
          corporate_number: data?.data?.corporate_number.toString(),
          qualified_invoice_issuer_number: data?.data?.qualified_invoice_issuer_number.toString(),
          travel_agency_license_number: data?.data?.travel_agency_license_number.toString(),
          invoice_print_account_number: data?.data?.invoice_print_account_number.toString(),
          logo: data?.data?.logo ? [data?.data?.logo] : [],
          company_seal_estimate_invoice: data?.data?.company_seal_estimate_invoice ?? [],
        };

        refFormCreate?.current?.form?.setFieldsValue(dataFields);
      }
      setIsLoading(false);
    } catch (error) {
      openNotificationFail('新規作成に失敗しました');
      console.log('error', error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchDetailCompany();
  }, []);

  const onSubmit = async () => {
    if (refFormCreate) {
      await refFormCreate.current.form.validateFields();
      setIsLoading(true);
      setFormChange(false);
      try {
        const values = refFormCreate.current.form.getFieldsValue();
        const payload = {
          ...values,
          logo: values?.logo?.[0],
        };

        const { status, error } = await createCompany(payload);
        if (status === STATUS_CODE.CREATED) {
          openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
          fetchDetailCompany();
        } else if (status === STATUS_CODE.INVALID) {
          const errors = error.data.errors;
          if (errors && Object.keys(errors).length) {
            Object.keys(errors).forEach((key) => {
              const message = errors[key];
              refFormCreate?.current?.form.setFields([
                {
                  name: key,
                  errors: message,
                },
              ]);
            });
          }
        } else {
          openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
        }
        setIsLoading(false);
      } catch (error) {
        openNotificationFail('新規作成に失敗しました');
        console.log('error', error);
        setIsLoading(false);
      }
    }
  };
  return (
    <Spin spinning={isloading}>
      <div className="p-9">
        <CompanyForm
          ref={refFormCreate}
          formProps={{
            onValuesChange: () => {
              setFormChange(true);
            },
          }}
        />
        <Row>
          <Col span={12}>
            <div className="flex items-center gap-x-[20px]">
              <BasicButton
                styleType="noneOutLine"
                onClick={() => history.goBack()}
                className="flex-1"
              >
                <div className="text-[#FF3B30] flex items-center justify-center">
                  <Image
                    preview={false}
                    src={IconCancelRed}
                    width={18}
                    height={18}
                    className="mt-[1.5px] mr-[2px]"
                  />
                  <p>{TEXT_ACTION.CANCEL}</p>
                </div>
              </BasicButton>
              <BasicButton
                className="flex items-center flex-1"
                styleType="accept"
                onClick={onSubmit}
                icon={<SaveOutlined width={16} height={16} />}
              >
                {hasData ? TEXT_ACTION.SAVE : TEXT_ACTION.CREATE_NEW}
              </BasicButton>
            </div>
          </Col>
        </Row>
      </div>
    </Spin>
  );
};

export default CreateCompany;
