import type { BusinessPartnerType } from '@/@types/businessPartner';
import { getDetailBusinessPartner, updateBusinessPartner } from '@/apis/businessPartner';
import BasicButton from '@/components/Commons/BasicButton';
import { openNotificationApprove, openNotificationFail } from '@/components/Notification';
import type { BusinessPartnerFormRef } from '@/components/pages/BusinessPartner/BusinessPartnerForm';
import BusinessPartnerForm from '@/components/pages/BusinessPartner/BusinessPartnerForm';
import { checkBusinessPartnerFormChange } from '@/components/pages/BusinessPartner/utils';
import { ROUTE_NAME } from '@/constants';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { SaveOutlined } from '@ant-design/icons';
import { Col, Row, Spin } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router';
import { history } from 'umi';

const EditBusinessPartner = () => {
  const { id } = useParams() as { id: string };

  const refFormEdit = useRef<BusinessPartnerFormRef>();

  const [isloading, setIsLoading] = useState<boolean>(false);
  const [formChange, setFormChange] = useState(false);

  const [detail, setDetail] = useState<BusinessPartnerType>();

  useEffect(() => {
    const unblock = formChange ? history.block(TEXT_WARNING.leave_page_and_lose_changes) : null;

    return () => {
      if (unblock) {
        unblock();
      }
    };
  }, [formChange, history]);

  const fetchDataDetail = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getDetailBusinessPartner(Number(id));
      const rsData = data.data;
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dataRs = {
          ...rsData,
          qualified_invoice_issuer_number:
            rsData?.qualified_invoice_issuer_number?.toString() ?? '',
          r_rate: rsData?.r_rate?.toString() ?? '',
          target: JSON.parse(rsData?.target as string) ?? undefined,
        };
        refFormEdit.current.form.setFieldsValue(dataRs);
        setDetail(dataRs as any);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  useEffect(() => {
    fetchDataDetail();
  }, [id]);

  const onSubmit = async () => {
    if (refFormEdit) {
      await refFormEdit.current.form.validateFields();
      setIsLoading(true);
      setFormChange(false);
      try {
        const values = refFormEdit.current.form.getFieldsValue();
        const payload = {
          ...values,
          target: JSON.stringify(values.target),
        };
        const { status, error } = await updateBusinessPartner(Number(id), payload);
        if (status === STATUS_CODE.SUCCESSFUL) {
          openNotificationApprove(MESSAGE_ALERT.EDIT_SUCCESS);
          history.push(`${ROUTE_NAME.BUSINESS_PARTNER_DETAIL}/${id}`);
        } else if (status === STATUS_CODE.INVALID) {
          const errors = error.data.errors;
          if (errors && Object.keys(errors).length) {
            Object.keys(errors).forEach((key) => {
              const message = errors[key];
              refFormEdit?.current?.form.setFields([
                {
                  name: key,
                  errors: message,
                },
              ]);
            });
          }
        } else {
          openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
        }

        setIsLoading(false);
      } catch (error) {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
        console.log('error', error);
        setIsLoading(false);
      }
    }
  };
  return (
    <Spin spinning={isloading}>
      <div className="p-9">
        <BusinessPartnerForm
          ref={refFormEdit}
          formProps={{
            onValuesChange: (changedValues, values) => {
              delete detail.created_at;
              delete detail.deleted_at;
              delete detail.deleted_by;
              delete detail.updated_at;
              delete detail.updated_by;
              delete detail.id;
              delete detail.created_by;
              delete detail.status;

              const isChanged = checkBusinessPartnerFormChange(detail, values);

              setFormChange(isChanged);
            },
          }}
        />
        <Row>
          <Col span={12}>
            <div className="flex items-center gap-x-[20px]">
              <BasicButton
                styleType="noneOutLine"
                onClick={() => history.goBack()}
                className="flex-1"
              >
                {TEXT_ACTION.CANCEL}
              </BasicButton>
              <BasicButton
                className="flex items-center flex-1"
                styleType="accept"
                onClick={onSubmit}
                icon={<SaveOutlined width={16} height={16} />}
              >
                {TEXT_ACTION.SAVE}
              </BasicButton>
            </div>
          </Col>
        </Row>
      </div>
    </Spin>
  );
};

export default EditBusinessPartner;
