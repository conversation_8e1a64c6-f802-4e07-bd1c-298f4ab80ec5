import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import type { BaseParams } from '@/@types/request';
import {
  exportBusinessPartner,
  getListBusinessPartner,
  patchChangeStatusBusinessPartner,
} from '@/apis/businessPartner';
import type { ImportCsvCommonRef } from '@/components/CommonModalImportCsv';
import CommonModalImportCsv, { EMenuType } from '@/components/CommonModalImportCsv';
import BasicButton from '@/components/Commons/BasicButton';
import BasicInputSearch from '@/components/Commons/BasicInputSearch';
import BasicTable from '@/components/Commons/BasicTable';
import { openNotificationFail } from '@/components/Notification';

import { ROUTE_NAME } from '@/constants';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE, TEXT_WARNING } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { ITEM_PER_PAGE } from '@/utils/constants';
import {
  DownloadOutlined,
  EyeOutlined,
  PlusOutlined,
  SearchOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { PageContainer } from '@ant-design/pro-layout';
import { Form, Switch, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, { useEffect, useRef, useState } from 'react';
import { history } from 'umi';
import { useUrlSearchParams } from 'use-url-search-params';

const initSearchParams: BaseParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};

const BusinessPartnerPage = () => {
  const refImportCsv = useRef<ImportCsvCommonRef>(null);

  const [dataSource, setDataSource] = useState<BusinessPartnerDetailType[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  // const [searchValues, setSearchValues] = useState<{ keyword: string }>();
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);

  const [form] = Form.useForm();

  const heightScreen = window.innerHeight;

  const onImportCsv = () => {
    if (refImportCsv) {
      refImportCsv.current.open();
    }
  };

  const handleGetList = async () => {
    setIsLoading(true);
    try {
      const params = {
        ...parameter,
      };
      const { data, status } = await getListBusinessPartner(params);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dataRs = data?.data?.map((item, index) => ({
          ...item,
          key: (Number(parameter?.page) - 1) * Number(parameter?.limit) + index + 1,
        }));
        setDataSource(dataRs);
        setTotal(data?.total);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  const onChangeStatus = async (id: number, checked: boolean) => {
    const statusChecked = checked ? 1 : 0;
    const { status: statusResponse } = await patchChangeStatusBusinessPartner(id, statusChecked);
    if (statusResponse === STATUS_CODE.SUCCESSFUL) {
      const newDts = dataSource?.map((item) =>
        item?.id === id
          ? {
              ...item,
              status: statusChecked,
            }
          : item,
      );
      setDataSource(newDts as BusinessPartnerDetailType[]);
    }
  };

  useEffect(() => {
    handleGetList();

    if (parameter?.keyword) {
      form.setFieldValue('keyword', parameter?.keyword);
    }
  }, [parameter?.keyword, parameter?.limit, parameter?.page]);

  const columns: ColumnsType<BusinessPartnerDetailType> = [
    {
      title: <div className="font-[400] text-[14px] leading-5">#</div>,
      dataIndex: 'key',
      key: 'key',
      width: '5%',
    },
    {
      title: null,
      dataIndex: 'switch',
      key: 'switch',
      width: '6%',
      render: (_value, { status, id }) => (
        <Switch checked={status === 1} onChange={(checked) => onChangeStatus(id, checked)} />
      ),
    },
    {
      title: (
        <div className="font-[400] text-[14px] leading-5">{TEXT_TITLE.Business_Partner_Code}</div>
      ),
      dataIndex: 'business_partner_code',
      key: 'business_partner_code',
      width: '13%',
      render: (_value, { business_partner_code }) => (
        <Tooltip title={business_partner_code}>
          <div className="text-base leading-5  text-[#24262B]">{business_partner_code}</div>
        </Tooltip>
      ),
    },
    {
      title: (
        <div className="font-[400] text-[14px] leading-5">{TEXT_TITLE.Business_Partner_Name}</div>
      ),
      dataIndex: 'business_partner_name',
      key: 'business_partner_name',
      width: '13%',
      render: (_value, { business_partner_name }) => (
        <Tooltip title={business_partner_name}>
          <div className="text-base leading-5  text-[#24262B]">{business_partner_name}</div>
        </Tooltip>
      ),
    },
    {
      title: <div className="font-[400] text-[14px] leading-5">{TEXT_TITLE.Address}</div>,
      dataIndex: 'address',
      key: 'address',
      width: '13%',
      render: (_value, { address }) => (
        <Tooltip title={address}>
          <div className="text-base leading-5  text-[#24262B]">{address}</div>
        </Tooltip>
      ),
    },
    {
      title: <div className="font-[400] text-[14px] leading-5">{TEXT_TITLE.Phone_Number}</div>,
      dataIndex: 'phone_number',
      key: 'phone_number',
      width: '13%',
      render: (_value, { phone_number }) => (
        <Tooltip title={phone_number}>
          <div className="text-base leading-5  text-[#24262B]">{phone_number}</div>
        </Tooltip>
      ),
    },
    {
      title: <div className="font-[400] text-[14px] leading-5">{TEXT_TITLE.Memo}</div>,
      dataIndex: 'memo',
      key: 'memo',
      render: (_value, { memo }) => (
        <Tooltip title={memo}>
          <div className="text-base leading-5  text-[#24262B] truncate">{memo}</div>
        </Tooltip>
      ),
    },
    {
      title: null,
      dataIndex: 'view',
      key: 'view',
      width: '14%',
      render: (_value, { id }) => (
        <div className="text-base leading-5 text-[#24262B]">
          <BasicButton
            className="flex items-center"
            icon={<EyeOutlined />}
            onClick={() => {
              history.push(`${ROUTE_NAME.BUSINESS_PARTNER_DETAIL}/${id}`);
            }}
          >
            取引先詳細
          </BasicButton>
        </div>
      ),
    },
  ];

  const onSearch = (value) => {
    const search = {
      ...parameter,
      keyword: value,
      page: 1,
    };
    setParameter(search);
  };

  const handleExportCsv = async () => {
    try {
      setIsLoading(true);
      const { data, status } = await exportBusinessPartner({
        ...parameter,
        limit: 'all',
      });
      setIsLoading(false);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const urlCsv = data?.file_link;
        if (urlCsv) {
          const a = document.createElement('a');
          a.href = urlCsv;
          a.download = 'csv_business_partner.csv';
          a.click();
          window.URL.revokeObjectURL(urlCsv);
        }
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  return (
    <PageContainer>
      <div className="flex items-end justify-between">
        <Form
          form={form}
          onFinish={(value) => {
            onSearch(value?.keyword);
          }}
        >
          <div className="flex items-end gap-x-[24px]">
            <Form.Item name={'keyword'} noStyle>
              <BasicInputSearch
                style={{
                  width: '280px',
                  height: '40px',
                }}
                title={TEXT_TITLE.Business_Code_Business_name}
                placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
                onSearch={(value) => {
                  onSearch(value);
                }}
              />
            </Form.Item>
            <BasicButton
              onClick={() => form.submit()}
              icon={<SearchOutlined />}
              styleType="accept"
              className="flex items-center min-w-[120px]"
            >
              {TEXT_ACTION.SEARCH}
            </BasicButton>
          </div>
        </Form>

        <div className="flex gap-x-[12px]">
          <BasicButton
            icon={<UploadOutlined style={{ color: '#EC980C' }} />}
            className="flex items-center !text-[#EC980C]"
            styleType="noneOutLine"
            onClick={onImportCsv}
          >
            {TEXT_ACTION.CSV_Import}
          </BasicButton>
          <BasicButton
            icon={<DownloadOutlined style={{ color: '#3997C8' }} />}
            className="flex items-center !text-main-color"
            styleType="noneOutLine"
            onClick={handleExportCsv}
          >
            {TEXT_ACTION.CSV_Export}
          </BasicButton>
          <BasicButton
            icon={<PlusOutlined />}
            onClick={() => history.push(ROUTE_NAME.BUSINESS_PARTNER_CREATE)}
            className="flex items-center"
            styleType="accept"
          >
            {TEXT_ACTION.CREATE_NEW}
          </BasicButton>
        </div>
      </div>
      <div className="p-2 rounded-xl bg-white mt-6">
        <BasicTable
          tableProps={{
            scroll: { x: 1200, y: heightScreen - 380 },
            columns,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            rowKey: 'id',
            loading: isLoading,
          }}
          className="!mt-0"
          page={Number(parameter?.page) ?? 1}
          pageSize={Number(parameter?.limit) ?? Number(ITEM_PER_PAGE)}
          onChangePage={(p: number) => {
            setParameter({
              ...parameter,
              page: Number(p),
            });
          }}
          total={total}
          onSelectPageSize={(v) => setParameter({ ...parameter, limit: v, page: 1 })}
        />
      </div>
      <CommonModalImportCsv
        ref={refImportCsv}
        type={EMenuType['business-partner']}
        refetch={handleGetList}
      />
    </PageContainer>
  );
};

export default BusinessPartnerPage;
