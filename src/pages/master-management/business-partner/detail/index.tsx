import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import { deleteBusinessPartner, getDetailBusinessPartner } from '@/apis/businessPartner';
import BasicButton from '@/components/Commons/BasicButton';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import BasicModal from '@/components/Commons/BasicModal';
import { openNotificationApprove, openNotificationFail } from '@/components/Notification';
import { YES_NO_OPTIONS } from '@/components/pages/BusinessPartner/dataField';
import ViewFieldItem from '@/components/pages/BusinessPartner/ViewFieldItem';
import { ROUTE_NAME } from '@/constants';
import { DELETE_POPUP, MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_TITLE } from '@/constants/commonText';
import { targetOptions } from '@/constants/data';
import STATUS_CODE from '@/constants/statusCode';
import { formatPercent } from '@/utils';
import { ArrowLeftOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { Col, Row, Spin } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { useParams } from 'react-router';
import { history } from 'umi';

const DetailBusinessPartner = () => {
  const { id } = useParams() as { id: string };

  const refDeleteModal = useRef<BasicModalRef>(null);

  const [dataDetail, setDataDetail] = useState<BusinessPartnerDetailType>();
  const [isLoading, setIsLoading] = useState(false);

  const fetchDataDetail = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getDetailBusinessPartner(Number(id));
      const rsData = data.data;
      const detail = {
        ...rsData,
        target: JSON.parse(rsData?.target as string),
      };
      if (status === STATUS_CODE.SUCCESSFUL) {
        setDataDetail(detail);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  useEffect(() => {
    fetchDataDetail();
  }, [id]);

  const onOpenModalDelete = () => {
    if (refDeleteModal) {
      refDeleteModal.current.open();
    }
  };

  const handleDelete = async () => {
    try {
      const { status } = await deleteBusinessPartner(Number(id));

      if (status === STATUS_CODE.SUCCESSFUL) {
        openNotificationApprove(MESSAGE_ALERT.DELETE_SUCCESS);
        history.push(ROUTE_NAME.BUSINESS_PARTNER_INDEX);
      } else {
        setIsLoading(false);
        openNotificationFail(MESSAGE_ALERT.DELETE_FAILED);
      }
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  return (
    <Spin spinning={isLoading}>
      <div className="p-9 space-y-[20px]">
        <ViewFieldItem
          label={TEXT_TITLE.Business_Partner_Code}
          type="text"
          value={dataDetail?.business_partner_code}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Business_Partner_Name}
          type="text"
          value={dataDetail?.business_partner_name}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Business_Partner_Name_Kana}
          type="text"
          value={dataDetail?.business_partner_name_kana}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Business_Partner_Name_En}
          type="text"
          value={dataDetail?.business_partner_name_en}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Qualified_Invoice_Issuer_Type}
          type="radio"
          value={dataDetail?.qualified_invoice_issuer_type}
          options={YES_NO_OPTIONS}
        />
        {dataDetail?.qualified_invoice_issuer_type === 1 ? (
          <>
            <ViewFieldItem
              label={TEXT_TITLE.Qualified_Invoice_Issuer_Number}
              type="text"
              value={dataDetail?.qualified_invoice_issuer_number}
              options={null}
            />
          </>
        ) : null}
        <ViewFieldItem
          label={TEXT_TITLE.Postal_Code}
          type="text"
          value={dataDetail?.postal_code}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Address}
          type="text"
          value={dataDetail?.address}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Address_En}
          type="text"
          value={dataDetail?.address_en}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Phone_Number}
          type="text"
          value={dataDetail?.phone_number}
          options={null}
        />
        <ViewFieldItem label={TEXT_TITLE.Fax} type="text" value={dataDetail?.fax} options={null} />
        <ViewFieldItem
          label={TEXT_TITLE.Email}
          type="text"
          value={dataDetail?.email}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Manager}
          type="text"
          value={dataDetail?.manager}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Memo}
          type="text"
          value={dataDetail?.memo}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Target}
          type="checkbox"
          value={dataDetail?.target}
          options={targetOptions}
        />
        <ViewFieldItem
          label={TEXT_TITLE.R_Target_Category}
          type="radio"
          value={dataDetail?.r_target_category}
          options={YES_NO_OPTIONS}
        />
        {dataDetail?.r_target_category ? (
          <>
            <ViewFieldItem
              label={TEXT_TITLE.R_Rate}
              type="text"
              value={formatPercent(dataDetail?.r_rate)}
              options={null}
            />
          </>
        ) : null}

        <ViewFieldItem
          label={TEXT_TITLE.Closing_Date}
          type="text"
          value={dataDetail?.closing_date}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Collection_Cycle}
          type="text"
          value={dataDetail?.collection_cycle}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Payment_Cycle}
          type="text"
          value={dataDetail?.payment_cycle}
          options={null}
        />
        <ViewFieldItem
          label={TEXT_TITLE.Internal_Category}
          type="radio"
          value={dataDetail?.internal_category}
          options={YES_NO_OPTIONS}
        />

        {dataDetail?.internal_category === 1 ? (
          <>
            <ViewFieldItem
              label={TEXT_TITLE.Accounting_Unit_Code}
              type="text"
              value={dataDetail?.accounting_unit_code}
              options={null}
            />
            <ViewFieldItem
              label={TEXT_TITLE.Profit_Loss_Unit_Code}
              type="text"
              value={dataDetail?.profit_loss_unit_code}
              options={null}
            />
            <ViewFieldItem
              label={TEXT_TITLE.Internal_Department_Currency_Code}
              type="text"
              value={dataDetail?.internal_department_currency_code}
              options={null}
            />
            <ViewFieldItem
              label={TEXT_TITLE.Internal_Department_Currency_Subject}
              type="text"
              value={dataDetail?.internal_department_currency_subject}
              options={null}
            />
            <ViewFieldItem
              label={TEXT_TITLE.Internal_Sales_Tax_Subject_Code}
              type="text"
              value={dataDetail?.internal_sales_tax_subject_code}
              options={null}
            />
            <ViewFieldItem
              label={TEXT_TITLE.Internal_Sales_Tax_Subject_Name}
              type="text"
              value={dataDetail?.internal_sales_tax_subject_name}
              options={null}
            />
            <ViewFieldItem
              label={TEXT_TITLE.Internal_Sales_Non_Taxable_Code}
              type="text"
              value={dataDetail?.internal_sales_non_taxable_code}
              options={null}
            />
            <ViewFieldItem
              label={TEXT_TITLE.Internal_Sales_Non_Taxable_Name}
              type="text"
              value={dataDetail?.internal_sales_non_taxable_name}
              options={null}
            />
          </>
        ) : null}
      </div>

      <Row>
        <Col span={12}>
          <div className="flex items-center justify-center gap-x-3 pb-6">
            <BasicButton
              onClick={() => history.goBack()}
              styleType="noneOutLine"
              icon={<ArrowLeftOutlined />}
              className="flex items-center gap-1"
            >
              {TEXT_ACTION.RETURN}
            </BasicButton>
            <BasicButton
              styleType="outline"
              icon={<EditOutlined />}
              onClick={() => {
                history.push(`${ROUTE_NAME.BUSINESS_PARTNER_EDIT}/${id}`);
              }}
              className="flex items-center gap-1"
            >
              {TEXT_ACTION.EDIT}
            </BasicButton>
            <BasicButton
              styleType="delete"
              onClick={onOpenModalDelete}
              icon={<DeleteOutlined />}
              className="flex items-center gap-1"
            >
              {TEXT_ACTION.DELETE}
            </BasicButton>
          </div>
        </Col>
      </Row>
      {/* Modal */}
      <BasicModal
        ref={refDeleteModal}
        title={DELETE_POPUP.TITLE}
        content={<div className="whitespace-pre-line text-center">{DELETE_POPUP.CONTENT}</div>}
        onSubmit={handleDelete}
      />
    </Spin>
  );
};

export default DetailBusinessPartner;
