import React, { useEffect, useRef, useState } from 'react';

import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import type { BusinessPartnerFormRef } from '@/components/pages/BusinessPartner/BusinessPartnerForm';
import BusinessPartnerForm from '@/components/pages/BusinessPartner/BusinessPartnerForm';
import BasicButton from '@/components/Commons/BasicButton';
import { SaveOutlined } from '@ant-design/icons';
import { Col, Row, Spin } from 'antd';
import type { BusinessPartnerType } from '@/@types/businessPartner';
import { createBusinessPartner } from '@/apis/businessPartner';
import { YES_NO_OPTIONS } from '@/components/pages/BusinessPartner/dataField';
import STATUS_CODE from '@/constants/statusCode';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { history } from 'umi';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import {
  checkBusinessPartnerFormChange,
  initialValuesFormBusinessPartner,
} from '@/components/pages/BusinessPartner/utils';

const CreateBusinessPartner = () => {
  const refFormCreate = useRef<BusinessPartnerFormRef>();

  const [isloading, setIsLoading] = useState<boolean>(false);

  const [formChange, setFormChange] = useState(false);

  useEffect(() => {
    const unblock = formChange ? history.block(TEXT_WARNING.leave_page_and_lose_changes) : null;

    return () => {
      if (unblock) {
        unblock();
      }
    };
  }, [formChange, history]);

  useEffect(() => {
    if (refFormCreate) {
      refFormCreate.current.form.setFieldsValue({
        qualified_invoice_issuer_type: YES_NO_OPTIONS[0]?.value,
        r_target_category: YES_NO_OPTIONS[0]?.value,
        internal_category: YES_NO_OPTIONS[1]?.value,
      });
    }
  }, []);

  const onSubmit = async () => {
    if (refFormCreate) {
      await refFormCreate.current.form.validateFields();
      setIsLoading(true);
      setFormChange(false);
      try {
        const values = refFormCreate.current.form.getFieldsValue();
        const payload: BusinessPartnerType = {
          ...values,
          target: JSON.stringify(values.target),
          // closing_date: moment(values.closing_date).format('YYYY-MM-DD'),
          status: 1,
        };
        const { status, error, data } = await createBusinessPartner(payload);
        if (status === STATUS_CODE.CREATED) {
          openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
          history.push(`/master-management/business-partner-master-detail/${data.data.id}`);
        } else if (status === STATUS_CODE.INVALID) {
          const errors = error.data.errors;
          if (errors && Object.keys(errors).length) {
            Object.keys(errors).forEach((key) => {
              const message = errors[key];
              refFormCreate?.current?.form.setFields([
                {
                  name: key,
                  errors: message,
                },
              ]);
            });
          }
        } else {
          openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
        }
        setIsLoading(false);
      } catch (error) {
        openNotificationFail('新規作成に失敗しました');
        console.log('error', error);
        setIsLoading(false);
      }
    }
  };

  return (
    <Spin spinning={isloading}>
      <div className="p-9">
        <BusinessPartnerForm
          ref={refFormCreate}
          formProps={{
            onValuesChange: (changedValues, values) => {
              const isChanged = checkBusinessPartnerFormChange(
                initialValuesFormBusinessPartner,
                values,
              );

              setFormChange(isChanged);
            },
          }}
        />
        <Row>
          <Col span={12}>
            <div className="flex items-center gap-x-[20px]">
              <BasicButton
                styleType="noneOutLine"
                onClick={() => history.goBack()}
                className="flex-1"
              >
                {TEXT_ACTION.CANCEL}
              </BasicButton>
              <BasicButton
                className="flex items-center flex-1"
                styleType="accept"
                onClick={onSubmit}
                icon={<SaveOutlined width={16} height={16} />}
              >
                {TEXT_ACTION.SAVE}
              </BasicButton>
            </div>
          </Col>
        </Row>
      </div>
    </Spin>
  );
};

export default CreateBusinessPartner;
