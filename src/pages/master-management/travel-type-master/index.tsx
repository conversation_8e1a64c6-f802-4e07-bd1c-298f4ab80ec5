import type { TravelTypeMasterDetailType } from '@/@types/travelTypeMaster';
import type { ImportCsvCommonRef } from '@/components/CommonModalImportCsv';
import CommonModalImportCsv, { EMenuType } from '@/components/CommonModalImportCsv';
import BasicButton from '@/components/Commons/BasicButton';
import BasicInputSearch from '@/components/Commons/BasicInputSearch';
import PageContainer from '@/components/Commons/Page/Container';
import {
  DownloadOutlined,
  PlusOutlined,
  SaveOutlined,
  SearchOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { Form } from 'antd';
import { useEffect, useRef, useState } from 'react';
import TableTravelType from './components/TableTravelType';

import type { BaseParams } from '@/@types/request';
import {
  createOneTravelTypeMaster,
  createTravelTypeMaster,
  exportTravelTypeMaster,
  getListTravelTypeMaster,
} from '@/apis/travelTypeMaster';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import {
  openNotificationBlock,
  openNotificationFail,
  openNotificationSuccess,
} from '@/components/Notification';
import type { TravelTypeMasterFormRef } from '@/components/pages/TraveltypeMaster/form/TravelTypeMasterForm';
import TravelTypeMasterForm from '@/components/pages/TraveltypeMaster/form/TravelTypeMasterForm';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE, TEXT_WARNING } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { ITEM_PER_PAGE } from '@/utils/constants';
import { useHistory } from 'umi';
import { useUrlSearchParams } from 'use-url-search-params';

const initSearchParams: BaseParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};

const TravelTypeMaster = () => {
  const [form] = Form.useForm();
  const [formSearch] = Form.useForm();

  const refImportCsv = useRef<ImportCsvCommonRef>(null);
  const refModal = useRef<BasicModalRef>(null);
  const refFormAddNewItem = useRef<TravelTypeMasterFormRef>(null);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<TravelTypeMasterDetailType[]>();
  const [isEditPage, setIsEditPage] = useState(false);
  const [listItemChange, setListItemChange] = useState<string[]>([]);
  const [total, setTotal] = useState<number>();

  // const [paramSearch, setParamSearch] = useState<BaseParams>(initSearchParams);
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);

  const history = useHistory();

  useEffect(() => {
    const unblock =
      listItemChange.length > 0 ? history.block(TEXT_WARNING.leave_page_and_lose_changes) : null;

    return () => {
      if (unblock) {
        unblock();
      }
    };
  }, [listItemChange, history]);
  //fetch list data
  const handleFetchData = async () => {
    setIsLoading(true);
    try {
      const payload: BaseParams = {
        ...parameter,
        sort: 'travel_code', //required: sort tax_category_code => desc
        order: 'asc',
      };
      const { data, status } = await getListTravelTypeMaster(payload);

      if (status === STATUS_CODE.SUCCESSFUL) {
        const rs = data.data;
        const dts = rs.map((item, index) => ({
          ...item,
          key: item?.id,
          numericalOrder: (Number(parameter?.page) - 1) * Number(parameter?.limit) + index + 1,
          status: item?.status === 1,
        }));
        setDataSource(dts);
        setTotal(data.total);
        setIsLoading(false);
      } else {
        setIsLoading(false);
        openNotificationBlock(MESSAGE_ALERT.SERVER_ERROR);
      }
    } catch (error) {}
  };

  useEffect(() => {
    handleFetchData();
    if (parameter?.keyword) {
      formSearch.setFieldValue('keyword', parameter?.keyword);
    }
  }, [parameter?.keyword, parameter?.limit, parameter?.page]);
  //
  const removeListItemChange = () => {
    setListItemChange([]);
  };

  useEffect(() => {
    dataSource?.forEach((item) => {
      Object.keys(item).forEach((key) => {
        const keyForm = `${item.key}.${key}`;
        form.setFieldValue(keyForm, item[key]);
      });
    });
    removeListItemChange();
  }, [dataSource]);

  const onImportCsv = () => {
    if (refImportCsv) {
      refImportCsv.current.open();
    }
  };

  const save = async () => {
    setIsLoading(true);
    try {
      const values = await form.validateFields();
      const resultObj = Object.keys(values).reduce((acc, key) => {
        const [index, property] = key.split('.');
        const value = values[key];

        if (!acc[index]) {
          acc[index] = {};
        }

        acc[index][property] = value;

        return acc;
      }, {});

      const newData = [...dataSource];

      Object.keys(resultObj).forEach((key) => {
        const index = dataSource.findIndex((item) => item.key === Number(key));
        newData[index] = { ...dataSource[index], ...resultObj[key] };
      });

      setDataSource([...newData]);
      // Filter data change
      const dataChange = newData.filter((item) => listItemChange.includes(item.key.toString()));
      const dataUpdate = dataChange.map((item) => {
        delete item?.created_at;
        delete item?.updated_at;
        item.status = item.status ? 1 : 0;
        return item;
      });

      // => Call API here
      const responseUpdate = await createTravelTypeMaster({ data: dataUpdate }); // create or ubdate in table
      if (
        responseUpdate.status === STATUS_CODE.SUCCESSFUL ||
        responseUpdate.status === STATUS_CODE.CREATED
      ) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        handleFetchData();
        setIsEditPage(false);
        setIsLoading(false);

        // Reset when update successfully
        removeListItemChange();
      } else if (responseUpdate.status === STATUS_CODE.INVALID) {
        const errors = responseUpdate?.error?.data?.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const nameField = key.split('.').slice(1).join('.');
            const message = errors[key];
            form.setFields([
              {
                name: nameField,
                errors: message,
              },
            ]);
          });
        }
        setIsLoading(false);
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
        setIsLoading(false);
      }
    } catch (errInfo) {
      setIsLoading(false);
      console.log('Save failed:', errInfo);
    }
  };

  //search
  const onSearch = (value: string) => {
    const search = {
      ...parameter,
      keyword: value,
    };
    setParameter(search);
  };

  // add new Item
  const handleAddNew = async (values) => {
    try {
      const payload = {
        travel_code: values?.travel_code,
        travel_name: values?.travel_name,
        status: values?.status ? 1 : 0,
      };

      const dataSend = { data: payload };
      const { status, error } = await createOneTravelTypeMaster(dataSend);
      if (status === STATUS_CODE.CREATED) {
        openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
        refModal?.current?.close();
        setParameter(initSearchParams);
        handleFetchData();
        refFormAddNewItem?.current?.form?.resetFields();
      } else if (status === STATUS_CODE.INVALID) {
        const errors = error?.data?.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key];
            console.log(key, message);

            refFormAddNewItem?.current?.form.setFields([
              {
                name: key,
                errors: message,
              },
            ]);
          });
        }
      } else {
        openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
      }
    } catch (error) {
      console.log(error);

      openNotificationBlock(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  //export csv
  const handleExportCsv = async () => {
    try {
      setIsLoading(true);
      const { data, status } = await exportTravelTypeMaster({
        ...parameter,
        limit: 'all',
      });
      setIsLoading(false);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const urlCsv = data?.file_link;
        if (urlCsv) {
          const a = document.createElement('a');
          a.href = urlCsv;
          a.download = 'csv_travel_type_master.csv';
          a.click();
          window.URL.revokeObjectURL(urlCsv);
        }
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  const openModal = () => {
    refFormAddNewItem?.current?.form?.resetFields();
    if (refModal) {
      refFormAddNewItem?.current?.form?.setFieldsValue({
        status: true,
      });
      refModal.current.open();
    }
  };

  return (
    <PageContainer>
      <div className="flex items-end justify-between">
        <Form
          form={formSearch}
          onFinish={(value) => {
            onSearch(value?.keyword);
          }}
        >
          <div className="flex items-end gap-x-[24px]">
            <Form.Item name={'keyword'} noStyle>
              <BasicInputSearch
                style={{
                  width: '280px',
                  height: '40px',
                }}
                title={TEXT_TITLE.keyword}
                placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
                onSearch={(value) => {
                  onSearch(value);
                }}
              />
            </Form.Item>
            <BasicButton
              onClick={() => formSearch.submit()}
              icon={<SearchOutlined />}
              styleType="accept"
              className="flex items-center min-w-[120px]"
            >
              {TEXT_ACTION.SEARCH}
            </BasicButton>
          </div>
        </Form>

        <div className="flex gap-x-[12px]">
          <BasicButton
            icon={<SaveOutlined style={{ color: '#225DE0' }} />}
            className="flex items-center !text-[#225DE0]"
            styleType="noneOutLine"
            onClick={() => (isEditPage ? save() : setIsEditPage(true))}
          >
            {isEditPage ? TEXT_ACTION.SAVE : TEXT_ACTION.EDIT}
          </BasicButton>
          <BasicButton
            icon={<UploadOutlined style={{ color: '#EC980C' }} />}
            className="flex items-center !text-[#EC980C]"
            styleType="noneOutLine"
            onClick={onImportCsv}
          >
            {TEXT_ACTION.CSV_Import}
          </BasicButton>
          <BasicButton
            icon={<DownloadOutlined style={{ color: '#3997C8' }} />}
            className="flex items-center !text-main-color"
            styleType="noneOutLine"
            onClick={handleExportCsv}
          >
            {TEXT_ACTION.CSV_Export}
          </BasicButton>
          <BasicButton
            icon={<PlusOutlined />}
            onClick={openModal}
            className="flex items-center"
            styleType="accept"
          >
            {TEXT_ACTION.CREATE_NEW}
          </BasicButton>
        </div>
      </div>
      <div className="p-2 rounded-xl bg-white mt-6">
        <TableTravelType
          setDataSource={setDataSource}
          paramSearch={parameter}
          setParamSearch={setParameter}
          isEditPage={isEditPage}
          isLoading={isLoading}
          form={form}
          dataSource={dataSource}
          listItemChange={listItemChange}
          setIsLoading={setIsLoading}
          setListItemChange={setListItemChange}
          totalItem={total}
          refetch={handleFetchData}
        />
      </div>
      <CommonModalImportCsv
        ref={refImportCsv}
        type={EMenuType['travel-type-master']}
        refetch={handleFetchData}
      />
      <BasicFormModal
        ref={refModal}
        content={<TravelTypeMasterForm ref={refFormAddNewItem} onSubmit={handleAddNew} />}
        title="旅行種別作成"
        buttonCloseTitle={TEXT_ACTION.CANCEL}
        buttonSubmitTitle={TEXT_ACTION.SAVE}
        buttonSubmitStyle={{ backgroundColor: '#3997C8' }}
        onSubmit={() => refFormAddNewItem?.current?.form?.submit()}
        isValidate={true}
      />
    </PageContainer>
  );
};

export default TravelTypeMaster;
