import { Form, Image, Popconfirm, type FormInstance } from 'antd';
import type { FieldData } from 'rc-field-form/lib/interface';
import BasicTable from '@/components/Commons/BasicTable';
import BasicButton from '@/components/Commons/BasicButton';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import EditableCell from '@/components/Commons/EditableCell';
import type { TravelTypeMasterDetailType } from '@/@types/travelTypeMaster';
import { deleteTravelTypeMaster } from '@/apis/travelTypeMaster';
import STATUS_CODE from '@/constants/statusCode';
import {
  openNotificationBlock,
  openNotificationDeleteSuccess,
  openNotificationFail,
} from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import type { BaseParams } from '@/@types/request';
import { useRef, useState } from 'react';
import BasicModal from '@/components/Commons/BasicModal';
import { rules } from '@/constants/rules';
import { TEXT_ACTION, TEXT_TITLE, TEXT_WARNING } from '@/constants/commonText';

interface Props {
  isEditPage: boolean;
  isLoading: boolean;
  setIsLoading: (val: boolean) => void;
  form: FormInstance<any>;
  dataSource: TravelTypeMasterDetailType[];
  setDataSource: React.Dispatch<React.SetStateAction<TravelTypeMasterDetailType[]>>;
  listItemChange: string[];
  setListItemChange: (val: string[]) => void;
  totalItem: number;
  paramSearch: BaseParams;
  setParamSearch: (val: BaseParams) => void;

  refetch?: () => void;
}

const TableTravelType: React.FC<Props> = (props) => {
  const {
    form,
    isEditPage,
    isLoading,
    setIsLoading,
    dataSource,
    listItemChange,
    setListItemChange,
    totalItem,
    paramSearch,
    setParamSearch,
    refetch,
  } = props;

  const refModalConfirmChangePage = useRef(null);

  const [pageChange, setPageChange] = useState<number>(1);

  const handleListItemChange = (changeField: FieldData[]) => {
    const nameFieldChange = changeField?.[0]?.name?.[0];
    const keyChange = nameFieldChange.split('.')[0];
    setListItemChange([...listItemChange, keyChange]);
  };

  const confirmDeleteRow = async (key) => {
    setIsLoading(true);
    try {
      const { status } = await deleteTravelTypeMaster(key);

      if (status === STATUS_CODE.SUCCESSFUL) {
        openNotificationDeleteSuccess(MESSAGE_ALERT.DELETE_SUCCESS);
        refetch?.();
      } else {
        openNotificationFail(MESSAGE_ALERT.DELETE_SUCCESS);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationBlock(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'numericalOrder',
      key: 'numericalOrder',
      width: '60px',
    },
    {
      title: '',
      dataIndex: 'status',
      editable: true,
      key: 'status',
      formType: 'switch',
      width: 60,
    },
    {
      title: TEXT_TITLE.Travel_Code,
      dataIndex: 'travel_code',
      key: 'travel_code',
      editable: true,
      formType: 'input',
      ruleFormItem: rules.requiredInput,
    },
    {
      title: TEXT_TITLE.Travel_Name,
      dataIndex: 'travel_name',
      key: 'travel_name',
      editable: true,
      formType: 'input',
      ruleFormItem: rules.requiredInput,
    },
    {
      title: '',
      dataIndex: 'deleteItem',
      key: 'deleteItem',
      width: 80,
      render: (_, record) => (
        <Popconfirm
          title={TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
          onConfirm={() => confirmDeleteRow(record.id)}
          okText={TEXT_ACTION.DELETE}
          cancelText={TEXT_ACTION.CANCEL}
        >
          <BasicButton styleType="danger" className="!h-[24px] w-[76px]">
            <Image preview={false} src={IconDelete} width={12} height={13} />
            <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
          </BasicButton>
        </Popconfirm>
      ),
    },
  ];

  const columns = defaultColumns.map((col) => {
    if (!col.editable && !isEditPage) {
      return { ...col, width: col.width || 250 };
    }
    return {
      ...col,
      width: col.width || 250,
      onCell: (record: Record<string, string>) => ({
        record,
        editable: col.editable,
        isEditPage,
        dataIndex: col.dataIndex,
        title: col.title,
        formType: col.formType,
        form,
        ruleFormItem: col?.ruleFormItem,
      }),
    };
  });

  const onConfirmChangePageWhenEdit = (pageConfirm: number) => {
    setPageChange(pageConfirm);
    refModalConfirmChangePage.current?.open();
  };

  return (
    <>
      <Form
        form={form}
        component={false}
        onFieldsChange={(changeField: FieldData[]) => handleListItemChange(changeField)}
      >
        <BasicTable
          className="!mt-0"
          tableProps={{
            scroll: { x: 1000 },
            loading: isLoading,
            components: {
              body: {
                cell: EditableCell,
              },
            },
            columns,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            rowKey: 'id',
          }}
          page={Number(paramSearch?.page) ?? 1}
          pageSize={Number(paramSearch?.limit) ?? 10}
          onChangePage={(p: number) => {
            if (listItemChange.length > 0) {
              onConfirmChangePageWhenEdit(p);
            } else {
              setParamSearch({
                ...paramSearch,
                page: Number(p),
              });
            }
          }}
          total={totalItem}
          onSelectPageSize={(v) => setParamSearch({ ...paramSearch, limit: v, page: 1 })}
        />

        <BasicModal
          ref={refModalConfirmChangePage}
          title={'警告'}
          content={<>{MESSAGE_ALERT.CONFIRM_CHANGE_PAGE}</>}
          okText="ページを変更する"
          onSubmit={() => {
            setParamSearch({ ...paramSearch, page: pageChange });
            setListItemChange([]);
            refModalConfirmChangePage.current?.close();
          }}
        />
      </Form>
    </>
  );
};

export default TableTravelType;
