import { Form } from 'antd';
import { forwardRef, useEffect, useImperativeHandle } from 'react';
import type { FormInstance } from 'antd/es/form';
import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { TaxCategoryMasterFields } from './TaxCategoryMasterFields';

interface Props {
  onSubmit: (values) => void;
}

export type TaxCategoryMasterFormRef = {
  form: FormInstance<any>;
};

const FormCreateTaxItem = forwardRef<TaxCategoryMasterFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    form,
  }));

  useEffect(() => {
    form?.setFieldsValue({
      status: true,
    });
  }, []);

  return (
    <Form form={form} onFinish={onSubmit}>
      {TaxCategoryMasterFields?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default FormCreateTaxItem;
