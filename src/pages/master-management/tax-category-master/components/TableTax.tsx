import type { BaseParams } from '@/@types/request';
import type {
  DataDeleteOneTaxCategorySuccessResponse,
  DatumGetListTaxCategory,
} from '@/apis/master/taxCategory';
import { deleteOneTaxCategory } from '@/apis/master/taxCategory';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicModal from '@/components/Commons/BasicModal';
import BasicTable from '@/components/Commons/BasicTable';
import EditableCell from '@/components/Commons/EditableCell';
import { openNotificationDeleteSuccess, openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_TITLE, TEXT_WARNING } from '@/constants/commonText';
import { targetOptions } from '@/constants/data';
import { rules } from '@/constants/rules';
import type { FormInstance } from 'antd';
import { Form, Image, Popconfirm } from 'antd';
import type { FieldData } from 'rc-field-form/lib/interface';
import { useRef, useState } from 'react';

// eslint-disable-next-line max-lines-per-function
const TableTax = ({
  form,
  isEditPage,
  isLoading,
  setIsLoading,
  dataSource,
  listItemChange,
  setListItemChange,
  paramSearch,
  setParamSearch,
  total,
  refetch,
}: {
  isEditPage: boolean;
  isLoading: boolean;
  setIsLoading: (val: boolean) => void;
  form: FormInstance<any>;
  dataSource: DatumGetListTaxCategory[];
  setDataSource: (val: DatumGetListTaxCategory[]) => void;
  listItemChange: string[];
  setListItemChange: (val: string[]) => void;
  paramSearch: BaseParams;
  setParamSearch: (val: BaseParams) => void;
  total: number;
  refetch?: () => void;
}) => {
  const refModalConfirmChangePage = useRef(null);

  const [pageChange, setPageChange] = useState<number>(1);

  const handleListItemChange = (changeField: FieldData[]) => {
    const nameFieldChange = changeField?.[0]?.name?.[0];
    const keyChange = nameFieldChange.split('.')[0];
    setListItemChange([...listItemChange, keyChange]);
  };

  const confirmDeleteRow = async (key) => {
    setIsLoading(true);
    try {
      const responseDelete = await deleteOneTaxCategory(key);
      if ((responseDelete?.data as DataDeleteOneTaxCategorySuccessResponse)?.data === true) {
        openNotificationDeleteSuccess(MESSAGE_ALERT.DELETE_SUCCESS);
        refetch?.();
      } else {
        openNotificationFail(MESSAGE_ALERT.DELETE_FAILED);
      }
    } catch (error) {}

    setIsLoading(false);
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'numericalOrder',
      key: 'numericalOrder',
      width: 60,
    },
    {
      title: '',
      dataIndex: 'status',
      editable: true,
      key: 'status',
      formType: 'switch',
      width: 60,
    },
    {
      title: TEXT_TITLE.Tax_Category_Code,
      dataIndex: 'tax_category_code',
      key: 'tax_category_code',
      editable: true,
      formType: 'input',
      ruleFormItem: rules.requiredInput,
    },
    {
      title: TEXT_TITLE.Tax_Category_Name,
      dataIndex: 'tax_category_name',
      key: 'tax_category_name',
      editable: true,
      formType: 'input',
      ruleFormItem: rules.requiredInput,
    },
    {
      title: TEXT_TITLE.Tax_Category_Abbreviation,
      dataIndex: 'tax_category_abbreviation',
      key: 'tax_category_abbreviation',
      editable: true,
      formType: 'input',
      ruleFormItem: rules.requiredInput,
    },
    {
      title: TEXT_TITLE.Tax_Rate,
      dataIndex: 'tax_rate',
      key: 'tax_rate',
      formType: 'inputNumber',
      editable: true,
      inputProps: { isRightAlign: true, isPercent: true },
      // ruleFormItem: rules.integerOnly,
      width: 150,
    },
    {
      title: TEXT_TITLE.Target,
      dataIndex: 'target',
      key: 'target',
      editable: true,
      formType: 'checkbox',
      width: 400,
      options: targetOptions,
      ruleFormItem: rules.isRequired,
    },
    {
      title: '',
      dataIndex: 'deleteItem',
      key: 'deleteItem',
      width: 80,
      render: (_, record) => (
        <Popconfirm
          title={TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
          onConfirm={() => confirmDeleteRow(record.id)}
          okText={TEXT_ACTION.DELETE}
          cancelText={TEXT_ACTION.CANCEL}
        >
          <BasicButton styleType="danger" className="!h-[24px] w-[76px]">
            <Image preview={false} src={IconDelete} width={12} height={13} />
            <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
          </BasicButton>
        </Popconfirm>
      ),
    },
  ];

  const columns = defaultColumns.map((col) => {
    if (!col.editable && !isEditPage) {
      return { ...col, width: col.width || 250 };
    }
    return {
      ...col,
      width: col.width || 250,
      onCell: (record: Record<string, string>) => ({
        record,
        editable: col.editable,
        isEditPage,
        dataIndex: col.dataIndex,
        title: col.title,
        formType: col.formType,
        options: col.options,
        inputProps: col.inputProps,
        form,
        ruleFormItem: col?.ruleFormItem,
      }),
    };
  });

  const onConfirmChangePageWhenEdit = (pageConfirm: number) => {
    setPageChange(pageConfirm);
    refModalConfirmChangePage.current?.open();
  };

  return (
    <>
      <Form
        form={form}
        component={false}
        onFieldsChange={(changeField: FieldData[]) => handleListItemChange(changeField)}
      >
        <BasicTable
          className="!mt-0"
          tableProps={{
            scroll: { x: 1500 },
            loading: isLoading,
            components: {
              body: {
                cell: EditableCell,
              },
            },
            columns,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            rowKey: 'id',
          }}
          page={Number(paramSearch?.page) ?? 1}
          pageSize={Number(paramSearch.limit) ?? 10}
          onChangePage={(p: number) => {
            if (listItemChange.length > 0) {
              onConfirmChangePageWhenEdit(p);
            } else {
              setParamSearch({
                ...paramSearch,
                page: Number(p),
              });
            }
          }}
          total={total}
          onSelectPageSize={(v) => setParamSearch({ ...paramSearch, limit: v, page: 1 })}
        />
        <BasicModal
          ref={refModalConfirmChangePage}
          title={'警告'}
          content={<>{MESSAGE_ALERT.CONFIRM_CHANGE_PAGE}</>}
          okText="ページを変更する"
          onSubmit={() => {
            setParamSearch({ ...paramSearch, page: pageChange });
            setListItemChange([]);
            refModalConfirmChangePage.current?.close();
          }}
        />
      </Form>
    </>
  );
};

export default TableTax;
