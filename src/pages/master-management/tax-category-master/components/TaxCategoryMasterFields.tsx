import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { rules } from '@/constants/rules';
import { targetOptions } from '@/constants/data';

export const TaxCategoryMasterFields: FormItemDetail[] = [
  {
    type: 'input',
    name: 'tax_category_code',
    title: '課税区分コード',
    isRequired: true,
    placeholder: '課税区分コード',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'tax_category_name',
    title: '課税区分名',
    isRequired: true,
    placeholder: '課税区分名',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'tax_category_abbreviation',
    title: '税区分略称',
    isRequired: true,
    placeholder: '税区分略称',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'tax_rate',
    title: '税率(%)',
    isRequired: true,
    placeholder: '税率(%)', //
    isPercent: true,
    rules: rules.requiredInput,
    colSpan: 24,
    numbericInputAllowDecimal: true,
  },
  {
    type: 'checkbox',
    name: 'target',
    title: '対象',
    isRequired: true,
    options: targetOptions,
    rules: rules.isRequired,
    colSpan: 24,
  },
  {
    type: 'switch',
    name: 'status',
    title: 'アクティブ',
    defaultCheckedSwitch: true,
    colSpan: 6,
  },
];
