import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { rules } from '@/constants/rules';

export const AccountingSubjectFields: FormItemDetail[] = [
  {
    type: 'input',
    name: 'account_code',
    title: '勘定科目コード',
    isRequired: true,
    placeholder: '勘定科目コード',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'account_name',
    title: '勘定科目名',
    isRequired: true,
    placeholder: '勘定科目名',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'account_abbreviation',
    title: '勘定科目名(略称)',
    isRequired: true,
    placeholder: '勘定科目名(略称)',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'business_partner_id',
    title: '取引先区分',
    isRequired: true,
    placeholder: '取引先区分',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'switch',
    name: 'status',
    title: 'アクティブ',
    defaultCheckedSwitch: true,
    colSpan: 6,
  },
];
