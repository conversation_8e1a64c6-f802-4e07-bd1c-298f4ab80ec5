import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { AccountingSubjectFields } from './AccountingSubjectFields';

interface Props {
  onSubmit: (values) => void;
}

export type AccountingSubjectFormRef = {
  form: FormInstance<any>;
};

const FormCreateAccountingSubject = forwardRef<AccountingSubjectFormRef, Props>(
  ({ onSubmit }, ref) => {
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
      form,
    }));

    useEffect(() => {
      form?.setFieldsValue({
        status: true,
      });
    }, []);

    return (
      <Form form={form} onFinish={onSubmit}>
        {AccountingSubjectFields?.map((item) => (
          <CommonRenderFormField key={item?.name} field={item} />
        ))}
      </Form>
    );
  },
);

export default FormCreateAccountingSubject;
