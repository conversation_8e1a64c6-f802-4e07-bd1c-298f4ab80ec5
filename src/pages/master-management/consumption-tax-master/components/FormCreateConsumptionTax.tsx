import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';
import React, { forwardRef, useEffect, useImperativeHandle } from 'react';
import { ConsumptionTaxFields } from './ConsumptionTaxFields';

interface Props {
  onSubmit: (values) => void;
}

export type ConsumptionTaxFormRef = {
  form: FormInstance<any>;
};

const FormCreateConsumptionTax = forwardRef<ConsumptionTaxFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));

  useEffect(() => {
    form?.setFieldsValue({
      status: true,
    });
  }, []);

  return (
    <Form form={form} onFinish={onSubmit}>
      {ConsumptionTaxFields?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default FormCreateConsumptionTax;
