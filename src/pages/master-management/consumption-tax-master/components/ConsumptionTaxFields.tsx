import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { targetOptions } from '@/constants/data';
import { rules } from '@/constants/rules';

export const ConsumptionTaxFields: FormItemDetail[] = [
  {
    type: 'input',
    name: 'consumption_tax_category_code',
    title: '消費税区分コード',
    isRequired: true,
    placeholder: '消費税区分コード',
    rules: [(rules?.validateInputLength[0] as any)?.validator(10)],
    colSpan: 24,
  },
  // {
  //   type: 'input',
  //   name: 'tax_category_code',
  //   title: '税区分コード',
  //   isRequired: true,
  //   placeholder: '税区分コード',
  //   rules: [(rules?.validateInputLength[0] as any)?.validator(10)],
  //   colSpan: 24,
  // },
  {
    type: 'input',
    name: 'consumption_tax_name',
    title: '消費税名称',
    isRequired: true,
    placeholder: '消費税名称',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'consumption_tax_abbreviation',
    title: '消費税略称',
    isRequired: true,
    placeholder: '消費税略称',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'consumption_tax_rate',
    title: '消費税率',
    isRequired: true,
    placeholder: '消費税率',
    isPercent: true,
    rules: rules.requiredInput,
    colSpan: 24,
    maxLength: 10,
  },
  {
    type: 'checkbox',
    name: 'target',
    title: '消費税率',
    isRequired: true,
    rules: rules.isRequired,
    options: targetOptions,
    colSpan: 24,
  },
  {
    type: 'switch',
    name: 'status',
    title: 'アクティブ',
    defaultCheckedSwitch: true,
    colSpan: 6,
  },
];
