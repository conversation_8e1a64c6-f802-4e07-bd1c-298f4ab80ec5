import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { rules } from '@/constants/rules';

export const AggregationItemFields: FormItemDetail[] = [
  {
    type: 'input',
    name: 'summary_item_code',
    title: '集計科目コード',
    isRequired: true,
    placeholder: '集計科目コード',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'summary_item_category_name',
    title: '集計科目区分名',
    isRequired: true,
    placeholder: '集計科目区分名',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'summary_item_name',
    title: '集計科目名',
    isRequired: true,
    placeholder: '集計科目名',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'summary_item_name_en',
    title: '集計科目名(英字)',
    placeholder: '集計科目名(英字)',
    colSpan: 24,
  },
  {
    type: 'switch',
    name: 'status',
    title: 'アクティブ',
    defaultCheckedSwitch: true,
    colSpan: 6,
  },
];
