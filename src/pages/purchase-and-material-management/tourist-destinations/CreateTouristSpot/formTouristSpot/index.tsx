import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance, FormProps } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle, useMemo } from 'react';
import { TouristDestinationFieldItem } from '../../components/TouristDestinationFieldItem';

export type touristFormRef = {
  form?: FormInstance<any>;
};

interface Props {
  formProps?: FormProps;
}

const FormTouristSpot = forwardRef<touristFormRef, Props>(({ formProps }, ref) => {
  const [form] = Form.useForm();
  const hasParking = Form.useWatch('parking_lot', form);

  useImperativeHandle(ref, () => ({
    form,
  }));

  const newFields = useMemo(() => {
    if (hasParking === 0) {
      return TouristDestinationFieldItem?.map((item) => {
        if (item.name === 'parking_fee') {
          return {
            ...item,
            disabled: true,
          };
        } else {
          return item;
        }
      });
    } else {
      return TouristDestinationFieldItem;
    }
  }, [hasParking]);

  return (
    <Form form={form} autoComplete="off" {...formProps}>
      {newFields?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default FormTouristSpot;
