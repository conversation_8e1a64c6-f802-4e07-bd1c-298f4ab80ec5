import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';

interface Props {
  onSubmit: (values) => void;
}

export type TouristPriceItemTypeFormRef = {
  form: FormInstance<any>;
};

import React, { forwardRef, useImperativeHandle } from 'react';
import { TouristDestinationPriceListField } from './TouristDestinationFieldItem';

const TouristDestinationsFormPriceList = forwardRef<TouristPriceItemTypeFormRef, Props>(
  ({ onSubmit }, ref) => {
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
      form,
    }));
    return (
      <Form form={form} onFinish={onSubmit}>
        {TouristDestinationPriceListField?.map((item) => (
          <CommonRenderFormField key={item?.name} field={item} />
        ))}
      </Form>
    );
  },
);

export default TouristDestinationsFormPriceList;
