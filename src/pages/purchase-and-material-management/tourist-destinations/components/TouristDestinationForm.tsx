import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { TouristDestinationCancellationFeeFieldItem } from './TouristDestinationFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type TouristDestinationCancellationFormRef = {
  form: FormInstance<any>;
};

const TouristDestinationForm = forwardRef<TouristDestinationCancellationFormRef, Props>(
  ({ onSubmit }, ref) => {
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
      form,
    }));
    return (
      <Form form={form} onFinish={onSubmit}>
        {TouristDestinationCancellationFeeFieldItem?.map((item) => (
          <CommonRenderFormField key={item?.name} field={item} />
        ))}
      </Form>
    );
  },
);

export default TouristDestinationForm;
