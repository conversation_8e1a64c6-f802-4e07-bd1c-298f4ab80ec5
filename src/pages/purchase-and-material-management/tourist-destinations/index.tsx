import IconAdd from '@/assets/imgs/common-icons/icon-add-white.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicInput from '@/components/Commons/BasicInput';
import BasicTable from '@/components/Commons/BasicTable';
import CustomSelectPrefectures from '@/components/Commons/CustomSelectPrefectures';

import { openNotificationFail } from '@/components/Notification';
import { Form, Image, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import { useEffect, useState } from 'react';
import { history } from 'umi';
import styles from './index.less';
import { ITEM_PER_PAGE } from '@/utils/constants';
import type { TouristSpotDetailType } from '@/apis/purchase-and-material-management/touristDestinations';
import { getListTouristSpot } from '@/apis/purchase-and-material-management/touristDestinations';
import { useUrlSearchParams } from 'use-url-search-params';
import { formatMoney } from '@/utils';
import FileDollarIcon from '@/components/SVG/FileDollarIcon';
import InformationCircle from '@/components/SVG/InformationCircle';
import SearchSVG from '@/components/SVG/SearchSVGWhite';
import FileIcon from '@/components/SVG/FileIcon';
import STATUS_CODE from '@/constants/statusCode';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import PageContainer from '@/components/Commons/Page/Container';

const initSearchParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};

const TouristSpot: React.FC = () => {
  const [form] = Form.useForm();

  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [dataSource, setDataSource] = useState<TouristSpotDetailType[]>();

  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);

  const handleGetListTravelSpot = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getListTouristSpot(parameter);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dts = data.data?.map((item, index) => ({
          ...item,
          key: (Number(parameter?.page) - 1) * Number(parameter?.limit) + index + 1,
          prefecture: item?.prefecture?.name_province,
          municipalities: item?.city?.name_city,
        }));
        setDataSource(dts);
        setTotal(data.total);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail('サーバーエラー');
    }
  };

  useEffect(() => {
    handleGetListTravelSpot();
  }, [parameter]);

  const columns: ColumnsType<TouristSpotDetailType> = [
    {
      title: <div className="font-[500] text-[13px] leading-4">#</div>,
      dataIndex: 'key',
      key: 'key',
      width: 60,
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">管理番号</div>,
      dataIndex: 'control_number',
      key: 'control_number',
      width: 120,
      render: (_, { control_number, id }) => (
        <div
          className="text-sm leading-5 font-medium text-main-color max-w-[200px]"
          onClick={() =>
            history.push(`/purchase-and-material-management/tourist-destinations/${id}`)
          }
        >
          {control_number}
        </div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">観光地</div>,
      dataIndex: 'name_jp',
      key: 'name_jp',
      render: (_, { name_jp }) => (
        <div className="text-sm leading-5 font-medium text-[#383B46]">{name_jp}</div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">都道府県</div>,
      dataIndex: 'prefecture',
      key: 'prefecture',
      width: 140,
      render: (_, { prefecture }) => (
        <div>
          <div className="text-sm leading-5 font-medium text-[#383B46]">{prefecture}</div>
        </div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">市区町村</div>,
      dataIndex: 'municipalities',
      key: 'municipalities',
      width: 140,
      render: (_, { municipalities }) => (
        <div>
          <div className="text-sm leading-5 font-medium text-[#383B46]">{municipalities}</div>
        </div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">住所</div>,
      dataIndex: 'address',
      key: 'address',
      render: (_, { address }) => (
        <div>
          <div className="text-sm leading-5 font-medium text-[#383B46]">{address}</div>
        </div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">駐車場</div>,
      dataIndex: 'parking_lot',
      key: 'parking_lot',
      width: 100,
      render: (_, { parking_lot }) => (
        <div className="text-sm leading-5 font-medium text-[#383B46] line-clamp-1">
          {parking_lot === 1 ? '有' : '無'}
        </div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">駐車料金</div>,
      dataIndex: 'parking_fee',
      key: 'parking_fee',
      render: (_, { parking_fee }) => (
        <p className="text-sm leading-5 font-medium text-[#383B46] text-right">
          {parking_fee ? `￥${formatMoney(parking_fee)}` : 0}
        </p>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">メモ</div>,
      dataIndex: 'memo',
      key: 'memo',
      render: (_, { memo }) => (
        <Tooltip placement="topLeft" title={memo}>
          <div className="text-sm leading-5 font-medium text-[#383B46] max-w-[400px] line-clamp-1">
            {memo}
          </div>
        </Tooltip>
      ),
    },
    {
      title: null,
      dataIndex: 'handle',
      key: 'handle',
      width: 320,
      fixed: 'right',
      render: (_, { id }) => (
        <div className="flex items-center justify-between">
          <div
            onClick={() =>
              history.push(`/purchase-and-material-management/tourist-destinations/${id}/materials`)
            }
            className="flex items-center py-1 px-4 text-xs font-medium leading-6 text-main-color border-none gap-[4px]"
          >
            <FileIcon /> 資料
          </div>
          <div
            onClick={() =>
              history.push(
                `/purchase-and-material-management/tourist-destinations/${id}/price-list`,
              )
            }
            className="flex items-center py-1 px-4 text-xs font-medium leading-6 text-main-color border-none gap-[4px]"
          >
            <FileDollarIcon /> 料金表
          </div>
          <div
            onClick={() =>
              history.push(
                `/purchase-and-material-management/tourist-destinations/${id}/cancellation-fee`,
              )
            }
            className="flex items-center py-1 px-4 text-xs font-medium leading-6 text-[#FDAF2E] border-none gap-[4px]"
          >
            <InformationCircle /> 取消料情報
          </div>
        </div>
      ),
    },
  ];

  const onSearch = () => {
    form.submit();
  };

  const handleSearch = (values) => {
    const params = {
      ...parameter,
      prefecture_code: values?.city?.prefectures?.value,
      city_code: values?.city?.municipalities?.value,
      keyword: values.keyword,
      address: values?.address,
    };
    setParameter(params);
  };

  return (
    <PageContainer>
      {/* <div className={`${styles.container} text-[#424242]`}> */}
      <div className="flex justify-between">
        <Form form={form} onFinish={handleSearch}>
          <div className="flex space-x-[16px] items-end flex-1">
            <Form.Item noStyle name={'city'}>
              <CustomSelectPrefectures />
            </Form.Item>
            <Form.Item name={'address'} noStyle>
              <BasicInput
                placeholder={'住所'}
                title="住所"
                className="!min-w-[140px] !rounded-[0.5rem]"
                allowClear
              />
            </Form.Item>
            <Form.Item name={'keyword'} noStyle>
              <BasicInput
                placeholder={'キーワードで検索'}
                title="観光地"
                className="!min-w-[240px] !rounded-[0.5rem]"
                allowClear
              />
            </Form.Item>
            <BasicButton
              onClick={onSearch}
              className="flex items-center justify-center w-[120px] space-x-[8px] border-main-color"
              icon={<SearchSVG />}
              styleType={'accept'}
            >
              検索
            </BasicButton>
          </div>
        </Form>

        <div className="space-x-[16px] flex items-end">
          <BasicButton
            className="flex items-center justify-center space-x-[8px]"
            icon={<Image preview={false} src={IconAdd} width={16} height={16} />}
            styleType={'accept'}
            onClick={() =>
              history.push('/purchase-and-material-management/tourist-destinations/create')
            }
          >
            新規作成
          </BasicButton>
        </div>
      </div>

      <BasicTable
        tableProps={{
          scroll: { x: 2000 },
          loading: isLoading,
          columns: columns as any,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
        }}
        page={Number(parameter?.page) ?? 1}
        pageSize={Number(parameter?.limit) ?? 10}
        onChangePage={(p: number) => setParameter({ ...parameter, page: p })}
        total={total}
        onSelectPageSize={(v) => setParameter({ ...parameter, limit: v, page: 1 })}
      />
      {/* </div> */}
    </PageContainer>
  );
};

export default TouristSpot;
