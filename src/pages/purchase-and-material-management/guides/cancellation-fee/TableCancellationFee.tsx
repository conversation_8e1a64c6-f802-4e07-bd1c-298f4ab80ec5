import type { BaseParams } from '@/@types/request';
import type { FieldData } from 'rc-field-form/lib/interface';
import { Form, Image, Tooltip } from 'antd';
import React, { useRef, useState } from 'react';
import BasicButton from '@/components/Commons/BasicButton';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import { rules } from '@/constants/rules';
import BasicTable from '@/components/Commons/BasicTable';
import EditableCell from '@/components/Commons/EditableCell';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import BasicModal from '@/components/Commons/BasicModal';
import type { FormInstance } from 'antd/es/form';
import { deleteTariff, type TariffType } from '@/apis/purchase-and-material-management/tariff';
import { DELETE_POPUP, MESSAGE_ALERT } from '@/constants/commonMessage';
import { openNotificationDeleteSuccess, openNotificationFail } from '@/components/Notification';

interface Props {
  parameter: BaseParams;
  setParameter: (v: BaseParams) => void;
  setDataSource: React.Dispatch<React.SetStateAction<TariffType[]>>;
  dataSource: TariffType[];
  total: number;
  onFetchData: () => void;
  isEditPage: boolean;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
  setListItemChange: React.Dispatch<React.SetStateAction<string[]>>;
  listItemChange: string[];
  form: FormInstance<any>;
}

const TableCancellationFee: React.FC<Props> = ({
  parameter,
  setParameter,
  dataSource,
  onFetchData,
  total,
  isEditPage,
  isLoading,
  setIsLoading,
  listItemChange,
  setListItemChange,
  form,
}) => {
  const refModalConfirmChangePage = useRef(null);
  const refDeleteModal = useRef<BasicModalRef>(null);

  const [pageChange, setPageChange] = useState<number>(1);
  const [idRowDeleted, setIdRowDeleted] = useState<number>();

  const handleConfirmModalDeleteRow = (key) => {
    setIdRowDeleted(key);
    if (refDeleteModal) {
      refDeleteModal.current.open();
    }
  };

  const handleCloseModalDeleteRow = () => {
    setIdRowDeleted(undefined);
  };

  const confirmDeleteRow = async () => {
    setIsLoading(true);
    try {
      if (idRowDeleted) {
        const responseDelete = await deleteTariff(idRowDeleted);
        if (responseDelete?.data?.data === true) {
          openNotificationDeleteSuccess(MESSAGE_ALERT.DELETE_SUCCESS);
          onFetchData?.();
          refDeleteModal?.current?.close();
          setIdRowDeleted(undefined);
        } else {
          openNotificationFail(MESSAGE_ALERT.DELETE_FAILED);
        }
      }
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };

  const handleListItemChange = (changeField: FieldData[]) => {
    const nameFieldChange = changeField?.[0]?.name?.[0];
    const keyChange = nameFieldChange.split('.')[0];
    setListItemChange([...listItemChange, keyChange]);
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      render: (_, {}, index) => {
        const order = (Number(parameter?.page) - 1) * Number(parameter?.limit ?? 0) + 1 + index;
        return <>{order}</>;
      },
    },
    {
      title: 'タイトル',
      dataIndex: 'title',
      key: 'title',
      editable: true,
      formType: 'input',
      ruleFormItem: rules.isRequired,
      render: (_, { title }) => (
        <Tooltip title={title}>
          <p className="truncate">{title}</p>
        </Tooltip>
      ),
    },
    {
      title: '取消料',
      dataIndex: 'fee_cancel',
      key: 'fee_cancel',
      editable: true,
      formType: 'input',
      ruleFormItem: rules.requiredInput,
    },
    {
      title: '',
      dataIndex: 'deleteItem',
      key: 'deleteItem',
      width: 80,
      render: (_, record) => (
        <BasicButton
          styleType="danger"
          className="!h-[24px] w-[76px]"
          onClick={() => handleConfirmModalDeleteRow(record.id)}
        >
          <Image preview={false} src={IconDelete} width={12} height={13} />
          <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
        </BasicButton>
      ),
    },
  ];

  const columns = defaultColumns.map((col) => {
    if (!col.editable && !isEditPage) {
      return { ...col, width: col.width || 250 };
    }
    return {
      ...col,
      width: col.width || 250,
      onCell: (record: Record<string, string>) => ({
        record,
        editable: col.editable,
        isEditPage,
        dataIndex: col.dataIndex,
        title: col.title,
        formType: col.formType,
        form,
        ruleFormItem: col?.ruleFormItem,
        width: col.width || 250,
      }),
    };
  });

  const onConfirmChangePageWhenEdit = (pageConfirm: number) => {
    setPageChange(pageConfirm);
    refModalConfirmChangePage.current?.open();
  };

  return (
    <Form
      form={form}
      component={false}
      onFieldsChange={(changeField: FieldData[]) => handleListItemChange(changeField)}
    >
      <BasicTable
        tableProps={{
          scroll: { x: 700 },
          loading: isLoading,
          components: {
            body: {
              cell: EditableCell,
            },
          },
          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
        }}
        page={Number(parameter?.page) ?? 1}
        pageSize={Number(parameter?.limit) ?? 10}
        onChangePage={(p: number) => {
          if (listItemChange.length > 0) {
            onConfirmChangePageWhenEdit(p);
          } else {
            setParameter({
              ...parameter,
              page: Number(p),
            });
          }
        }}
        total={total}
        onSelectPageSize={(v) => setParameter({ ...parameter, limit: v, page: 1 })}
      />

      <BasicModal
        ref={refModalConfirmChangePage}
        title={'警告'}
        content={<>まだ保存していません。編集したデータが失われます。ページを変更しますか？</>}
        okText="ページを変更する"
        onSubmit={() => {
          setParameter({ ...parameter, page: pageChange });
          setListItemChange([]);
          refModalConfirmChangePage.current?.close();
        }}
      />

      {/* Modal */}
      <BasicModal
        ref={refDeleteModal}
        title={DELETE_POPUP.TITLE}
        content={
          <div className="whitespace-pre-line text-center">
            {TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
          </div>
        }
        onClose={handleCloseModalDeleteRow}
        onSubmit={confirmDeleteRow}
      />
    </Form>
  );
};

export default TableCancellationFee;
