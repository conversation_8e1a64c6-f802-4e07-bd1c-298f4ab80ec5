import CustomSelectPrefectures from '@/components/Commons/CustomSelectPrefectures';
import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { rules } from '@/constants/rules';
import { ReservationMethod } from '@/constants/data';
import { GENDER } from '@/constants/masterData';
import CustomSelectBusinessPartner from '@/components/Commons/CustomSelectBusinessPartner';

export const GuidesFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'name_jp',
    title: 'ガイド/会社 (日本語)',
    isRequired: true,
    placeholder: 'ガイド/会社 (日本語)',
    rules: rules.isJapanName,
    maxLength: 155,
  },
  {
    type: 'input',
    name: 'name_en',
    title: 'ガイド/会社 (英語)',
    placeholder: 'ガイド/会社 (英語)',
    rules: rules.isEnglishName,
    maxLength: 155,
  },
  {
    type: 'customField',
    name: 'city',
    component: <CustomSelectPrefectures isRequired layout="vertical" />,
    rules: rules.requiredSelectCity,
  },
  {
    type: 'input',
    name: 'address',
    title: '住所',
    placeholder: '住所',
  },
  {
    type: 'input',
    name: 'home_page',
    title: 'ホームページ',
    placeholder: 'ホームページ',
    rules: rules.requiredInputUrl,
  },
  {
    type: 'textArea',
    name: 'memo',
    title: 'メモ',
    placeholder: 'メモ',
    maxLength: 1000,
  },
  {
    type: 'checkbox',
    name: 'reservation_method',
    title: '予約方法',
    options: ReservationMethod,
  },
  {
    type: 'input',
    name: 'phone_number',
    title: '電話番号',
    placeholder: '03-1234-56789',
    rules: rules.isPhoneNumber,
  },
  {
    type: 'input',
    name: 'fax',
    title: 'FAX',
    placeholder: '03-1234-56789',
    rules: rules.isFax,
  },
  {
    type: 'input',
    name: 'email',
    title: 'メールアドレス',
    placeholder: 'メールアドレス',
    rules: rules?.isEmail,
  },
  {
    type: 'input',
    name: 'manager',
    title: '担当者',
    placeholder: '担当者',
    maxLength: 255,
  },
  {
    type: 'customField',
    name: 'business_partner_id',
    title: '取引先',
    component: <CustomSelectBusinessPartner placeholder="取引先" title="取引先" />,
  },
];

export const CancellationFeeFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'title',
    title: 'タイトル',
    isRequired: true,
    placeholder: 'タイトル',
    rules: rules.requiredInput,
    colSpan: 24,
    maxLength: 155,
  },
  {
    type: 'input',
    name: 'fee_cancel',
    title: '取消料',
    isRequired: true,
    placeholder: '取消料',
    rules: rules.requiredInput,
    colSpan: 24,
  },
];

export const PriceListGuideFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'title',
    title: 'タイトル',
    isRequired: true,
    placeholder: 'タイトル',
    rules: rules.requiredInput,
    colSpan: 24,
    maxLength: 155,
  },
  {
    type: 'select',
    name: 'gender',
    title: '性別',
    placeholder: '性別',
    options: GENDER,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'language',
    title: '言語',
    placeholder: '言語',
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'guide_age',
    title: '年齢',
    placeholder: '年齢',
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'guide_experience',
    title: '経験年数',
    placeholder: '経験年数',
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'guide_fee_per_day',
    title: '料金 (1日)',
    placeholder: '料金 (1日)',
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'guide_tip',
    title: 'チップ',
    placeholder: 'チップ',
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'guide_other_fee',
    title: 'その他',
    placeholder: 'その他',
    colSpan: 24,
  },
];
