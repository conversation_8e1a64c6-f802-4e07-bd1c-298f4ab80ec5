import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { CancellationFeeFormFieldItem } from './GuidesFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type GuideCancellationFormRef = {
  form: FormInstance<any>;
};

const GuideFormCancellationFee = forwardRef<GuideCancellationFormRef, Props>(
  ({ onSubmit }, ref) => {
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
      form,
    }));
    return (
      <Form form={form} onFinish={onSubmit}>
        {CancellationFeeFormFieldItem?.map((item) => (
          <CommonRenderFormField key={item?.name} field={item} />
        ))}
      </Form>
    );
  },
);

export default GuideFormCancellationFee;
