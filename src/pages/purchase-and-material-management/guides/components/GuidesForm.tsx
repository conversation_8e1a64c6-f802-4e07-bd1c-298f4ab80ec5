import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance, FormProps } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { GuidesFormFieldItem } from './GuidesFieldItem';

export type GuideFormRef = {
  form?: FormInstance<any>;
};

interface Props {
  formProps?: FormProps;
}

const FormGuide = forwardRef<GuideFormRef, Props>(({ formProps }, ref) => {
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} autoComplete="off" {...formProps}>
      {GuidesFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default FormGuide;
