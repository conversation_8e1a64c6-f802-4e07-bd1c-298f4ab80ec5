import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { PriceListGuideFormFieldItem } from './GuidesFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type GuidePriceItemTypeFormRef = {
  form: FormInstance<any>;
};

const GuideFormPrice = forwardRef<GuidePriceItemTypeFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} onFinish={onSubmit}>
      {PriceListGuideFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default GuideFormPrice;
