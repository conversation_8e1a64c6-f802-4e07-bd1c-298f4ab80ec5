import {
  getListRailway,
  type RailwayDetailType,
} from '@/apis/purchase-and-material-management/railway';
import BasicButton from '@/components/Commons/BasicButton';
import BasicInput from '@/components/Commons/BasicInput';
import BasicTable from '@/components/Commons/BasicTable';
import IconAdd from '@/assets/imgs/common-icons/icon-add-white.svg';
import { openNotificationFail } from '@/components/Notification';
import FileDollarIcon from '@/components/SVG/FileDollarIcon';
import InformationCircle from '@/components/SVG/InformationCircle';
import SearchSVG from '@/components/SVG/SearchSVGWhite';
import { ITEM_PER_PAGE } from '@/utils/constants';
import { Form, Image, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, { useEffect, useState } from 'react';
import { history } from 'umi';
import { useUrlSearchParams } from 'use-url-search-params';
import FileIcon from '@/components/SVG/FileIcon';
import STATUS_CODE from '@/constants/statusCode';
import { MESSAGE_ALERT } from '@/constants/commonMessage';

const initSearchParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};
const Railways = () => {
  const [form] = Form.useForm();

  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);
  const [dataSource, setDataSource] = useState<RailwayDetailType[]>();

  const onFetchData = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getListRailway(parameter);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dts = data.data?.map((item, index) => ({
          ...item,
          key: (Number(parameter?.page) - 1) * Number(parameter?.limit) + index + 1,
        }));
        setDataSource(dts);
        setTotal(data.total);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    onFetchData();
  }, [parameter]);

  const columns: ColumnsType<RailwayDetailType> = [
    {
      title: <div className="font-[500] text-[13px] leading-4">#</div>,
      dataIndex: 'key',
      width: '100px',
      key: 'key',
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">管理番号</div>,
      dataIndex: 'control_number',
      width: '10%',
      key: 'control_number',
      render: (_, { control_number, id }) => (
        <div
          className="text-sm leading-5 font-medium text-main-color max-w-[200px]"
          onClick={() => history.push(`/purchase-and-material-management/railways/${id}`)}
        >
          {control_number}
        </div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">鉄道</div>,
      dataIndex: 'name_jp',
      width: '20%',
      key: 'name_jp',
      render: (_, { name_jp }) => (
        <div className="text-sm leading-5 font-medium text-[#383B46]">{name_jp}</div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">メモ</div>,
      dataIndex: 'memo',
      key: 'memo',
      render: (_, { memo }) => (
        <Tooltip placement="topLeft" title={memo}>
          <div className="text-sm leading-5 font-medium text-[#383B46] max-w-[400px] line-clamp-1">
            {memo}
          </div>
        </Tooltip>
      ),
    },
    {
      title: null,
      dataIndex: 'handle',
      width: '320px',
      key: 'handle',
      fixed: 'right',
      render: (_, { id }) => (
        <div className="flex items-center justify-between">
          <div
            onClick={() =>
              history.push(`/purchase-and-material-management/railways/${id}/materials`)
            }
            className="flex items-center py-1 px-4 text-xs font-medium leading-6 text-main-color border-none gap-[4px]"
          >
            <FileIcon /> 資料
          </div>
          <div
            onClick={() =>
              history.push(`/purchase-and-material-management/railways/${id}/price-list`)
            }
            className="flex items-center py-1 px-4 text-xs font-medium leading-6 text-main-color border-none gap-[4px]"
          >
            <FileDollarIcon /> 料金表
          </div>
          <div
            onClick={() =>
              history.push(`/purchase-and-material-management/railways/${id}/cancellation-fee`)
            }
            className="flex items-center py-1 px-4 text-xs font-medium leading-6 text-[#FDAF2E] border-none gap-[4px]"
          >
            <InformationCircle /> 取消料情報
          </div>
        </div>
      ),
    },
  ];

  const onSearch = () => {
    form.submit();
  };

  const handleSearch = (values) => {
    const params = {
      ...parameter,
      keyword: values.keyword,
    };
    setParameter(params);
  };

  return (
    <div className={`px-[30px] py-[24px] text-[#424242]`}>
      <div className="flex justify-between">
        <Form form={form} onFinish={handleSearch}>
          <div className="flex space-x-[16px] items-end flex-1">
            {/* <Form.Item noStyle name={'city'}>
              <CustomSelectPrefectures />
            </Form.Item> */}

            <Form.Item name={'keyword'} noStyle>
              <BasicInput
                placeholder={'キーワードで検索する'}
                title="鉄道"
                className="!min-w-[240px] !rounded-[0.5rem]"
                allowClear
              />
            </Form.Item>
            <BasicButton
              onClick={onSearch}
              className="flex items-center justify-center w-[120px] space-x-[8px] border-main-color"
              icon={<SearchSVG />}
              styleType={'accept'}
            >
              検索
            </BasicButton>
          </div>
        </Form>

        <div className="space-x-[16px] flex items-end">
          <BasicButton
            className="flex items-center justify-center space-x-[8px]"
            icon={<Image preview={false} src={IconAdd} width={16} height={16} />}
            styleType={'accept'}
            onClick={() => history.push('/purchase-and-material-management/railways/create')}
          >
            新規作成
          </BasicButton>
        </div>
      </div>

      <BasicTable
        tableProps={{
          scroll: { x: 1300 },
          loading: isLoading,
          columns: columns as any,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
        }}
        page={Number(parameter?.page) ?? 1}
        pageSize={Number(parameter?.limit) ?? 10}
        onChangePage={(p: number) => setParameter({ ...parameter, page: p })}
        total={total}
        onSelectPageSize={(v) => setParameter({ ...parameter, limit: v, page: 1 })}
      />
    </div>
  );
};

export default Railways;
