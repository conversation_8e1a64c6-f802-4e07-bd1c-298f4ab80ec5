import {
  deleteFileMasterDataByEntity,
  getFileMasterDataByEntity,
  UploadFileEntityEnum,
} from '@/apis/purchase-and-material-management/tariff';
import BasicButton from '@/components/Commons/BasicButton';
import BasicInput from '@/components/Commons/BasicInput';
import { openNotificationDeleteSuccess, openNotificationFail } from '@/components/Notification';
import SearchSVG from '@/components/SVG/SearchSVGWhite';
import { initSearchParams } from '@/constants';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { LeftOutlined } from '@ant-design/icons';
import { Form } from 'antd';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { history } from 'umi';
import { useUrlSearchParams } from 'use-url-search-params';
import UploadMasterialFile from '../../components/UploadMasterialFile';
import AttachmentTableList from './AttachmentTableList';

const Materials = () => {
  const { id } = useParams() as any;
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [listAttachedFile, setListAttachedFile] = useState<any[]>([]);
  const [searchValue, setSearchvalue] = useState<string>();

  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);
  const [total, setTotal] = useState<number>(0);

  const handleFetchData = async () => {
    try {
      const payload = {
        ...parameter,
        type: UploadFileEntityEnum.bullet_train,
        id: id,
        keyword: searchValue,
      };
      const { data, status } = await getFileMasterDataByEntity(payload);

      if (status === STATUS_CODE.SUCCESSFUL) {
        const dts = data?.data;
        setListAttachedFile(dts);
        setTotal(data?.total);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleDeleteAttachedFile = async (url: string) => {
    setIsLoading(true);
    try {
      const { status } = await deleteFileMasterDataByEntity(url);
      if (status === STATUS_CODE.SUCCESSFUL) {
        openNotificationDeleteSuccess(MESSAGE_ALERT.DELETE_SUCCESS);
        handleFetchData();
      } else {
        openNotificationFail(MESSAGE_ALERT.DELETE_FAILED);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
    setIsLoading(false);
  };

  const handleSearch = (values) => {
    setSearchvalue(values.keyword);
  };

  useEffect(() => {
    handleFetchData();
  }, [searchValue, parameter?.page, parameter?.limit]);

  return (
    <div
      className={`px-[30px] py-[24px] flex flex-col`}
      style={{
        minHeight: 'calc(100vh - 80px)',
      }}
    >
      <div className="flex-1 flex flex-col justify-between">
        <div className="flex-1 rounded-md mb-4 p-4">
          <div className="flex justify-between items-end mb-4 flex-1">
            <div className="flex gap-x-[24px] items-end">
              <Form form={form} onFinish={handleSearch}>
                <Form.Item name={'keyword'} noStyle>
                  <BasicInput allowClear title={'ファイル名'} placeholder="キーワードで検索する" />
                </Form.Item>
              </Form>
              <BasicButton
                onClick={() => form.submit()}
                className="flex items-center justify-center w-[120px] space-x-[8px] border-main-color"
                icon={<SearchSVG />}
                styleType={'accept'}
              >
                検索
              </BasicButton>
            </div>
            <UploadMasterialFile
              id={id}
              type={UploadFileEntityEnum.bullet_train}
              handleFetchData={handleFetchData}
              setIsLoading={setIsLoading}
            />
          </div>
          <AttachmentTableList
            handleDeleteAttachedFile={handleDeleteAttachedFile}
            listAttachedFile={listAttachedFile}
            loading={isLoading}
            parameter={parameter}
            setParameter={setParameter}
            total={total}
          />
        </div>
        <div className="flex justify-center items-center my-4 gap-5">
          <BasicButton
            styleType="back"
            className="w-[290px] flex items-center justify-center"
            onClick={() => history.goBack()}
          >
            <LeftOutlined width={16} height={16} /> {TEXT_ACTION.RETURN}
          </BasicButton>
          {/* <BasicButton styleType="accept" className="w-[290px]">
            <SaveSvg /> 保存
          </BasicButton> */}
        </div>
      </div>
    </div>
  );
};

export default Materials;
