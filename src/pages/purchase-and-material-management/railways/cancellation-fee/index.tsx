import type { BasicFormModalRef } from '@/components/Commons/BasicFormModal';
import PageContainer from '@/components/Commons/Page/Container';
import React, { useEffect, useRef, useState } from 'react';
import { Form } from 'antd';
import { useUrlSearchParams } from 'use-url-search-params';
import { useParams } from 'react-router';
import STATUS_CODE from '@/constants/statusCode';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_PLACEHOLDER } from '@/constants/commonText';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import BasicButton from '@/components/Commons/BasicButton';
import {
  DownloadOutlined,
  LeftOutlined,
  PlusOutlined,
  SaveOutlined,
  SearchOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import BasicInputSearch from '@/components/Commons/BasicInputSearch';
import type { RailwayCancellationFormRef } from '../components/RailwaysFormCancellationFee';
import RailwayFormCancellationFee from '../components/RailwaysFormCancellationFee';
import TableCancellationFee from './TableCancellationFee';
import { initSearchParams } from '@/constants';
import type { ImportCsvCommonRef } from '@/components/CommonModalImportCsv';
import {
  createTariff,
  EntityCancelFeeEnum,
  EntityEnum,
  getListTariffByEntity,
  updateTariff,
  type TariffType,
} from '@/apis/purchase-and-material-management/tariff';
import { exportCsvCancelFeeRailway } from '@/apis/purchase-and-material-management/railway';
import CommonModalImportCsv from '@/components/CommonModalImportCsv';
import { history } from 'umi';
import { CANCEL_PRICE_TITLE } from '@/utils/constants';

const TouristCancellationFee = () => {
  const [form] = Form.useForm();

  const refModalCreateMaster = useRef<BasicFormModalRef>(null);
  const refFormAddNewItem = useRef<RailwayCancellationFormRef>(null);
  const refImportCsv = useRef<ImportCsvCommonRef>(null);

  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [listItemChange, setListItemChange] = useState<string[]>([]);
  const [dataSource, setDataSource] = useState<TariffType[]>([]);
  const [isEditPage, setIsEditPage] = useState(false);
  const [total, setTotal] = useState(0);
  const [valueSearch, setValueSearch] = useState<string>();

  const { id } = useParams() as any;

  const onFetchData = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getListTariffByEntity(
        { ...parameter, type: 'cancel_fee', entity_id: id },
        EntityEnum['bullet-train'],
      );
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dts = data.data?.map((item) => ({ ...item, key: item.id }));
        setDataSource(dts);
        setTotal(data.total);
      }
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    onFetchData();
  }, [parameter?.keyword, parameter?.limit, parameter?.page]);

  const removeListItemChange = () => {
    setListItemChange([]);
  };

  useEffect(() => {
    dataSource.forEach((item) => {
      Object.keys(item).forEach((key) => {
        const keyForm = `${item.key}.${key}`;
        form.setFieldValue(keyForm, item[key]?.toString());
      });
    });
    removeListItemChange();
  }, [dataSource]);

  const handleAddNew = async (values) => {
    try {
      const payload = {
        type: 'cancel_fee',
        tariffable_id: id,
        data: [{ ...values }],
      };
      const responseCreate = await createTariff(EntityEnum['bullet-train'], payload);
      if (
        responseCreate.status === STATUS_CODE.SUCCESSFUL ||
        responseCreate.status === STATUS_CODE.CREATED
      ) {
        openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
        refModalCreateMaster?.current?.close();
        setParameter(initSearchParams);
        onFetchData();
        refFormAddNewItem?.current?.form?.resetFields();
      } else if (responseCreate.status === STATUS_CODE.INVALID) {
        const errors = responseCreate?.error?.data?.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key];
            const keyForm = key.split('.')?.[2];
            refFormAddNewItem?.current?.form.setFields([
              {
                name: keyForm,
                errors: message,
              },
            ]);
          });
        }
      } else {
        openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const saveEdit = async () => {
    setIsLoading(true);
    try {
      if (listItemChange.length === 0) {
        setIsLoading(false);
        setIsEditPage(false);
        return;
      }
      const values = await form.validateFields();
      const resultObj = Object.keys(values).reduce((acc, key) => {
        const [index, property] = key.split('.');
        const value = values[key];

        if (!acc[index]) {
          acc[index] = {};
        }

        acc[index][property] = value;

        return acc;
      }, {});

      const newData = [...dataSource];

      Object.keys(resultObj).forEach((key) => {
        const index = dataSource.findIndex((item) => item.key === Number(key));
        newData[index] = { ...dataSource[index], ...resultObj[key] };
      });

      // Filter data change
      const dataChange = newData.filter((item) => listItemChange.includes(item.key.toString()));

      const dataUpdate = dataChange.map((item) => {
        delete item.key;
        return item;
      });
      const payload: any = {
        type: 'cancel_fee',
        tariffable_id: id,
        data: dataUpdate,
      };

      // => Call API here
      const responseUpdate = await updateTariff(payload, EntityEnum['bullet-train']);
      if (
        responseUpdate.status === STATUS_CODE.SUCCESSFUL ||
        responseUpdate.status === STATUS_CODE.CREATED
      ) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        onFetchData();
        setIsEditPage(false);
        setIsLoading(false);

        // Reset when update successfully
        removeListItemChange();
      } else if (responseUpdate.status === STATUS_CODE.INVALID) {
        const errors = responseUpdate?.error?.data?.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const nameField = key.split('.').slice(1).join('.');
            const message = errors[key];
            form.setFields([
              {
                name: nameField,
                errors: message,
              },
            ]);
          });
        }
        setIsLoading(false);
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
        setIsLoading(false);
      }
    } catch (errInfo) {
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      setIsLoading(false);
      console.log('Save failed:', errInfo);
    }
  };

  const onImportCsv = () => {
    if (refImportCsv) {
      refImportCsv.current.open();
    }
  };

  const onExportCsv = async () => {
    const cloneParams = { ...parameter };
    const responseExport = await exportCsvCancelFeeRailway(
      {
        ...cloneParams,
        limit: 'all',
        page: 1,
      },
      id,
    );
    if (responseExport.status === STATUS_CODE.SUCCESSFUL) {
      const urlCsv = responseExport?.data?.file_link;
      if (urlCsv) {
        const a = document.createElement('a');
        a.href = urlCsv;
        a.download = 'csv_cancel_fee_railway.csv';
        a.click();
        window.URL.revokeObjectURL(urlCsv);
      }
    }
  };

  const openModal = () => {
    refFormAddNewItem?.current?.form?.resetFields();
    if (refModalCreateMaster) {
      refModalCreateMaster.current.open();
    }
  };

  return (
    <PageContainer>
      <div>
        <div className="flex justify-between items-end">
          <div className="flex items-end gap-x-[24px]">
            <BasicInputSearch
              style={{
                width: '280px',
                height: '40px',
              }}
              value={valueSearch}
              onChange={(e) => setValueSearch(e.target.value)}
              title={'タイトル'}
              placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
            />
            <BasicButton
              onClick={() => setParameter({ ...parameter, page: 1, keyword: valueSearch })}
              icon={<SearchOutlined />}
              styleType="accept"
              className="flex items-center min-w-[120px]"
            >
              {TEXT_ACTION.SEARCH}
            </BasicButton>
          </div>
          <div className="flex gap-x-[12px]">
            <BasicButton
              icon={<SaveOutlined style={{ color: '#225DE0' }} />}
              className="flex items-center !text-[#225DE0]"
              styleType="noneOutLine"
              onClick={() => (isEditPage ? saveEdit() : setIsEditPage(true))}
            >
              {isEditPage ? TEXT_ACTION.SAVE : TEXT_ACTION.EDIT}
            </BasicButton>
            <BasicButton
              icon={<UploadOutlined style={{ color: '#EC980C' }} />}
              className="flex items-center !text-[#EC980C] !bg-white"
              styleType="noneOutLine"
              onClick={onImportCsv}
            >
              {TEXT_ACTION.CSV_Import}
            </BasicButton>
            <BasicButton
              icon={<DownloadOutlined style={{ color: '#3997C8' }} />}
              className="flex items-center !text-main-color !bg-white"
              styleType="noneOutLine"
              onClick={onExportCsv}
            >
              {TEXT_ACTION.CSV_Export}
            </BasicButton>
            <BasicButton
              icon={<PlusOutlined />}
              onClick={openModal}
              className="flex items-center"
              styleType="accept"
            >
              {TEXT_ACTION.CREATE_NEW}
            </BasicButton>
          </div>
        </div>
        <TableCancellationFee
          form={form}
          parameter={parameter}
          setParameter={setParameter}
          setDataSource={setDataSource}
          dataSource={dataSource}
          total={total}
          onFetchData={onFetchData}
          isEditPage={isEditPage}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
          listItemChange={listItemChange}
          setListItemChange={setListItemChange}
        />
        <div className="flex justify-center items-center my-4 gap-5">
          <BasicButton
            styleType="back"
            className="w-[290px] flex items-center justify-center"
            onClick={() => history.goBack()}
          >
            <LeftOutlined width={16} height={16} /> {TEXT_ACTION.RETURN}
          </BasicButton>
          {/* <BasicButton styleType="accept" className="w-[290px]">
            <SaveSvg /> 保存
          </BasicButton> */}
        </div>
        <BasicFormModal
          ref={refModalCreateMaster}
          content={<RailwayFormCancellationFee ref={refFormAddNewItem} onSubmit={handleAddNew} />}
          title={CANCEL_PRICE_TITLE}
          buttonCloseTitle={TEXT_ACTION.CANCEL}
          buttonSubmitTitle={TEXT_ACTION.SAVE}
          buttonSubmitStyle={{ backgroundColor: '#3997C8' }}
          onSubmit={() => refFormAddNewItem?.current?.form?.submit()}
          isValidate={true}
        />

        <CommonModalImportCsv
          confirm={onFetchData}
          ref={refImportCsv}
          type={EntityCancelFeeEnum['bullet-train-cancel-fee']}
          _entity_id={id}
        />
      </div>
    </PageContainer>
  );
};

export default TouristCancellationFee;
