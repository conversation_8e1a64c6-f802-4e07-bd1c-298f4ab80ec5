import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { rules } from '@/constants/rules';
import { optionPaymentmethod } from '../../accommodation/components/HotelFormFieldItem';
import { ReservationMethod } from '@/constants/data';
import CustomSelectBusinessPartner from '@/components/Commons/CustomSelectBusinessPartner';
import { Departure_Arrival_Option } from '@/constants/prefectures';

export const RailwaysFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'name_jp',
    title: '鉄道 (日本語)',
    isRequired: true,
    placeholder: '鉄道 (日本語)',
    rules: rules.isJapanName,
    maxLength: 155,
  },
  {
    type: 'input',
    name: 'name_en',
    title: '鉄道 (英語)',
    placeholder: '鉄道 (英語)',
    rules: rules.isEnglishName,
    maxLength: 155,
  },
  {
    type: 'input',
    name: 'home_page',
    title: 'ホームページ',
    placeholder: 'ホームページ',
    rules: rules.requiredInputUrl,
  },
  {
    type: 'checkbox',
    name: 'reservation_method',
    title: '予約方法',
    options: ReservationMethod,
  },
  {
    type: 'checkbox',
    name: 'payment_method',
    title: '支払方法',
    options: optionPaymentmethod,
    isRequired: true,
    rules: rules.isRequired,
  },
  {
    type: 'textArea',
    name: 'memo',
    title: 'メモ',
    placeholder: 'メモ',
    maxLength: 1000,
  },
  {
    type: 'input',
    name: 'phone_number',
    title: '電話番号',
    placeholder: '03-1234-56789',
    rules: rules.isPhoneNumber,
  },
  {
    type: 'input',
    name: 'fax',
    title: 'FAX',
    placeholder: '03-1234-56789',
    rules: rules.isFax,
  },
  {
    type: 'input',
    name: 'email',
    title: 'メールアドレス',
    placeholder: 'メールアドレス',
    rules: rules?.isEmail,
  },
  {
    type: 'input',
    name: 'manager',
    title: '担当者',
    placeholder: '担当者',
    maxLength: 255,
  },
  {
    type: 'customField',
    name: 'business_partner_id',
    title: '取引先',
    component: <CustomSelectBusinessPartner placeholder="取引先" title="取引先" />,
  },
];

export const CancellationFeeFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'title',
    title: 'タイトル',
    isRequired: true,
    placeholder: 'タイトル',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'fee_cancel',
    title: '取消料',
    isRequired: true,
    placeholder: '取消料',
    rules: rules.requiredInput,
    colSpan: 24,
  },
];

export const PriceListRailwayFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'place_departure',
    title: '出発地',
    isRequired: true,
    placeholder: '出発地',
    // options: Departure_Arrival_Option,
    rules: rules.requiredInput,
    colSpan: 24,
    maxLength: 155,
  },
  {
    type: 'input',
    name: 'place_arrival',
    title: '目的地',
    placeholder: '目的地',
    isRequired: true,
    // options: Departure_Arrival_Option,
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'bullet_train_adult_fee',
    title: '大人',
    placeholder: '大人',
    // isRequired: true,
    // rules: rules.isRequired,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'bullet_train_adult_green_fee',
    title: 'グリーン車',
    placeholder: 'グリーン車',
    // isRequired: true,
    // rules: rules.isRequired,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'bullet_train_adult_child',
    title: '子供',
    placeholder: '子供',
    // isRequired: true,
    // rules: rules.isRequired,
    colSpan: 24,
  },
];
