import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { PriceListRailwayFormFieldItem } from './RailwaysFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type RailwayPriceItemTypeFormRef = {
  form: FormInstance<any>;
};

const RailwayFormPrice = forwardRef<RailwayPriceItemTypeFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} onFinish={onSubmit}>
      {PriceListRailwayFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default RailwayFormPrice;
