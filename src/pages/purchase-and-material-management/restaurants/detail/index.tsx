import { useEffect, useRef, useState } from 'react';
import { history, useParams } from 'umi';
import { openNotificationDeleteSuccess, openNotificationFail } from '@/components/Notification';
import { Col, Row, Spin } from 'antd';
import STATUS_CODE from '@/constants/statusCode';
import { DELETE_POPUP, MESSAGE_ALERT } from '@/constants/commonMessage';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import BasicModal from '@/components/Commons/BasicModal';
import ViewFieldItem from '@/components/pages/BusinessPartner/ViewFieldItem';
import {
  optionPaymentmethod,
  optionYesNo,
} from '../../accommodation/components/HotelFormFieldItem';
import BasicButton from '@/components/Commons/BasicButton';
import { ArrowLeftOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { TEXT_ACTION } from '@/constants/commonText';
import {
  deleteRestaurant,
  getRestaurantDetail,
  type RestaurantDetaiType,
} from '@/apis/purchase-and-material-management/restaurant';
import { ReservationMethod } from '@/constants/data';

const RestaurantDetail = () => {
  const { id } = useParams() as any;

  const refDeleteModal = useRef<BasicModalRef>(null);

  const [isLoading, setIsLoading] = useState(false);
  const [dataDetail, setDataDetal] = useState<RestaurantDetaiType>();

  const fetchDataDetail = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getRestaurantDetail(id);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const res = data as any;
        const resDetail = {
          ...res.data,
          prefecture: res?.data?.prefecture?.name_province,
          municipalities: res?.data?.city?.name_city,
        };

        setDataDetal(resDetail);
        setIsLoading(false);
      } else {
        setIsLoading(false);
        openNotificationFail(MESSAGE_ALERT?.SERVER_ERROR);
      }
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT?.SERVER_ERROR);
    }
  };

  useEffect(() => {
    fetchDataDetail();
  }, [id]);

  const onOpenModalDelete = () => {
    if (refDeleteModal) {
      refDeleteModal.current.open();
    }
  };

  const onDelete = async () => {
    setIsLoading(true);
    try {
      const { status } = await deleteRestaurant(Number(id));

      if (status === STATUS_CODE.SUCCESSFUL) {
        openNotificationDeleteSuccess(MESSAGE_ALERT.DELETE_SUCCESS);
        history.push('/purchase-and-material-management/restaurants');
      } else {
        openNotificationFail(MESSAGE_ALERT.DELETE_FAILED);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  return (
    <Spin spinning={isLoading}>
      <div
        className="mx-8 my-6 px-6 py-4 bg-[#fff] rounded-xl flex flex-col"
        style={{
          minHeight: 'calc(100vh - 120px)',
        }}
      >
        <div className="space-y-[20px]">
          <ViewFieldItem
            label={'観光地名称 (日本語)'}
            type="text"
            value={dataDetail?.name_jp ?? ''}
            options={null}
          />
          <ViewFieldItem
            label={'観光地名称 (英語)'}
            type="text"
            value={dataDetail?.name_en ?? ''}
            options={null}
          />
          <ViewFieldItem
            label={'都道府県'}
            type="text"
            value={dataDetail?.prefecture ?? ''}
            options={null}
          />
          <ViewFieldItem
            label={'市区町村'}
            type="text"
            value={dataDetail?.municipalities ?? ''}
            options={null}
          />
          <ViewFieldItem
            label={'住所'}
            type="text"
            value={dataDetail?.address ?? ''}
            options={null}
          />
          <ViewFieldItem
            label={'ホームページ'}
            type="text"
            value={dataDetail?.home_page ?? ''}
            options={null}
          />
          <ViewFieldItem
            label={'予約方法'}
            type="checkbox"
            value={dataDetail?.reservation_method ?? ''}
            options={ReservationMethod}
          />
          <ViewFieldItem
            label={'支払方法'}
            type="checkbox"
            value={dataDetail?.payment_method ?? ''}
            options={optionPaymentmethod}
          />
          <ViewFieldItem
            label={'駐車場'}
            type="radio"
            value={dataDetail?.parking_lot ?? ''}
            options={optionYesNo}
          />
          <ViewFieldItem
            label={'駐車料金'}
            type="text"
            value={dataDetail?.parking_fee ?? ''}
            options={null}
          />
          <ViewFieldItem
            label={'メモ'}
            type="textArea"
            value={dataDetail?.memo ?? ''}
            options={null}
          />
          <ViewFieldItem
            label={'電話番号'}
            type="text"
            value={dataDetail?.phone_number ?? ''}
            options={null}
          />
          <ViewFieldItem label={'FAX'} type="text" value={dataDetail?.fax ?? ''} options={null} />
          <ViewFieldItem
            label={'メールアドレス'}
            type="text"
            value={dataDetail?.email ?? ''}
            options={null}
          />

          <ViewFieldItem
            label={'担当者'}
            type="text"
            value={dataDetail?.manager ?? ''}
            options={null}
          />
          <ViewFieldItem
            label={'取引先'}
            type="text"
            value={dataDetail?.business_partner?.business_partner_name ?? ''}
            options={null}
          />

          <Row>
            <Col span={12}>
              <div className="flex items-center justify-center gap-x-3 pb-6">
                <BasicButton
                  onClick={() => history.goBack()}
                  styleType="noneOutLine"
                  icon={<ArrowLeftOutlined />}
                  className="flex-1 flex items-center justify-center gap-1"
                >
                  {TEXT_ACTION.RETURN}
                </BasicButton>
                <BasicButton
                  styleType="delete"
                  onClick={onOpenModalDelete}
                  icon={<DeleteOutlined />}
                  className="flex-1 flex items-center justify-center gap-1"
                >
                  {TEXT_ACTION.DELETE}
                </BasicButton>
                <BasicButton
                  styleType="accept"
                  icon={<EditOutlined />}
                  onClick={() => {
                    history.push(`/purchase-and-material-management/restaurants/edit/${id}`);
                  }}
                  className="flex-1 flex items-center justify-center gap-1"
                >
                  {TEXT_ACTION.EDIT}
                </BasicButton>
              </div>
            </Col>
          </Row>
        </div>
      </div>

      {/* Modal */}
      <BasicModal
        ref={refDeleteModal}
        title={DELETE_POPUP.TITLE}
        content={<div className="whitespace-pre-line text-center">{DELETE_POPUP.CONTENT}</div>}
        onSubmit={onDelete}
      />
    </Spin>
  );
};

export default RestaurantDetail;
