import React, { useEffect, useRef, useState } from 'react';
import { Col, Row, Spin } from 'antd';
import BasicButton from '@/components/Commons/BasicButton';
import { history } from 'umi';
import { openNotificationSuccess } from '@/components/Notification';
import STATUS_CODE from '@/constants/statusCode';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION } from '@/constants/commonText';
import { SaveOutlined } from '@ant-design/icons';
import type { restaurantFormRef } from '../components/RestaurantForm';
import { createRestaurant } from '@/apis/purchase-and-material-management/restaurant';
import FormRestaurant from '../components/RestaurantForm';
import { optionYesNo } from '../../accommodation/components/HotelFormFieldItem';

const CreateRestaurant = () => {
  const refFormCreate = useRef<restaurantFormRef>();

  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (refFormCreate) {
      refFormCreate.current.form.setFieldsValue({
        parking_lot: optionYesNo?.[1]?.value,
      });
    }
  }, []);

  const onSubmit = async () => {
    await refFormCreate.current.form.validateFields();
    setIsLoading(true);
    try {
      const values = refFormCreate.current.form.getFieldsValue();
      const body = {
        ...values,
        business_partner_id: values?.business_partner_id?.value,
        prefecture_code: values?.city?.prefectures?.value,
        city_code: values?.city?.municipalities?.value,
      };
      delete body.city;

      const { status } = await createRestaurant(body);
      if (status === STATUS_CODE?.CREATED) {
        history.push('/purchase-and-material-management/restaurants');
        openNotificationSuccess(MESSAGE_ALERT?.CREATED_SUCCESS);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.log(error);
    }
  };

  return (
    <Spin spinning={isLoading}>
      <div
        className="mx-8 my-6 px-6 py-4 bg-[#fff] rounded-xl flex flex-col"
        style={{
          minHeight: 'calc(100vh - 120px)',
        }}
      >
        <FormRestaurant ref={refFormCreate} />
        <Row>
          <Col span={12}>
            <div className="flex items-center gap-x-[20px]">
              <BasicButton
                styleType="noneOutLine"
                onClick={() => history.goBack()}
                className="flex-1"
              >
                {TEXT_ACTION.CANCEL}
              </BasicButton>
              <BasicButton
                className="flex items-center flex-1"
                styleType="accept"
                onClick={onSubmit}
                icon={<SaveOutlined width={16} height={16} />}
              >
                {TEXT_ACTION.SAVE}
              </BasicButton>
            </div>
          </Col>
        </Row>
      </div>
    </Spin>
  );
};

export default CreateRestaurant;
