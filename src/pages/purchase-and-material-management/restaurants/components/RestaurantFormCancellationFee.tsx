import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { RestaurantsCancellationFeeFieldItem } from './RestaurantsFieldItem';
interface Props {
  onSubmit: (values) => void;
}

export type RestaurantCancellationFormRef = {
  form: FormInstance<any>;
};

const RestaurantsFormCancellationFee = forwardRef<RestaurantCancellationFormRef, Props>(
  ({ onSubmit }, ref) => {
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
      form,
    }));
    return (
      <Form form={form} onFinish={onSubmit}>
        {RestaurantsCancellationFeeFieldItem?.map((item) => (
          <CommonRenderFormField key={item?.name} field={item} />
        ))}
      </Form>
    );
  },
);

export default RestaurantsFormCancellationFee;
