import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance, FormProps } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle, useMemo } from 'react';
import { RestaurantsFormFieldItem } from './RestaurantsFieldItem';

export type restaurantFormRef = {
  form?: FormInstance<any>;
};

interface Props {
  formProps?: FormProps;
}

const FormRestaurant = forwardRef<restaurantFormRef, Props>(({ formProps }, ref) => {
  const [form] = Form.useForm();
  const hasParking = Form.useWatch('parking_lot', form);

  useImperativeHandle(ref, () => ({
    form,
  }));

  const newFields = useMemo(() => {
    if (hasParking === 0) {
      return RestaurantsFormFieldItem?.map((item) => {
        if (item.name === 'parking_fee') {
          return {
            ...item,
            disabled: true,
          };
        } else {
          return item;
        }
      });
    } else {
      return RestaurantsFormFieldItem;
    }
  }, [hasParking]);

  return (
    <Form form={form} autoComplete="off" {...formProps}>
      {newFields?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default FormRestaurant;
