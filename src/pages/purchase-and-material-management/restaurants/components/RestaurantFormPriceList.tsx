import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';

interface Props {
  onSubmit: (values) => void;
}

export type RestaurantPriceItemTypeFormRef = {
  form: FormInstance<any>;
};

import React, { forwardRef, useImperativeHandle } from 'react';
import { RestaurantPriceListField } from './RestaurantsFieldItem';

const RestaurantFormPriceList = forwardRef<RestaurantPriceItemTypeFormRef, Props>(
  ({ onSubmit }, ref) => {
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
      form,
    }));
    return (
      <Form form={form} onFinish={onSubmit}>
        {RestaurantPriceListField?.map((item) => (
          <CommonRenderFormField key={item?.name} field={item} />
        ))}
      </Form>
    );
  },
);

export default RestaurantFormPriceList;
