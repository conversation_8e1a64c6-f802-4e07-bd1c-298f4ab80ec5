import CustomSelectPrefectures from '@/components/Commons/CustomSelectPrefectures';
import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { rules } from '@/constants/rules';
import {
  optionPaymentmethod,
  optionYesNo,
} from '../../accommodation/components/HotelFormFieldItem';
import { ReservationMethod } from '@/constants/data';
import CustomSelectBusinessPartner from '@/components/Commons/CustomSelectBusinessPartner';

export const RestaurantsFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'name_jp',
    title: '飲食店 (日本語)',
    isRequired: true,
    placeholder: '飲食店 (日本語)',
    rules: rules.isJapanName,
    maxLength: 155,
  },
  {
    type: 'input',
    name: 'name_en',
    title: '飲食店 (英語)',
    placeholder: '飲食店 (英語)',
    rules: rules.isEnglishName,
    maxLength: 155,
  },
  {
    type: 'customField',
    name: 'city',
    component: <CustomSelectPrefectures isRequired layout="vertical" />,
    rules: rules.requiredSelectCity,
  },
  {
    type: 'input',
    name: 'address',
    title: '住所',
    placeholder: '住所',
  },
  {
    type: 'input',
    name: 'home_page',
    title: 'ホームページ',
    placeholder: 'ホームページ',
    rules: rules.requiredInputUrl,
  },
  {
    type: 'checkbox',
    name: 'reservation_method',
    title: '予約方法',
    options: ReservationMethod,
  },
  {
    type: 'checkbox',
    name: 'payment_method',
    title: '支払方法',
    options: optionPaymentmethod,
    isRequired: true,
    rules: rules.isRequired,
  },
  {
    type: 'radio',
    name: 'parking_lot',
    title: '駐車場',
    // isRequired: true,
    options: optionYesNo,
    // rules: rules.isRequired,
    defaultValueRadio: optionYesNo?.[1]?.value,
  },
  {
    type: 'numbericInput',
    name: 'parking_fee',
    title: '駐車料金',
    placeholder: '0',
    maxLength: 10,
  },
  // {
  //   type: 'input',
  //   name: 'reservation_method',
  //   title: '飲食店 (英語)',
  //   placeholder: '飲食店 (英語)',
  // },
  //todo: ????
  {
    type: 'textArea',
    name: 'memo',
    title: 'メモ',
    placeholder: 'メモ',
    maxLength: 1000,
  },
  {
    type: 'input',
    name: 'phone_number',
    title: '電話番号',
    placeholder: '03-1234-56789',
    rules: rules.isPhoneNumber,
  },
  {
    type: 'input',
    name: 'fax',
    title: 'FAX',
    placeholder: '03-1234-56789',
    rules: rules.isFax,
  },
  {
    type: 'input',
    name: 'email',
    title: 'メールアドレス',
    placeholder: 'メールアドレス',
    rules: rules?.isEmail,
  },
  {
    type: 'input',
    name: 'manager',
    title: '担当者',
    placeholder: '担当者',
    maxLength: 255,
  },
  {
    type: 'customField',
    name: 'business_partner_id',
    title: '取引先',
    component: <CustomSelectBusinessPartner placeholder="取引先" title="取引先" />,
  },
];

export const RestaurantPriceListField: FormItemDetail[] = [
  {
    type: 'input',
    name: 'title',
    title: 'メニュー',
    isRequired: true,
    placeholder: 'メニュー',
    rules: rules.requiredInput,
    colSpan: 24,
    maxLength: 155,
  },
  {
    type: 'numbericInput',
    name: 'restaurant_adult_fee',
    title: '大人',
    isRequired: true,
    placeholder: '大人',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'restaurant_child_fee',
    title: '子供',
    isRequired: true,
    placeholder: '子供',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'restaurant_guide_fee',
    title: 'TL/ガイド',
    isRequired: true,
    placeholder: 'TL/ガイド',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'restaurant_driver_fee',
    title: 'ドライバー',
    isRequired: true,
    placeholder: 'ドライバー',
    rules: rules.requiredInput,
    colSpan: 24,
  },
];

export const RestaurantsCancellationFeeFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'title',
    title: 'タイトル',
    isRequired: true,
    placeholder: 'タイトル',
    rules: rules.requiredInput,
    colSpan: 24,
  },
  {
    type: 'input',
    name: 'fee_cancel',
    title: '取消料',
    isRequired: true,
    placeholder: '取消料',
    rules: rules.requiredInput,
    colSpan: 24,
  },
];
