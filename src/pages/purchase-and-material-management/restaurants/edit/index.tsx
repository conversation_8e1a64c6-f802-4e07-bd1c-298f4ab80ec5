import BasicButton from '@/components/Commons/BasicButton';
import { Col, Row, Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { history, useParams } from 'umi';
import { openNotificationApprove, openNotificationFail } from '@/components/Notification';
import STATUS_CODE from '@/constants/statusCode';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import type { restaurantFormRef } from '../components/RestaurantForm';
import {
  getRestaurantDetail,
  updateRestaurant,
} from '@/apis/purchase-and-material-management/restaurant';
import FormRestaurant from '../components/RestaurantForm';
import { TEXT_ACTION } from '@/constants/commonText';
import { SaveOutlined } from '@ant-design/icons';

const EditRestaurant = () => {
  const { id } = useParams() as any;
  const refFormEdit = useRef<restaurantFormRef>();

  const [isLoading, setIsLoading] = useState(false);

  const onFetchDataDetail = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getRestaurantDetail(Number(id));
      const rsData = data.data;
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dataRs = {
          ...rsData,
          city: {
            prefectures: {
              value: rsData?.prefecture?.id,
              label: rsData?.prefecture?.name_province,
            },
            municipalities: {
              value: rsData?.city?.id,
              label: rsData?.city?.name_city,
            },
          },
          business_partner_id: {
            label: rsData?.business_partner?.business_partner_name,
            value: rsData?.business_partner?.id,
          },
        };
        refFormEdit.current.form.setFieldsValue(dataRs);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT?.SERVER_ERROR);
    }
  };

  useEffect(() => {
    onFetchDataDetail();
  }, [id]);

  const onSubmit = async () => {
    await refFormEdit?.current?.form.validateFields();
    setIsLoading(true);
    try {
      const values = refFormEdit.current.form.getFieldsValue();
      const body = {
        ...values,
        business_partner_id: values?.business_partner_id?.value,
        prefecture_code: values?.city?.prefectures?.value,
        city_code: values?.city?.municipalities?.value,
      };

      const { status, error } = await updateRestaurant(id, body);
      if (status === STATUS_CODE?.SUCCESSFUL) {
        history.push(`/purchase-and-material-management/restaurants/${id}`);

        openNotificationApprove(MESSAGE_ALERT?.EDIT_SUCCESS);
      } else if (status === STATUS_CODE.INVALID) {
        const errors = error.data.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key][0];
            openNotificationFail(message);
          });
        }
      } else {
        openNotificationFail(MESSAGE_ALERT?.EDIT_FAILED);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT?.EDIT_FAILED);
    }
  };

  return (
    <Spin spinning={isLoading}>
      <div
        className="mx-8 my-6 px-6 py-4 bg-[#fff] rounded-xl flex flex-col"
        style={{
          minHeight: 'calc(100vh - 120px)',
        }}
      >
        <FormRestaurant ref={refFormEdit} />
        <Row>
          <Col span={12}>
            <div className="flex items-center gap-x-[20px]">
              <BasicButton
                styleType="noneOutLine"
                onClick={() => history.goBack()}
                className="flex-1"
              >
                {TEXT_ACTION.CANCEL}
              </BasicButton>
              <BasicButton
                className="flex items-center flex-1"
                styleType="accept"
                onClick={onSubmit}
                icon={<SaveOutlined width={16} height={16} />}
              >
                {TEXT_ACTION.SAVE}
              </BasicButton>
            </div>
          </Col>
        </Row>
      </div>
    </Spin>
  );
};

export default EditRestaurant;
