import BasicButton from '@/components/Commons/BasicButton';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import BasicModal from '@/components/Commons/BasicModal';
import { Col, Row, Spin } from 'antd';
import { useEffect, useRef, useState } from 'react';

import { useParams } from 'react-router';

import { DELETE_POPUP, MESSAGE_ALERT } from '@/constants/commonMessage';
import { openNotificationDeleteSuccess, openNotificationFail } from '@/components/Notification';
import {
  deleteBus,
  getBusDetail,
  type BusDetailType,
} from '@/apis/purchase-and-material-management/buses';
import ViewFieldItem from '@/components/pages/BusinessPartner/ViewFieldItem';
import { optionPaymentmethod } from '../../accommodation/components/HotelFormFieldItem';
import { ArrowLeftOutlined, DeleteOutlined, EditOutlined } from '@ant-design/icons';
import { history } from 'umi';
import { TEXT_ACTION } from '@/constants/commonText';
import { CarModel } from '../components/BusFormFieldItem';
import STATUS_CODE from '@/constants/statusCode';
import { ReservationMethod } from '@/constants/data';

function BusDetail() {
  const { id } = useParams() as { id: string };

  const refDeleteModal = useRef<BasicModalRef>(null);

  const [dataDetail, setDataDetail] = useState<BusDetailType>();
  const [isLoading, setIsLoading] = useState(false);

  const onOpenModalDelete = () => {
    if (refDeleteModal) {
      refDeleteModal.current.open();
    }
  };

  const handleDelete = async () => {
    setIsLoading(true);
    try {
      const { status } = await deleteBus(Number(id));

      if (status === STATUS_CODE.SUCCESSFUL) {
        openNotificationDeleteSuccess(MESSAGE_ALERT.DELETE_SUCCESS);
        history.push('/purchase-and-material-management/buses');
      } else {
        openNotificationFail(MESSAGE_ALERT.DELETE_FAILED);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.log(error);
    }
  };

  const fetchDataDetail = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getBusDetail(Number(id));
      const rsData = data.data;
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dataRs = {
          ...rsData,
          prefecture: rsData?.prefecture?.name_province,
          municipalities: rsData?.city?.name_city,
        };
        setDataDetail(dataRs);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    fetchDataDetail();
  }, [id]);

  return (
    <Spin spinning={isLoading}>
      <div className="p-9 space-y-[20px]">
        <ViewFieldItem
          label={'バス (日本語)'}
          type="text"
          value={dataDetail?.name_jp ?? ''}
          options={null}
        />
        <ViewFieldItem
          label={'バス (英語)'}
          type="text"
          value={dataDetail?.name_en ?? ''}
          options={null}
        />
        <ViewFieldItem
          label={'都道府県'}
          type="text"
          value={dataDetail?.prefecture ?? ''}
          options={null}
        />
        <ViewFieldItem
          label={'市区町村'}
          type="text"
          value={dataDetail?.municipalities ?? ''}
          options={null}
        />
        <ViewFieldItem
          label={'住所'}
          type="text"
          value={dataDetail?.address ?? ''}
          options={null}
        />
        <ViewFieldItem
          label={'ホームページ'}
          type="text"
          value={dataDetail?.home_page ?? ''}
          options={null}
        />
        <ViewFieldItem
          label={'対応エリア'}
          type="text"
          value={dataDetail?.area ?? ''}
          options={null}
        />
        <ViewFieldItem
          label={'予約方法'}
          type="checkbox"
          value={dataDetail?.reservation_method ?? ''}
          options={ReservationMethod}
        />
        <ViewFieldItem
          label={'車種'}
          type="checkbox"
          value={dataDetail?.vehicle_type ?? ''}
          options={CarModel}
        />
        <ViewFieldItem
          label={'支払方法'}
          type="checkbox"
          value={dataDetail?.payment_method ?? ''}
          options={optionPaymentmethod}
        />
        <ViewFieldItem
          label={'メモ'}
          type="textArea"
          value={dataDetail?.memo ?? ''}
          options={null}
        />
        <ViewFieldItem
          label={'電話番号'}
          type="text"
          value={dataDetail?.phone_number ?? ''}
          options={null}
        />
        <ViewFieldItem label={'FAX'} type="text" value={dataDetail?.fax ?? ''} options={null} />
        <ViewFieldItem
          label={'メールアドレス'}
          type="text"
          value={dataDetail?.email ?? ''}
          options={null}
        />

        <ViewFieldItem
          label={'担当者'}
          type="text"
          value={dataDetail?.manager ?? ''}
          options={null}
        />
        <ViewFieldItem
          label={'取引先'}
          type="text"
          value={dataDetail?.business_partner?.business_partner_name ?? ''}
          options={null}
        />
        <Row>
          <Col span={12}>
            <div className="flex items-center justify-center gap-x-3 pb-6">
              <BasicButton
                onClick={() => history.goBack()}
                styleType="noneOutLine"
                icon={<ArrowLeftOutlined />}
                className="flex-1 flex items-center justify-center gap-1"
              >
                {TEXT_ACTION.RETURN}
              </BasicButton>
              <BasicButton
                styleType="delete"
                onClick={onOpenModalDelete}
                icon={<DeleteOutlined />}
                className="flex-1 flex items-center justify-center gap-1"
              >
                {TEXT_ACTION.DELETE}
              </BasicButton>
              <BasicButton
                styleType="accept"
                icon={<EditOutlined />}
                onClick={() => {
                  history.push(`/purchase-and-material-management/buses/edit/${id}`);
                }}
                className="flex-1 flex items-center justify-center gap-1"
              >
                {TEXT_ACTION.EDIT}
              </BasicButton>
            </div>
          </Col>
        </Row>
      </div>

      {/* Modal */}
      <BasicModal
        ref={refDeleteModal}
        title={DELETE_POPUP.TITLE}
        content={<div className="whitespace-pre-line text-center">{DELETE_POPUP.CONTENT}</div>}
        onSubmit={handleDelete}
      />
    </Spin>
  );
}

export default BusDetail;
