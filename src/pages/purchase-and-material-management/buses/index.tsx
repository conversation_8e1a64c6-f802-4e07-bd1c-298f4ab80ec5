import BasicButton from '@/components/Commons/BasicButton';
import BasicInput from '@/components/Commons/BasicInput';
import BasicTable from '@/components/Commons/BasicTable';
import CustomSelectPrefectures from '@/components/Commons/CustomSelectPrefectures';
import PageContainer from '@/components/Commons/Page/Container';
import { openNotificationFail } from '@/components/Notification';
import AddSVG from '@/components/SVG/AddSVG';
import SearchSVG from '@/components/SVG/SearchSVGWhite';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import STATUS_CODE from '@/constants/statusCode';
import { Form, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import { useEffect, useState } from 'react';
import { history } from 'umi';
import type { BusDetailType } from '@/apis/purchase-and-material-management/buses';
import { getListBus } from '@/apis/purchase-and-material-management/buses';
import { ITEM_PER_PAGE } from '@/utils/constants';
import { useUrlSearchParams } from 'use-url-search-params';
import FileDollarIcon from '@/components/SVG/FileDollarIcon';
import InformationCircle from '@/components/SVG/InformationCircle';
import FileIcon from '@/components/SVG/FileIcon';

const initSearchParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};
const Buses = () => {
  const [form] = Form.useForm();

  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [dataSource, setDataSource] = useState<BusDetailType[]>([]);

  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);

  const onFetchData = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getListBus(parameter);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dts = data.data?.map((item, index) => ({
          ...item,
          key: (Number(parameter?.page) - 1) * Number(parameter?.limit) + index + 1,
          prefecture: item?.prefecture?.name_province,
          city: item?.city?.name_city,
        }));
        setTotal(data.total);
        setDataSource(dts);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    onFetchData();
  }, [parameter]);

  const handleSearch = (values) => {
    const params = {
      ...values,
      prefecture_code: values?.city?.prefectures?.value,
      city_code: values?.city?.municipalities?.value,
      page: 1,
    };

    setParameter(params);
  };

  const columns: ColumnsType<BusDetailType> = [
    {
      title: <div className="font-[500] text-[13px] leading-4 z">#</div>,
      dataIndex: 'key',
      key: 'key',
      width: 60,
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">管理番号</div>,
      dataIndex: 'control_number',
      key: 'control_number',
      width: 120,
      render: (_value, { control_number, id }) => (
        <div
          className="text-sm leading-5 font-medium text-main-color"
          onClick={() => history.push('/purchase-and-material-management/buses/' + id)}
        >
          {control_number}
        </div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">バス</div>,
      dataIndex: 'name_jp',
      key: 'name_jp',
      render: (_, { name_jp }) => (
        <div className="text-sm leading-5 font-medium text-[#383B46]">{name_jp}</div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">都道府県</div>,
      dataIndex: 'prefecture',
      key: 'prefecture',
      width: 140,
      render: (_, { prefecture }) => (
        <div className="text-sm leading-5 font-medium text-[#383B46]">{prefecture}</div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">市区町村</div>,
      dataIndex: 'city',
      key: 'city',
      width: 140,
      render: (_, { city }) => (
        <div className="text-sm leading-5 font-medium text-[#383B46]">{city}</div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">住所</div>,
      dataIndex: 'address',
      key: 'address',
      render: (_, { address }) => (
        <div>
          <div className="text-sm leading-5 font-medium text-[#383B46]">{address}</div>
        </div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">対応エリア</div>,
      dataIndex: 'area',
      key: 'area',
      render: (_, { area }) => (
        <div>
          <div className="text-sm leading-5 font-medium text-[#383B46]">{area}</div>
        </div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">メモ</div>,
      dataIndex: 'memo',
      key: 'memo',
      width: '20%',
      render: (_, { memo }) => (
        <Tooltip title={memo} placement="topLeft">
          <div className="text-sm leading-5 font-medium text-[#383B46] max-w-[400px] line-clamp-1">
            {memo}
          </div>
        </Tooltip>
      ),
    },
    {
      title: null,
      dataIndex: 'handle',
      key: 'handle',
      width: '320px',
      fixed: 'right',
      render: (_, { id }) => (
        <div className="flex items-center justify-between">
          <div
            onClick={() => history.push(`/purchase-and-material-management/buses/${id}/materials`)}
            className="flex items-center py-1 px-4 text-xs font-medium leading-6 text-main-color border-none gap-[4px]"
          >
            <FileIcon /> 資料
          </div>
          <div
            onClick={() => history.push(`/purchase-and-material-management/buses/${id}/price-list`)}
            className="flex items-center py-1 px-4 text-xs font-medium leading-6 text-main-color border-none gap-[4px]"
          >
            <FileDollarIcon /> 料金表
          </div>
          <div
            onClick={() =>
              history.push(`/purchase-and-material-management/buses/${id}/cancellation-fee`)
            }
            className="flex items-center py-1 px-4 text-xs font-medium leading-6 text-[#FDAF2E] border-none gap-[4px]"
          >
            <InformationCircle /> 取消料情報
          </div>
        </div>
      ),
    },
  ];

  return (
    <PageContainer>
      <div className="mt-[32px]">
        <Form form={form} onFinish={handleSearch}>
          <div className="flex">
            <div className="flex space-x-4 items-end flex-1">
              <Form.Item noStyle name={'city'}>
                <CustomSelectPrefectures
                  className="!w-[300px]"
                  placeHolder={{
                    placeHolderMunicipalities: 'すべて',
                    placeHolderPrefecture: 'すべて',
                  }}
                />
              </Form.Item>
              <Form.Item name={'address'} noStyle>
                <BasicInput
                  placeholder={'住所'}
                  title="住所"
                  className="!min-w-[140px] !rounded-[0.5rem]"
                  allowClear
                />
              </Form.Item>
              <Form.Item name={'keyword'} noStyle>
                <BasicInput
                  title={'バス名'}
                  placeholder={'キーワードで検索する'}
                  className="!w-[300px] !rounded-[0.5rem]"
                  allowClear
                />
              </Form.Item>
              <BasicButton
                onClick={() => form.submit()}
                className="flex items-center justify-center w-[120px] space-x-[8px] border-main-color"
                icon={<SearchSVG />}
                styleType={'accept'}
              >
                検索
              </BasicButton>
            </div>
            <div className="flex space-x-4 items-end flex-1 justify-end">
              <BasicButton
                styleType="accept"
                className="flex items-center justify-center border-main-color space-x-[8px]"
                onClick={() => history.push('/purchase-and-material-management/buses/create')}
              >
                <AddSVG />
                新規作成
              </BasicButton>
            </div>
          </div>
        </Form>
      </div>
      <BasicTable
        tableProps={{
          scroll: { x: 2000 },
          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
          loading: isLoading,
        }}
        page={Number(parameter.page)}
        pageSize={Number(parameter.limit)}
        onChangePage={(p: number) => setParameter({ ...parameter, page: p })}
        total={total}
        onSelectPageSize={(v) => setParameter({ ...parameter, page: 1, limit: v })}
      />
    </PageContainer>
  );
};

export default Buses;
