export type Bus = {
  id: number;
  controlNumber: string;
  japanBusName: string;
  englishBusName: string;
  prefectures: string;
  municipalities: string;
  compatibleArea: string;
  phone: string;
  hompageUrl: string;
  memo: string;
};

export type CarModel = {
  id: number;
  name: string;
  fee: number;
};

export interface DataTariffBusType {
  id?: number | string;
  title: string;
  bus_large_fee: number;
  bus_medium_fee: number;
  bus_small_fee: number;
  description?: string;
}
