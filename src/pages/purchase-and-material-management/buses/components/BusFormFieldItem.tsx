import CustomSelectPrefectures from '@/components/Commons/CustomSelectPrefectures';
import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { rules } from '@/constants/rules';
import { optionPaymentmethod } from '../../accommodation/components/HotelFormFieldItem';
import { ReservationMethod } from '@/constants/data';
import CustomSelectBusinessPartner from '@/components/Commons/CustomSelectBusinessPartner';

export const CarModel = [
  { value: 'large', label: '大型' },
  { value: 'medium', label: '中型' },
  { value: 'small', label: '小型・マイクロ' },
  { value: 'commuter', label: 'コミューター' },
  { value: 'others', label: 'その他' },
];

export const CarModelShortName = [
  { value: 'large', label: '大' },
  { value: 'medium', label: '中' },
  { value: 'small', label: '小' },
  { value: 'commuter', label: 'コ' },
  { value: 'others', label: '他' },
];

export const BusFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'name_jp',
    title: 'バス (日本語)',
    isRequired: true,
    placeholder: 'バス (日本語)',
    rules: rules.isJapanName,
    maxLength: 155,
  },
  {
    type: 'input',
    name: 'name_en',
    title: 'バス (英語)',
    placeholder: 'バス (英語)',
    rules: rules.isEnglishName,
    maxLength: 155,
  },
  {
    type: 'customField',
    name: 'city',
    component: <CustomSelectPrefectures isRequired layout="vertical" />,
    rules: rules.requiredSelectCity,
  },
  {
    type: 'input',
    name: 'address',
    title: '住所',
    placeholder: '住所',
  },
  {
    type: 'input',
    name: 'home_page',
    title: 'ホームページ',
    placeholder: 'ホームページ',
    rules: rules.requiredInputUrl,
  },
  {
    type: 'input',
    name: 'area',
    title: '対応エリア',
    placeholder: '対応エリア',
  },
  {
    type: 'checkbox',
    name: 'reservation_method',
    title: '予約方法',
    options: ReservationMethod,
    // isRequired: true,
    // // rules: rules.isRequired,
  },
  {
    type: 'checkbox',
    name: 'vehicle_type',
    title: '車種',
    // isRequired: true,
    options: CarModel,
    // rules: rules.isRequired,
  },
  {
    type: 'checkbox',
    name: 'payment_method',
    title: '支払方法',
    isRequired: true,
    options: optionPaymentmethod,
    rules: rules.isRequired,
  },
  {
    type: 'textArea',
    name: 'memo',
    title: 'メモ',
    placeholder: 'メモ',
    maxLength: 1000,
  },
  {
    type: 'input',
    name: 'phone_number',
    title: '電話番号',
    placeholder: '03-1234-56789',
    rules: rules.isPhoneNumber,
  },
  {
    type: 'input',
    name: 'fax',
    title: 'FAX',
    placeholder: '03-1234-56789',
    rules: rules.isFax,
  },
  {
    type: 'input',
    name: 'email',
    title: 'メールアドレス',
    placeholder: 'メールアドレス',
    rules: rules?.isEmail,
  },
  {
    type: 'input',
    name: 'manager',
    title: '担当者',
    placeholder: '担当者',
  },
  {
    type: 'customField',
    name: 'business_partner_id',
    title: '取引先',
    component: <CustomSelectBusinessPartner placeholder="取引先" title="取引先" />,
  },
];

export const CancellationFeeFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'title',
    title: 'タイトル',
    isRequired: true,
    placeholder: 'タイトル',
    rules: rules.requiredInput,
    colSpan: 24,
    maxLength: 155,
  },
  {
    type: 'input',
    name: 'fee_cancel',
    title: '取消料',
    isRequired: true,
    placeholder: '取消料',
    rules: rules.requiredInput,
    colSpan: 24,
  },
];

export const PriceListBusFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'title',
    title: 'タイトル',
    isRequired: true,
    placeholder: 'タイトル',
    rules: rules.requiredInput,
    colSpan: 24,
    maxLength: 155,
  },
  {
    type: 'numbericInput',
    name: 'bus_large_fee',
    title: '大型',
    isRequired: true,
    rules: rules.isRequired,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'bus_medium_fee',
    title: '中型',
    isRequired: true,
    rules: rules.isRequired,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'bus_small_fee',
    title: '小型・マイクロ',
    isRequired: true,
    rules: rules.isRequired,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'bus_commuter_fee',
    title: 'コミューター',
    isRequired: true,
    rules: rules.isRequired,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'bus_other_fee',
    title: 'その他',
    // isRequired: true,
    // rules: rules.isRequired,
    colSpan: 24,
  },
];
