import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { PriceListBusFormFieldItem } from './BusFormFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type BusPriceItemTypeFormRef = {
  form: FormInstance<any>;
};

const BusFormPrice = forwardRef<BusPriceItemTypeFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} onFinish={onSubmit}>
      {PriceListBusFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default BusFormPrice;
