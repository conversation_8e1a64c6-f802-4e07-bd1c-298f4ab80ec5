import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance, type FormProps } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { BusFormFieldItem } from './BusFormFieldItem';

export type BusFormRef = {
  form?: FormInstance<any>;
};

interface Props {
  formProps?: FormProps;
}

const BusForm = forwardRef<BusFormRef, Props>(({ formProps }, ref) => {
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    form,
  }));

  return (
    <Form form={form} autoComplete="off" {...formProps}>
      {BusFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default BusForm;
