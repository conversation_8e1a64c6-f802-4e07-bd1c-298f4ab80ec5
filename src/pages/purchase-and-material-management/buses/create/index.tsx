import BasicButton from '@/components/Commons/BasicButton';
import { Col, Row, Spin } from 'antd';
import { useRef, useState } from 'react';
import { history } from 'umi';
import type { BusFormRef } from '../components/BusForm';
import BusForm from '../components/BusForm';
import STATUS_CODE from '@/constants/statusCode';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { createBus } from '@/apis/purchase-and-material-management/buses';
import { TEXT_ACTION } from '@/constants/commonText';
import { SaveOutlined } from '@ant-design/icons';

function BusCreate() {
  const refFormCreate = useRef<BusFormRef>();

  const [isloading, setIsLoading] = useState<boolean>(false);

  const onSubmit = async () => {
    if (refFormCreate) {
      await refFormCreate.current.form.validateFields();
      setIsLoading(true);

      try {
        const values = refFormCreate.current.form.getFieldsValue();
        const payload = {
          ...values,
          business_partner_id: values?.business_partner_id?.value,
          prefecture_code: values?.city?.prefectures?.value,
          city_code: values?.city?.municipalities?.value,
        };
        delete payload.city;
        const { status, error } = await createBus(payload);

        if (status === STATUS_CODE.CREATED) {
          openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
          history.push('/purchase-and-material-management/buses');
        } else if (status === STATUS_CODE.INVALID) {
          const errors = error?.data?.errors;
          if (errors && Object.keys(errors).length) {
            Object.keys(errors).forEach((key) => {
              const message = errors[key];
              openNotificationFail(message);
            });
          }
        } else {
          openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
        }
        setIsLoading(false);
      } catch (error) {
        openNotificationFail('新規作成に失敗しました');
        console.log('error', error);
        setIsLoading(false);
      }
    }
  };

  return (
    <Spin spinning={isloading}>
      <div className="p-9">
        <BusForm ref={refFormCreate} />
        <Row>
          <Col span={12}>
            <div className="flex items-center gap-x-[20px]">
              <BasicButton
                styleType="noneOutLine"
                onClick={() => history.goBack()}
                className="flex-1"
              >
                {TEXT_ACTION.CANCEL}
              </BasicButton>
              <BasicButton
                className="flex items-center flex-1"
                styleType="accept"
                onClick={onSubmit}
                icon={<SaveOutlined width={16} height={16} />}
              >
                {TEXT_ACTION.SAVE}
              </BasicButton>
            </div>
          </Col>
        </Row>
      </div>
    </Spin>
  );
}

export default BusCreate;
