import type { BaseParams } from '@/@types/request';
import type { FieldData } from 'rc-field-form/lib/interface';
import type { ImportCsvCommonRef } from '@/components/CommonModalImportCsv';
import BasicButton from '@/components/Commons/BasicButton';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import type { BasicFormModalRef } from '@/components/Commons/BasicFormModal';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_WARNING } from '@/constants/commonText';
import {
  DownloadOutlined,
  PlusOutlined,
  SaveOutlined,
  SearchOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { Form, Image, Popconfirm, Tooltip } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import STATUS_CODE from '@/constants/statusCode';
import {
  openNotificationDeleteSuccess,
  openNotificationFail,
  openNotificationSuccess,
} from '@/components/Notification';
import { DELETE_POPUP, MESSAGE_ALERT } from '@/constants/commonMessage';
import { rules } from '@/constants/rules';
import BasicModal, { BasicModalRef } from '@/components/Commons/BasicModal';
import EditableCell from '@/components/Commons/EditableCell';
import BasicTable from '@/components/Commons/BasicTable';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import type { DeliveryPriceItemTypeFormRef } from '../components/DeliveryFormPriceList';
import DeliveryFormPrice from '../components/DeliveryFormPriceList';
import {
  createTariff,
  deleteTariff,
  EntityEnum,
  updateTariff,
  type TariffType,
} from '@/apis/purchase-and-material-management/tariff';
import { useParams } from 'react-router';
import { initSearchParams } from '@/constants';
import { exportCsvPriceDelivery } from '@/apis/purchase-and-material-management/delivery';
import BasicInputSearch from '@/components/Commons/BasicInputSearch';
import CommonModalImportCsv from '@/components/CommonModalImportCsv';
import { formatMoney } from '@/utils';
import { PRICE_LIST_TITLE } from '@/utils/constants';

interface Props {
  parameter: BaseParams;
  setParameter: (v: BaseParams) => void;
  setDataSource: React.Dispatch<React.SetStateAction<TariffType[]>>;
  dataSource: TariffType[];
  total: number;
  onFetchData: () => void;
  isLoading: boolean;
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>;
}

const TableDeliveryPriceList: React.FC<Props> = ({
  parameter,
  setParameter,
  dataSource,
  onFetchData,
  total,
  isLoading,
  setIsLoading,
}) => {
  const { id } = useParams() as any;
  const [form] = Form.useForm();
  const [formSearch] = Form.useForm();
  const refModalConfirmChangePage = useRef(null);
  const refDeleteModal = useRef<BasicModalRef>(null);

  const refModalCreateMaster = useRef<BasicFormModalRef>(null);
  const refFormAddNewItem = useRef<DeliveryPriceItemTypeFormRef>(null);
  const refImportCsv = useRef<ImportCsvCommonRef>(null);

  const [listItemChange, setListItemChange] = useState<string[]>([]);
  const [isEditPage, setIsEditPage] = useState(false);
  const [pageChange, setPageChange] = useState<number>(1);
  const [idRowDeleted, setIdRowDeleted] = useState<number>();

  const [valueSearch, setValueSearch] = useState<string>();

  useEffect(() => {
    onFetchData();
  }, [parameter?.keyword, parameter?.limit, parameter?.page]);

  const handleConfirmModalDeleteRow = (key) => {
    setIdRowDeleted(key);
    if (refDeleteModal) {
      refDeleteModal.current.open();
    }
  };

  const handleCloseModalDeleteRow = () => {
    setIdRowDeleted(undefined);
  };

  const confirmDeleteRow = async () => {
    setIsLoading(true);
    try {
      if (idRowDeleted) {
        const responseDelete = await deleteTariff(idRowDeleted);
        if (responseDelete?.data?.data === true) {
          openNotificationDeleteSuccess(MESSAGE_ALERT.DELETE_SUCCESS);
          onFetchData?.();
          refDeleteModal?.current?.close();
          setIdRowDeleted(undefined);
        } else {
          openNotificationFail(MESSAGE_ALERT.DELETE_FAILED);
        }
      }
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      render: (_, {}, index) => {
        const order = (Number(parameter?.page) - 1) * Number(parameter?.limit ?? 0) + 1 + index;
        return <>{order}</>;
      },
    },
    {
      title: 'タイトル',
      dataIndex: 'title',
      key: 'title',
      editable: true,
      formType: 'input',
      ruleFormItem: rules.requiredInput,
      render: (_, { title }) => (
        <Tooltip title={title}>
          <p className="truncate">{title}</p>
        </Tooltip>
      ),
    },
    {
      title: <p>料金</p>,
      dataIndex: 'delivery_fee',
      key: 'delivery_fee',
      editable: true,
      formType: 'inputNumber',
      inputProps: { isRightAlign: true, notAllowNegativeNumber: true },
      ruleFormItem: rules.requiredInput,
      render: (_, { delivery_fee }) => (
        <p className="text-right">{formatMoney(delivery_fee)}</p>
      ),
    },
    {
      title: '',
      dataIndex: 'handle',
      key: 'handle',
      width: 80,
      render: (_, record) => (
        <div className="flex items-center justify-center gap-x-[16px]">
          <BasicButton
            styleType="danger"
            className="!h-[24px] w-[76px]"
            onClick={() => handleConfirmModalDeleteRow(record.id)}
          >
            <Image preview={false} src={IconDelete} width={12} height={13} />
            <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
          </BasicButton>
        </div>
      ),
    },
  ];

  const removeListItemChange = () => {
    setListItemChange([]);
  };

  const handleListItemChange = (changeField: FieldData[]) => {
    const nameFieldChange = changeField?.[0]?.name?.[0];
    const keyChange = nameFieldChange.split('.')[0];
    setListItemChange([...listItemChange, keyChange]);
  };

  useEffect(() => {
    dataSource.forEach((item) => {
      Object.keys(item).forEach((key) => {
        const keyForm = `${item.key}.${key}`;
        form.setFieldValue(keyForm, item[key]?.toString());
      });
    });
    removeListItemChange();
  }, [dataSource]);

  const handleAddNew = async (values) => {
    try {
      const payload = {
        type: 'price',
        tariffable_id: id,
        data: [{ ...values, hotel_meal: values?.hotel_meal?.[0] }],
      };
      const responseCreate = await createTariff(EntityEnum.delivery, payload);
      if (
        responseCreate.status === STATUS_CODE.SUCCESSFUL ||
        responseCreate.status === STATUS_CODE.CREATED
      ) {
        openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
        refModalCreateMaster?.current?.close();
        setParameter(initSearchParams);
        onFetchData();
        refFormAddNewItem?.current?.form?.resetFields();
      } else if (responseCreate.status === STATUS_CODE.INVALID) {
        const errors = responseCreate?.error?.data?.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key];
            const keyForm = key.split('.')?.[2];
            refFormAddNewItem?.current?.form.setFields([
              {
                name: keyForm,
                errors: message,
              },
            ]);
          });
        }
      } else {
        openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const saveEdit = async () => {
    setIsLoading(true);
    try {
      if (listItemChange.length === 0) {
        setIsLoading(false);
        setIsEditPage(false);
        return;
      }
      const values = await form.validateFields();
      const resultObj = Object.keys(values).reduce((acc, key) => {
        const [index, property] = key.split('.');
        const value = values[key];

        if (!acc[index]) {
          acc[index] = {};
        }

        acc[index][property] = value;

        return acc;
      }, {});

      const newData = [...dataSource];

      Object.keys(resultObj).forEach((key) => {
        const index = dataSource.findIndex((item) => item.key === Number(key));
        newData[index] = { ...dataSource[index], ...resultObj[key] };
      });

      // Filter data change
      const dataChange = newData.filter((item) => listItemChange.includes(item.key.toString()));

      const dataUpdate = dataChange.map((item) => {
        delete item.key;
        return item;
      });
      const payload: any = {
        type: 'price',
        tariffable_id: id,
        data: dataUpdate,
      };

      // => Call API here
      const responseUpdate = await updateTariff(payload, EntityEnum.delivery);
      if (
        responseUpdate.status === STATUS_CODE.SUCCESSFUL ||
        responseUpdate.status === STATUS_CODE.CREATED
      ) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        onFetchData();
        setIsEditPage(false);
        setIsLoading(false);

        // Reset when update successfully
        removeListItemChange();
      } else if (responseUpdate.status === STATUS_CODE.INVALID) {
        const errors = responseUpdate?.error?.data?.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const nameField = key.split('.').slice(1).join('.');
            const message = errors[key];
            form.setFields([
              {
                name: nameField,
                errors: message,
              },
            ]);
          });
        }
        setIsLoading(false);
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
        setIsLoading(false);
      }
    } catch (errInfo) {
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      setIsLoading(false);
      console.log('Save failed:', errInfo);
    }
  };
  const openModal = () => {
    refFormAddNewItem?.current?.form?.resetFields();
    if (refModalCreateMaster) {
      refModalCreateMaster.current.open();
    }
  };

  const onImportCsv = () => {
    if (refImportCsv) {
      refImportCsv.current.open();
    }
  };

  const onExportCsv = async () => {
    const cloneParams = { ...parameter };
    const responseExport = await exportCsvPriceDelivery(
      {
        ...cloneParams,
        limit: 'all',
        page: 1,
      },
      id,
    );
    if (responseExport.status === STATUS_CODE.SUCCESSFUL) {
      const urlCsv = responseExport?.data?.file_link;
      if (urlCsv) {
        const a = document.createElement('a');
        a.href = urlCsv;
        a.download = 'csv_price_delivery.csv';
        a.click();
        window.URL.revokeObjectURL(urlCsv);
      }
    }
  };

  const onConfirmChangePageWhenEdit = (pageConfirm: number) => {
    setPageChange(pageConfirm);
    refModalConfirmChangePage.current?.open();
  };

  const columns = defaultColumns.map((col) => {
    if (!col.editable && !isEditPage) {
      return { ...col, width: col.width || 250 };
    }
    return {
      ...col,
      width: col.width || 250,
      onCell: (record: Record<string, string>) => ({
        record,
        editable: col.editable,
        isEditPage,
        dataIndex: col.dataIndex,
        title: col.title,
        formType: col.formType,
        form,
        ruleFormItem: col?.ruleFormItem,
        inputProps: col?.inputProps,
        width: col.width || 250,
      }),
    };
  });

  const onSearch = (values) => {
    console.log(values);
    // () => setParameter({ ...parameter, keyword: valueSearch, page: 1 })
  };

  return (
    <div>
      <div className="flex justify-between items-end">
        <div className="flex items-end gap-x-[24px]">
          <Form form={formSearch} onFinish={onSearch}>
            <div className="flex items-center gap-x-[24px]">
              <Form.Item name={'keyword'} noStyle>
                <BasicInputSearch
                  placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
                  title={'タイトル'}
                  className="w-[280px]"
                  onChange={(e) => setValueSearch(e.target?.value)}
                  allowClear
                />
              </Form.Item>
            </div>
          </Form>
          <BasicButton
            // onClick={() => formSearch.submit()}
            onClick={() => setParameter({ ...parameter, keyword: valueSearch, page: 1 })}
            icon={<SearchOutlined />}
            styleType="accept"
            className="flex items-center min-w-[120px]"
          >
            {TEXT_ACTION.SEARCH}
          </BasicButton>
        </div>
        <div className="flex gap-x-[12px]">
          <BasicButton
            icon={<SaveOutlined style={{ color: '#225DE0' }} />}
            className="flex items-center !text-[#225DE0]"
            styleType="noneOutLine"
            onClick={() => (isEditPage ? saveEdit() : setIsEditPage(true))}
          >
            {isEditPage ? TEXT_ACTION.SAVE : TEXT_ACTION.EDIT}
          </BasicButton>
          <BasicButton
            icon={<UploadOutlined style={{ color: '#EC980C' }} />}
            className="flex items-center !text-[#EC980C] !bg-white"
            styleType="noneOutLine"
            onClick={onImportCsv}
          >
            {TEXT_ACTION.CSV_Import}
          </BasicButton>
          <BasicButton
            icon={<DownloadOutlined style={{ color: '#3997C8' }} />}
            className="flex items-center !text-main-color !bg-white"
            styleType="noneOutLine"
            onClick={onExportCsv}
          >
            {TEXT_ACTION.CSV_Export}
          </BasicButton>
          <BasicButton
            icon={<PlusOutlined />}
            onClick={openModal}
            className="flex items-center"
            styleType="accept"
          >
            {TEXT_ACTION.CREATE_NEW}
          </BasicButton>
        </div>
      </div>

      <div>
        <Form
          form={form}
          component={false}
          onFieldsChange={(changeField: FieldData[]) => handleListItemChange(changeField)}
        >
          <BasicTable
            tableProps={{
              scroll: { x: 700 },
              loading: isLoading,
              components: {
                body: {
                  cell: EditableCell,
                },
              },
              columns: columns as any,
              dataSource: dataSource,
              bordered: false,
              pagination: false,
              rowKey: 'id',
            }}
            page={Number(parameter?.page) ?? 1}
            pageSize={Number(parameter?.limit) ?? 10}
            onChangePage={(p: number) => {
              if (listItemChange.length > 0) {
                onConfirmChangePageWhenEdit(p);
              } else {
                setParameter({
                  ...parameter,
                  page: Number(p),
                });
              }
            }}
            total={total}
            onSelectPageSize={(v) => setParameter({ ...parameter, limit: v, page: 1 })}
          />

          <BasicModal
            ref={refModalConfirmChangePage}
            title={'警告'}
            content={<>まだ保存していません。編集したデータが失われます。ページを変更しますか？</>}
            okText="ページを変更する"
            onSubmit={() => {
              setParameter({ ...parameter, page: pageChange });
              setListItemChange([]);
              refModalConfirmChangePage.current?.close();
            }}
          />
        </Form>
      </div>

      <BasicFormModal
        ref={refModalCreateMaster}
        content={<DeliveryFormPrice ref={refFormAddNewItem} onSubmit={handleAddNew} />}
        title={PRICE_LIST_TITLE}
        buttonCloseTitle={TEXT_ACTION.CANCEL}
        buttonSubmitTitle={TEXT_ACTION.SAVE}
        buttonSubmitStyle={{ backgroundColor: '#3997C8' }}
        onSubmit={() => refFormAddNewItem?.current?.form?.submit()}
        isValidate={true}
      />

      <CommonModalImportCsv
        confirm={onFetchData}
        ref={refImportCsv}
        type={EntityEnum.delivery}
        _entity_id={id}
      />

      {/* Modal */}
      <BasicModal
        ref={refDeleteModal}
        title={DELETE_POPUP.TITLE}
        content={
          <div className="whitespace-pre-line text-center">
            {TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
          </div>
        }
        onClose={handleCloseModalDeleteRow}
        onSubmit={confirmDeleteRow}
      />
    </div>
  );
};

export default TableDeliveryPriceList;
