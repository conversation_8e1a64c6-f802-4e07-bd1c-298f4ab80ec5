import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance, FormProps } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { DeliveryFormFieldItem } from './DeliveryFieldItem';

export type DeliveryFormRef = {
  form?: FormInstance<any>;
};

interface Props {
  formProps?: FormProps;
}

const FormDelivery = forwardRef<DeliveryFormRef, Props>(({ formProps }, ref) => {
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} autoComplete="off" {...formProps}>
      {DeliveryFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default FormDelivery;
