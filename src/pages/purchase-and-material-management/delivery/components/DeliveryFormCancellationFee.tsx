import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { CancellationFeeFormFieldItem } from './DeliveryFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type DeliveryCancellationFormRef = {
  form: FormInstance<any>;
};

const DeliveryFormCancellationFee = forwardRef<DeliveryCancellationFormRef, Props>(
  ({ onSubmit }, ref) => {
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
      form,
    }));
    return (
      <Form form={form} onFinish={onSubmit}>
        {CancellationFeeFormFieldItem?.map((item) => (
          <CommonRenderFormField key={item?.name} field={item} />
        ))}
      </Form>
    );
  },
);

export default DeliveryFormCancellationFee;
