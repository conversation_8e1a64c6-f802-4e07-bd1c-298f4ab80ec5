import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { PriceListDeliveryFormFieldItem } from './DeliveryFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type DeliveryPriceItemTypeFormRef = {
  form: FormInstance<any>;
};

const DeliveryFormPrice = forwardRef<DeliveryPriceItemTypeFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} onFinish={onSubmit}>
      {PriceListDeliveryFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default DeliveryFormPrice;
