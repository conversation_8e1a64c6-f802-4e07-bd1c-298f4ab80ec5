import React, { useRef, useState } from 'react';
import { Col, Row, Spin } from 'antd';
import BasicButton from '@/components/Commons/BasicButton';
import { history } from 'umi';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import STATUS_CODE from '@/constants/statusCode';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION } from '@/constants/commonText';
import { SaveOutlined } from '@ant-design/icons';
import type { DeliveryFormRef } from '../components/DeliveryForm';
import FormDelivery from '../components/DeliveryForm';
import { createDelivery } from '@/apis/purchase-and-material-management/delivery';

const CreateDelivery = () => {
  const refFormCreate = useRef<DeliveryFormRef>();

  const [isLoading, setIsLoading] = useState(false);

  const onGoback = () => {
    history.goBack();
  };
  //submit form
  const onSubmit = async () => {
    await refFormCreate.current.form.validateFields();
    setIsLoading(true);
    try {
      const values = refFormCreate.current.form.getFieldsValue();
      const payload = {
        ...values,
        business_partner_id: values?.business_partner_id?.value,
      };
      delete payload.city;

      const { status, error } = await createDelivery(payload);
      if (status === STATUS_CODE?.CREATED) {
        openNotificationSuccess(MESSAGE_ALERT?.CREATED_SUCCESS);
        history.push('/purchase-and-material-management/delivery');
      } else if (status === STATUS_CODE.INVALID) {
        const errors = error?.data?.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key];
            openNotificationFail(message);
          });
        }
      } else {
        openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
      }

      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT?.SERVER_ERROR);
    }
  };

  const renderFormButton = () => {
    return (
      <Row>
        <Col span={12}>
          <div className="flex items-center gap-x-[20px]">
            <BasicButton className="flex-1" styleType="noneOutLine" onClick={onGoback}>
              {TEXT_ACTION.CANCEL}
            </BasicButton>
            <BasicButton
              className="flex items-center flex-1"
              styleType="accept"
              icon={<SaveOutlined width={16} height={16} />}
              onClick={onSubmit}
            >
              {TEXT_ACTION.SAVE}
            </BasicButton>
          </div>
        </Col>
      </Row>
    );
  };

  return (
    <Spin spinning={isLoading}>
      <div
        className="mx-8 my-6 px-6 py-4 bg-[#fff] rounded-xl flex flex-col"
        style={{
          minHeight: 'calc(100vh - 120px)',
        }}
      >
        <div className="mt-4 flex-grow flex flex-col justify-between">
          <FormDelivery ref={refFormCreate} />
        </div>

        {renderFormButton()}
      </div>
    </Spin>
  );
};

export default CreateDelivery;
