import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { CancellationFeeFormFieldItem } from './AirlinesFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type AirlineCancellationFormRef = {
  form: FormInstance<any>;
};

const AirlineFormCancellationFee = forwardRef<AirlineCancellationFormRef, Props>(
  ({ onSubmit }, ref) => {
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
      form,
    }));
    return (
      <Form form={form} onFinish={onSubmit}>
        {CancellationFeeFormFieldItem?.map((item) => (
          <CommonRenderFormField key={item?.name} field={item} />
        ))}
      </Form>
    );
  },
);

export default AirlineFormCancellationFee;
