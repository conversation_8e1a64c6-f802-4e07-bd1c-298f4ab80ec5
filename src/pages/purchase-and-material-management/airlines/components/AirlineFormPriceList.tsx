import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { PriceListAirlineFormFieldItem } from './AirlinesFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type AirlinePriceItemTypeFormRef = {
  form: FormInstance<any>;
};

const AirlineFormPrice = forwardRef<AirlinePriceItemTypeFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} onFinish={onSubmit}>
      {PriceListAirlineFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default AirlineFormPrice;
