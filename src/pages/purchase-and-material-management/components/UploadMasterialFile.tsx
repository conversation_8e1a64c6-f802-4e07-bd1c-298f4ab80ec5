import { uploadFileMasterDataByEntity } from '@/apis/purchase-and-material-management/tariff';
import BasicButton from '@/components/Commons/BasicButton';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import STATUS_CODE from '@/constants/statusCode';
import { UploadOutlined } from '@ant-design/icons';
import { notification, Upload } from 'antd';

const UploadMasterialFile = ({ id, setIsLoading, handleFetchData, type }) => {
  const MAX_FILE_SIZE_MB = 10;

  const handleBeforeUpload = async (file: File) => {
    const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

    if (file.size > MAX_FILE_SIZE_BYTES) {
      notification.error({
        message: `※1ファイル${MAX_FILE_SIZE_MB}MBまでとすること`,
      });
      return;
    }
    setIsLoading(true);
    try {
      const config = {
        headers: { 'Content-Type': 'multipart/form-data' },
      };

      const formData = new FormData();
      formData.append('type', type);
      formData.append('file', file);
      formData.append('id', id);

      const {
        data: dataFile,
        status,
        error,
      } = await uploadFileMasterDataByEntity(formData, config);
      if (status === STATUS_CODE.SUCCESSFUL) {
        // const urlFile = (dataFile?.data as any)?.path;
        // const newList = [...listAttachedFile, urlFile];
        // setListAttachedFile(newList);
        handleFetchData();
      } else {
        const errors = error.data.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key][0];
            openNotificationFail(message);
          });
        } else {
          openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
        }
      }
      setIsLoading(false);
    } catch (error) {
      console.log('upload fail', error);
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col justify-end items-end">
      <span className="text-xs text-[#363840] mb-2">
        ※1ファイル{MAX_FILE_SIZE_MB}MBまでとすること
      </span>
      <Upload name="attachedFile" showUploadList={false} beforeUpload={handleBeforeUpload}>
        <BasicButton
          icon={<UploadOutlined style={{ color: '#EC980C' }} />}
          className="flex justify-center items-center h-10 border !border-[#DCDEE3] !text-[#FDAF2E]"
        >
          <span>ファイルアップロード</span>
        </BasicButton>
      </Upload>
    </div>
  );
};

export default UploadMasterialFile;
