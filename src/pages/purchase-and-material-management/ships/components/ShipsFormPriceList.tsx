import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { PriceListShipFormFieldItem } from './ShipsFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type ShipPriceItemTypeFormRef = {
  form: FormInstance<any>;
};

const ShipFormPrice = forwardRef<ShipPriceItemTypeFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} onFinish={onSubmit}>
      {PriceListShipFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default ShipFormPrice;
