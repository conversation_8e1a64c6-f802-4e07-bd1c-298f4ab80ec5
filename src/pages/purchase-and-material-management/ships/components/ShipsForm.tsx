import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance, FormProps } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { ShipsFormFieldItem } from './ShipsFieldItem';

export type ShipFormRef = {
  form?: FormInstance<any>;
};

interface Props {
  formProps?: FormProps;
}

const FormShip = forwardRef<ShipFormRef, Props>(({ formProps }, ref) => {
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} autoComplete="off" {...formProps}>
      {ShipsFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default FormShip;
