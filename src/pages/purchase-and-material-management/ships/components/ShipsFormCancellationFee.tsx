import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { CancellationFeeFormFieldItem } from './ShipsFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type ShipCancellationFormRef = {
  form: FormInstance<any>;
};

const ShipFormCancellationFee = forwardRef<ShipCancellationFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} onFinish={onSubmit}>
      {CancellationFeeFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default ShipFormCancellationFee;
