import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance, FormProps } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { OtherFormFieldItem } from './OtherFieldItem';

export type OtherFormRef = {
  form?: FormInstance<any>;
};

interface Props {
  formProps?: FormProps;
}

const FormOther = forwardRef<OtherFormRef, Props>(({ formProps }, ref) => {
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} autoComplete="off" {...formProps}>
      {OtherFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default FormOther;
