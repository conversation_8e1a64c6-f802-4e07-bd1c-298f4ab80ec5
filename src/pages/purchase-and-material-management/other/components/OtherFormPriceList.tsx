import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { PriceListOtherFormFieldItem } from './OtherFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type OtherPriceItemTypeFormRef = {
  form: FormInstance<any>;
};

const OtherFormPrice = forwardRef<OtherPriceItemTypeFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} onFinish={onSubmit}>
      {PriceListOtherFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default OtherFormPrice;
