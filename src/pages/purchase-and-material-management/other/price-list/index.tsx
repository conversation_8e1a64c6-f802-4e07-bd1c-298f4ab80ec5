import PageContainer from '@/components/Commons/Page/Container';
import { useUrlSearchParams } from 'use-url-search-params';
import { useState } from 'react';
import { useParams } from 'react-router';
import TableGuidePriceList from './TableOtherPriceList';
import type { TariffType } from '@/apis/purchase-and-material-management/tariff';
import { EntityEnum, getListTariffByEntity } from '@/apis/purchase-and-material-management/tariff';
import STATUS_CODE from '@/constants/statusCode';
import { initSearchParams } from '@/constants';
import BasicButton from '@/components/Commons/BasicButton';
import { history } from 'umi';
import { LeftOutlined } from '@ant-design/icons';
import { TEXT_ACTION } from '@/constants/commonText';

const GuidePriceList = () => {
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);
  const [dataSource, setDataSource] = useState<TariffType[]>([]);
  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const { id } = useParams() as any;

  const onFetchData = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getListTariffByEntity(
        { ...parameter, type: 'price', entity_id: id },
        EntityEnum['service-other'],
      );
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dts = data.data?.map((item) => ({ ...item, key: item.id }));
        setDataSource(dts);
        setTotal(data.total);
      }
    } catch (error) {
      console.log(error);
    }
    setIsLoading(false);
  };

  return (
    <PageContainer>
      <TableGuidePriceList
        dataSource={dataSource}
        parameter={parameter}
        setDataSource={setDataSource}
        setParameter={setParameter}
        total={total}
        onFetchData={onFetchData}
        isLoading={isLoading}
        setIsLoading={setIsLoading}
      />
      <div className="flex justify-center items-center my-4 gap-5">
        <BasicButton
          styleType="back"
          className="w-[290px] flex items-center justify-center"
          onClick={() => history.goBack()}
        >
          <LeftOutlined width={16} height={16} /> {TEXT_ACTION.RETURN}
        </BasicButton>
        {/* <BasicButton styleType="accept" className="w-[290px]">
            <SaveSvg /> 保存
          </BasicButton> */}
      </div>
    </PageContainer>
  );
};

export default GuidePriceList;
