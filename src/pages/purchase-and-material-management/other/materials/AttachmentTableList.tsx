import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import IconEye from '@/assets/imgs/common-icons/view-eye.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicTable from '@/components/Commons/BasicTable';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import { Image, Popconfirm } from 'antd';
import type { InitialType } from 'use-url-search-params';

interface Props {
  listAttachedFile: string[];
  handleDeleteAttachedFile: (url: string) => void;
  loading: boolean;
  parameter: InitialType;
  setParameter: (nextQuery: InitialType) => void;
  total: number;
}

const AttachmentTableList: React.FC<Props> = ({
  loading,
  listAttachedFile,
  handleDeleteAttachedFile,
  parameter,
  setParameter,
  total,
}) => {
  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      render: (_, record, index) => {
        return <>{index + 1}</>;
      },
    },
    {
      title: 'ファイル名',
      dataIndex: 'file_name',
      key: 'file_name',
      render: (_, record) => {
        return <>{record?.file_name}</>;
      },
    },
    {
      title: '',
      dataIndex: 'preview',
      key: 'preview',
      width: '280px',
      render: (_, record) => {
        return (
          <div className="flex items-center justify-center gap-2">
            <BasicButton
              styleType="outline"
              className=" flex items-center justify-center !h-[24px] w-[112px] !bg-[transparent] !shadow-none !border-none text-[#3997C8]"
              onClick={() => {
                window.open(record?.path, '_blank');
              }}
            >
              <Image preview={false} src={IconEye} width={16} height={16} />
              <p className="text-xs !ml-1">{TEXT_ACTION.PREVIEW}</p>
            </BasicButton>
            <Popconfirm
              title={TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
              onConfirm={() => handleDeleteAttachedFile(record?.path)}
              okText={TEXT_ACTION.DELETE}
              cancelText={TEXT_ACTION.CANCEL}
              placement="topRight"
            >
              <BasicButton
                styleType="danger"
                className="!h-[24px] w-[84px] hover:shadow-md hover:opacity-70 !border-none !shadow-none !bg-[transparent]"
              >
                <Image preview={false} src={IconDelete} width={15} height={14} />
                <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
              </BasicButton>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  return (
    <BasicTable
      className="!mt-0"
      tableProps={{
        // scroll: { x: 300, y: 300 },
        columns: defaultColumns,
        dataSource: listAttachedFile,
        bordered: false,
        pagination: false,
        loading: loading,
        rowKey: 'id',
      }}
      total={total}
      page={Number(parameter?.page)}
      pageSize={Number(parameter?.limit)}
      onChangePage={(p) => {
        setParameter({ ...parameter, page: p });
      }}
      onSelectPageSize={(v) => setParameter({ ...parameter, page: 1, limit: v })}
    />
  );
};

export default AttachmentTableList;
