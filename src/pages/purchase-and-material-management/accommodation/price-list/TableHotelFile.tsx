import type { BaseParams } from '@/@types/request';
import BasicButton from '@/components/Commons/BasicButton';
import BasicInputSearch from '@/components/Commons/BasicInputSearch';
import BasicTable from '@/components/Commons/BasicTable';
import { UploadImageType } from '@/components/Commons/BasicUploads';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_WARNING } from '@/constants/commonText';
import { SearchOutlined, UploadOutlined } from '@ant-design/icons';
import { Image, Popconfirm, Upload } from 'antd';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import IconEye from '@/assets/imgs/common-icons/view-eye.svg';
import React, { useState } from 'react';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import STATUS_CODE from '@/constants/statusCode';
import { uploadImage } from '@/apis/common';

interface Props {
  parameter: BaseParams;
  setParameter: (v: BaseParams) => void;
  listAttachedFile: string[];
  setListAttachedFile: React.Dispatch<React.SetStateAction<string[]>>;
}
const TableHotelFile: React.FC<Props> = ({
  parameter,
  listAttachedFile,
  setListAttachedFile,
  setParameter,
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);

  const handleDeleteAttachedFile = async (url: string) => {
    setIsLoading(true);
    try {
      const newList = listAttachedFile.filter((item) => item !== url);
      setListAttachedFile(newList);
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: 'ファイル名',
      dataIndex: 'file_name',
      key: 'file_name',
      width: '50%',
      render: (_, { file_name }) => {
        const pathUrl = `${UploadImageType.travels}/`;
        const index = file_name.indexOf(pathUrl);
        const textFileName = file_name.substring(index + pathUrl.length);
        return <>{textFileName}</>;
      },
    },
    {
      title: '',
      dataIndex: 'preview',
      key: 'preview',
      render: (_, record) => {
        return (
          <div className="flex items-center justify-center">
            <BasicButton
              styleType="outline"
              className=" flex items-center justify-center !h-[24px] w-[112px] !bg-[transparent] !shadow-none !border-none text-[#3997C8]"
              onClick={() => {
                window.open(record?.file_name, '_blank');
              }}
            >
              <Image preview={false} src={IconEye} width={16} height={16} />
              <p className="text-xs !ml-1">{TEXT_ACTION.PREVIEW}</p>
            </BasicButton>
          </div>
        );
      },
    },
    {
      title: '',
      dataIndex: 'deleteFile',
      key: 'deleteFile',
      render: (_, record) => (
        <Popconfirm
          title={TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
          onConfirm={() => handleDeleteAttachedFile(record?.file_name)}
          okText={TEXT_ACTION.DELETE}
          cancelText={TEXT_ACTION.CANCEL}
          placement="topRight"
        >
          <BasicButton
            styleType="danger"
            className="!h-[24px] w-[84px] hover:shadow-md hover:opacity-70 !border-none !shadow-none !bg-[transparent]"
          >
            <Image preview={false} src={IconDelete} width={15} height={14} />
            <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
          </BasicButton>
        </Popconfirm>
      ),
    },
  ];

  const dataSource = listAttachedFile?.map((item, index) => ({
    id: index + 1,
    file_name: item,
  }));

  const handleBeforeUpload = async (file: File) => {
    setIsLoading(true);
    try {
      const config = {
        headers: { 'Content-Type': 'multipart/form-data' },
      };

      const formData = new FormData();
      formData.append('type', UploadImageType.travels);
      formData.append('file', file);

      const { data: dataFile, status, error } = await uploadImage(formData, config);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const urlFile = dataFile.data;
        const newList = [...listAttachedFile, urlFile];
        setListAttachedFile(newList);
      } else {
        const errors = error.data.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key][0];
            openNotificationFail(message);
          });
        } else {
          openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
        }
      }
      setIsLoading(false);
    } catch (error) {
      console.log('upload fail', error);
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-[24px]">
      <div className="flex justify-between items-end">
        <div className="flex items-end gap-x-[24px]">
          <BasicInputSearch
            style={{
              width: '280px',
              height: '40px',
            }}
            title={'ルームタイプ'}
            placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
          />
          <BasicButton
            onClick={() => {}}
            icon={<SearchOutlined />}
            styleType="accept"
            className="flex items-center min-w-[120px]"
          >
            {TEXT_ACTION.SEARCH}
          </BasicButton>
        </div>
        <div className="flex gap-x-[12px]">
          <Upload name="attachedFile" showUploadList={false} beforeUpload={handleBeforeUpload}>
            <BasicButton
              icon={<UploadOutlined style={{ color: '#EC980C' }} />}
              className="flex items-center !text-[#EC980C] !bg-white"
              styleType="noneOutLine"
            >
              {TEXT_ACTION.CSV_Import}
            </BasicButton>
          </Upload>
        </div>
      </div>

      <div>
        <BasicTable
          className="!mt-0"
          tableProps={{
            scroll: { y: 300 },
            columns: defaultColumns,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            loading: isLoading,
            rowKey: 'id',
          }}
          hasPagination={false}
        />
      </div>
    </div>
  );
};

export default TableHotelFile;
