import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { CancellationFeeFormFieldItem } from './HotelFormFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type HotelCancellationFormRef = {
  form: FormInstance<any>;
};

const HotelFormCancellationFee = forwardRef<HotelCancellationFormRef, Props>(
  ({ onSubmit }, ref) => {
    const [form] = Form.useForm();
    useImperativeHandle(ref, () => ({
      form,
    }));
    return (
      <Form form={form} onFinish={onSubmit}>
        {CancellationFeeFormFieldItem?.map((item) => (
          <CommonRenderFormField key={item?.name} field={item} />
        ))}
      </Form>
    );
  },
);

export default HotelFormCancellationFee;
