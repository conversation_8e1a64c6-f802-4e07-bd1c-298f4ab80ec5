import CustomSelectBusinessPartner from '@/components/Commons/CustomSelectBusinessPartner';
import CustomSelectPrefectures from '@/components/Commons/CustomSelectPrefectures';
import type { FormItemDetail } from '@/components/Form/CommonRenderFormField';
import { ReservationMethod } from '@/constants/data';
import { rules } from '@/constants/rules';

export const optionRanking = [
  { label: '1★', value: '1' },
  { label: '2★', value: '2' },
  { label: '3★', value: '3' },
  { label: '4★', value: '4' },
  { label: '5★', value: '5' },
];

export const optionPaymentmethod = [
  { value: 'all_travel', label: '全旅' },
  { value: 'JTB', label: 'JTB' },
  { value: 'prepayment', label: '前振込' },
  { value: 'postpayment', label: '後払い' },
  { value: 'deposit ', label: '預り金' },
  { value: 'others', label: 'その他' },
];

export const optionYesNo = [
  { value: 1, label: '有' },
  { value: 0, label: '無' },
];

export const optionMeal = [
  { value: 'breakfast', label: '朝食' },
  { value: 'dinner', label: '夕食' },
];

export const HotelFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'name_jp',
    title: '宿泊施設名 (日本語)',
    isRequired: true,
    placeholder: '宿泊施設名 (日本語)',
    rules: rules.isJapanName,
    maxLength: 155,
  },
  {
    type: 'input',
    name: 'name_en',
    title: '宿泊施設名 (英語)',
    placeholder: '宿泊施設名 (英語)',
    rules: rules.isEnglishName,
    maxLength: 155,
  },
  {
    type: 'customField',
    name: 'city',
    component: <CustomSelectPrefectures isRequired layout="vertical" />,
    rules: rules.requiredSelectCity,
  },
  {
    type: 'select',
    name: 'rank',
    title: 'ランク',
    isRequired: true,
    placeholder: 'ランク',
    rules: rules.requiredSelect,
    options: optionRanking?.filter((item) => item.value !== ''),
  },
  {
    type: 'input',
    name: 'address',
    title: '住所',
    placeholder: '住所',
  },
  {
    type: 'input',
    name: 'home_page',
    title: 'ホームページ',
    placeholder: 'ホームページ',
    rules: rules.requiredInputUrl,
  },
  {
    type: 'checkbox',
    name: 'reservation_method',
    title: '予約方法',
    options: ReservationMethod,
  },
  {
    type: 'checkbox',
    name: 'payment_method',
    title: '支払方法',
    options: optionPaymentmethod,
    isRequired: true,
    rules: rules.requiredSelect,
  },
  {
    type: 'radio',
    name: 'parking_lot',
    title: '駐車場',
    isRequired: true,
    options: optionYesNo,
    rules: rules.requiredSelect,
    defaultValueRadio: optionYesNo?.[1]?.value,
  },
  {
    type: 'numbericInput',
    name: 'parking_fee',
    title: '駐車料金',
    placeholder: '0',
    maxLength: 10,
  },
  {
    type: 'textArea',
    name: 'memo',
    title: 'メモ',
    placeholder: 'メモ',
    maxLength: 1000,
  },
  {
    type: 'input',
    name: 'phone_number',
    title: '電話番号',
    placeholder: '03-1234-56789',
    rules: rules.isPhoneNumber,
  },
  {
    type: 'input',
    name: 'fax',
    title: 'FAX',
    placeholder: '03-1234-56789',
    rules: rules.isFax,
  },
  {
    type: 'input',
    name: 'email',
    title: 'メールアドレス',
    placeholder: 'メールアドレス',
    rules: rules?.isEmail,
  },
  {
    type: 'input',
    name: 'manager',
    title: '担当者',
    placeholder: '担当者',
    maxLength: 255,
  },
  {
    type: 'customField',
    name: 'business_partner_id',
    title: '取引先',
    component: <CustomSelectBusinessPartner placeholder="取引先" title="取引先" />,
  },
];

export const PriceListHotelFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'title',
    title: 'ルームタイプ',
    isRequired: true,
    placeholder: 'ルームタイプ',
    rules: rules.requiredInput,
    colSpan: 24,
    maxLength: 155,
  },
  {
    type: 'checkbox',
    name: 'hotel_meal',
    title: '食事',
    isRequired: true,
    options: optionMeal,
    rules: rules.isRequired,
    colSpan: 24,
  },
  {
    type: 'numbericInput',
    name: 'fee',
    title: '料金',
    isRequired: true,
    placeholder: '料金',
    rules: rules.isRequired,
    colSpan: 24,
  },
];

export const CancellationFeeFormFieldItem: FormItemDetail[] = [
  {
    type: 'input',
    name: 'title',
    title: 'タイトル',
    isRequired: true,
    placeholder: 'タイトル',
    rules: rules.requiredInput,
    colSpan: 24,
    maxLength: 155,
  },
  {
    type: 'input',
    name: 'fee_cancel',
    title: '取消料',
    isRequired: true,
    placeholder: '取消料',
    rules: rules.requiredInput,
    colSpan: 24,
  },
];
