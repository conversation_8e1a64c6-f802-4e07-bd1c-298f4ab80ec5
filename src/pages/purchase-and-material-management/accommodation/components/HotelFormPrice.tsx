import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { PriceListHotelFormFieldItem } from './HotelFormFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type HotelPriceItemTypeFormRef = {
  form: FormInstance<any>;
};

const HotelFormPrice = forwardRef<HotelPriceItemTypeFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} onFinish={onSubmit}>
      {PriceListHotelFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default HotelFormPrice;
