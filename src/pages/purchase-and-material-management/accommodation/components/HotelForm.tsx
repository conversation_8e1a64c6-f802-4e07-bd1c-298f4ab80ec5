import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance, type FormProps } from 'antd';
import React, { forwardRef, useImperativeHandle, useMemo } from 'react';
import { HotelFormFieldItem } from './HotelFormFieldItem';

export type hotelFormRef = {
  form?: FormInstance<any>;
};

interface Props {
  formProps?: FormProps;
}

const HotelForm = forwardRef<hotelFormRef, Props>(({ formProps }, ref) => {
  const [form] = Form.useForm();
  const hasParking = Form.useWatch('parking_lot', form);

  useImperativeHandle(ref, () => ({
    form,
  }));

  const newFields = useMemo(() => {
    if (hasParking === 0) {
      return HotelFormFieldItem?.map((item) => {
        if (item.name === 'parking_fee') {
          return {
            ...item,
            disabled: true,
          };
        } else {
          return item;
        }
      });
    } else {
      return HotelFormFieldItem;
    }
  }, [hasParking]);

  return (
    <Form form={form} autoComplete="off" {...formProps}>
      {newFields?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default HotelForm;
