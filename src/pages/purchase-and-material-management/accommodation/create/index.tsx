import React, { useEffect, useRef, useState } from 'react';
import type { hotelFormRef } from '../components/HotelForm';
import HotelForm from '../components/HotelForm';
import { Col, Row, Spin } from 'antd';
import { optionYesNo } from '../components/HotelFormFieldItem';
import BasicButton from '@/components/Commons/BasicButton';
import { history } from 'umi';
import { TEXT_ACTION } from '@/constants/commonText';
import { SaveOutlined } from '@ant-design/icons';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { createHotel } from '@/apis/purchase-and-material-management/hotel';
import STATUS_CODE from '@/constants/statusCode';
import { MESSAGE_ALERT } from '@/constants/commonMessage';

const HotelCreate = () => {
  const refFormCreate = useRef<hotelFormRef>();

  const [isloading, setIsLoading] = useState<boolean>(false);

  useEffect(() => {
    if (refFormCreate) {
      refFormCreate.current.form.setFieldsValue({
        parking_lot: optionYesNo?.[1]?.value,
      });
    }
  }, []);

  const onSubmit = async () => {
    if (refFormCreate) {
      await refFormCreate.current.form.validateFields();
      setIsLoading(true);

      try {
        const values = refFormCreate.current.form.getFieldsValue();
        const payload = {
          ...values,
          business_partner_id: values?.business_partner_id?.value,
          prefecture_code: values?.city?.prefectures?.value,
          city_code: values?.city?.municipalities?.value,
        };
        delete payload.city;

        const { status, error } = await createHotel(payload);
        if (status === STATUS_CODE.CREATED) {
          openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
          history.push('/purchase-and-material-management/hotel');
        } else if (status === STATUS_CODE.INVALID) {
          const errors = error?.data?.errors;
          if (errors && Object.keys(errors).length) {
            Object.keys(errors).forEach((key) => {
              const message = errors[key];
              openNotificationFail(message);
            });
          }
        } else {
          openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
        }

        setIsLoading(false);
      } catch (error) {
        console.log('error', error);
        setIsLoading(false);
      }
    }
  };
  return (
    <Spin spinning={isloading}>
      <div className="p-9">
        <HotelForm ref={refFormCreate} />
        <Row>
          <Col span={12}>
            <div className="flex items-center gap-x-[20px]">
              <BasicButton
                styleType="noneOutLine"
                onClick={() => history.goBack()}
                className="flex-1"
              >
                {TEXT_ACTION.CANCEL}
              </BasicButton>
              <BasicButton
                className="flex items-center flex-1"
                styleType="accept"
                onClick={onSubmit}
                icon={<SaveOutlined width={16} height={16} />}
              >
                {TEXT_ACTION.SAVE}
              </BasicButton>
            </div>
          </Col>
        </Row>
      </div>
    </Spin>
  );
};

export default HotelCreate;
