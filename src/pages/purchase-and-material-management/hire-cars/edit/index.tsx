import BasicButton from '@/components/Commons/BasicButton';
import { Col, Row, Spin } from 'antd';
import { useRef, useEffect, useState } from 'react';
import { history, useParams } from 'umi';
import { openNotificationApprove, openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import type { HireCarFormRef } from '../components/HireCarForm';
import HireCarForm from '../components/HireCarForm';
import { TEXT_ACTION } from '@/constants/commonText';
import { SaveOutlined } from '@ant-design/icons';
import { getHireCarDetail, updateHireCar } from '@/apis/purchase-and-material-management/hireCar';
import STATUS_CODE from '@/constants/statusCode';

function HireCarEdit() {
  const { id } = useParams<{ id: string }>();
  const refFormEdit = useRef<HireCarFormRef>();

  const [isloading, setIsLoading] = useState<boolean>(false);

  const fetchDataDetail = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getHireCarDetail(Number(id));
      const rsData = data.data;
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dataRs = {
          ...rsData,
          city: {
            prefectures: {
              value: rsData?.prefecture?.id,
              label: rsData?.prefecture?.name_province,
            },
            municipalities: {
              value: rsData?.city?.id,
              label: rsData?.city?.name_city,
            },
          },

          business_partner_id: {
            label: rsData?.business_partner?.business_partner_name,
            value: rsData?.business_partner?.id,
          },
        };
        refFormEdit.current.form.setFieldsValue(dataRs);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    fetchDataDetail();
  }, [id]);

  const onSubmit = async () => {
    if (refFormEdit) {
      await refFormEdit.current.form.validateFields();
      setIsLoading(true);
      try {
        const values = refFormEdit.current.form.getFieldsValue();
        const payload = {
          ...values,
          business_partner_id: values?.business_partner_id?.value,
          prefecture_code: values?.city?.prefectures?.value,
          city_code: values?.city?.municipalities?.value,
        };

        const { status, error } = await updateHireCar(Number(id), payload);
        if (status === STATUS_CODE.SUCCESSFUL) {
          openNotificationApprove(MESSAGE_ALERT.EDIT_SUCCESS);
          history.push(`/purchase-and-material-management/hire-cars/${id}`);
        } else if (status === STATUS_CODE.INVALID) {
          const errors = error.data.errors;
          if (errors && Object.keys(errors).length) {
            Object.keys(errors).forEach((key) => {
              const message = errors[key];
              openNotificationFail(message);
            });
          }
        } else {
          openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
        }

        setIsLoading(false);
      } catch (error) {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
        console.log('error', error);
        setIsLoading(false);
      }
    }
  };

  return (
    <Spin spinning={isloading}>
      <div className="p-9">
        <HireCarForm ref={refFormEdit} />
        <Row>
          <Col span={12}>
            <div className="flex items-center gap-x-[20px]">
              <BasicButton
                styleType="noneOutLine"
                onClick={() => history.goBack()}
                className="flex-1"
              >
                {TEXT_ACTION.CANCEL}
              </BasicButton>
              <BasicButton
                className="flex items-center flex-1"
                styleType="accept"
                onClick={onSubmit}
                icon={<SaveOutlined width={16} height={16} />}
              >
                {TEXT_ACTION.SAVE}
              </BasicButton>
            </div>
          </Col>
        </Row>
      </div>
    </Spin>
  );
}

export default HireCarEdit;
