import BasicButton from '@/components/Commons/BasicButton';
import BasicInput from '@/components/Commons/BasicInput';
import BasicTable from '@/components/Commons/BasicTable';
import PageContainer from '@/components/Commons/Page/Container';
import AddSVG from '@/components/SVG/AddSVG';
import { Form, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import React, { useEffect, useState } from 'react';
import { history } from 'umi';
import STATUS_CODE from '@/constants/statusCode';
import CustomSelectPrefectures from '@/components/Commons/CustomSelectPrefectures';
import { ITEM_PER_PAGE } from '@/utils/constants';
import type { HireCarDetailType } from '@/apis/purchase-and-material-management/hireCar';
import { getListHireCar } from '@/apis/purchase-and-material-management/hireCar';
import { useUrlSearchParams } from 'use-url-search-params';
import FileDollarIcon from '@/components/SVG/FileDollarIcon';
import InformationCircle from '@/components/SVG/InformationCircle';
import FileIcon from '@/components/SVG/FileIcon';
import SearchSVG from '@/components/SVG/SearchSVGWhite';

const initSearchParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};
const HireCarPage: React.FC = () => {
  const [form] = Form.useForm();

  const [total, setTotal] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [dataSource, setDataSource] = useState<HireCarDetailType[]>([]);
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);

  const onFetchData = async () => {
    setIsLoading(true);
    try {
      const { data, status } = await getListHireCar(parameter);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const res = data as any;
        setTotal(Number(res?.total ?? 0));
        const dts = res.data?.map((item, index) => ({
          ...item,
          key: (Number(parameter?.page) - 1) * Number(parameter?.limit) + index + 1,
          prefecture: item?.prefecture?.name_province,
          municipalities: item?.city?.name_city,
        }));
        // return 1;
        setDataSource(dts);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      console.log(error);
    }
  };

  useEffect(() => {
    onFetchData();
  }, [parameter]);

  const handleSearch = (values) => {
    const params = {
      prefecture_code: values?.city?.prefectures?.value,
      city_code: values?.city?.municipalities?.value,
      keyword: values.keyword,
      address: values?.address,
      page: 1,
    };
    setParameter(params);
  };

  const columns: ColumnsType<HireCarDetailType> = [
    {
      title: <div className="font-[500] text-[13px] leading-4">#</div>,
      dataIndex: 'key',
      key: 'key',
      width: '60px',
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">管理番号</div>,
      dataIndex: 'control_number',
      key: 'control_number',
      width: '8%',
      render: (_value, { control_number, id }) => (
        <div
          className="text-sm leading-5 font-medium text-main-color"
          onClick={() => history.push(`/purchase-and-material-management/hire-cars/${id}`)}
        >
          {control_number}
        </div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">ハイヤー</div>,
      dataIndex: 'name_jp',
      key: 'name_jp',
      render: (_, { name_jp }) => {
        return <div className="text-sm leading-5 font-medium text-[#383B46]">{name_jp}</div>;
      },
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">都道府県</div>,
      dataIndex: 'prefecture',
      key: 'prefecture',
      width: 140,
      render: (_, { prefecture }) => (
        <div className="text-sm leading-5 font-medium text-[#383B46]">{prefecture}</div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">市区町村</div>,
      dataIndex: 'municipalities',
      key: 'municipalities',
      width: 140,
      render: (_, { municipalities }) => (
        <div className="text-sm leading-5 font-medium text-[#383B46]">{municipalities}</div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">住所</div>,
      dataIndex: 'address',
      key: 'address',
      render: (_, { address }) => (
        <div>
          <div className="text-sm leading-5 font-medium text-[#383B46]">{address}</div>
        </div>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">対応エリア</div>,
      dataIndex: 'area',
      key: 'area',
      render: (_, { area }) => (
        <Tooltip placement="topLeft" title={area}>
          <div className="text-sm leading-5 font-medium text-[#383B46] line-clamp-1">{area}</div>
        </Tooltip>
      ),
    },
    {
      title: <div className="font-[500] text-[13px] leading-4">メモ</div>,
      dataIndex: 'memo',
      key: 'memo',
      render: (_, { memo }) => (
        <Tooltip placement="topLeft" title={memo}>
          <div className="text-sm leading-5 font-medium text-[#383B46] max-w-[350px] line-clamp-1">
            {memo}
          </div>
        </Tooltip>
      ),
    },
    {
      title: null,
      dataIndex: 'handle',
      key: 'handle',
      width: '320px',
      fixed: 'right',
      render: (_, { id }) => (
        <div className="flex items-center justify-between">
          <div
            onClick={() =>
              history.push(`/purchase-and-material-management/hire-cars/${id}/materials`)
            }
            className="flex items-center py-1 px-4 text-xs font-medium leading-6 text-main-color border-none gap-[4px]"
          >
            <FileIcon /> 資料
          </div>
          <div
            onClick={() =>
              history.push(`/purchase-and-material-management/hire-cars/${id}/price-list`)
            }
            className="flex items-center py-1 px-4 text-xs font-medium leading-6 text-main-color border-none gap-[4px]"
          >
            <FileDollarIcon /> 料金表
          </div>
          <div
            onClick={() =>
              history.push(`/purchase-and-material-management/hire-cars/${id}/cancellation-fee`)
            }
            className="flex items-center py-1 px-4 text-xs font-medium leading-6 text-[#FDAF2E] border-none gap-[4px]"
          >
            <InformationCircle /> 取消料情報
          </div>
        </div>
      ),
    },
  ];

  return (
    <PageContainer>
      <div className="mt-[32px]">
        <Form form={form} onFinish={handleSearch}>
          <div className="flex">
            <div className="flex space-x-4 items-end flex-1">
              <Form.Item noStyle name={'city'}>
                <CustomSelectPrefectures
                  className="!w-[300px]"
                  placeHolder={{
                    placeHolderMunicipalities: 'すべて',
                    placeHolderPrefecture: 'すべて',
                  }}
                />
              </Form.Item>
              <Form.Item name={'address'} noStyle>
                <BasicInput
                  placeholder={'住所'}
                  title="住所"
                  className="!min-w-[140px] !rounded-[0.5rem]"
                  allowClear
                />
              </Form.Item>
              <Form.Item name={'keyword'} noStyle>
                <BasicInput
                  placeholder={'キーワードで検索する'}
                  title="ハイヤー"
                  className="!min-w-[240px] !rounded-[0.5rem]"
                  allowClear
                />
              </Form.Item>
              <BasicButton
                onClick={() => form.submit()}
                className="flex items-center justify-center w-[120px] space-x-[8px] border-main-color"
                icon={<SearchSVG />}
                styleType={'accept'}
              >
                検索
              </BasicButton>
            </div>
            <div className="flex space-x-4 items-end flex-1 justify-end">
              <BasicButton
                styleType="accept"
                className="flex items-center justify-center border-main-color space-x-[8px]"
                onClick={() => history.push('/purchase-and-material-management/hire-cars/create')}
              >
                <AddSVG />
                新規作成
              </BasicButton>
            </div>
          </div>
        </Form>
      </div>
      <BasicTable
        tableProps={{
          scroll: { x: 2000 },
          loading: isLoading,
          columns: columns as any,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
        }}
        page={Number(parameter?.page)}
        pageSize={Number(parameter?.limit)}
        onChangePage={(p: number) => setParameter({ ...parameter, page: p })}
        total={total}
        onSelectPageSize={(v) => setParameter({ ...parameter, page: 1, limit: v })}
      />
    </PageContainer>
  );
};

export default HireCarPage;
