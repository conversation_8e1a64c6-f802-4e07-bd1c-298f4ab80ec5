export type HireCarType = {
  id: number;
  controlNumber: string;
  nameJapan: string;
  nameEnglish: string;
  prefectures: string;
  cities: string;
  address: string;
  home_page: string;
  main_phone_number: string;
  phone: string;
  area: string;
  representative: string;
  memo: string;
  manager: string;
  email: string;
  fax: string;
};

export interface DataTariffHireCarType {
  id?: number | string;
  title: string;
  hire_car_sedan_fee?: number;
  hire_car_alphard_fee?: number;
  hire_car_hiace_fee?: number;
  description?: string;
}
