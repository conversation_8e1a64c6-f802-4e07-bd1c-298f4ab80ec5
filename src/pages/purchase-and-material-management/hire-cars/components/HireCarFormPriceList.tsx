import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { PriceListHireCarFormFieldItem } from './HireCarFieldItem';

interface Props {
  onSubmit: (values) => void;
}

export type HireCarPriceItemTypeFormRef = {
  form: FormInstance<any>;
};

const HireCarFormPrice = forwardRef<HireCarPriceItemTypeFormRef, Props>(({ onSubmit }, ref) => {
  const [form] = Form.useForm();
  useImperativeHandle(ref, () => ({
    form,
  }));
  return (
    <Form form={form} onFinish={onSubmit}>
      {PriceListHireCarFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default HireCarFormPrice;
