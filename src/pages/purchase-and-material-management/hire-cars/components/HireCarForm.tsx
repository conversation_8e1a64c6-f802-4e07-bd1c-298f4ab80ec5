import CommonRenderFormField from '@/components/Form/CommonRenderFormField';
import { Form, type FormInstance, type FormProps } from 'antd';
import React, { forwardRef, useImperativeHandle } from 'react';
import { HireCarFormFieldItem } from './HireCarFieldItem';

export type HireCarFormRef = {
  form?: FormInstance<any>;
};

interface Props {
  formProps?: FormProps;
}

const HireCarForm = forwardRef<HireCarFormRef, Props>(({ formProps }, ref) => {
  const [form] = Form.useForm();

  useImperativeHandle(ref, () => ({
    form,
  }));

  return (
    <Form form={form} autoComplete="off" {...formProps}>
      {HireCarFormFieldItem?.map((item) => (
        <CommonRenderFormField key={item?.name} field={item} />
      ))}
    </Form>
  );
});

export default HireCarForm;
