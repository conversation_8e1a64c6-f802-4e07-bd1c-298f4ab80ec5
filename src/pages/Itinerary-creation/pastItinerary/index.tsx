import type { BookingPastItem } from '@/@types/booking';
import { getListBookingPast } from '@/apis/itineraries';
import { openNotificationFail } from '@/components/Notification';
import STATUS_CODE from '@/constants/statusCode';
import type { PlanTab } from '@/providers';
import { PlanCreateContext } from '@/providers';
import { ITEM_PER_PAGE } from '@/utils/constants';
import React, { useCallback, useContext, useEffect, useRef, useState } from 'react';
import type { ColumnsType } from 'antd/lib/table';
import { Tooltip } from 'antd';
import moment from 'moment';
import { formatDateYMD } from '@/utils/date';
import type { PlanInfo } from '@/@types/plan';
import BasicButton from '@/components/Commons/BasicButton';
import AddCircleSVG from '@/components/SVG/AddCircleSVG';
import BasicPagination from '@/components/Commons/BasicPagination';
import BasicTable from '@/components/Commons/BasicTable';
import FormSearchPastItinerary from './FormSearchPastItinerary';
import BadgeCheckSVG from '@/components/SVG/BadgeCheckSVG';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import BasicModal from '@/components/Commons/BasicModal';
import PastItineraryDetail from './PastItineraryDetail';
import { useParams } from 'react-router';

const initialSearch = {
  page: 1,
  limit: ITEM_PER_PAGE,
};

const PastItineraryTab = () => {
  const { onImportOldPlanToNewPlan, planInfo } = useContext(PlanCreateContext);
  const refBookingDetailModal = useRef<BasicModalRef>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [dataPastItinerary, setDataPastItinerary] = useState<BookingPastItem[]>();
  const [paramSearch, setParamSearch] = useState<{
    page: string | number;
    limit: string | number;
    keyword?: string;
    prefecture_code?: string | number;
    month?: string | number;
    travel_spot_id?: string | number;
    is_model_course?: 1 | 0 | any;
  }>(initialSearch);
  const [itemSelected, setItemSelected] = useState<React.Key[]>([]);
  const [dataBookingDetailSelected, setDataBookingDetailSelected] = useState<BookingPastItem>();

  const heightScreen = window.innerHeight;

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { id } = useParams() as { id: string };

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setItemSelected(newSelectedRowKeys);
  };

  const rowSelection = {
    itemSelected,
    onChange: onSelectChange,
  };

  const handleShowDetail = (record: BookingPastItem) => {
    if (record) {
      setDataBookingDetailSelected(record);
      refBookingDetailModal?.current?.open();
    }
  };

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const params = { ...paramSearch, current_itinerary_id: id }; // ignore current travel

      const { data, status } = await getListBookingPast(params);

      if (status === STATUS_CODE.SUCCESSFUL) {
        const dts = (data?.data as any)?.data?.map((item, index) => ({
          ...item,
          orderNumber: (Number(paramSearch?.page) - 1) * Number(paramSearch?.limit) + (index + 1),
        }));
        setDataPastItinerary(dts);
        setTotal(Number((data?.data as any)?.total ?? 0));
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail('サーバーエラー');
    }
  }, [paramSearch]);

  const columns: ColumnsType<BookingPastItem> = [
    {
      title: <div className="leading-[19px] text-[13px] font-medium">ツアーID</div>,
      dataIndex: 'travel_id',
      key: 'travel_id',
      width: 100,
      render: (value, { travel_id }, key) => <div className="flex items-center">{travel_id}</div>,
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">旅程名</div>,
      dataIndex: 'tour_name',
      key: 'tour_name',
      render: (value, { tour_name }) => (
        <Tooltip title={tour_name}>
          <div className="truncate">{tour_name}</div>
        </Tooltip>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">得意先</div>,
      dataIndex: 'business_partner_name',
      key: 'business_partner_name',
      render: (value, { business_partner_name }) => (
        <div className="truncate">{business_partner_name}</div>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium ">出発日</div>,
      dataIndex: 'departure_date',
      key: 'departure_date',
      width: 140,
      render: (value, { departure_date }) => (
        <div className="">{departure_date ? moment(departure_date).format('YYYY/MM/DD') : '-'}</div>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium ">日数</div>,
      dataIndex: 'total_days',
      key: 'total_days',
      width: 80,
      render: (value, { total_days }) => <div className="text-right">{total_days ?? '-'}</div>,
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">{'モデルコース'}</div>,
      dataIndex: 'is_model_course',
      key: 'is_model_course',
      width: 120,
      render: (value, { is_model_course }) => (
        <div className="flex items-center justify-center">
          {is_model_course ? <BadgeCheckSVG /> : null}
        </div>
      ),
    },
    {
      title: null,
      dataIndex: 'detail',
      key: 'detail',
      width: 170,
      render: (value, record) => (
        <div className="flex items-center justify-center">
          <BasicButton onClick={() => handleShowDetail(record)}>詳細を表示する</BasicButton>
        </div>
      ),
    },
  ];

  useEffect(() => {
    if (planInfo?.itinerary_id) {
      fetchData();
    }
  }, [planInfo, paramSearch]);

  const handleGetListPlanTabs = (_planInfor: PlanInfo) => {
    if (!_planInfor) return;

    const startDate = new Date(_planInfor?.start_date);
    const endDate = new Date(_planInfor?.end_date);
    const diffTime = Math.abs(endDate.valueOf() - startDate.valueOf());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    const list = Array(diffDays + 1)
      .fill(0)
      .map((_i, index) => {
        const nextDate = new Date(startDate);
        nextDate.setDate(startDate.getDate() + index);
        const date = formatDateYMD(nextDate);

        return {
          day: index + 1,
          date: date,
          plans: _planInfor?.plan_items
            ?.filter((plan) => plan?.date === date && plan?.reference_type_key !== 'preview_memo')
            ?.map((item) => ({ ...item, reference_type: item?.reference_type_key })),
        };
      });
    return list;
  };

  const handleAddPastItineraries = () => {
    if (itemSelected) {
      const itemTab: PlanTab[] = [];

      itemSelected.forEach((item) => {
        const bookingInfor = dataPastItinerary?.find((booking) => booking.id === item);

        const bookingInforLatest = bookingInfor?.plans?.reduce((acc, plan) => {
          if (plan?.is_final) {
            return plan;
          } else {
            return Number(plan?.plan_version) > Number(acc?.plan_version) ? plan : acc;
          }
        }, bookingInfor?.plans?.[0]);

        if (bookingInforLatest) {
          const listPlanTabs = handleGetListPlanTabs(bookingInforLatest);
          itemTab.push(listPlanTabs as any);
        }
      });

      const newPlanstab = itemTab.flat().map((item, index) => ({
        ...item,
        day: index + 1,
      }));

      // debugger;
      onImportOldPlanToNewPlan(newPlanstab);
      setItemSelected([]);
    }
  };

  return (
    <div className="flex flex-col justify-between h-full">
      <div className="flex flex-col flex-1">
        <FormSearchPastItinerary
          onSubmit={(v) => {
            const payload = {
              keyword: v?.keyword,
              prefecture_code: v?.city?.prefectures?.value,
              travel_spot_id: v?.touristSpot?.value,
              month: v?.month ? Number(v?.month) : undefined,
              is_model_course: v?.is_model_course ? 1 : 0,
              business_partner_id: v?.business_partner_id?.value,
              day_go_start: v?.day_go_start
                ? moment(v?.day_go_start as string).format('YYYY-MM-DD')
                : undefined,
              day_go_end: v?.day_go_end
                ? moment(v?.day_go_end as string).format('YYYY-MM-DD')
                : undefined,
            };

            setParamSearch({ ...paramSearch, ...payload });
          }}
        />
        <BasicTable
          className={`!mt-0 h-full`}
          hasPagination={false}
          tableProps={{
            loading: isLoading,
            scroll: { y: heightScreen - 500, x: 800 },
            columns,
            //   loading: isLoading,
            dataSource: dataPastItinerary,
            bordered: false,
            pagination: false,
            rowKey: 'id',
            rowSelection: rowSelection,
          }}
          page={0}
          pageSize={0}
          onChangePage={null}
          total={0}
          onSelectPageSize={null}
        />
      </div>

      <div className="flex items-center justify-between mt-[14px]">
        <BasicButton
          disabled={itemSelected.length === 0}
          styleType="accept"
          className="flex items-center justify-center space-x-[8px]"
          onClick={handleAddPastItineraries}
        >
          <AddCircleSVG />
          過去の旅程を追加する
        </BasicButton>

        <BasicPagination
          page={Number(paramSearch?.page)}
          pageSize={Number(paramSearch?.limit)}
          onChange={(p: number) => setParamSearch({ ...paramSearch, page: p })}
          total={total}
          onSelectPageSize={(v) => setParamSearch({ ...paramSearch, page: 1, limit: v })}
        />
      </div>

      <BasicModal
        ref={refBookingDetailModal}
        title={null}
        content={
          <PastItineraryDetail
            dataBokingDetail={dataBookingDetailSelected}
            onClose={() => {
              setDataBookingDetailSelected(null);
              refBookingDetailModal?.current?.close();
            }}
          />
        }
        onSubmit={() => {}}
        className="!w-[600px] [&_.ant-modal-content]:!h-[90vh] [&_.ant-modal-content]:overflow-y-auto [&_.ant-modal-body]:!h-full [&_.modal_content]:!h-full"
        styleTypeButtonSubmit="accept"
        onClose={() => {
          setDataBookingDetailSelected(null);
        }}
        hideCloseButton
        hideSubmitButton
      />
    </div>
  );
};

export default PastItineraryTab;
