import type { BookingPastItem } from '@/@types/booking';
import type { Plan, PlanInfo } from '@/@types/plan';
import { Tabs } from 'antd';
import PlanForDayTabItems from '../PlanForDayTabItems';
import { useCallback, useEffect, useState } from 'react';
import { formatDateYMD } from '@/utils/date';
import CancelXSVG from '@/components/SVG/CancelXSVG';

interface Props {
  dataBokingDetail: BookingPastItem;
  onClose: () => void;
}

type PlanTab = {
  day: number;
  date: string;
  plans: Plan[];
};

const PastItineraryDetail: React.FC<Props> = ({ dataBokingDetail, onClose }) => {
  const [activetabKey, setActiveTabKey] = useState<string | number>();

  const [planTabs, setPlanTabs] = useState<PlanTab[]>([]);
  const [planInfo, setPlanInfo] = useState<PlanInfo>(null);

  const fetchPlans = async () => {
    const planVersionLatest = dataBokingDetail?.plans?.reduce((acc, plan) => {
      if (plan?.is_final) {
        return plan;
      } else {
        return Number(plan?.plan_version) > Number(acc?.plan_version) ? plan : acc;
      }
    }, dataBokingDetail?.plans?.[0]);
    setPlanInfo(planVersionLatest);
  };

  const getListPlanTabs = useCallback(() => {
    if (!planInfo) return;
    const startDate = new Date(planInfo?.start_date);
    const endDate = new Date(planInfo?.end_date);
    const diffTime = Math.abs(endDate.valueOf() - startDate.valueOf());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const list = Array(diffDays + 1)
      .fill(0)
      .map((_i, index) => {
        const nextDate = new Date(startDate);
        nextDate.setDate(startDate.getDate() + index);
        const date = formatDateYMD(nextDate);

        return {
          day: index + 1,
          date: date,
          plans: planInfo?.plan_items
            ?.filter((plan) => plan?.date === date && plan?.reference_type_key !== 'preview_memo')
            ?.map((item) => ({
              ...item,
              reference_type: item?.reference_type_key,
              name_jp: item?.name,
            })),
        };
      });
    setPlanTabs(list);
  }, [planInfo]);

  useEffect(() => {
    fetchPlans();
  }, [dataBokingDetail]);

  useEffect(() => {
    getListPlanTabs();
  }, [planInfo]);

  console.log(dataBokingDetail);

  return (
    <div className="flex flex-col items-center gap-y-[16px] h-full">
      <div className="border border-[#DCDEE3] rounded-lg px-3 py-2 w-full h-[176px]">
        <p className="text-lg leading-7 text-[#363840] font-bold">1. 旅程概要</p>
        <div className="flex items-center justify-between py-2 border-b border-[#EFF0F2]">
          <div>旅程名</div>
          <div>{dataBokingDetail.tour_name}</div>
        </div>
        <div className="grid grid-cols-2 gap-x-[20px]">
          <div className="col-span-1 flex items-center justify-between py-2 border-b border-[#EFF0F2]">
            <div>管理番号</div>
            <div>{dataBokingDetail.travel_id}</div>
          </div>
          <div className="col-span-1 flex items-center justify-between py-2 border-b border-[#EFF0F2]">
            <div>得意先</div>
            <div>{dataBokingDetail.business_partner_name}</div>
          </div>
          <div className="col-span-1 flex items-center justify-between py-2">
            {' '}
            <div>出発日</div>
            <div>{dataBokingDetail.departure_date}</div>
          </div>
          <div className="col-span-1 flex items-center justify-between py-2">
            <div>日数</div>
            <div>{dataBokingDetail.total_days}</div>
          </div>
        </div>
      </div>
      <div className="border border-[#DCDEE3] rounded-lg px-3 py-2 w-full flex-1 [&_.ant-tabs]:!h-[90%]">
        <p className="text-lg leading-7 text-[#363840] font-bold">2. 行程表</p>
        <div className="h-full">
          <Tabs
            className="flex flex-1 overflow-auto [&_.ant-tabs-content-holder]:overflow-y-auto  [&_.ant-tabs-content]:!h-full [&_.ant-tabs-tabpane]:!h-full"
            onChange={(key) => {
              setActiveTabKey(key);
            }}
            activeKey={activetabKey?.toString()}
          >
            {planTabs?.map((item, index) => (
              <Tabs.TabPane
                key={index + 1}
                tab={
                  <span className="text-[15px] font-medium leading-6 text-[#7D7E82]">
                    {item?.day}日目
                  </span>
                }
              >
                <PlanForDayTabItems plans={item?.plans} hasRemoveItem={false} />
              </Tabs.TabPane>
            ))}
          </Tabs>
        </div>
      </div>

      <div
        className="h-[40px] w-[248px] gap-x-2 border rounded border-[#DCDEE3] flex items-center justify-center text-[#FF3B30] cursor-pointer"
        onClick={onClose}
      >
        <CancelXSVG /> 閉じる
      </div>
    </div>
  );
};

export default PastItineraryDetail;
