import { Tabs } from 'antd';
import { forwardRef, useContext } from 'react';
import { PlanCreateContext } from '@/providers';
import TravelSpot from './travelSpot';
import Hoteltab from './hotel';
import PastItineraryTab from './pastItinerary';
import RestaurantTab from './restaurant';
import BusTab from './bus';
import HireCarTab from './hireCar';
import RailwayTab from './railway';
import ShipTab from './ship';
import DeliveryTab from './delivery';
import GuideTab from './guide';
import OtherTab from './other';
import AirlineTab from './airline';

function PlanOptionTabs() {
  const { onChangeTabOptions } = useContext(PlanCreateContext);

  return (
    <>
      <div
        className="bg-white rounded-xl p-4 [&_.ant-tabs-content-top]:h-full [&_.ant-tabs-tabpane-active]:h-full [&_.ant-tabs-tabpane-active]:overflow-y-auto"
        style={{ height: '100%', width: '100%' }}
      >
        <Tabs
          defaultActiveKey="1"
          onChange={onChangeTabOptions}
          className="w-full itinerary-content-container"
        >
          <Tabs.TabPane
            key={1}
            tab={
              <span className="text-[15px] font-medium leading-6 text-[#7D7E82]">過去行程表</span>
            }
          >
            <PastItineraryTab />
          </Tabs.TabPane>
          <Tabs.TabPane
            forceRender
            key={2}
            tab={<span className="text-[15px] font-medium leading-6 text-[#7D7E82]">観光地</span>}
          >
            <TravelSpot />
          </Tabs.TabPane>
          <Tabs.TabPane
            key={3}
            tab={<span className="text-[15px] font-medium leading-6 text-[#7D7E82]">宿泊施設</span>}
          >
            <Hoteltab />
          </Tabs.TabPane>
          <Tabs.TabPane
            key={4}
            tab={<span className="text-[15px] font-medium leading-6 text-[#7D7E82]">飲食店</span>}
          >
            <RestaurantTab />
          </Tabs.TabPane>
          <Tabs.TabPane
            key={5}
            tab={<span className="text-[15px] font-medium leading-6 text-[#7D7E82]">バス</span>}
          >
            <BusTab />
          </Tabs.TabPane>
          <Tabs.TabPane
            key={6}
            tab={<span className="text-[15px] font-medium leading-6 text-[#7D7E82]">ハイヤー</span>}
          >
            <HireCarTab />
          </Tabs.TabPane>
          <Tabs.TabPane
            key={7}
            tab={<span className="text-[15px] font-medium leading-6 text-[#7D7E82]">鉄道</span>}
          >
            <RailwayTab />
          </Tabs.TabPane>
          <Tabs.TabPane
            key={8}
            tab={<span className="text-[15px] font-medium leading-6 text-[#7D7E82]">航空券</span>}
          >
            <AirlineTab />
          </Tabs.TabPane>
          <Tabs.TabPane
            key={9}
            tab={<span className="text-[15px] font-medium leading-6 text-[#7D7E82]">船</span>}
          >
            <ShipTab />
          </Tabs.TabPane>
          <Tabs.TabPane
            key={10}
            tab={<span className="text-[15px] font-medium leading-6 text-[#7D7E82]">宅配</span>}
          >
            <DeliveryTab />
          </Tabs.TabPane>
          <Tabs.TabPane
            key={11}
            tab={<span className="text-[15px] font-medium leading-6 text-[#7D7E82]">ガイド</span>}
          >
            <GuideTab />
          </Tabs.TabPane>
          <Tabs.TabPane
            key={12}
            tab={<span className="text-[15px] font-medium leading-6 text-[#7D7E82]">その他</span>}
          >
            <OtherTab />
          </Tabs.TabPane>
        </Tabs>
      </div>
    </>
  );
}

export default forwardRef(PlanOptionTabs);
