import type { PlanInfo, PlanInfoPayload } from '@/@types/plan';
// import { updatePlanInfo } from '@/apis/plan';
import BasicDateRangePicker from '@/components/Commons/BasicDateRangePicker';
import BasicInput from '@/components/Commons/BasicInput';
import BasicTextArea from '@/components/Commons/BasicTextArea';
import RequiredTag from '@/components/Commons/RequiredTag/RequiredTag';
import {
  openNotificationBlock,
  openNotificationFail,
  openNotificationSuccess,
} from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { rules } from '@/constants/rules';
import STATUS_CODE from '@/constants/statusCode';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import type { FormProps } from 'antd/es/form';
import type { Moment } from 'moment';
import moment from 'moment';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import { useParams } from 'umi';

type InfoPlanEditProps = {
  onSubmit: () => void;
  onClosePopup: () => void;
  plan: PlanInfo;
  onEmitEventReRenderData?: () => void;
};

export type InfoPlanEditRef = {
  form: FormInstance<any>;
};

const InfoPlanEdit = forwardRef<InfoPlanEditRef, InfoPlanEditProps>(
  (
    {
      onSubmit,
      plan,
      onClosePopup,
      onEmitEventReRenderData,
      ...others
    }: InfoPlanEditProps & FormProps,
    ref,
  ) => {
    const [form] = Form.useForm();
    const { id } = useParams<{ id: string }>();
    const [dates, setDates] = useState<[Moment, Moment] | null>(null);

    useImperativeHandle(ref, () => ({
      form,
    }));

    const handleSubmit = async (values) => {
      // todo: update it
      try {
        const payload = {
          title: plan?.title,
          plan_description: values?.plan_description,
          start_date: moment(values?.period?.[0]).format('YYYY-MM-DD'),
          end_date: moment(values?.period?.[1]).format('YYYY-MM-DD'),
        } as PlanInfoPayload;

        onClosePopup();

        const { status } = (await {}) as any;
        // updatePlanInfo({
        //   booking_id: plan?.booking_id,
        //   planId: plan?.id,
        //   data: payload,
        // });

        if (status === STATUS_CODE.SUCCESSFUL) {
          // fetch data
          onSubmit();
          onEmitEventReRenderData();
          openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
        } else {
          openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
        }
      } catch (error) {
        openNotificationBlock(MESSAGE_ALERT.SERVER_ERROR);
      }
    };

    const onChangeDates = (selectedDates: [Moment, Moment] | null) => {
      setDates(selectedDates);
    };

    const dateDisabled = (current: any) => {
      return current && current < moment().startOf('day');
    };

    useEffect(() => {
      if (plan) {
        const startDate = moment(plan?.start_date, 'YYYY-MM-DD');
        const endDate = moment(plan?.end_date, 'YYYY-MM-DD');
        const periodDates = [startDate, endDate];

        setDates([startDate, endDate]);

        form?.setFieldsValue({
          title: plan?.title + '_v' + plan?.plan_version,
          plan_description: plan?.plan_description,
          period: periodDates,
        });
      }
    }, [plan]);

    return (
      <Form {...others} form={form} onFinish={handleSubmit} className="">
        <Form.Item name="title">
          <BasicInput
            disabled
            title={
              <div className="flex items-center gap-[6px]">
                タイトル <RequiredTag />
              </div>
            }
          />
        </Form.Item>
        {/* <Form.Item
          name="period"
          className="[&_.ant-picker-range]:h-[40px]"
          rules={[{ required: true, message: '※必須項目が未入力です。' }]}
        >
          <BasicDateRangePicker
            value={dates}
            title={
              <div className="flex items-center gap-[6px]">
                期間 <RequiredTag />
              </div>
            }
            onChange={onChangeDates}
            disabledDate={dateDisabled}
          />
        </Form.Item> */}
        <Form.Item name="plan_description">
          <BasicTextArea title="コメント" placeholder="コメント" maxLength={1000} />
        </Form.Item>
      </Form>
    );
  },
);

export default InfoPlanEdit;
