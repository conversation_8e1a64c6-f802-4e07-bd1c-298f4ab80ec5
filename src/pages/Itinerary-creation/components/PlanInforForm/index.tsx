import type { PlanInfo } from '@/@types/plan';
import BasicDateRangePicker from '@/components/Commons/BasicDateRangePicker';
import BasicInput from '@/components/Commons/BasicInput';
import RequiredTag from '@/components/Commons/RequiredTag/RequiredTag';
import { rules } from '@/constants/rules';
import type { FormProps } from 'antd';
import { Form } from 'antd';
import type { FormInstance } from 'antd/es/form';
import moment from 'moment';
import React, { forwardRef, useEffect, useImperativeHandle } from 'react';

type PlanFormProps = {
  onSubmit?: (v: any) => void;
  initialData?: PlanInfo;
};

export type PlanFormRefType = {
  form: FormInstance<any>;
};

const PlanInforForm = forwardRef<PlanFormRefType, PlanFormProps>(
  ({ onSubmit, initialData, ...others }: PlanFormProps & FormProps, ref) => {
    const [form] = Form.useForm();

    useImperativeHandle(ref, () => ({
      form,
    }));

    useEffect(() => {
      if (initialData) {
        form.setFieldsValue({
          title: initialData?.title,
          time_period: [
            initialData?.start_date ? moment(initialData?.start_date) : undefined,
            initialData?.end_date ? moment(initialData?.end_date) : undefined,
          ],
        });
      }
    }, [initialData]);

    return (
      <div>
        <Form {...others} form={form} onFinish={onSubmit} className="">
          <Form.Item name="title" rules={rules?.requiredInput}>
            <BasicInput
              title={
                <div className="flex items-center gap-[6px]">
                  旅程表タイトル
                  <RequiredTag isSymbol />
                </div>
              }
              disabled
              placeholder="ツアー名を入力"
            />
          </Form.Item>
          <Form.Item
            name="time_period"
            rules={[
              { required: true, message: '※必須項目が未入力です。' },
              {
                validator: (_, value) =>
                  value && value[0] && value[1]
                    ? Promise.resolve()
                    : Promise.reject(new Error('※必須項目が未入力です。')),
              },
            ]}
          >
            <BasicDateRangePicker title="出発日" className="!h-10" required />
          </Form.Item>
          {/* <Form.Item name="plan_description">
            <BasicInput title="区間" placeholder="区間" />
          </Form.Item> */}
        </Form>
      </div>
    );
  },
);

export default PlanInforForm;
