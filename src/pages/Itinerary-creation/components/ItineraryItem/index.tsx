import type { Plan } from '@/@types/plan';
import TrashSVGV2 from '@/components/SVG/TrashSVGV2';
import { PlanCreateContext } from '@/providers';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { Popover } from 'antd';
import { useContext, useState, type FC } from 'react';

interface ItinerarySortableItemProps {
  id: string | number;
  hasRemoveItem: boolean;
  plan: Plan;
  hasClickItem?: boolean;
  onClickItem?: () => void;
}

export const ItinerarySortableItem: FC<ItinerarySortableItemProps> = ({
  hasRemoveItem,
  plan,
  id,
  hasClickItem,
  onClickItem,
}) => {
  const { setNodeRef, listeners, transform, transition, attributes } = useSortable({
    id,
  });
  const { onRemovePlan } = useContext(PlanCreateContext);
  const [openPopover, setOpenPopover] = useState(false);

  const styles = {
    transform: CSS.Transform.toString(transform),
    transition,
  };

  const hide = () => {
    setOpenPopover(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpenPopover(newOpen);
  };

  const acceptSvg = (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="green"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="feather feather-check"
    >
      <polyline points="20 6 9 17 4 12" />
    </svg>
  );

  const closeSvg = (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="red"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="feather feather-x"
    >
      <line x1="18" y1="6" x2="6" y2="18" />
      <line x1="6" y1="6" x2="18" y2="18" />
    </svg>
  );

  return (
    <div style={{ position: 'relative' }}>
      <div
        style={styles}
        ref={setNodeRef}
        {...listeners}
        {...attributes}
        key={`${plan?.id}-plan`}
        className="bg-[#fff] w-full rounded-[8px] p-3 flex items-center justify-between gap-1"
      >
        <div className="text-main-black w-[88%] leading-7 text-[15px] font-bold truncate">
          {plan?.name_jp ?? plan?.name}
        </div>
      </div>
      {hasRemoveItem ? (
        <Popover
          content={
            <div className="flex justify-between w-full">
              <button onClick={hide} className="px-3 hover:opacity-50">
                {closeSvg}
              </button>
              <button onClick={() => onRemovePlan(plan)} className="px-3">
                {acceptSvg}
              </button>
            </div>
          }
          title="これを削除しますか？"
          trigger="click"
          open={openPopover}
          onOpenChange={handleOpenChange}
          overlayInnerStyle={{ width: '100%' }}
          overlayClassName="[&_.ant-popover-inner-content]:!w-full"
        >
          <button
            style={styles}
            // onClick={() => onRemovePlan(plan)}
            className="absolute bottom-4 right-3"
          >
            <TrashSVGV2 />
          </button>
        </Popover>
      ) : null}
    </div>
  );
};
