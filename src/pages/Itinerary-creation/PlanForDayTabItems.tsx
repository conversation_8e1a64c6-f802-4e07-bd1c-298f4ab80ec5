import type { Plan } from '@/@types/plan';
import { useContext, useEffect, useState } from 'react';
import { ItinerarySortableItem } from './components/ItineraryItem';
import type { DragEndEvent } from '@dnd-kit/core';
import {
  closestCenter,
  DndContext,
  MouseSensor,
  TouchSensor,
  useSensor,
  useSensors,
} from '@dnd-kit/core';
import { PlanCreateContext } from '@/providers';
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';

type Props = {
  plans: Plan[];
  hasRemoveItem?: boolean;
};

function PlanForDayTabItems({ plans, hasRemoveItem = true }: Props) {
  const { onRemovePlan, updatePositionPlans } = useContext(PlanCreateContext);
  const [dataSource, setDataSource] = useState<Plan[]>(plans);
  const [isDragging, setIsDragging] = useState(false);
  const sensors = useSensors(useSensor(MouseSensor), useSensor(TouchSensor));

  useEffect(() => {
    setDataSource(plans);
  }, [plans]);

  const onDragStart = () => {
    setIsDragging(true);
  };

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id) {
      setDataSource((prevState) => {
        const activeIndex = prevState.findIndex((record) => record.id === active?.id);
        const overIndex = prevState.findIndex((record) => record.id === over?.id);
        updatePositionPlans(arrayMove(prevState, activeIndex, overIndex));
        return arrayMove(prevState, activeIndex, overIndex);
      });
    }
  };

  return (
    <div className="ItineraryItemList flex flex-col gap-[10px] h-full flex-1 overflow-y-scroll bg-[#EFF0F2] p-2 rounded-lg">
      <DndContext
        autoScroll={false}
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={onDragStart}
        onDragEnd={onDragEnd}
        modifiers={[restrictToVerticalAxis]}
      >
        <SortableContext items={dataSource} strategy={verticalListSortingStrategy}>
          {dataSource?.map((item) => (
            <ItinerarySortableItem
              key={`${item?.id}-plan`}
              plan={item}
              id={item?.id}
              hasRemoveItem={hasRemoveItem}
            />
          ))}
        </SortableContext>
      </DndContext>
    </div>
  );
}

export default PlanForDayTabItems;
