import type { PlanCreateContextProps } from '@/providers';
import { PlanCreateContext } from '@/providers';
import { Checkbox, Popconfirm, Tooltip } from 'antd';
import { useContext, useEffect, useMemo, useRef, useState } from 'react';

import BasicButton from '@/components/Commons/BasicButton';
import type { BasicModalRef } from '@/components/Commons/BasicModal';
import BasicModal from '@/components/Commons/BasicModal';
import { history } from 'umi';
import TrashSVGV2 from '@/components/SVG/TrashSVGV2';
import PlanForDayTabItems from './PlanForDayTabItems';
import type { InfoPlanEditRef } from './components/InfoPlanEdit';
import InfoPlanEdit from './components/InfoPlanEdit';
import CalendarIconSVG from '@/components/SVG/CalendarIconSVG';
import FileDownloadSVG from '@/components/SVG/FileDownloadSVG';
import OverwriteSaveSVG from '@/components/SVG/OverwriteSaveSVG';
import CustomTabs from '@/components/Commons/CustomTabs';
import CancelXSVG from '@/components/SVG/CancelXSVG';
import type { PlanFormRefType } from './components/PlanInforForm';
import PlanInforForm from './components/PlanInforForm';
import moment from 'moment';

type Props = {
  isHidePlanAction?: boolean;
};

function PlanForDayTabs({ isHidePlanAction = false }: Props) {
  const {
    onChangePlanTab,
    planInfo,
    planTabs,
    setDataPlanTabs,
    onPlanUpdate,
    onDuplicatePlan,
    isPlanDaysNotAvailable,
    fetchPlans,
    countDayRange,
    activePlanTab,
    onUpdatePlanInfor,
    changeDatePlanInfor,
  } = useContext<PlanCreateContextProps>(PlanCreateContext);
  const refCreatePlanModal = useRef<BasicModalRef>(null);
  const refPlanForm = useRef<PlanFormRefType>(null);
  const [loading, setLoading] = useState(true);
  const refDuplicatePlanModal = useRef(null);
  const refUpdatePlanModal = useRef(null);
  const refInfoPlanEditPopup = useRef<BasicModalRef>(null);
  const refInfoPlanEdit = useRef<InfoPlanEditRef>(null);
  const [selectedDate, setSelectedDate] = useState<Date>();
  const [activetabKey, setActiveTabKey] = useState<string | number>();

  const [isModelCourse, setIsModelCourse] = useState(false);
  const bookingId = planInfo?.itinerary_id;

  const acceptSvg = (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="green"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="feather feather-check"
    >
      <polyline points="20 6 9 17 4 12" />
    </svg>
  );

  const closeSvg = (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="red"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className="feather feather-x"
    >
      <line x1="18" y1="6" x2="6" y2="18" />
      <line x1="6" y1="6" x2="18" y2="18" />
    </svg>
  );

  const onUpdatePlans = () => {
    onPlanUpdate(isModelCourse, () => {
      history.push('/itinerary-management/' + bookingId);
    });
  };

  const onDuplicatePlans = () => {
    onDuplicatePlan(isModelCourse, () => {
      history.push('/itinerary-management/' + bookingId);
    });
  };

  const handleInfoPlanEdit = () => {
    // fetch data
  };

  const onCloseInfoPlanEditPopup = () => {
    if (refInfoPlanEditPopup) {
      refInfoPlanEditPopup.current?.close();
    }
  };

  const openModalConfirmDuplicatePlan = () => {
    if (refDuplicatePlanModal) {
      refDuplicatePlanModal.current.open();
    }
  };

  const openModalConfirmUpdatePlan = () => {
    if (refUpdatePlanModal) {
      refUpdatePlanModal.current.open();
    }
  };

  const removePlanDay = (day: number) => {
    const dataDelete = planTabs.filter((val) => val.day !== day);
    setDataPlanTabs(dataDelete);
    if (day > 1) {
      setActiveTabKey(day - 1);
    }
    if (day <= 1) {
      setActiveTabKey(1);
    }
  };

  useEffect(() => {
    const date = planInfo && new Date(planInfo?.start_date);
    setSelectedDate(date);
    setIsModelCourse(planInfo?.is_model_course);
    if (planInfo && planTabs) {
      setLoading(false); // Data has loaded, set loading to false
    }
  }, [planInfo]);

  useEffect(() => {
    setActiveTabKey(activePlanTab);
  }, [activePlanTab]);

  const DataTabRender = useMemo(() => {
    const newData = planTabs?.map((item, index) => ({
      key: item?.day?.toString(),
      tabTitle: (
        <div
          className="text-[15px] font-medium leading-6 text-[#7D7E82] flex items-center justify-between gap-1"
          onClick={() => {
            const date = new Date(item.date);
            setSelectedDate(date);
          }}
        >
          {item?.day}日目
          {countDayRange !== planTabs.length ? (
            <Popconfirm
              title="この日付を削除しますか?"
              onConfirm={() => removePlanDay(item?.day)}
              okText={acceptSvg}
              cancelText={closeSvg}
            >
              <button
                style={{
                  zIndex: 100,
                }}
                // onClick={(e) => handleOpenPopover(e, index + 1)}
                // onContextMenu={(e) => handleOpenPopover(e, index + 1)}
                className={countDayRange === planTabs.length ? '!hidden' : ''}
              >
                <TrashSVGV2 color="red" />
              </button>
            </Popconfirm>
          ) : null}
        </div>
      ),
      tabContent: <PlanForDayTabItems plans={item?.plans} hasRemoveItem={true} />,
    }));

    return newData;
  }, [planTabs]);

  const handleUpdatePlan = async (values: any) => {
    try {
      const payload = {
        ...planInfo,
        start_date: values?.time_period?.[0]
          ? moment(values?.time_period?.[0]).format('YYYY-MM-DD')
          : '',
        end_date: values?.time_period?.[1]
          ? moment(values?.time_period?.[1]).format('YYYY-MM-DD')
          : '',
      };
      changeDatePlanInfor(payload.start_date, payload.end_date);
      refCreatePlanModal?.current?.close();
    } catch (error) {}
  };

  return (
    <>
      <div className="flex flex-col mb-6">
        <div className="flex justify-between">
          <Tooltip title={planInfo?.title}>
            <div className="text-[18px] text-main-black font-bold leading-7 line-clamp-1">
              {planInfo?.title}
            </div>
          </Tooltip>
        </div>
      </div>
      <div>
        <span className="text-[12px] leading-4 font-medium text-[#363840]">期間</span>
        <div className="mt-2 flex items-center justify-between space-x-[18px] border border-[#DCDEE3] rounded px-[12px] py-[10px]">
          <div className="flex items-center">
            <div>{planInfo?.start_date ?? '-'}</div>
            <div className="mx-1">~</div>
            <div>{planInfo?.end_date ?? '-'}</div>
          </div>
          <div>
            <CalendarIconSVG
              className="cursor-pointer"
              onClick={() => refCreatePlanModal?.current?.open()}
            />
          </div>
        </div>
      </div>
      <div className="flex-1 h-[60%] flex [&_.ant-tabs-content-holder]:overflow-y-auto [&_.ant-tabs-content]:!h-full [&_.ant-tabs-tabpane]:!h-full [&_.ItineraryItemList]:!w-[248px]">
        <CustomTabs
          data={DataTabRender}
          handleChangeTab={(key) => onChangePlanTab(key)}
          currrentTab={activetabKey?.toString() ?? '1'}
          setCurrentTab={setActiveTabKey}
        />
      </div>

      {isHidePlanAction ? (
        ''
      ) : (
        <div className="flex flex-col border-[#F4F4F4] pt-4 gap-y-4">
          <Checkbox
            checked={isModelCourse}
            className="text-[16px] leading-6 font-medium text-[#24262B] py-2"
            onChange={() => setIsModelCourse(!isModelCourse)}
          >
            モデルコースにする
          </Checkbox>
          <BasicButton
            className="flex items-center justify-center gap-x-2 "
            styleType="noneOutLine"
            width={78}
            icon={<OverwriteSaveSVG />}
            onClick={openModalConfirmUpdatePlan}
          >
            <span className="text-main-color">上書き保存</span>
          </BasicButton>
          <BasicButton
            className="flex items-center justify-center gap-x-2"
            styleType="noneOutLine"
            width={78}
            icon={<FileDownloadSVG />}
            onClick={openModalConfirmDuplicatePlan}
          >
            <span className="text-[#225DE0]">別バージョン保存</span>
          </BasicButton>
          <BasicButton
            styleType="noneOutLine"
            width={78}
            onClick={() => history.goBack()}
            className="flex items-center justify-center gap-x-2 text-[#FF3B30]"
          >
            <CancelXSVG /> キャンセル
          </BasicButton>
        </div>
      )}

      <BasicModal
        ref={refInfoPlanEditPopup}
        title="旅程を更新する"
        content={
          <InfoPlanEdit
            ref={refInfoPlanEdit}
            plan={planInfo}
            onSubmit={handleInfoPlanEdit}
            onClosePopup={onCloseInfoPlanEditPopup}
            onEmitEventReRenderData={fetchPlans}
          />
        }
        onSubmit={() => {
          refInfoPlanEdit?.current?.form?.submit();
        }}
        buttonCloseTitle="キャンセル"
        styleTypeButtonSubmit="accept"
        buttonSubmitTitle="保存"
      />
      {/* Modal confirm duplicate plan */}
      <BasicModal
        ref={refDuplicatePlanModal}
        title="更新"
        buttonCloseTitle="キャンセル"
        buttonSubmitTitle="更新"
        content="この操作で新しいバージョンの旅程表が保存されます。元に戻すことはできません。"
        onSubmit={onDuplicatePlans}
      />

      {/* Modal confirm update plan */}
      <BasicModal
        ref={refUpdatePlanModal}
        title="上書き保存" //一時保存
        buttonCloseTitle="キャンセル"
        buttonSubmitTitle="上書き保存"
        content="この操作で現在の旅程表が新しい情報に上書きされます。元に戻すことはできません。"
        onSubmit={onUpdatePlans}
      />
      {/* modal update date plan */}
      <BasicModal
        ref={refCreatePlanModal}
        title="期間編集"
        content={
          <PlanInforForm ref={refPlanForm} onSubmit={handleUpdatePlan} initialData={planInfo} />
        }
        onSubmit={() => {
          refPlanForm?.current?.form?.submit();
        }}
        className="!w-[600px] [&_.ant-modal-content]:!max-h-[90vh] [&_.ant-modal-content]:overflow-y-auto"
        buttonCloseTitle="キャンセル"
        styleTypeButtonSubmit="accept"
        buttonSubmitTitle="保存"
        buttonContainerStyle={{ justifyContent: 'center' }}
      />
    </>
  );
}

export default PlanForDayTabs;
