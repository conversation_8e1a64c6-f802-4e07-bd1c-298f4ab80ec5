
import PlanOptionTabs from './PlanOptionTabs';
import PlanForDayTabs from './PlanForDayTabs';
import { useParams } from 'react-router';
import { PlanCreateProvider } from '@/providers';

const ItineraryCreation = () => {
  const { plan_id } = useParams<{ plan_id: string }>();

  return (
    <PlanCreateProvider planId={plan_id}>
      <div
        className="flex itinerary-container overflow-x-auto"
        style={{
          height: 'calc(100vh - 90px)',
        }}
      >
        <div className="flex flex-col w-[320px] bg-white px-9 pt-6 pb-9">
          <PlanForDayTabs />
        </div>
        <div className="m-9" style={{ width: 'calc(100% - 392px)' }}>
          <PlanOptionTabs />
        </div>
      </div>
    </PlanCreateProvider>
  );
};

export default ItineraryCreation;
