const Style = {
  styleRule: {
    backgroundColor: 'rgba(255, 241, 240, 1)',
    color: 'rgba(255, 0, 0, 1)',
    fontSize: 10,
    width: 'fit-content',
    padding: 5,
    borderRadius: 5,
    height: 25,
    whiteSpace: 'nowrap',
  },

  styleLabel: {
    paddingLeft: '1%',
    display: 'flex',
  },

  styleBorder: { border: '1px solid rgba(204, 204, 204, 1)' },
  styleBorderNoneTop: { border: '1px solid rgba(204, 204, 204, 1)', borderTop: 'none' },
  styleLabelDownLine: {
    paddingLeft: '1%',
    width: '100%',
    alignItems: 'center',
    flexDirection: 'column',
    fontWeight: 700,
    color: 'rgba(68, 85, 99, 1)',
  },
  styleButton: {
    backgroundColor: '#8D9300',
    marginLeft: 10,
    minWidth: 160,
    width: 'fit-content',
    height: 50,
    borderRadius: 50,
    color: '#fff',
    fontSize: 18,
    fontWeight: 700,
  },

  styleButtonOnSearch: {
    backgroundColor: '#FFFFFF',
    marginLeft: 10,
    width: 120,
    height: 36,
    fontSize: 14,

    border: '2px solid #8D9300',
    borderRadius: 50,
    color: 'rgba(141, 147, 0, 1)',
    fontWeight: 700,
  },

  styleButtonOnTable: {
    width: 128,
    height: 40,
    borderRadius: 44,
    color: '#8D9300',
    fontSize: 14,

    fontWeight: 500,
    border: '2px solid #8D9300',
  },
  styleButtonEditOnTable: {
    backgroundColor: 'rgba(255, 255, 255, 1)',

    width: 80,
    height: 40,
    fontSize: 14,
    display: 'flex',
    alignItems: 'center',
    gap: 3,
    borderRadius: 50,
    color: 'rgba(141, 147, 0, 1)',
    border: '2px solid  rgba(141, 147, 0, 1)',
    fontWeight: 700,
  },
  stylePaddingLeft: {
    paddingLeft: 0,
  },

  stylePaddingLeft2: {
    paddingLeft: 0,
  },
  styleRow: {
    display: 'flex',

    flexDirection: 'row',
  },
  styleRowAndCenter: {
    display: 'flex',
    alignItems: 'center',
    flexDirection: 'row',
  },

  styleRowAndBetween: { display: 'flex', flexDirection: 'row', justifyContent: 'space-between' },
  stylePadding: { padding: '5px 10px' },
};

export default Style;
