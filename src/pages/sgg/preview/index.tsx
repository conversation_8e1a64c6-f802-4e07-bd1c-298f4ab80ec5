import type { ItemSgg } from '@/apis/sgg';
import { deleteSgg, getDetailSgg } from '@/apis/sgg';
import IconBack from '@/assets/imgs/common-icons/back-btn.svg';
import IconDelete from '@/assets/imgs/common-icons/delete-white.svg';
import IconEdit from '@/assets/imgs/common-icons/edit-white.svg';
import BasicButton from '@/components/Commons/BasicButton';
import Exception from '@/components/Exception';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_TITLE, TEXT_WARNING } from '@/constants/commonText';
import { typeSGG } from '@/constants/data';
import STATUS_CODE from '@/constants/statusCode';
import { Image, Popconfirm, Spin } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { history, Link, useParams } from 'umi';

const SGGPage: React.FC = () => {
  const paramUrl = useParams();
  const sggId = (paramUrl as { id: string })?.id;
  const [data, setData] = useState<ItemSgg>();
  const [loadingApi, setLoadingApi] = useState<boolean>(false);
  const [loadingPage, setLoadingPage] = useState<boolean>(false);

  const onFetchData = async (id) => {
    setLoadingPage(true);
    const res = await getDetailSgg(id);
    if (res?.data?.data) {
      setData(res?.data?.data);
    } else {
      setData(null);
    }
    setLoadingPage(false);
  };

  useEffect(() => {
    if (sggId) {
      onFetchData(sggId);
    }
  }, [sggId]);

  const confirmDeleteRow = async () => {
    setLoadingApi(true);
    const resDelete = await deleteSgg(Number(sggId));
    if (resDelete?.status === STATUS_CODE.SUCCESSFUL) {
      openNotificationSuccess(MESSAGE_ALERT.DELETE_SUCCESS);
      history.replace('/sgg-management/list');
    } else {
      openNotificationFail(MESSAGE_ALERT.DELETE_SUCCESS);
    }
    setLoadingApi(false);
  };
  const onHandleEdit = () => {
    history.push(`/sgg-management/${sggId}/edit`);
  };

  return (
    <>
      <Spin spinning={loadingPage || loadingApi}>
        {data === null ? (
          <Exception type="404" style={{ minHeight: 500, height: '100%' }} linkElement={Link} />
        ) : (
          <div className="p-6 flex flex-col gap-5 max-w-[600px]">
            <div className="">
              <p className="text-xs text-[#7F8493] font-medium mb-2">{TEXT_TITLE.Title}</p>
              <p className="text-sm text-[#363840] font-medium">{data?.title}</p>
            </div>
            <div className="">
              <p className="text-xs text-[#7F8493] font-medium mb-2">{TEXT_TITLE.Type}</p>
              <p className="text-sm text-[#363840] font-medium">
                {data?.type ? typeSGG?.find((item) => item.value === data.type).label : ''}
              </p>
            </div>
            <div className="">
              <p className="text-xs text-[#7F8493] font-medium mb-2">{TEXT_TITLE.Display_Date}</p>
              <p className="text-sm text-[#363840] font-medium">
                {data?.display_date ? moment(data?.display_date).format('YYYY/MM/DD') : ''}
              </p>
            </div>
            <div className="">
              <p className="text-xs text-[#7F8493] font-medium mb-2">
                {TEXT_TITLE.Expiration_Date}
              </p>
              <p className="text-sm text-[#363840] font-medium">
                {data?.display_date ? moment(data?.expiration_date).format('YYYY/MM/DD') : ''}
              </p>
            </div>
            <div className="">
              <p className="text-xs text-[#7F8493] font-medium mb-2">{TEXT_TITLE.Content}</p>
              <div dangerouslySetInnerHTML={{ __html: data?.content }} />
            </div>
            <div className="flex gap-5 mt-4">
              <BasicButton
                styleType="noneOutLine"
                disabled={loadingApi || loadingPage}
                className="flex-1 !bg-white flex justify-center items-center"
                onClick={() => {
                  history.goBack();
                }}
              >
                <Image preview={false} src={IconBack} width={8} />
                <p className="ml-2 !text-[#363840]">{TEXT_ACTION.RETURN}</p>
              </BasicButton>
              <Popconfirm
                title={TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
                disabled={loadingApi || loadingPage}
                onConfirm={() => confirmDeleteRow()}
                okText={TEXT_ACTION.DELETE}
                cancelText={TEXT_ACTION.CANCEL}
              >
                <BasicButton
                  disabled={loadingApi || loadingPage}
                  styleType="noneOutLine"
                  className="flex-1 !bg-[#FF3B30]"
                >
                  <div className="flex-1 text-white flex items-center justify-center">
                    <Image preview={false} src={IconDelete} width={16} />
                    <p className="ml-2">{TEXT_ACTION.DELETE}</p>
                  </div>
                </BasicButton>
              </Popconfirm>
              <BasicButton
                className="flex-1 flex justify-center items-center !border-[#225DE0] !bg-[#225DE0] hover:shadow-md hover:opacity-70 "
                styleType="noneOutLine"
                disabled={loadingApi || loadingPage}
                onClick={() => onHandleEdit()}
              >
                <Image preview={false} src={IconEdit} width={16} height={16} />
                <p className="text-xs !ml-1 !text-white">{TEXT_ACTION.MODIFY}</p>
              </BasicButton>
            </div>
          </div>
        )}
      </Spin>
    </>
  );
};
export default SGGPage;
