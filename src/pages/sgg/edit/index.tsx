import type { ItemSgg } from '@/apis/sgg';
import { getDetailSgg, updateSgg } from '@/apis/sgg';
import IconCancelRed from '@/assets/imgs/common-icons/cancel-red.svg';
import IconSave from '@/assets/imgs/common-icons/save-white-btn.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTextEditor from '@/components/Commons/BasicTextEditor';
import LoadingGlobal from '@/components/Commons/LoadingGlobal';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_TITLE } from '@/constants/commonText';
import { typeSGG } from '@/constants/data';
import STATUS_CODE from '@/constants/statusCode';
import { Form, Image, Spin } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { history, useParams } from 'umi';

const SGGEditPage: React.FC = () => {
  const paramUrl = useParams();
  const sggId = (paramUrl as { id: string })?.id;
  const [data, setData] = useState<ItemSgg>();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [loadingPage, setLoadingPage] = useState<boolean>(false);
  const [form] = Form.useForm();

  const onFetchData = async (id) => {
    setLoadingPage(true);
    const res = await getDetailSgg(id);
    if (res.status === STATUS_CODE.SUCCESSFUL && res?.data?.data) {
      const resData = res?.data?.data;
      form.setFieldsValue({
        title: resData?.title,
        type: resData?.type,
        display_date: moment(resData?.display_date),
        expiration_date: moment(resData?.expiration_date),
        content: resData?.content,
      });
      setData(resData);
    } else {
      form.resetFields();
      setData(null);
    }
    setLoadingPage(false);
  };

  useEffect(() => {
    if (sggId) {
      onFetchData(sggId);
    }
  }, [sggId]);

  function isQuillEmpty(_, value) {
    if (value) {
      if (value?.replace(/<(.|\n)*?>/g, '')?.trim()?.length === 0 && !value?.includes('<img')) {
        return Promise.reject(new Error('※必須項目が未入力です。'));
      }
    }
    return Promise.resolve();
  }

  const onSave = async () => {
    try {
      setIsLoading(true);
      await form.validateFields();
      const values = form.getFieldsValue();
      const resUpdate = await updateSgg({
        ...values,
        id: sggId,
        expiration_date: values.expiration_date?.format('YYYY-MM-DD'),
        display_date: values.display_date?.format('YYYY-MM-DD'),
      });
      if (resUpdate.status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        onFetchData(sggId);
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      console.log('error', error);
    }
  };

  return (
    <Spin spinning={loadingPage || isLoading}>
      <div className="p-6 flex flex-col gap-5 max-w-[600px]">
        {loadingPage && <LoadingGlobal />}
        <Form form={form} component={false}>
          <Form.Item
            className="!mb-0"
            name="title"
            rules={[{ required: true, message: 'を入力してください' }]}
          >
            <BasicInput
              required
              title={TEXT_TITLE.Title}
              className="!h-10"
              placeholder={TEXT_TITLE.Title}
            />
          </Form.Item>
          <Form.Item
            className="!mb-0"
            name="type"
            rules={[{ required: true, message: 'を入力してください' }]}
            initialValue={typeSGG[1].value}
          >
            <BasicSelect title={TEXT_TITLE.Type} options={typeSGG} required={true} />
          </Form.Item>
          <Form.Item
            className="!mb-0"
            name="display_date"
            rules={[{ required: true, message: 'を入力してください' }]}
          >
            <BasicDatePicker title={TEXT_TITLE.Display_Date} className="!h-10" required={true} />
          </Form.Item>
          <Form.Item
            className="!mb-0"
            name="expiration_date"
            rules={[{ required: true, message: 'を入力してください' }]}
          >
            <BasicDatePicker title={TEXT_TITLE.Expiration_Date} className="!h-10" required={true} />
          </Form.Item>
          <Form.Item
            className="!mb-0"
            name="content"
            rules={[{ required: true, message: 'を入力してください', validator: isQuillEmpty }]}
            getValueFromEvent={(value) => value}
          >
            <BasicTextEditor
              form={form}
              name="content"
              required
              className={` [&_.ql-container]:!h-[280px] `}
              title={TEXT_TITLE.Content}
            />
          </Form.Item>
        </Form>
        <div className="flex justify-center mt-6 gap-5">
          <BasicButton
            className="flex-1 flex items-center justify-center rounded-[4px] shadow-md !bg-white hover:opacity-40 font-medium !text-[#FF3B30] !border-[#DCDEE3]"
            onClick={() => {
              form.resetFields();
              history.replace('/sgg-management/list');
            }}
          >
            <Image preview={false} src={IconCancelRed} width={18} height={18} />
            <p className="pl-2 font-medium">{TEXT_ACTION.CANCEL}</p>
          </BasicButton>
          <BasicButton
            disabled={isLoading}
            styleType="accept"
            className={`flex-1 flex justify-center items-center ${
              isLoading ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
            }`}
            onClick={onSave}
          >
            <Image preview={false} src={IconSave} width={12} height={14} />
            <p>{TEXT_ACTION.SAVE}</p>
          </BasicButton>
        </div>
      </div>
    </Spin>
  );
};
export default SGGEditPage;
