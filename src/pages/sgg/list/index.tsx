import { useEffect, useState } from 'react';
import TableListSGG from './components/TableList';
import { BaseParams } from '@/@types/request';
import { ITEM_PER_PAGE } from '@/utils/constants';
import HeaderAction from './components/HeaderAction';
import { useUrlSearchParams } from 'use-url-search-params';
import type { ItemSgg } from '@/apis/sgg';
import { getListSgg } from '@/apis/sgg';

const initSearchParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};

const SGGListPage: React.FC = () => {
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);
  const [dataSource, setDataSource] = useState<ItemSgg[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [total, setTotal] = useState<number>(0);

  const onFetchData = async () => {
    setIsLoading(true);
    const res = await getListSgg(parameter);
    if (res?.data?.data) {
      const resData = res?.data?.data?.map((item, index) => ({
        ...item,
        key: (Number(parameter?.page) - 1) * Number(parameter?.limit) + index + 1,
      }));
      setTotal(res?.data?.total);
      setDataSource(resData);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    onFetchData();
  }, [parameter?.keyword, parameter?.limit, parameter?.page]);
  return (
    <div className="p-6">
      <HeaderAction paramSearch={parameter} setParamSearch={setParameter} />
      <div className="h-6" />
      <TableListSGG
        isLoading={isLoading}
        dataSource={dataSource}
        total={total}
        paramSearch={parameter}
        setParamSearch={setParameter}
      />
    </div>
  );
};
export default SGGListPage;
