import BasicButton from '@/components/Commons/BasicButton';
import BasicInput from '@/components/Commons/BasicInput';
import SearchSVG from '@/components/SVG/SearchSVG';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import { PictureOutlined, PlusOutlined } from '@ant-design/icons';
import { useState } from 'react';
import { history } from 'umi';

const HeaderAction = ({ paramSearch, setParamSearch }) => {
  const [searchValue, setSearchValue] = useState<string>('');

  const handleSearchByValue = () => {
    const paramToSearch = { ...paramSearch, page: 1, keyword: searchValue };
    setParamSearch({
      ...paramToSearch,
      keyword: searchValue,
    });
  };

  return (
    <>
      <div className="flex items-end justify-between gap-4 flex-wrap">
        <div className="flex flex-wrap gap-4 ">
          <BasicInput
            style={{
              width: '280px',
              height: '40px',
            }}
            title="タイトル"
            placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
            onChange={(val) => {
              setSearchValue(val.target.value);
            }}
          />
          <div className="flex items-end">
            <BasicButton
              icon={<SearchSVG colorSvg="white" />}
              className="flex items-center w-[120px]"
              styleType="accept"
              onClick={handleSearchByValue}
            >
              <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
            </BasicButton>
          </div>
        </div>

        <div className="flex gap-x-[12px]">
          <BasicButton
            icon={<PictureOutlined />}
            className="flex items-center"
            styleType="accept"
            onClick={() => {
              history.push('/sgg-management/image-setting');
            }}
          >
            {TEXT_ACTION.IMAGE_SETTING}
          </BasicButton>
          <BasicButton
            icon={<PlusOutlined />}
            className="flex items-center"
            styleType="accept"
            onClick={() => {
              history.push('/sgg-management/create');
            }}
          >
            {TEXT_ACTION.CREATE_NEW}
          </BasicButton>
        </div>
      </div>
    </>
  );
};

export default HeaderAction;
