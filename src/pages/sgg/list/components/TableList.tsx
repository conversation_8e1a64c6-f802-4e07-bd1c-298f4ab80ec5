import type { TravelListSearchParams } from '@/apis/accounting/traveList';
import IconEye from '@/assets/imgs/common-icons/view-eye.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicTable from '@/components/Commons/BasicTable';
import { typeSGG } from '@/constants/data';
import { ITEM_PER_PAGE } from '@/utils/constants';
import { Image } from 'antd';
import moment from 'moment';
import { history } from 'umi';

const TableListSGG = ({
  isLoading,
  dataSource,
  paramSearch,
  setParamSearch,
  total,
}: {
  isLoading: boolean;
  dataSource: any;
  paramSearch?: TravelListSearchParams;
  setParamSearch?: (val: TravelListSearchParams) => void;
  total: number;
}) => {
  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'key',
      key: 'key',
      width: 60,
    },
    {
      title: 'タイトル',
      dataIndex: 'title',
      key: 'title',
      width: 384,
      render: (_, { title }) => {
        return <div className="line-clamp-2">{title}</div>;
      },
    },
    {
      title: 'タイプ',
      dataIndex: 'type',
      key: 'type',
      width: 140,
      render: (_, { type }) => {
        const sggTypeData = typeSGG?.find((item) => item.value === type);
        return (
          <div
            className="font-medium flex items-center justify-center !h-[24px] w-[108px] rounded-[4px]"
            style={{ color: sggTypeData?.textColor, background: sggTypeData?.backgroundColor }}
          >
            {sggTypeData?.label}
          </div>
        );
      },
    },
    {
      title: '表示日付',
      dataIndex: 'display_date',
      key: 'display_date',
      width: 140,
      render: (_, { display_date }) => {
        return <div>{display_date ? moment(display_date).format('YYYY/MM/DD') : ''}</div>;
      },
    },
    {
      title: '作成日',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 220,
      render: (_, { created_at }) => {
        return <div>{created_at ? moment(created_at).format('YYYY/MM/DD HH:mm:ss') : ''}</div>;
      },
    },
    {
      title: '更新日',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 220,
      render: (_, { updated_at }) => {
        return <div>{updated_at ? moment(updated_at).format('YYYY/MM/DD HH:mm:ss') : ''}</div>;
      },
    },
    {
      title: '',
      dataIndex: 'viewItem',
      key: 'viewItem',
      width: 108,
      render: (_, record) => (
        <BasicButton
          styleType="outline"
          className=" flex items-center justify-center !h-[24px] w-[76px] border-[transparent] hover:border-[#3997C8] text-main-color"
          onClick={() => {
            history.push(`/sgg-management/${record.id}/preview`);
          }}
        >
          <Image preview={false} src={IconEye} width={16} height={16} />
          <p className="text-xs !ml-1">詳細</p>
        </BasicButton>
      ),
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1200 },
          loading: isLoading,
          columns: defaultColumns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
        }}
        page={Number(paramSearch?.page) ?? 1}
        pageSize={Number(paramSearch?.limit) ?? Number(ITEM_PER_PAGE)}
        onChangePage={(p: number) => {
          setParamSearch({ ...paramSearch, page: p });
          // setParameter({ page: p, limit: paramSearch?.limit });
        }}
        total={total}
        onSelectPageSize={(v) => {
          setParamSearch({ ...paramSearch, limit: v, page: 1 });
          // setParameter({ page: 1, limit: v });
        }}
      />
    </>
  );
};

export default TableListSGG;
