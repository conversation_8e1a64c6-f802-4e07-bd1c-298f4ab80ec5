import { PlusOutlined } from '@ant-design/icons';
import { Image, Modal, Upload, message } from 'antd';
import type { RcFile, UploadProps } from 'antd/es/upload';
import type { UploadFile } from 'antd/es/upload/interface';
import React, { useEffect, useState } from 'react';

const getBase64 = (file: RcFile): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.readAsDataURL(file);
    reader.onload = () => resolve(reader.result as string);
    reader.onerror = (error) => reject(error);
  });

interface UploadImageSettingProps {
  maxLength?: number;
  title?: string | React.ReactNode | any;
  onchange?: (i: any) => void;
  value?: string | string[] | UploadFile<any>[];
  fileName: string;
  fileType?: 'image' | 'video' | 'both';
}

const UploadImageSetting: React.FC<UploadImageSettingProps> = ({
  maxLength,
  title,
  onchange,
  value,
  fileName,
  fileType = 'image',
}) => {
  const MAX_IMAGE_FILE_SIZE_MB = 5;
  const MAX_VIDEO_FILE_SIZE_MB = 100;
  const [previewOpen, setPreviewOpen] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [urlString, setUrlString] = useState<string>();
  const handleCancel = () => setPreviewOpen(false);

  const acceptTypes =
    fileType === 'both' ? 'image/*,video/*' : fileType === 'image' ? 'image/*' : 'video/*';

  useEffect(() => {
    if (typeof value === 'string') {
      setUrlString(value);
    }
  }, [value]);

  const handlePreview = async (file: UploadFile) => {
    if (!file.url && !file.preview) {
      file.preview = await getBase64(file.originFileObj as RcFile);
    }

    setPreviewImage(file.url || (file.preview as string));
    setPreviewOpen(true);
    setPreviewTitle(file.name || file.url!.substring(file.url!.lastIndexOf('/') + 1));
  };

  const handleChange: UploadProps['onChange'] = ({ fileList: newFileList }) => {
    setFileList(newFileList);
    onchange?.(newFileList);
  };

  const beforeUpload = (file: RcFile) => {
    const isImage = file.type.startsWith('image/');
    const isVideo = file.type.startsWith('video/');
    const isLt5M = file.size / 1024 / 1024 < MAX_IMAGE_FILE_SIZE_MB;
    const isLt100M = file.size / 1024 / 1024 < MAX_VIDEO_FILE_SIZE_MB;

    if (isImage && !isLt5M) {
      message.error(`※画像ファイル1ファイル${MAX_IMAGE_FILE_SIZE_MB}MBまでとすること`);
      return Upload.LIST_IGNORE;
    }

    if (isVideo && !isLt100M) {
      message.error(`※動画ファイル1ファイル${MAX_VIDEO_FILE_SIZE_MB}MBまでとすること`);
      return Upload.LIST_IGNORE;
    }

    return true;
  };

  const uploadButton = (
    <div className="flex justify-center items-center">
      <PlusOutlined />
      <div className="ml-[2px]">アップロード</div>
    </div>
  );
  return (
    <>
      {title ? <div className="mb-2 text-[13px] leading-4 font-medium">{title}</div> : null}
      <div className="flex items-center">
        {urlString && fileList.length < 1 && (
          <div
            className="p-2"
            style={{ height: 140, border: '1px solid #d9d9d9', borderRadius: '2px' }}
          >
            {urlString.match(/\.(mp4|webm|ogg)$/i) ? (
              <video
                style={{ objectFit: 'contain', width: 'auto', height: '124px' }}
                src={urlString}
                height={124}
                controls
              />
            ) : (
              <Image
                style={{ objectFit: 'contain' }}
                src={urlString}
                width={124}
                height={124}
                preview
              />
            )}
          </div>
        )}
        <div className="flex flex-col">
          <Upload
            className={`
        [&_.ant-upload]:!w-[140px] [&_.ant-upload]:!h-[40px] ${
          urlString && fileList.length < 1
            ? '[&_.ant-upload-list-picture-card]:!ml-4'
            : '[&_.ant-upload-select-picture-card]:!mt-[50px]'
        }
        [&_.ant-upload-list-picture-card-container]:!w-[140px] [&_.ant-upload-list-picture-card-container]:!h-[140px]`}
            listType="picture-card"
            fileList={fileList}
            onPreview={handlePreview}
            beforeUpload={beforeUpload}
            onChange={handleChange}
            maxCount={maxLength}
            accept={acceptTypes}
          >
            {fileList.length >= 8 ? null : uploadButton}
          </Upload>
          {fileType !== 'video' && (
            <p className="text-xs text-[#363840] mb-1 ml-4">
              ※画像ファイル1ファイル{MAX_IMAGE_FILE_SIZE_MB}MBまでとすること
            </p>
          )}
          {fileType !== 'image' && (
            <p className="text-xs text-[#363840] mb-1 ml-4">
              ※動画ファイル1ファイル{MAX_VIDEO_FILE_SIZE_MB}MBまでとすること
            </p>
          )}
        </div>
      </div>

      <Modal open={previewOpen} title={previewTitle} footer={null} onCancel={handleCancel}>
        {previewImage?.match(/\.(mp4|webm|ogg)$/i) ? (
          <video
            style={{ objectFit: 'contain', width: 'auto', height: '124px' }}
            src={previewImage}
            height={124}
            controls
          />
        ) : (
          <img alt="example" style={{ width: '100%' }} src={previewImage} />
        )}
      </Modal>
    </>
  );
};

export default UploadImageSetting;
