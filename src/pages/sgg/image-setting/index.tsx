import BasicButton from '@/components/Commons/BasicButton';
import BasicUploads from '@/components/Commons/BasicUploads';
import { Spin, Image, Form } from 'antd';
import { useEffect, useState } from 'react';
import IconSave from '@/assets/imgs/common-icons/save-white-btn.svg';
import IconCancelRed from '@/assets/imgs/common-icons/cancel-red.svg';
import { history } from 'umi';
import { TEXT_ACTION } from '@/constants/commonText';
import { getListImageSgg, updateListImageSgg } from '@/apis/sgg';
import UploadImageSetting from './components/UploadImageSetting';
import STATUS_CODE from '@/constants/statusCode';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';

const SGGSettingImagePage: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [form] = Form.useForm();
  const onFetchData = async () => {
    const resFetch = await getListImageSgg();
    if (resFetch.status === STATUS_CODE.SUCCESSFUL) {
      form.setFieldsValue({
        sgg_company_logo: resFetch.data?.data?.sgg_company_logo,
        sgg_photo: resFetch.data?.data.sgg_photo,
      });
    }
  };

  const onSave = async () => {
    try {
      setIsLoading(true);
      const values = await form.validateFields();
      const formData = new FormData();
      if (values.sgg_company_logo?.[0]?.originFileObj) {
        formData.append('sgg_company_logo', values.sgg_company_logo?.[0]?.originFileObj);
      }
      if (values.sgg_photo?.[0]?.originFileObj) {
        formData.append('sgg_photo', values.sgg_photo?.[0]?.originFileObj);
      }
      const config = {
        headers: { 'Content-Type': 'multipart/form-data' },
      };
      if (!values.sgg_company_logo?.[0]?.originFileObj && !values.sgg_photo?.[0]?.originFileObj) {
        openNotificationFail('画像を選択してください');
        setIsLoading(false);
        return;
      }
      const resUpdate = await updateListImageSgg(formData, config);
      if (resUpdate.status === STATUS_CODE.SUCCESSFUL || resUpdate.status === STATUS_CODE.CREATED) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        form.resetFields();
        onFetchData();
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    onFetchData();
  }, []);

  return (
    <Spin spinning={isLoading}>
      <div className="p-6 flex flex-col gap-5 max-w-[600px]">
        <Form form={form} component={false}>
          <Form.Item name="sgg_company_logo">
            <UploadImageSetting
              fileName="sgg_company_logo"
              maxLength={1}
              title={<div className="flex items-center gap-[6px]">会社ロゴ</div>}
              onchange={(value) => {
                form.setFieldValue('sgg_company_logo', value);
              }}
              value={form.getFieldValue('sgg_company_logo') ?? []}
            />
          </Form.Item>
          <Form.Item name="sgg_photo">
            <UploadImageSetting
              fileName="sgg_photo"
              maxLength={1}
              title={<div className="flex items-center gap-[6px]">写真</div>}
              onchange={(value) => {
                form.setFieldValue('sgg_photo', value);
              }}
              value={form.getFieldValue('sgg_photo') ?? []}
              fileType="both"
            />
          </Form.Item>
        </Form>
        <div className="flex justify-center mt-6 gap-5">
          <BasicButton
            className="flex-1 flex items-center justify-center rounded-[4px] shadow-md !bg-white hover:opacity-40 font-medium !text-[#FF3B30] !border-[#DCDEE3]"
            onClick={() => {
              history.replace('/sgg-management/list');
            }}
          >
            <Image preview={false} src={IconCancelRed} width={18} height={18} />
            <p className="pl-2 font-medium">{TEXT_ACTION.CLOSE}</p>
          </BasicButton>
          <BasicButton
            disabled={isLoading}
            styleType="accept"
            className={`flex-1 flex justify-center items-center ${
              isLoading ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
            }`}
            onClick={onSave}
          >
            <Image preview={false} src={IconSave} width={12} height={14} />
            <p>{TEXT_ACTION.SAVE}</p>
          </BasicButton>
        </div>
      </div>
    </Spin>
  );
};
export default SGGSettingImagePage;
