import type { ItemGlobalSgg, ItemImageSgg } from '@/apis/sgg';
import { getListGlobalSgg, getListImageSgg } from '@/apis/sgg';
import SggGoal from '@/assets/imgs/common-icons/sgg_goal.png';
import SggInformation from '@/assets/imgs/common-icons/sgg_information.png';
import SggTask from '@/assets/imgs/common-icons/sgg_task.png';
import ArrowRight from '@/assets/imgs/svg/arrow-right-01.svg';
import STATUS_CODE from '@/constants/statusCode';
import { Image, Spin } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { history } from 'umi';

const SGGPage: React.FC = () => {
  const [dataGoal, setDataGoal] = useState<ItemGlobalSgg[]>([]);
  const [dataInformation, setDataInformation] = useState<ItemGlobalSgg[]>([]);
  const [dataTask, setDataTask] = useState<ItemGlobalSgg[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataImageSetting, setDataImageSetting] = useState<ItemImageSgg>();

  const onFetchData = async () => {
    setIsLoading(true);
    const resFetch = await getListImageSgg();
    if (resFetch.status === STATUS_CODE.SUCCESSFUL) {
      setDataImageSetting(resFetch.data?.data);
    }

    const resGoal = await getListGlobalSgg({ type: 1 });
    if (resGoal.status && resGoal?.data) {
      setDataGoal(resGoal?.data);
    } else {
      setDataGoal([]);
    }

    const resInformation = await getListGlobalSgg({ type: 2 });
    if (resInformation.status && resInformation?.data) {
      const dataRender = [...resInformation?.data];
      dataRender.sort((a, b) => {
        return moment(a.created_at).unix() > moment(b.created_at).unix() ? -1 : 1;
      });
      setDataInformation(dataRender);
    } else {
      setDataInformation([]);
    }

    const resTask = await getListGlobalSgg({ type: 3 });
    if (resTask.status && resTask?.data) {
      const dataRender = [...resTask?.data];
      dataRender.sort((a, b) => {
        return moment(a.expiration_date).unix() < moment(b.expiration_date).unix() ? -1 : 1;
      });
      setDataTask(dataRender);
    } else {
      setDataTask([]);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    onFetchData();
  }, []);

  return (
    <Spin spinning={isLoading}>
      <div className="flex p-5 justify-center h-full">
        <div className="flex flex-col lg:flex-row w-full gap-5">
          <div className="flex flex-1 flex-col gap-9">
            {dataImageSetting?.sgg_company_logo && (
              <div className="flex ">
                <Image src={dataImageSetting?.sgg_company_logo} preview={false} width={'100%'} />
              </div>
            )}
            <div className="flex flex-1 h-full">
              <div className="flex flex-1 flex-col w-full h-full">
                <Image src={SggGoal} preview={false} width={109} height={36} />
                <div className="flex flex-col bg-white p-2 w-full flex-1 mt-2 h-[400px]">
                  <div className="px-4 py-3 bg-[#3997C8] text-white">Content</div>
                  <div className="flex flex-col overflow-y-auto">
                    {dataGoal.map((item, index) => {
                      return (
                        <div
                          key={`goal_${index}`}
                          className="flex items-center justify-between px-4 py-3 cursor-pointer"
                          onClick={() => {
                            history.push(`/global-sgg/${item.id}/preview`);
                          }}
                        >
                          <div className="line-clamp-2 flex-1 text-[20px] font-bold mr-2">
                            {item.title}
                          </div>
                          <Image src={ArrowRight} preview={false} width={6} height={12} />
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            </div>
            {dataImageSetting?.sgg_photo && (
              <div className="flex ">
                {dataImageSetting?.sgg_photo.match(/\.(mp4|webm|ogg)$/i) ? (
                  <video src={dataImageSetting?.sgg_photo} controls width="100%" height="100%" />
                ) : (
                  <Image src={dataImageSetting?.sgg_photo} preview={false} width={'100%'} />
                )}
              </div>
            )}
          </div>
          <div className="flex flex-1 flex-col gap-9">
            <div className="flex flex-1 flex-col">
              <Image src={SggInformation} preview={false} width={186} height={36} />
              <div className="flex flex-col bg-white p-2 w-full flex-1 mt-2">
                <div className="flex gap-1">
                  <div className="w-[140px] px-4 py-3 bg-[#3997C8] text-white">Date</div>
                  <div className="flex-1 px-4 py-3 bg-[#3997C8] text-white">Content</div>
                </div>
                <div className="flex flex-col overflow-y-auto max-h-[336px]">
                  {dataInformation.map((item, index) => {
                    return (
                      <div
                        key={`information_${index}`}
                        className="flex gap-1  cursor-pointer"
                        onClick={() => {
                          history.push(`/global-sgg/${item.id}/preview`);
                        }}
                      >
                        <div className="w-[140px] px-4 py-3">
                          {item.created_at ? moment(item.created_at).format('YYYY/MM/DD') : ''}
                        </div>
                        <div className="px-4 py-3 flex flex-1 justify-between items-center gap-2">
                          <div className="line-clamp-2 flex-1">{item.title}</div>
                          <Image src={ArrowRight} preview={false} width={6} height={12} />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
            <div className="flex flex-1 flex-col">
              <Image src={SggTask} preview={false} width={98} height={36} />
              <div className="flex flex-col bg-white p-2 w-full flex-1 mt-2">
                <div className="flex gap-1">
                  <div className="w-[140px] px-4 py-3 bg-[#3997C8] text-white">Date</div>
                  <div className="flex-1 px-4 py-3 bg-[#3997C8] text-white">Content</div>
                </div>
                <div className="flex flex-col overflow-y-auto max-h-[336px]">
                  {dataTask.map((item, index) => {
                    return (
                      <div
                        key={`task_${index}`}
                        className="flex gap-1 cursor-pointer"
                        onClick={() => {
                          history.push(`/global-sgg/${item.id}/preview`);
                        }}
                      >
                        <div className="w-[140px] px-4 py-3">{item.expiration_date}</div>
                        <div className="px-4 py-3 flex flex-1 justify-between items-center gap-2">
                          <div className="line-clamp-2 flex-1">{item.title}</div>
                          <Image src={ArrowRight} preview={false} width={6} height={12} />
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Spin>
  );
};
export default SGGPage;
