import { createSgg } from '@/apis/sgg';
import IconCancelRed from '@/assets/imgs/common-icons/cancel-red.svg';
import IconSave from '@/assets/imgs/common-icons/save-white-btn.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTextEditor from '@/components/Commons/BasicTextEditor';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_TITLE } from '@/constants/commonText';
import { typeSGG } from '@/constants/data';
import STATUS_CODE from '@/constants/statusCode';
import { Form, Image } from 'antd';
import { useState } from 'react';
import { history } from 'umi';

const SGGCreationPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [form] = Form.useForm();

  function isQuillEmpty(_, value) {
    if (value) {
      if (value?.replace(/<(.|\n)*?>/g, '')?.trim()?.length === 0 && !value?.includes('<img')) {
        return Promise.reject(new Error('※必須項目が未入力です。'));
      }
    }
    return Promise.resolve();
  }

  const onSave = async () => {
    try {
      setIsLoading(true);
      await form.validateFields();
      const values = form.getFieldsValue();
      const resCreate = await createSgg({
        ...values,
        expiration_date: values.expiration_date?.format('YYYY-MM-DD'),
        display_date: values.display_date?.format('YYYY-MM-DD'),
      });
      if (resCreate.status === STATUS_CODE.CREATED) {
        openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
        history.replace(`/sgg-management/${resCreate.data.data.id}/preview`);
      } else {
        openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
      }
      setIsLoading(false);
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
      setIsLoading(false);
      console.log('error', error);
    }
  };

  return (
    <div className="p-6 flex flex-col gap-5 max-w-[600px]">
      <Form form={form} component={false}>
        <Form.Item
          className="!mb-0"
          name="title"
          rules={[{ required: true, message: 'を入力してください' }]}
        >
          <BasicInput
            required
            title={TEXT_TITLE.Title}
            className="!h-10"
            placeholder={TEXT_TITLE.Title}
          />
        </Form.Item>
        <Form.Item
          className="!mb-0"
          name="type"
          rules={[{ required: true, message: 'を入力してください' }]}
          initialValue={typeSGG[1].value}
        >
          <BasicSelect title={TEXT_TITLE.Type} options={typeSGG} required={true} />
        </Form.Item>
        <Form.Item
          className="!mb-0"
          name="display_date"
          rules={[{ required: true, message: 'を入力してください' }]}
        >
          <BasicDatePicker title={TEXT_TITLE.Display_Date} className="!h-10" required={true} />
        </Form.Item>
        <Form.Item
          className="!mb-0"
          name="expiration_date"
          rules={[{ required: true, message: 'を入力してください' }]}
        >
          <BasicDatePicker title={TEXT_TITLE.Expiration_Date} className="!h-10" required={true} />
        </Form.Item>
        <Form.Item
          className="!mb-0"
          name="content"
          rules={[{ required: true, message: 'を入力してください', validator: isQuillEmpty }]}
          getValueFromEvent={(value) => value}
        >
          <BasicTextEditor
            form={form}
            name="content"
            required
            className={` [&_.ql-container]:!h-[280px] `}
            title={TEXT_TITLE.Content}
          />
        </Form.Item>
      </Form>
      <div className="flex justify-center mt-6 gap-5">
        <BasicButton
          className="w-[290px] flex items-center justify-center rounded-[4px] shadow-md !bg-white hover:opacity-40 font-medium !text-[#FF3B30] !border-[#DCDEE3]"
          onClick={() => {
            form.resetFields();
            history.replace('/sgg-management/list');
          }}
        >
          <Image preview={false} src={IconCancelRed} width={18} height={18} />
          <p className="pl-2 font-medium">{TEXT_ACTION.CLOSE}</p>
        </BasicButton>
        <BasicButton
          disabled={isLoading}
          styleType="accept"
          className={`!w-[190px] flex justify-center items-center ${
            isLoading ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
          }`}
          onClick={onSave}
        >
          <Image preview={false} src={IconSave} width={12} height={14} />
          <p>{TEXT_ACTION.SAVE}</p>
        </BasicButton>
      </div>
    </div>
  );
};
export default SGGCreationPage;
