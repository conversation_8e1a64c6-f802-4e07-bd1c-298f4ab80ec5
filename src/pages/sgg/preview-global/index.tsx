import type { ItemSgg } from '@/apis/sgg';
import { getDetailSgg } from '@/apis/sgg';
import IconBack from '@/assets/imgs/common-icons/back-btn.svg';
import BasicButton from '@/components/Commons/BasicButton';
import Exception from '@/components/Exception';
import { TEXT_ACTION } from '@/constants/commonText';
import { Image, Spin } from 'antd';
import { useEffect, useState } from 'react';
import { history, Link, useParams } from 'umi';

const SGGPage: React.FC = () => {
  const paramUrl = useParams();
  const sggId = (paramUrl as { id: string })?.id;
  const [data, setData] = useState<ItemSgg>();
  const [loadingPage, setLoadingPage] = useState<boolean>(false);

  const onFetchData = async (id) => {
    setLoadingPage(true);
    const res = await getDetailSgg(id);
    if (res?.data?.data) {
      setData(res?.data?.data);
    } else {
      setData(null);
    }
    setLoadingPage(false);
  };

  useEffect(() => {
    if (sggId) {
      onFetchData(sggId);
    }
  }, [sggId]);

  return (
    <>
      <Spin spinning={loadingPage}>
        {data === null ? (
          <Exception type="404" style={{ minHeight: 500, height: '100%' }} linkElement={Link} />
        ) : (
          <div className="px-9 py-8 flex flex-col gap-5">
            <div className="flex justify-between items-center gap-10">
              <p className="text-[24px] font-bold text-[#363840] flex-1">{data?.title}</p>
              <p className="text-[16px] font-medium text-[#7F8493]">{data?.display_date}</p>
            </div>
            <div className="!bg-white p-6 flex-1 flex min-h-[200px]">
              <div dangerouslySetInnerHTML={{ __html: data?.content }} />
            </div>
            <div className="flex justify-center">
              <BasicButton
                styleType="noneOutLine"
                disabled={loadingPage}
                className="!w-[400px] !bg-white flex justify-center items-center"
                onClick={() => {
                  history.goBack();
                }}
              >
                <Image preview={false} src={IconBack} width={8} />
                <p className="ml-2 !text-[#363840]">{TEXT_ACTION.RETURN}</p>
              </BasicButton>
            </div>
          </div>
        )}
      </Spin>
    </>
  );
};
export default SGGPage;
