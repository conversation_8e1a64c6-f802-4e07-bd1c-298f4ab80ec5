import { PlanCreateProvider } from '@/providers';
import React from 'react';
import { useParams } from 'react-router';
import PlanForDayTabs from './components/PlanForDayTabs';
import ItineraryEstimateContent from './components/ItineraryEstimateContent';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

const ItineraryEstimateCreation = () => {
  const { plan_id } = useParams<{ plan_id: string }>();
  const deviceType = useDeviceType();

  console.log('plan_id 123', plan_id)

  return (
    <PlanCreateProvider planId={plan_id}>
      <div
        className={`${
          deviceType === DeviceTypeEnum.DESKTOP ? 'flex' : 'flex-col'
        } itinerary-container overflow-x-auto`}
        style={{
          height: 'calc(100vh - 90px)',
        }}
      >
        <div
          className="flex flex-col w-[320px] bg-white px-9 pt-6 pb-9"
          style={{
            borderRadius: deviceType !== DeviceTypeEnum.DESKTOP ? '12px' : undefined,
            height: deviceType !== DeviceTypeEnum.DESKTOP ? '500px' : undefined,
            width: deviceType !== DeviceTypeEnum.DESKTOP ? 'auto' : undefined,
            margin: deviceType !== DeviceTypeEnum.DESKTOP ? '36px 36px 0' : undefined,
          }}
        >
          <PlanForDayTabs />
        </div>
        <div className="m-9 flex-1">
          <ItineraryEstimateContent />
        </div>
      </div>
    </PlanCreateProvider>
  );
};

export default ItineraryEstimateCreation;
