import type {
  EstimatePriceItem,
  EstimatePriceResponse,
  PayloadUpdateEstimatePrice,
} from '@/apis/itineraries/estimate-price';
import BasicTable from '@/components/Commons/BasicTable';
import { formatMoney } from '@/utils';
import { Button, Image, Table, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import IconEdit from '@/assets/imgs/common-icons/edit-blue.svg';
import moment from 'moment';
import {
  arrayMove,
  SortableContext,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import type { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import type { EstimatePriceDetailRef } from './ItineraryEstimateDetail';
import ItineraryEstimateDetail from './ItineraryEstimateDetail';
import { HolderOutlined } from '@ant-design/icons';
import type { DragEndEvent } from '@dnd-kit/core';
import { DndContext } from '@dnd-kit/core';
import { TagCategory } from '@/components/Commons/TagCategory';
import type { InitialType } from 'use-url-search-params';
import BasicPagination from '@/components/Commons/BasicPagination';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: SyntheticListenerMap;
}

const RowContext = React.createContext<RowContextProps>({});

const DragHandle: React.FC = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <Button
      type="text"
      size="small"
      icon={<HolderOutlined />}
      style={{ cursor: 'move' }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

const Row: React.FC<RowProps> = (props) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    setActivatorNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: props['data-row-key'] });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
  };

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners],
  );

  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};

interface Props {
  data: EstimatePriceResponse;
  refetch: () => void;
  setDataPayloadSorted: React.Dispatch<React.SetStateAction<PayloadUpdateEstimatePrice[]>>;
  setParameter: (nextQuery: InitialType) => void;
  parameter: InitialType;
  total: number;
}

const ItineraryEstimateTable = ({
  data,
  refetch,
  setDataPayloadSorted,
  parameter,
  setParameter,
  total,
}: Props) => {
  const [dataItemSelected, setDataItemSelected] = useState<EstimatePriceItem>();
  const [dataEstimatePriceItem, setDataEstimatePriceItem] = useState<EstimatePriceItem[]>([]);

  const refEstimatePricePlan = useRef<EstimatePriceDetailRef>(null);
  const deviceType = useDeviceType();

  const heightScreen = window.innerHeight;

  useEffect(() => {
    setDataEstimatePriceItem(data?.plan_items?.data ?? []);
  }, [data]);

  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active?.id !== over?.id) {
      const prevArr = [...dataEstimatePriceItem];
      const activeIndex = prevArr?.findIndex((record) => record?.id === active?.id);
      const overIndex = prevArr?.findIndex((record) => record?.id === over?.id);

      const newArr = arrayMove(prevArr, activeIndex, overIndex);

      setDataEstimatePriceItem(newArr);

      const sortingPayload = newArr?.map((item, index) => ({
        plan_item_id: item?.id,
        sort_order: index,
        options: {
          additional_data: item?.additional_data,
          available_data: item?.available_data,
        },
      }));

      setDataPayloadSorted(sortingPayload);
    }
  };

  const columns: ColumnsType<EstimatePriceItem> = [
    {
      title: null,
      key: 'id',
      dataIndex: 'id',
      align: 'center',
      width: 60,
      render: () => <DragHandle />,
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">仕入・素材</div>,
      dataIndex: 'title',
      key: 'title',
      width: '20%',
      render: (value, { title }) => (
        <Tooltip title={title}>
          <div className="truncate">{title}</div>
        </Tooltip>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">カテゴリー</div>,
      dataIndex: 'reference_type',
      key: 'reference_type',
      width: 140,
      render: (value, { reference_type }) => (
        <div className="truncate">{TagCategory(reference_type)}</div>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">利用予定日</div>,
      dataIndex: 'date',
      key: 'date',
      width: 140,
      render: (value, { date }) => (
        <div className="truncate">{date ? moment(date).format('YYYY/MM/DD') : '-'}</div>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">売上金額</div>,
      dataIndex: 'sales_amount',
      key: 'sales_amount',
      width: 140,
      render: (value, { sales_amount }) => (
        <div className="text-right">{formatMoney(sales_amount)}</div>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">仕入金額</div>,
      dataIndex: 'purchase_amount',
      key: 'purchase_amount',
      width: 140,
      render: (value, { purchase_amount }) => (
        <div className="text-right">{formatMoney(purchase_amount)}</div>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">粗利率</div>,
      dataIndex: 'profit_percent',
      key: 'profit_percent',
      width: 140,
      render: (value, { profit_percent }) => <div className="text-right">{profit_percent}%</div>,
    },
    {
      title: null,
      dataIndex: 'edit',
      key: 'edit',
      width: deviceType === DeviceTypeEnum.DESKTOP ? 140 : 60,
      render: (value, record) => (
        <div
          className="flex items-center justify-center text-[#225DE0] gap-x-1"
          onClick={() => {
            setDataItemSelected(record);
            refEstimatePricePlan?.current?.onShow();
          }}
        >
          <Image preview={false} src={IconEdit} width={16} height={16} />
          {deviceType === DeviceTypeEnum.DESKTOP ? '料金を入力' : null}
        </div>
      ),
    },
  ];

  return (
    <div className="[&_.ant-table_tfoot_>_tr_>_td]:border-0">
      <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
        <SortableContext
          items={dataEstimatePriceItem?.map((i) => i?.id)}
          strategy={verticalListSortingStrategy}
        >
          <BasicTable
            className={`!mt-0 h-full`}
            hasPagination={false}
            tableProps={{
              scroll: { y: heightScreen - 470, x: 1000 },
              columns,
              size: deviceType === DeviceTypeEnum?.MOBILE ? 'small' : 'middle',
              dataSource: dataEstimatePriceItem,
              pagination: false,
              components: { body: { row: Row } },
              rowKey: 'id',
              summary: () => {
                return (
                  <>
                    <Table.Summary.Row>
                      <Table.Summary.Cell index={1} colSpan={3} />
                      <Table.Summary.Cell index={2} className="bg-[#EFF0F2] text-base leading-6">
                        合計
                      </Table.Summary.Cell>
                      <Table.Summary.Cell
                        index={3}
                        className="bg-[#EFF0F2] text-right text-base leading-6"
                      >
                        {formatMoney(data?.total_sales_amount)}
                      </Table.Summary.Cell>
                      <Table.Summary.Cell
                        index={4}
                        className="bg-[#EFF0F2] text-right text-base leading-6"
                      >
                        {formatMoney(data?.total_purchase_amount)}
                      </Table.Summary.Cell>
                      <Table.Summary.Cell
                        index={5}
                        className="bg-[#EFF0F2] text-right text-base leading-6"
                      >
                        {data?.total_profit_percent}%
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={6} colSpan={1} />
                    </Table.Summary.Row>
                  </>
                );
              },
            }}
            page={0}
            pageSize={0}
            onChangePage={null}
            total={0}
            onSelectPageSize={null}
          />
        </SortableContext>
      </DndContext>
      <div className="flex items-center justify-center my-4">
        <BasicPagination
          onSelectPageSize={(s) => setParameter({ ...parameter, limit: s, page: 1 })}
          pageSize={Number(parameter?.limit)}
          page={Number(parameter?.page)}
          total={total}
          onChange={(p) => setParameter({ ...parameter, page: p })}
        />
      </div>
      <ItineraryEstimateDetail
        ref={refEstimatePricePlan}
        refetch={refetch}
        // onSubmitSuccess={onSubmitEstimateTabSuccess}
        dataDetail={dataItemSelected}
      />
    </div>
  );
};

export default ItineraryEstimateTable;
