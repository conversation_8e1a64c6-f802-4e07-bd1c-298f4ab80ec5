import BasicSelect from '@/components/Commons/BasicSelect';
import ItineraryEstimateTable from './ItineraryEstimateTable';
import CancelXSVG from '@/components/SVG/CancelXSVG';
import { history, useParams } from 'umi';
import SaveSvg from '@/components/SVG/SaveSvg';
import { useEffect, useState, useContext, useRef } from 'react';
import STATUS_CODE from '@/constants/statusCode';
import type {
  EstimatePriceResponse,
  PayloadUpdateEstimatePrice,
} from '@/apis/itineraries/estimate-price';
import { ExportExeclEstimateTypeEnum } from '@/apis/itineraries/estimate-price';
import {
  ExportExcelEstimate,
  getListEstimatePrice,
  saveEstimatePrice,
} from '@/apis/itineraries/estimate-price';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { ITEM_PER_PAGE } from '@/utils/constants';
import { useUrlSearchParams } from 'use-url-search-params';
import type { MenuProps } from 'antd';
import { Dropdown } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import type { PlanCreateContextProps } from '@/providers';
import { PlanCreateContext } from '@/providers';

const filterListingOptions = [
  {
    label: '日付',
    value: 'date',
  },
  {
    label: 'カテゴリー',
    value: 'category',
  },
];

const inittialParameters = {
  page: '1',
  limit: ITEM_PER_PAGE,
};

const ItineraryEstimateContent = () => {
  const { activePlanTab, planTabs } = useContext<PlanCreateContextProps>(PlanCreateContext);
  const { id, plan_id } = useParams<{ id: string; plan_id: string }>();
  const [dataSource, setDataSource] = useState<EstimatePriceResponse>();
  const [paramFilter, setParamFilter] = useState();
  const [parameter, setParameter] = useUrlSearchParams(inittialParameters);
  const [total, setTotal] = useState<number>(0);
  const [dateSelected, setDateSelected] = useState<string>('');
  const [dataPayloadSorted, setDataPayloadSorted] = useState<PayloadUpdateEstimatePrice[]>([]);

  const cacheActivePlanTab = useRef<number>(activePlanTab);

  const deviceType = useDeviceType();

  const fetchData = async () => {
    try {
      const defaultSorting = { sort: 'sort_order', order: 'asc' };
      const params = paramFilter ? { sort: paramFilter } : defaultSorting;

      const payload = {
        ...parameter,
        ...params,
        date: dateSelected,
      };

      const { data, status } = await getListEstimatePrice(Number(plan_id), payload);
      if (status === STATUS_CODE.SUCCESSFUL) {
        setDataSource(data?.data);
        setTotal(data.data.plan_items?.total);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (planTabs && activePlanTab) {
      setDateSelected(planTabs?.find((item) => item?.day === activePlanTab)?.date);
    }
  }, [planTabs, activePlanTab]);

  useEffect(() => {
    if (
      plan_id &&
      planTabs.length > 0 &&
      !!dateSelected &&
      cacheActivePlanTab.current !== activePlanTab
    ) {
      
      fetchData();
    }
  }, [plan_id, paramFilter, parameter, activePlanTab, planTabs, dateSelected]);

  useEffect(() => {
    const sortingPayload = dataSource?.plan_items?.data?.map((item, index) => ({
      plan_item_id: item?.id,
      sort_order: index,
      options: {
        additional_data: item?.additional_data,
        available_data: item?.available_data,
      },
    }));

    setDataPayloadSorted(sortingPayload);
  }, [dataSource]);

  const handleSubmitSaveItem = async () => {
    const { status } = await saveEstimatePrice(Number(plan_id), dataPayloadSorted);
    if (status === STATUS_CODE?.SUCCESSFUL) {
      openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
      fetchData();
    } else {
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
    }
  };
  const handleExportExcelEstimate = async (type: ExportExeclEstimateTypeEnum) => {
    try {
      const { data, status } = await ExportExcelEstimate(Number(plan_id), type);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const urlCsv = data?.data?.file_url;
        if (urlCsv) {
          const a = document.createElement('a');
          a.href = urlCsv;
          a.download = 'csv_見積書作成.csv';
          a.click();
          window.URL.revokeObjectURL(urlCsv);
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  const items: MenuProps['items'] = [
    {
      key: '1',
      label: (
        <div
          className="hover:opacity-90 text-center text-[#3997C8]"
          onClick={() => handleExportExcelEstimate(ExportExeclEstimateTypeEnum.Individual)}
        >
          個別
        </div>
      ),
    },
    {
      key: '2',
      label: (
        <div
          className="hover:opacity-90 text-center text-[#3997C8]"
          onClick={() => handleExportExcelEstimate(ExportExeclEstimateTypeEnum.Inclusive)}
        >
          包括
        </div>
      ),
    },
    {
      key: '3',
      label: (
        <div
          className="hover:opacity-90 text-center text-[#3997C8]"
          onClick={() => handleExportExcelEstimate(ExportExeclEstimateTypeEnum.Domestic)}
        >
          国内
        </div>
      ),
    },
  ];

  return (
    <div className="h-full flex flex-col">
      <div className="bg-white rounded-xl px-2 pb-3 pt-4 flex flex-col justify-end items-end flex-1">
        <div className="px-4 mb-4">
          <div className="mb-1">並び替え</div>
          <BasicSelect
            value={paramFilter}
            allowClear
            options={filterListingOptions}
            className="w-[140px] [&_.ant-select-selector]:!h-9"
            onChange={(value) => setParamFilter(value)}
          />
        </div>
        <div className="h-full w-full">
          <ItineraryEstimateTable
            data={dataSource}
            refetch={fetchData}
            setDataPayloadSorted={setDataPayloadSorted}
            parameter={parameter}
            setParameter={setParameter}
            total={total}
          />
        </div>
      </div>
      <div
        className={`flex ${
          deviceType === DeviceTypeEnum.MOBILE
            ? 'flex-col gap-y-[8px]'
            : 'items-center justify-center gap-x-[20px]'
        } mt-6`}
      >
        <div
          style={{
            width: deviceType === DeviceTypeEnum.MOBILE ? '100%' : '162px',
          }}
          className="h-[40px] gap-x-2 border rounded border-[#DCDEE3] hover:opacity-90 bg-[#FFF] flex items-center justify-center text-[#FF3B30] cursor-pointer"
          onClick={() => history.push(`/itinerary-management/${id}`)}
        >
          <CancelXSVG /> キャンセル
        </div>
        <Dropdown menu={{ items }} placement="bottomRight">
          <div
            style={{
              width: deviceType === DeviceTypeEnum.MOBILE ? '100%' : '187px',
            }}
            className="h-[40px] gap-x-2 border rounded bg-main-color hover:opacity-90 flex items-center justify-center text-[#FFF] cursor-pointer"
            onClick={() => {}}
          >
            {'見積書出力（EXCEL)'} <DownOutlined />
          </div>
        </Dropdown>
        <div
          style={{
            width: deviceType === DeviceTypeEnum.MOBILE ? '100%' : '162px',
          }}
          className="h-[40px] gap-x-2 border rounded bg-main-color hover:opacity-90 flex items-center justify-center text-[#FFF] cursor-pointer"
          onClick={handleSubmitSaveItem}
        >
          <SaveSvg /> 保存
        </div>
      </div>
    </div>
  );
};

export default ItineraryEstimateContent;
