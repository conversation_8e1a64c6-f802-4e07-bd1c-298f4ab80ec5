import {
  filterFeeTariffByColumn,
  type EstimateDataType,
  type EstimatePriceItem,
} from '@/apis/itineraries/estimate-price';
import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import type { EstimateItemRef } from '../ItineraryEstimateDetail';
import { generateRandomId } from '@/utils';
import BasicButton from '@/components/Commons/BasicButton';
import TrashSVGV2 from '@/components/SVG/TrashSVGV2';
import { Form, type TableColumnsType } from 'antd';
import BasicTable from '@/components/Commons/BasicTable';
import EditableCell from '@/components/Commons/EditableCell';
import AddSVG from '@/components/SVG/AddSVG';
import { rules } from '@/constants/rules';
import type { SelectItemType } from '@/@types/common';
import { EntityEnum, getListTariffByEntity } from '@/apis/purchase-and-material-management/tariff';
import STATUS_CODE from '@/constants/statusCode';
import { debounce } from 'lodash';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

interface Props {
  detail: EstimatePriceItem;
}

const optionsType: SelectItemType[] = [
  {
    label: '大人',
    value: 'travel_spot_fee_adult',
  },
  {
    label: '子供',
    value: 'travel_spot_fee_child',
  },
  {
    label: '幼児',
    value: 'travel_spot_fee_infant',
  },
];

const EstimateTravelSpot = ({ detail }: Props, ref) => {
  const [formEstimate] = Form.useForm();
  const [formAdditional] = Form.useForm();
  const [dataSource, setDataSource] = useState<EstimateDataType[]>(detail?.available_data ?? []);
  const [dataSourceAdditional, setDataSourceAdditional] = useState<EstimateDataType[]>(
    detail?.additional_data ?? [],
  );
  const [optionsListPriceByEntity, setOptionsListPriceByEntity] = useState<SelectItemType[]>([]);
  const deviceType = useDeviceType();

  useEffect(() => {
    setDataSource(detail?.available_data);
    setDataSourceAdditional(detail?.additional_data);
  }, [detail]);

  const getOptionBtEntity = async () => {
    if (detail?.id) {
      const { data, status } = await getListTariffByEntity(
        { page: 1, limit: 9999, type: 'price', entity_id: detail?.reference_id },
        EntityEnum[detail?.reference_type],
      );

      if (status === STATUS_CODE.SUCCESSFUL) {
        const dts = data.data?.map((item) => ({
          label: item.title,
          value: item.id?.toString(),
        }));
        setOptionsListPriceByEntity(dts);
      }
    }
  };

  useEffect(() => {
    getOptionBtEntity();
  }, [detail]);

  // handle estimate price
  const handleDeleteRowItemEstimate = (id: string | number) => {
    const cloneData = [...dataSource];

    const index = cloneData?.findIndex((item) => item.key === id);
    if (index !== -1) {
      cloneData.splice(index, 1);
    }
    setDataSource(cloneData);
  };

  const handleAddNewEstimate = () => {
    const newItem: EstimateDataType = {
      title: undefined,
      type: undefined,
      sales_amount: 0,
      purchase_amount: 0,
      quantity: 0,
      id: generateRandomId(),
      key: generateRandomId(),
    };

    const cloneNewDataSource = [...dataSource, newItem];

    setDataSource(cloneNewDataSource);
  };

  // handle additional
  const handleDeleteRowItemAdditional = (id: string | number) => {
    const cloneData = [...dataSourceAdditional];

    const index = cloneData?.findIndex((item) => item.key === id);
    if (index !== -1) {
      cloneData.splice(index, 1);
    }
    setDataSourceAdditional(cloneData);
  };

  const handleAddNewAdditional = () => {
    const newItemAdditional: EstimateDataType = {
      title: undefined,
      sales_amount: 0,
      purchase_amount: 0,
      quantity: 0,
      id: generateRandomId(),
      key: generateRandomId(),
    };

    const cloneNewDataSourceAdditional = [...dataSourceAdditional, newItemAdditional];

    setDataSourceAdditional(cloneNewDataSourceAdditional);
  };

  const columns = [
    {
      title: 'タイトル',
      dataIndex: 'tariff_id',
      key: 'tariff_id',
      editable: true,
      width: deviceType === DeviceTypeEnum.MOBILE ? '164px' : '30%',
      formType: 'select',
      options: optionsListPriceByEntity ?? [],
      ruleFormItem: rules.requiredSelect,
    },
    {
      title: <div>分類</div>,
      dataIndex: 'tariff_type',
      key: 'tariff_type',
      editable: true,
      width: deviceType === DeviceTypeEnum.MOBILE ? '164px' : '15%',
      formType: 'select',
      options: optionsType,
      ruleFormItem: rules.requiredSelect,
    },
    {
      title: <div>売上金額</div>,
      dataIndex: 'sales_amount',
      editable: true,
      width: deviceType === DeviceTypeEnum.MOBILE ? '164px' : '15%',
      formType: 'inputNumber',
      inputProps: { isRightAlign: true, isNumber: true },
      ruleFormItem: rules.requiredInput,
    },
    {
      title: <div>仕入金額</div>,
      dataIndex: 'purchase_amount',
      editable: true,
      width: deviceType === DeviceTypeEnum.MOBILE ? '164px' : '15%',
      formType: 'inputNumber',
      inputProps: { isRightAlign: true, isNumber: true },
      ruleFormItem: rules.requiredInput,
    },
    {
      title: <div>人数/個数/台数</div>,
      dataIndex: 'quantity',
      editable: true,
      width: deviceType === DeviceTypeEnum.MOBILE ? '164px' : '15%',
      formType: 'inputNumber',
      inputProps: { isRightAlign: true, isNumber: true },
      ruleFormItem: rules.requiredInput,
    },
    {
      title: <div />,
      width: deviceType === DeviceTypeEnum.MOBILE ? '60px' : '10%',
      render: (_, { key }) => (
        <div className="flex items-center justify-center">
          <BasicButton
            onClick={() => handleDeleteRowItemEstimate(key)}
            styleType="danger"
            className="flex items-center space-x-[8px]"
            icon={<TrashSVGV2 styleType="white" width={16} height={16} />}
          >
            {deviceType === DeviceTypeEnum.DESKTOP ? '削除' : null}
          </BasicButton>
        </div>
      ),
    },
  ];

  const columnAdditionals = [
    {
      title: 'オプション',
      dataIndex: 'title',
      editable: true,
      width: deviceType === DeviceTypeEnum.MOBILE ? '164px' : '30%',
      formType: 'input',
      maxLength: 155,
      ruleFormItem: rules.requiredInput,
    },
    {
      title: <div>売上金額</div>,
      dataIndex: 'sales_amount',
      editable: true,
      width: deviceType === DeviceTypeEnum.MOBILE ? '164px' : '15%',
      formType: 'inputNumber',
      inputProps: { isRightAlign: true, isNumber: true },
      ruleFormItem: rules.requiredInput,
    },
    {
      title: <div>仕入金額</div>,
      dataIndex: 'purchase_amount',
      editable: true,
      width: deviceType === DeviceTypeEnum.MOBILE ? '164px' : '15%',
      formType: 'inputNumber',
      inputProps: { isRightAlign: true, isNumber: true },
      ruleFormItem: rules.requiredInput,
    },
    {
      title: <div>人数/個数/台数</div>,
      dataIndex: 'quantity',
      editable: true,
      width: deviceType === DeviceTypeEnum.MOBILE ? '164px' : '15%',
      formType: 'inputNumber',
      inputProps: { isRightAlign: true, isNumber: true },
      ruleFormItem: rules.requiredInput,
    },
    {
      title: <div />,
      width: deviceType === DeviceTypeEnum.MOBILE ? '60px' : '10%',
      render: (_, { key }) => (
        <div className="flex items-center justify-center">
          <BasicButton
            onClick={() => handleDeleteRowItemAdditional(key)}
            styleType="danger"
            className="flex items-center space-x-[8px]"
            icon={<TrashSVGV2 styleType="white" width={16} height={16} />}
          >
            {deviceType === DeviceTypeEnum.DESKTOP ? '削除' : null}
          </BasicButton>
        </div>
      ),
    },
  ];

  const editableColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }

    return {
      ...col,
      onCell: (record: Record<string, any>) => ({
        record,
        editable: col?.editable,
        isEditPage: true,
        dataIndex: col?.dataIndex,
        title: col.title,
        formType: col.formType,
        inputProps: col.inputProps,
        form: formEstimate,
        ruleFormItem: col?.ruleFormItem,
        options: col?.options,
      }),
    };
  });

  const editableColumnAdditionals = columnAdditionals.map((col) => {
    if (!col.editable) {
      return col;
    }

    return {
      ...col,
      onCell: (record: Record<string, any>) => ({
        record,
        editable: col?.editable,
        isEditPage: true,
        dataIndex: col?.dataIndex,
        title: col.title,
        formType: col.formType,
        inputProps: col.inputProps,
        form: formAdditional,
        ruleFormItem: col?.ruleFormItem,
        maxLength: col.maxLength,
      }),
    };
  });

  useImperativeHandle(ref, () => ({
    formEstimate: formEstimate,
    formAdditional: formAdditional,
  }));

  useEffect(() => {
    if (detail?.id) {
      //set field value
      detail?.available_data?.forEach((item) => {
        Object.keys(item).forEach((key) => {
          const keyForm = `${item.key}.${key}`;
          formEstimate.setFieldValue(keyForm, item[key]?.toString());
        });
      });

      detail?.additional_data?.forEach((item) => {
        Object.keys(item).forEach((key) => {
          const keyForm = `${item.key}.${key}`;
          formAdditional.setFieldValue(keyForm, item[key]?.toString());
        });
      });
    }
  }, [detail?.id]);

  const handleFieldsChange = debounce((changedFields, allFields) => {
    changedFields.forEach((changedField) => {
      const fieldName = changedField.name[0];
      const [rowId, column] = fieldName.split('.');

      if (column === 'title') {
        const newTitleValue = changedField.value;

        const allTitles = formAdditional.getFieldsValue(
          allFields
            .filter((field) => field.name[0].endsWith('.title'))
            .map((field) => field.name[0]),
        );

        const isDuplicate = Object.entries(allTitles).some(
          ([key, value]) => key !== fieldName && value === newTitleValue,
        );

        if (isDuplicate) {
          formAdditional.setFields([
            {
              name: changedField.name,
              errors: ['※このオプションが既に存在します。'],
            },
          ]);
        } else {
          formAdditional.setFields([
            {
              name: changedField.name,
              errors: [],
            },
          ]);
        }
      }
    });
  }, 300);

  return (
    <div className="[&_.ant-form-item-explain-error]:!text-[10px]">
      <div className="flex justify-between items-center">
        <div className="text-xl leading-[30px] font-bold text-[#414244] line-clamp-1">
          観光地 - {detail.title}
        </div>
        <BasicButton styleType="accept" onClick={handleAddNewEstimate}>
          <AddSVG color="white" /> 料金追加
        </BasicButton>
      </div>
      <div className="mb-5">
        <Form
          form={formEstimate}
          component={false}
          onValuesChange={async (changedValues, values) => {
            const nameKeyChanged = Object.keys(changedValues)?.[0];
            const [keyChanged, property] = nameKeyChanged.split('.');

            const result = Object.entries(values).reduce((acc, [key, value]) => {
              const [prefix] = key.split('.');
              if (!acc[prefix]) {
                acc[prefix] = {};
              }
              acc[prefix][key] = value;
              return acc;
            }, {});

            const arrayResult = Object.values(result);

            const rowSelected = arrayResult.find(
              (item) => Object.keys(item)?.[0]?.split('.')?.[0] === keyChanged,
            );
            if (
              keyChanged &&
              (nameKeyChanged === `${keyChanged}.tariff_id` ||
                nameKeyChanged === `${keyChanged}.tariff_type`)
            ) {
              const { data } = await filterFeeTariffByColumn(
                rowSelected[`${keyChanged}.tariff_id`],
                rowSelected[`${keyChanged}.tariff_type`],
              );

              const init_sales_amount_by_tariff_type =
                data?.data?.[rowSelected[`${keyChanged}.tariff_type`]];
              if (init_sales_amount_by_tariff_type) {
                formEstimate.setFieldValue(
                  `${keyChanged}.sales_amount`,
                  init_sales_amount_by_tariff_type?.toString(),
                );
              }
            }
          }}
        >
          <BasicTable
            hasPagination={false}
            tableProps={{
              scroll: { x: 600, y: 260 },
              rowKey: 'id',
              size: deviceType !== DeviceTypeEnum.DESKTOP ? 'small' : 'middle',
              components: { body: { cell: EditableCell } },
              columns: editableColumns as TableColumnsType<any>,
              dataSource: dataSource,
              pagination: false,
            }}
            total={0}
            onChangePage={null}
            page={0}
            pageSize={0}
          />
        </Form>
      </div>
      <div className="flex items-center justify-end">
        <BasicButton onClick={handleAddNewAdditional} styleType="accept" className="">
          <AddSVG color="white" />
          <span>オプション追加</span>
        </BasicButton>
      </div>
      <div className="">
        <Form form={formAdditional} component={false} onFieldsChange={handleFieldsChange}>
          <BasicTable
            hasPagination={false}
            tableProps={{
              scroll: { x: 600, y: 260 },
              rowKey: 'id',
              size: deviceType !== DeviceTypeEnum.DESKTOP ? 'small' : 'middle',
              components: { body: { cell: EditableCell } },
              columns: editableColumnAdditionals as TableColumnsType<any>,
              dataSource: dataSourceAdditional,
              pagination: false,
            }}
            total={0}
            onChangePage={null}
            page={0}
            pageSize={0}
          />
        </Form>
      </div>
    </div>
  );
};

export default forwardRef<EstimateItemRef, Props>(EstimateTravelSpot);
