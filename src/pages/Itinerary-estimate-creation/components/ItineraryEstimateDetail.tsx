import { saveEstimatePrice, type EstimatePriceItem } from '@/apis/itineraries/estimate-price';
import { EntityEnum } from '@/apis/purchase-and-material-management/tariff';
import { Drawer, type FormInstance } from 'antd';
import React, {
  forwardRef,
  useEffect,
  useImperativeHandle,
  useMemo,
  useRef,
  useState,
} from 'react';
import EstimateTravelSpot from './EstimateEntityComponent/EstimateTravelSpot';
import EstimateHotel from './EstimateEntityComponent/EstimateHotel';
import EstimateRestaurant from './EstimateEntityComponent/EstimateRestaurant';
import EstimateBus from './EstimateEntityComponent/EstimateBus';
import EstimateHireCar from './EstimateEntityComponent/EstimateHireCar';
import EstimateBulletTrain from './EstimateEntityComponent/EstimateBulletTrain';
import EstimateAirplane from './EstimateEntityComponent/EstimateAirplane';
import EstimateShip from './EstimateEntityComponent/EstimateShip';
import EstimateDelivery from './EstimateEntityComponent/EstimateDelivery';
import EstimateGuide from './EstimateEntityComponent/EstimateGuide';
import EstimateServiceOther from './EstimateEntityComponent/EstimateServiceOther';
import CancelXSVG from '@/components/SVG/CancelXSVG';
import SaveSvg from '@/components/SVG/SaveSvg';
import { generateRandomId } from '@/utils';
import { useParams } from 'react-router';
import STATUS_CODE from '@/constants/statusCode';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

export type EstimatePriceDetailRef = {
  onShow: () => void;
  close: () => void;
};

interface PricePlanDetailType {
  dataDetail: EstimatePriceItem;
  // onSubmitSuccess?: (tabData: any) => void;
  refetch?: () => void;
}

export type EstimateItemRef = {
  formEstimate?: FormInstance<any>;
  formAdditional?: FormInstance<any>;
  getFormData?: any;
};

const ItineraryEstimateDetail = ({ dataDetail, refetch }: PricePlanDetailType, ref) => {
  const { plan_id } = useParams<{ plan_id: string }>();
  const [openDetail, setOpenDetail] = useState<boolean>(false);

  const refEstimatePriceItem = useRef<EstimateItemRef>(null);

  const [detail, setDetail] = useState<EstimatePriceItem>();
  const deviceType = useDeviceType();

  useEffect(() => {
    const newAvailableData = dataDetail?.available_data?.map((item, index) => ({
      ...item,
      key: `${index}${generateRandomId()}`,
    }));
    const newAdditionalData = dataDetail?.additional_data?.map((item, index) => ({
      ...item,
      key: `${index}${generateRandomId()}`,
    }));

    const newDataDetail = {
      ...dataDetail,
      available_data: newAvailableData,
      additional_data: newAdditionalData,
    };
    setDetail(newDataDetail);
  }, [dataDetail]);

  const close = () => {
    setOpenDetail(false);
    // setDetail(undefined);
  };

  const onShow = async () => {
    setOpenDetail(true);
  };

  useImperativeHandle(ref, () => ({
    close,
    onShow,
  }));

  const RenderForm = useMemo(() => {
    switch (detail?.reference_type) {
      case EntityEnum['travel-spot']:
        return (
          <>
            <EstimateTravelSpot detail={detail} ref={refEstimatePriceItem} />
          </>
        );
      case EntityEnum.hotel:
        return (
          <>
            <EstimateHotel detail={detail} ref={refEstimatePriceItem} />
          </>
        );
      case EntityEnum.restaurant:
        return (
          <>
            <EstimateRestaurant detail={detail} ref={refEstimatePriceItem} />
          </>
        );
      case EntityEnum.bus:
        return (
          <>
            <EstimateBus detail={detail} ref={refEstimatePriceItem} />
          </>
        );
      case EntityEnum['hire-car']:
        return (
          <>
            <EstimateHireCar detail={detail} ref={refEstimatePriceItem} />
          </>
        );
      case EntityEnum['bullet-train']:
        return (
          <>
            <EstimateBulletTrain detail={detail} ref={refEstimatePriceItem} />
          </>
        );
      case EntityEnum.airplane:
        return (
          <>
            <EstimateAirplane detail={detail} ref={refEstimatePriceItem} />
          </>
        );
      case EntityEnum.ship:
        return (
          <>
            <EstimateShip detail={detail} ref={refEstimatePriceItem} />
          </>
        );
      case EntityEnum.delivery:
        return (
          <>
            <EstimateDelivery detail={detail} ref={refEstimatePriceItem} />
          </>
        );
      case EntityEnum.guide:
        return (
          <>
            <EstimateGuide detail={detail} ref={refEstimatePriceItem} />
          </>
        );
      case EntityEnum['service-other']:
        return (
          <>
            <EstimateServiceOther detail={detail} ref={refEstimatePriceItem} />
          </>
        );
      default:
        // Handle default case for any unmatched types
        break;
    }
  }, [detail?.id]);

  const handleSubmitForm = async () => {
    const valuesEstimate = await refEstimatePriceItem.current?.formEstimate?.validateFields();
    const valuesAdditional = await refEstimatePriceItem.current?.formAdditional?.validateFields();

    const resultObjEstimate = Object.keys(valuesEstimate).reduce((acc, key) => {
      const [index, property] = key.split('.');
      const value = valuesEstimate[key];

      if (!acc[index]) {
        acc[index] = {};
      }

      acc[index][property] = value;

      return acc;
    }, {});

    const resultAdditional = Object.keys(valuesAdditional).reduce((acc, key) => {
      const [index, property] = key.split('.');
      const value = valuesAdditional[key];

      if (!acc[index]) {
        acc[index] = {};
      }

      acc[index][property] = value;

      return acc;
    }, {});

    const payload = [
      {
        plan_item_id: detail?.id,
        sort_order: detail?.sort_order,
        options: {
          available_data: Object.values(resultObjEstimate),
          additional_data: Object.values(resultAdditional),
        },
      },
    ];

    if (payload) {
      const { status } = await saveEstimatePrice(Number(plan_id), payload);
      if (status === STATUS_CODE?.SUCCESSFUL) {
        refetch();
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        close();
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      }
    }
  };

  return (
    <Drawer
      width={deviceType === DeviceTypeEnum.MOBILE ? '100%' : '70%'}
      headerStyle={{ display: 'none' }}
      closeIcon={null}
      onClose={close}
      open={openDetail}
    >
      <div className="h-full flex flex-col overflow-auto pr-3">
        <div className="mt-[10px] flex-1 flex flex-col justify-center">
          <div className="flex-1">{RenderForm}</div>
          <div className="flex items-center justify-center gap-x-[20px] mt-6">
            <div
              className="h-[40px] w-[162px] gap-x-2 border rounded border-[#DCDEE3] bg-[#FFF] flex items-center justify-center text-[#FF3B30] cursor-pointer"
              onClick={close}
            >
              <CancelXSVG /> キャンセル
            </div>
            <div
              className="h-[40px] w-[162px] gap-x-2 border rounded bg-main-color flex items-center justify-center text-[#FFF] cursor-pointer"
              onClick={handleSubmitForm}
            >
              <SaveSvg /> 保存
            </div>
          </div>
        </div>
      </div>
    </Drawer>
  );
};

export default forwardRef<EstimatePriceDetailRef, PricePlanDetailType>(ItineraryEstimateDetail);
