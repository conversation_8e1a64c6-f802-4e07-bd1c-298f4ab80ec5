import type { PlanCreateContextProps } from '@/providers';
import { PlanCreateContext } from '@/providers';
import { Tabs, Tooltip } from 'antd';
import { useContext, useEffect, useMemo, useState } from 'react';
import CalendarIconSVG from '@/components/SVG/CalendarIconSVG';
import PlanForDayTabItems from '../../Itinerary-creation/PlanForDayTabItems';
import CustomTabs from '@/components/Commons/CustomTabs';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

function PlanForDayTabs() {
  const { planInfo, planTabs, activePlanTab } =
    useContext<PlanCreateContextProps>(PlanCreateContext);
  const [activetabKey, setActiveTabKey] = useState<string | number>();
  const deviceType = useDeviceType();

  useEffect(() => {
    setActiveTabKey(activePlanTab);
  }, []);

  const DataTabRender = useMemo(() => {
    const newData = planTabs?.map((item) => ({
      key: item?.day?.toString(),
      tabTitle: (
        <div className="text-[15px] min-w-[50px] font-medium leading-6 text-[#7D7E82] flex justify-center gap-1">
          {item?.day}日目
        </div>
      ),
      tabContent: <PlanForDayTabItems plans={item?.plans} hasRemoveItem={false} />,
    }));

    return newData;
  }, [planTabs]);

  return (
    <>
      <div className="flex flex-col mb-6">
        <div className="flex justify-between">
          <Tooltip title={planInfo?.title}>
            <div className="text-[18px] text-main-black font-bold leading-7 line-clamp-1">
              {planInfo?.title}
            </div>
          </Tooltip>
        </div>
      </div>
      <div>
        <span className="text-[12px] leading-4 font-medium text-[#363840]">期間</span>
        <div className="mt-2 flex items-center justify-between space-x-[18px] border border-[#DCDEE3] rounded px-[12px] py-[10px]">
          <div
            className="flex items-center"
            style={{
              fontSize: deviceType === DeviceTypeEnum.MOBILE ? '12px' : '14px',
            }}
          >
            <div>{planInfo?.start_date ?? '-'}</div>
            <div className="mx-1">~</div>
            <div>{planInfo?.end_date ?? '-'}</div>
          </div>
          <div>
            <CalendarIconSVG />
          </div>
        </div>
      </div>

      <div
        className={`flex flex-1 h-[60%] [&_.ant-tabs-content-holder]:overflow-y-auto  [&_.ant-tabs-content]:!h-full [&_.ant-tabs-tabpane]:!h-full ${
          deviceType === DeviceTypeEnum.DESKTOP
            ? '[&_.ItineraryItemList]:!w-[248px]'
            : '[&_.ItineraryItemList]:!w-full'
        }`}
      >
        <CustomTabs data={DataTabRender} />
      </div>
    </>
  );
}

export default PlanForDayTabs;
