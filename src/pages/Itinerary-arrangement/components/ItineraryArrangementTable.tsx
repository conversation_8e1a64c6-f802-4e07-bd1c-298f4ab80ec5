import type { arrangementType, UpdateCancelDateType } from '@/apis/itineraries/arrangement';
import { TagCategory } from '@/components/Commons/TagCategory';
import { Image, Tooltip } from 'antd';
import type { ColumnsType } from 'antd/lib/table';
import type { Moment } from 'moment';
import moment from 'moment';
import React, { useRef, useState } from 'react';
import IconEye from '@/assets/imgs/common-icons/view-eye.svg';
import BasicTable from '@/components/Commons/BasicTable';
import { StatusArrangement } from '@/constants/data';
import type { InitialType } from 'use-url-search-params';
import { history, useParams } from 'umi';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicModal from '@/components/Commons/BasicModal';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { formatMoney } from '@/utils';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

interface Props {
  data: arrangementType[];
  setData: React.Dispatch<React.SetStateAction<arrangementType[]>>;
  totalItem: number;
  parameter: InitialType;
  setParameterFilter: (nextQuery: InitialType) => void;
  listItemChange: UpdateCancelDateType;
  setListItemChange: React.Dispatch<React.SetStateAction<UpdateCancelDateType>>;
}

const ItineraryArrangementTable: React.FC<Props> = ({
  data,
  setData,
  totalItem,
  parameter,
  setParameterFilter,
  listItemChange,
  setListItemChange,
}) => {
  const { plan_id, id } = useParams<{ plan_id: string; id: string }>();
  const refModalConfirmChangePage = useRef(null);
  const [pageChange, setPageChange] = useState<number>(1);
  const heightScreen = window.innerHeight;
  const deviceType = useDeviceType();

  const handleChangeCancellationDate = (item_id: number, value: Moment) => {
    const formatDate = moment(value).format('YYYY/MM/DD');
    const newData = [...data]?.map((item) =>
      item?.id === item_id ? { ...item, cancel_date: formatDate } : item,
    );
    let cloneListItemChange = [...listItemChange];
    const exitsItemChange = cloneListItemChange?.find((item) => item?.id === item_id);
    if (exitsItemChange) {
      cloneListItemChange = cloneListItemChange?.map((item) =>
        item?.id === item_id
          ? {
              ...item,
              cancel_date: formatDate,
            }
          : item,
      );
    } else {
      cloneListItemChange.push({
        id: item_id,
        cancel_date: formatDate,
      });
    }
    setListItemChange(cloneListItemChange);
    setData(newData);
  };

  const columns: ColumnsType<arrangementType> = [
    {
      title: <div>#</div>,
      key: 'id',
      dataIndex: 'id',
      align: 'center',
      width: 60,
      render: (_, { no }) => <div>{no}</div>,
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">仕入・素材</div>,
      dataIndex: 'title',
      key: 'title',
      width: 200,
      render: (value, { name }) => (
        <Tooltip title={name}>
          <div className="truncate">{name}</div>
        </Tooltip>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">ステータス</div>,
      dataIndex: 'status',
      key: 'status',
      width: 140,
      render: (value, { status }) => {
        const statusName = StatusArrangement?.find(
          (item) => Number(item.value) === Number(status),
        ).label;
        return <div>{statusName}</div>;
      },
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">料金</div>,
      dataIndex: 'fee',
      key: 'fee',
      width: 140,
      render: (value, { fee, status }) => {
        const isReserved = status === StatusArrangement[3].value; // status: Reserved

        return <div className="text-right">{isReserved ? formatMoney(fee) : null}</div>;
      },
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">人数</div>,
      dataIndex: 'quantity',
      key: 'quantity',
      width: 140,
      render: (value, { quantity, status }) => {
        const isReserved = status === StatusArrangement[3].value; // status: Reserved
        return <div className="text-right">{isReserved ? quantity : null}</div>;
      },
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">カテゴリー</div>,
      dataIndex: 'reference_type',
      key: 'reference_type',
      width: 140,
      render: (value, { reference_type }) => (
        <div className="truncate">{TagCategory(reference_type)}</div>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">利用予定日</div>,
      dataIndex: 'date',
      key: 'date',
      width: 140,
      render: (value, { scheduled_date }) => (
        <div className="truncate">
          {scheduled_date ? moment(scheduled_date).format('YYYY/MM/DD') : '-'}
        </div>
      ),
    },
    {
      title: <div className="leading-[19px] text-[13px] font-medium">キャンセル期日</div>,
      dataIndex: 'cancel_date',
      key: 'cancel_date',
      width: 140,
      render: (value, record) => (
        <div className="text-right">
          <BasicDatePicker
            hideSuffixIcon
            allowClear={false}
            defaultValue={record.cancel_date ? moment(record.cancel_date) : undefined}
            onChange={(v) => handleChangeCancellationDate(record.id, v)}
            suffixIcon={undefined}
            placeholder="YYYY/MM/DD"
            format={'YYYY/MM/DD'}
          />
        </div>
      ),
    },

    {
      title: null,
      dataIndex: 'edit',
      key: 'edit',
      width: deviceType === DeviceTypeEnum.DESKTOP ? 140 : 60,
      render: (value, record) => (
        <div
          className="flex items-center justify-center text-main-color gap-x-1"
          onClick={() => {
            history.push(`/itinerary-management/${id}/plan/${plan_id}/arrangement/${record.id}`);
          }}
        >
          <Image preview={false} src={IconEye} width={16} height={16} />
          {deviceType === DeviceTypeEnum.DESKTOP ? '詳細を見る' : null}
        </div>
      ),
    },
  ];

  const onConfirmChangePageWhenEdit = (pageConfirm: number) => {
    setPageChange(pageConfirm);
    refModalConfirmChangePage.current?.open();
  };

  return (
    <div>
      <BasicTable
        className={`!mt-0 h-full`}
        tableProps={{
          scroll: { y: heightScreen - 470, x: 500 },
          columns,
          dataSource: data,
          bordered: false,
          pagination: false,
          size: deviceType === DeviceTypeEnum?.MOBILE ? 'small' : 'middle',
          rowKey: 'id',
        }}
        page={Number(parameter?.page)}
        pageSize={Number(parameter?.limit)}
        onChangePage={(p) => {
          if (listItemChange.length) {
            onConfirmChangePageWhenEdit(p);
          } else {
            setParameterFilter({ ...parameter, page: p });
          }
        }}
        total={totalItem}
        onSelectPageSize={(v) => {
          setParameterFilter({ ...parameter, page: 1, limit: v });
        }}
      />

      <BasicModal
        ref={refModalConfirmChangePage}
        title={'警告'}
        content={<>{MESSAGE_ALERT.CONFIRM_CHANGE_PAGE}</>}
        okText="ページを変更する"
        onSubmit={() => {
          setParameterFilter({ ...parameter, page: pageChange });
          setListItemChange([]);
          refModalConfirmChangePage.current?.close();
        }}
      />
    </div>
  );
};

export default ItineraryArrangementTable;
