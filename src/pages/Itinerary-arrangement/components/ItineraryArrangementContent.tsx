import type { arrangementType, UpdateCancelDateType } from '@/apis/itineraries/arrangement';
import {
  ExportExcelArrangement,
  getListArrangement,
  UpdateCancelDate,
} from '@/apis/itineraries/arrangement';
import BasicSelect from '@/components/Commons/BasicSelect';
import STATUS_CODE from '@/constants/statusCode';
import { ITEM_PER_PAGE } from '@/utils/constants';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { history } from 'umi';
import { useUrlSearchParams } from 'use-url-search-params';
import ItineraryArrangementTable from './ItineraryArrangementTable';
import CancelXSVG from '@/components/SVG/CancelXSVG';
import SaveSvg from '@/components/SVG/SaveSvg';
import { openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { useDeviceType } from '@/providers/DeviceProvider';

const filterListingOptions = [
  {
    label: '日付',
    value: 'date',
  },
  {
    label: 'カテゴリー',
    value: 'category',
  },
];

const initSearchParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
  sort: filterListingOptions?.[0]?.value,
};

const ItineraryArrangementContent = () => {
  const { plan_id } = useParams<{ plan_id: string }>();
  const [dataSource, setDataSource] = useState<arrangementType[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [listItemChange, setListItemChange] = useState<UpdateCancelDateType>([]);

  const deviceType = useDeviceType();
  const [parameterFilter, setParameterFilter] = useUrlSearchParams(
    initSearchParams,
    undefined,
    true,
  );

  const fetchData = async () => {
    try {
      const params = { ...parameterFilter };
      const { data, status } = await getListArrangement(Number(plan_id), params);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dts = data?.data?.map((item, index) => ({
          ...item,
          key: item?.id,
          no: (Number(parameterFilter?.page) - 1) * Number(parameterFilter?.limit) + index + 1,
        }));
        setDataSource(dts);
        setTotal(data.total);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    if (plan_id) {
      fetchData();
    }
  }, [plan_id, parameterFilter]);

  const handleSubmitSaveItem = async () => {
    try {
      if (listItemChange.length) {
        const { status } = await UpdateCancelDate(Number(plan_id), listItemChange);
        if (status === STATUS_CODE.SUCCESSFUL) {
          openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
          setListItemChange([]);
          fetchData();
        } else {
          openNotificationSuccess(MESSAGE_ALERT.EDIT_FAILED);
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  const handleExportExcelArrangement = async () => {
    try {
      const { data, status } = await ExportExcelArrangement(Number(plan_id));
      if (status === STATUS_CODE.SUCCESSFUL) {
        const urlCsv = data?.data?.file_url;
        if (urlCsv) {
          const a = document.createElement('a');
          a.href = urlCsv;
          a.download = 'csv_手配状況.csv';
          a.click();
          window.URL.revokeObjectURL(urlCsv);
        }
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div className="h-full flex flex-col">
      <div className="bg-white rounded-xl px-2 pb-3 pt-4 flex flex-col justify-end items-end flex-1">
        <div className="px-4 mb-4">
          <div className="mb-1">並び替え</div>
          <BasicSelect
            value={parameterFilter?.sort}
            options={filterListingOptions}
            className="w-[140px] [&_.ant-select-selector]:!h-9"
            onChange={(value) => setParameterFilter({ ...parameterFilter, sort: value })}
          />
        </div>
        <div className="h-full w-full">
          <ItineraryArrangementTable
            data={dataSource}
            setData={setDataSource}
            totalItem={total}
            parameter={parameterFilter}
            setParameterFilter={setParameterFilter}
            listItemChange={listItemChange}
            setListItemChange={setListItemChange}
          />
        </div>
      </div>
      <div
        className={`flex ${
          deviceType === DeviceTypeEnum.MOBILE
            ? 'flex-col gap-y-[8px]'
            : 'items-center justify-center gap-x-[20px]'
        } mt-6`}
      >
        <div
          style={{
            width: deviceType === DeviceTypeEnum.MOBILE ? '100%' : '162px',
          }}
          className="h-[40px] gap-x-2 border rounded border-[#DCDEE3] hover:opacity-90 bg-[#FFF] flex items-center justify-center text-[#FF3B30] cursor-pointer"
          onClick={() => history.goBack()}
        >
          <CancelXSVG /> キャンセル
        </div>
        <div
          style={{
            width: deviceType === DeviceTypeEnum.MOBILE ? '100%' : '187px',
          }}
          className="h-[40px] gap-x-2 border rounded bg-main-color hover:opacity-90 flex items-center justify-center text-[#FFF] cursor-pointer"
          onClick={handleExportExcelArrangement}
        >
          {'手配書出力（EXCEL)'}
        </div>
        <div
          style={{
            width: deviceType === DeviceTypeEnum.MOBILE ? '100%' : '187px',
          }}
          className="h-[40px] gap-x-2 border rounded bg-main-color hover:opacity-90 flex items-center justify-center text-[#FFF] cursor-pointer"
          onClick={() => {
            history.push('/accounting-management/purchase-management/invoices');
          }}
        >
          {'仕入伝票作成'}
        </div>
        <div
          style={{
            width: deviceType === DeviceTypeEnum.MOBILE ? '100%' : '162px',
          }}
          className="h-[40px] gap-x-2 border rounded bg-main-color hover:opacity-90 flex items-center justify-center text-[#FFF] cursor-pointer"
          onClick={handleSubmitSaveItem}
        >
          <SaveSvg /> 保存
        </div>
      </div>
    </div>
  );
};

export default ItineraryArrangementContent;
