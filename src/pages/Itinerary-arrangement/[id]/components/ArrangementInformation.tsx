import type { arrangementDetailType } from '@/apis/itineraries/arrangement';
import { StatusArrangement } from '@/constants/data';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { useDeviceType } from '@/providers/DeviceProvider';
import moment from 'moment';
import React, { useMemo } from 'react';

const FieldInformation = (title: string, value: string) => {
  return (
    <div className="flex flex-col gap-y-2">
      <div className="text-xs leading-4 text-[#6B7180]">{title}</div>
      <div className="text-sm leading-5 text-[#363840] h-10 flex items-center">{value}</div>
    </div>
  );
};

interface Props {
  dataDetail: arrangementDetailType;
}

const ArrangementInformation = ({ dataDetail }: Props) => {
  const deviceType = useDeviceType();

  const statusText = useMemo(() => {
    const statusName = StatusArrangement?.find(
      (item) => Number(item.value) === Number(dataDetail?.status),
    )?.label;

    return statusName;
  }, [dataDetail]);

  return (
    <div className="bg-white rounded-xl p-4">
      <div className="font-bold text-lg leading-7 text-[#363840]">手配先情報</div>
      <div
        className={`mt-4 grid ${
          deviceType === DeviceTypeEnum.DESKTOP ? 'grid-cols-4' : 'grid-cols-2'
        }  gap-x-6 gap-y-4`}
      >
        <div className="col-span-1">
          {FieldInformation('手配先', dataDetail?.place_sort ?? '-')}
        </div>
        <div className="col-span-1">{FieldInformation('ステータス', statusText)}</div>
        <div className="col-span-1">
          {FieldInformation(
            '利用予定日',
            dataDetail?.scheduled_date
              ? moment(dataDetail?.scheduled_date).format('YYYY/MM/DD')
              : '-',
          )}
        </div>
        <div className="col-span-1">
          {FieldInformation(
            'キャンセル期日',
            dataDetail?.cancel_date ? moment(dataDetail?.cancel_date).format('YYYY/MM/DD') : '-',
          )}
        </div>
        <div className="col-span-1">
          {FieldInformation('電話番号', dataDetail?.phone_number ?? '-')}
        </div>
        <div className="col-span-1">{FieldInformation('FAX', dataDetail?.fax ?? '-')}</div>
        <div className="col-span-1">
          {FieldInformation('メールアドレス', dataDetail?.email ?? '-')}
        </div>
        <div className="col-span-1">
          {FieldInformation('担当者', dataDetail?.charge_person ?? '-')}
        </div>
      </div>
    </div>
  );
};

export default ArrangementInformation;
