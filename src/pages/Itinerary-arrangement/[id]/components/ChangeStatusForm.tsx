import type { arrangementDetailType } from '@/apis/itineraries/arrangement';
import BasicInput from '@/components/Commons/BasicInput';
import BasicNumbericInput from '@/components/Commons/BasicNumbericInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTextArea from '@/components/Commons/BasicTextArea';
import CancelXSVG from '@/components/SVG/CancelXSVG';
import SaveSvg from '@/components/SVG/SaveSvg';
import { StatusArrangement } from '@/constants/data';
import { rules } from '@/constants/rules';
import { Form, type FormInstance } from 'antd';
import type { FormProps } from 'antd/es/form';
import React, { forwardRef, useEffect, useImperativeHandle, useMemo } from 'react';

export type ChangeStatusFormRef = {
  form?: FormInstance<any>;
};

interface Props {
  formProps?: FormProps;
  onClose?: () => void;
  onSubmit?: (values: any) => void;
  dataArrangement?: arrangementDetailType;
}

const ChangeStatusForm = forwardRef<ChangeStatusFormRef, Props>(
  ({ formProps, onClose, onSubmit, dataArrangement }, ref) => {
    const [form] = Form.useForm();

    const statusWatch = Form.useWatch('status', form);

    const isStatusReserved = useMemo(() => {
      return statusWatch === 3;
    }, [statusWatch]);

    useImperativeHandle(ref, () => ({
      form,
    }));

    useEffect(() => {
      form.setFieldsValue({
        status: dataArrangement?.status,
        quantity: dataArrangement?.quantity?.toString(),
        fee: dataArrangement?.fee?.toString(),
      });
    }, [dataArrangement]);
    return (
      <div className="[&_.ant-form-item-label_>_label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before]:hidden">
        <div className="text-2xl leading-9 font-bold text-[#121315]">ステータス変更</div>
        <Form form={form} autoComplete="off" {...formProps} onFinish={onSubmit} layout="vertical">
          <Form.Item
            name={'status'}
            rules={rules.requiredSelect}
            label={
              <div className="flex items-center gap-[6px] after:content-['*'] after:ml-0.5 after:text-[#ff4d4f]">
                {'ステータス'}
              </div>
            }
          >
            <BasicSelect title={null} options={StatusArrangement} />
          </Form.Item>
          <div className="flex items-center space-x-[16px]">
            <Form.Item
              className="flex-1"
              name={'fee'}
              rules={rules.requiredInput}
              label={
                <div
                  className={`flex items-center gap-[6px] ${
                    isStatusReserved ? "after:content-['*'] after:ml-0.5 after:text-[#ff4d4f]" : ''
                  }`}
                >
                  {'料金'}
                </div>
              }
            >
              <BasicNumbericInput title={null} disabled={!isStatusReserved} />
            </Form.Item>
            <Form.Item
              className="flex-1"
              name={'quantity'}
              rules={rules.requiredInput}
              label={
                <div
                  className={`flex items-center gap-[6px] ${
                    isStatusReserved ? "after:content-['*'] after:ml-0.5 after:text-[#ff4d4f]" : ''
                  }`}
                >
                  {'人数'}
                </div>
              }
            >
              <BasicInput title={null} disabled={!isStatusReserved} />
            </Form.Item>
          </div>
          <Form.Item
            className="flex-1"
            name={'charge_person'}
            label={<div className="flex items-center gap-[6px] ">{'先方の担当者'}</div>}
          >
            <BasicInput title={null} maxLength={155} />
          </Form.Item>
          <Form.Item
            className="flex-1"
            name={'memo'}
            label={<div className="flex items-center gap-[6px] ">{'メモ'}</div>}
          >
            <BasicTextArea title={null} maxLength={1000} />
          </Form.Item>
        </Form>
        <div className="flex items-center gap-x-[20px]">
          <div
            className="h-[40px] flex-1 gap-x-2 border rounded border-[#DCDEE3] hover:opacity-90 bg-[#FFF] flex items-center justify-center text-[#FF3B30] cursor-pointer"
            onClick={onClose}
          >
            <CancelXSVG /> キャンセル
          </div>
          <div
            className="h-[40px] flex-1 gap-x-2 border rounded bg-main-color hover:opacity-90 flex items-center justify-center text-[#FFF] cursor-pointer"
            onClick={() => form.submit()}
          >
            <SaveSvg /> 保存
          </div>
        </div>
      </div>
    );
  },
);

export default ChangeStatusForm;
