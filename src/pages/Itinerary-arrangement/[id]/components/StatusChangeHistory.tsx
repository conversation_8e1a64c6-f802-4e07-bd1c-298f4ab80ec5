import type { arrangementDetailType } from '@/apis/itineraries/arrangement';
import { updateStatus, type StatusChangeType } from '@/apis/itineraries/arrangement';
import type { BasicFormModalRef } from '@/components/Commons/BasicFormModal';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import ArrowUpDownSvg from '@/components/SVG/ArrowUpDownSvg';
import { DoubleRightOutlined } from '@ant-design/icons';
import moment from 'moment';
import React, { useRef } from 'react';
import type { ChangeStatusFormRef } from './ChangeStatusForm';
import ChangeStatusForm from './ChangeStatusForm';
import { useParams } from 'react-router';
import STATUS_CODE from '@/constants/statusCode';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { StatusArrangement } from '@/constants/data';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

const StatusChangeItem: React.FC<{ data: StatusChangeType }> = ({ data }) => {
  const deviceType = useDeviceType();

  const currentStatus = StatusArrangement?.find(
    (item) => Number(item.value) === Number(data?.current_status),
  )?.label;

  const oldStatus = StatusArrangement?.find(
    (item) => Number(item.value) === Number(data?.old_status),
  )?.label;

  return (
    <div className="rounded bg-white">
      <div className="flex items-center justify-between px-2 py-3 border-b border-[#DCDEE3]">
        <div className="text-sm leading-5 font-bold text-[#363840]">{data?.creator?.name}</div>
        <div className="text-xs leading-4 text-[#474B55]">
          {moment(data?.created_at).format('YYYY/MM/DD HH:mm:ss')}
        </div>
      </div>
      <div className="px-2 pb-7">
        <div className="flex items-center justify-between text-xs leading-4 py-2 border-b border-[#EFF0F2]">
          <div className="text-[#9499A5] min-w-[75px]">ステータス　</div>
          <div className="flex items-center">
            <span className="text-[#225DE0]">{oldStatus}</span>{' '}
            <DoubleRightOutlined color="#C7CAD1" className="mx-2 text-[#C7CAD1]" />
            <span className="text-[#FDAF2E]">{currentStatus}</span>
          </div>
        </div>
        <div className="flex items-center justify-between text-xs leading-4 py-2 border-b border-[#EFF0F2]">
          <div className="text-[#9499A5] min-w-[75px]">先方の担当者</div>
          <div
            className="text-[#363840]"
            style={{
              wordWrap: 'break-word',
              wordBreak: 'break-word',
              overflowWrap: 'break-word',
              whiteSpace: 'normal',
            }}
          >
            {data.charge_person}
          </div>
        </div>
        <div className="flex items-center justify-between text-xs leading-4 py-2">
          <div className="text-[#9499A5] min-w-[75px]">メモ</div>
          <div
            className="text-[#363840]"
            style={{
              wordWrap: 'break-word',
              wordBreak: 'break-word',
              overflowWrap: 'break-word',
              whiteSpace: 'normal',
            }}
          >
            {data.memo}
          </div>
        </div>
      </div>
    </div>
  );
};

interface Props {
  refetch: () => void;
  dataStatusChange: StatusChangeType[];
  dataArrangementDetal: arrangementDetailType;
}

const StatusChangeHistory = ({ dataStatusChange, refetch, dataArrangementDetal }: Props) => {
  const { plan_id, arrangement_id } = useParams<{ plan_id: string; arrangement_id: string }>();
  const refModal = useRef<BasicFormModalRef>(null);
  const refFormAddNewItem = useRef<ChangeStatusFormRef>(null);
  const deviceType = useDeviceType();

  const openModal = () => {
    refModal?.current?.open();
  };

  const closeModal = () => {
    refModal?.current?.close();
  };

  const handleSubmitChangeStatus = async (values) => {
    try {
      const payload = {
        ...values,
      };

      const { status } = await updateStatus(Number(arrangement_id), payload);

      if (status === STATUS_CODE.SUCCESSFUL) {
        refetch();
        closeModal();
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      }
    } catch (error) {
      console.log(error);
    }
  };

  return (
    <div
      className={`${
        deviceType === DeviceTypeEnum.DESKTOP ? 'w-[30%]' : 'w-full min-h-[500px] max-h-[500px]'
      }  bg-white rounded-xl p-4 flex flex-col gap-y-4`}
    >
      <div className="text-lg leading-7 font-bold text-[#363840]">ステータス変更履歴</div>
      <div className="flex-1 bg-[#EFF0F2] p-2 rounded-lg  flex flex-col gap-y-2 overflow-auto">
        {dataStatusChange?.map((item) => (
          <StatusChangeItem key={item.id} data={item} />
        ))}
      </div>
      <div className="flex items-center justify-center">
        <div
          onClick={openModal}
          className="h-10 w-[240px] rounded bg-[#225DE0] text-white flex items-center justify-center cursor-pointer hover:opacity-90"
        >
          <ArrowUpDownSvg /> ステータス変更
        </div>
      </div>

      <BasicFormModal
        className="!w-[600px] [&_.ant-modal-body]:!p-0 [&_.ant-modal-body]:!px-[22px]"
        ref={refModal}
        content={
          <ChangeStatusForm
            dataArrangement={dataArrangementDetal}
            ref={refFormAddNewItem}
            onSubmit={handleSubmitChangeStatus}
            onClose={closeModal}
          />
        }
        hideListButton
        isValidate={true}
      />
    </div>
  );
};

export default StatusChangeHistory;
