import type { CommentType } from '@/apis/itineraries/arrangement';
import { addCommentToPlanItem } from '@/apis/itineraries/arrangement';
import { openNotificationFail } from '@/components/Notification';
import SendSVG from '@/components/SVG/SendSVG';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import STATUS_CODE from '@/constants/statusCode';
import { useDeviceType } from '@/providers/DeviceProvider';
import { Input } from 'antd';
import moment from 'moment';
import React, { useState } from 'react';
import { useParams } from 'react-router';

const MessageItem: React.FC<{ messageItemDetail: CommentType }> = ({ messageItemDetail }) => {
  return (
    <div className="rounded bg-white scale-95">
      <div className="flex justify-between px-2 py-3 border-b border-[#DCDEE3]">
        <div className="font-bold text-sm leading-5 text-[#363840]">
          {messageItemDetail.creator?.name}
        </div>
        <div className="text-xs leading-4 text-[#474B55]">
          {moment(messageItemDetail.created_at)?.format('YYYY/MM/DD HH:mm:ss')}
        </div>
      </div>
      <div
        style={{
          wordWrap: 'break-word',
          wordBreak: 'break-word',
          overflowWrap: 'break-word',
          whiteSpace: 'normal',
        }}
        className="p-2 text-xs leading-4 text-[#363840]"
      >
        {messageItemDetail.memo}
      </div>
    </div>
  );
};

interface Props {
  refetch: () => void;
  dataMessageList: CommentType[];
}

const MessageHistory: React.FC<Props> = ({ refetch, dataMessageList }) => {
  const { plan_id, arrangement_id } = useParams<{ plan_id: string; arrangement_id: string }>();
  const [inputValue, setInputValue] = useState<string>();
  const deviceType = useDeviceType();

  const handleAddComment = async () => {
    try {
      if (inputValue.trim()) {
        const { status } = await addCommentToPlanItem(Number(arrangement_id), { memo: inputValue });
        if (status === STATUS_CODE.SUCCESSFUL) {
          setInputValue(undefined);
          refetch();
        } else {
          openNotificationFail(MESSAGE_ALERT.COMMENT_FAILED);
        }
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  return (
    <div
      className={`flex-1 bg-white rounded-xl p-4 flex flex-col gap-y-4 ${
        deviceType !== DeviceTypeEnum.DESKTOP ? 'min-h-[500px] max-h-[500px]' : ''
      }`}
    >
      <div className="font-bold text-lg leading-7 text-[#363840]">メッセージ履歴</div>
      <div className="flex-1 bg-[#EFF0F2] flex flex-col overflow-y-auto gap-y-2 p-2">
        {dataMessageList?.map((item, index) => (
          <MessageItem key={`${index}-${item.id}`} messageItemDetail={item} />
        ))}
      </div>
      <Input
        size={deviceType !== DeviceTypeEnum.DESKTOP ? 'small' : 'middle'}
        maxLength={1000}
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            handleAddComment();
          }
        }}
        suffix={
          <div className="p-1 cursor-pointer hover:opacity-90">
            {inputValue ? (
              <SendSVG onClick={handleAddComment} />
            ) : (
              <SendSVG className="cursor-not-allowed" onClick={() => {}} />
            )}
          </div>
        }
        className="h-12 !rounded-[10px]"
        placeholder="メッセージ内容を入力してください"
      />{' '}
    </div>
  );
};

export default MessageHistory;
