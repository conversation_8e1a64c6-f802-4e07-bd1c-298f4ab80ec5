import type { arrangementDetailType } from '@/apis/itineraries/arrangement';
import { getArrangementDetail } from '@/apis/itineraries/arrangement';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import ArrangementInformation from './components/ArrangementInformation';
import StatusChangeHistory from './components/StatusChangeHistory';
import MessageHistory from './components/MessageHistory';
import { LeftOutlined } from '@ant-design/icons';
import STATUS_CODE from '@/constants/statusCode';
import { history } from 'umi';
import { TEXT_ACTION } from '@/constants/commonText';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

const ItineraryArrangementDetail = () => {
  const { plan_id, arrangement_id } = useParams<{ plan_id: string; arrangement_id: string }>();
  const [dataDetail, setDataDetail] = useState<arrangementDetailType>();
  const deviceType = useDeviceType();

  const fetchDataDetail = async () => {
    try {
      const { data, status } = await getArrangementDetail(Number(plan_id), Number(arrangement_id));
      if (status === STATUS_CODE.SUCCESSFUL) {
        setDataDetail(data.data);
      }
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    fetchDataDetail();
  }, []);

  return (
    <div
      className="flex flex-col p-9 gap-y-6"
      style={{
        height: deviceType === DeviceTypeEnum.DESKTOP ? 'calc(100vh - 90px)' : '100%',
      }}
    >
      <ArrangementInformation dataDetail={dataDetail} />
      <div
        className={`flex-1 flex ${
          deviceType !== DeviceTypeEnum.DESKTOP ? 'flex-col gap-y-6' : 'gap-x-6'
        } justify-between overflow-auto`}
      >
        <StatusChangeHistory
          refetch={fetchDataDetail}
          dataStatusChange={dataDetail?.history_change_status}
          dataArrangementDetal={dataDetail}
        />
        <MessageHistory refetch={fetchDataDetail} dataMessageList={dataDetail?.list_comment} />
      </div>

      <div className="flex items-center justify-center">
        <div
          onClick={() => history.goBack()}
          className="flex items-center justify-center h-10 w-[186px] bg-white rounded border border-[#DCDEE3] cursor-pointer hover:opacity-70"
        >
          <LeftOutlined className="mr-2 " width={16} height={16} /> {TEXT_ACTION.RETURN}
        </div>
      </div>
    </div>
  );
};

export default ItineraryArrangementDetail;
