import { PlanCreateProvider } from '@/providers';
import React from 'react';
import { useParams } from 'react-router';
import PlanForDayTabs from './components/PlanForDayTabs';
import ItineraryArrangementContent from './components/ItineraryArrangementContent';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { useDeviceType } from '@/providers/DeviceProvider';

const ItineraryArrangement = () => {
  const { plan_id } = useParams<{ plan_id: string }>();
  const deviceType = useDeviceType();

  return (
    <PlanCreateProvider planId={plan_id}>
      <div
        className={`${
          deviceType === DeviceTypeEnum.DESKTOP ? 'flex' : 'flex-col'
        } itinerary-container overflow-x-auto`}
        style={{
          height: 'calc(100vh - 90px)',
        }}
      >
        <div
          className="flex flex-col w-[320px] bg-white px-9 pt-6 pb-9"
          style={{
            borderRadius: deviceType !== DeviceTypeEnum.DESKTOP ? '12px' : undefined,
            height: deviceType !== DeviceTypeEnum.DESKTOP ? '500px' : undefined,
            width: deviceType !== DeviceTypeEnum.DESKTOP ? 'auto' : undefined,
            margin: deviceType !== DeviceTypeEnum.DESKTOP ? '36px 36px 0' : undefined,
          }}
        >
          <PlanForDayTabs />
        </div>
        <div className="m-9 flex-1">
          <ItineraryArrangementContent />
        </div>
      </div>
    </PlanCreateProvider>
  );
};

export default ItineraryArrangement;
