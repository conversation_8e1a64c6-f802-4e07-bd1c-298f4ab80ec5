import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import {
  listReportDepositInvoice,
  markReportedDepositInvoiceItem,
} from '@/apis/accounting/depositManagement';
import { getDetailBusinessPartner, getListBusinessPartner } from '@/apis/businessPartner';
import SelectIcon from '@/assets/imgs/common-icons/select-icon.svg';
import type { ImportCsvCommonRef } from '@/components/CommonModalImportCsv';
import CommonModalImportCsv, { EMenuType } from '@/components/CommonModalImportCsv';
import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicDateRangePicker from '@/components/Commons/BasicDateRangePicker';
import type { BasicFormModalRef } from '@/components/Commons/BasicFormModal';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import BasicInput from '@/components/Commons/BasicInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import FormAccountingCollaboration from '@/components/Form/FormAccountingCollaboration';
import FormSelectCompany from '@/components/Form/FormSelectCompany';
import CloseSVG from '@/components/SVG/CloseSVG';
import SearchSVG from '@/components/SVG/SearchSVG';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { useDebounce } from '@uidotdev/usehooks';
import { Image } from 'antd';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { history } from 'umi';

const MAX_WIDTH_HEADER = 1880;

const HeaderAction = ({ paramSearch, setParamSearch, onFetchData, optionsCurrencyType }) => {
  const [currentCompany, setCurrentCompany] = useState<BusinessPartnerDetailType>();
  const [searchBusinessPartnerCodeValue, setSearchBusinessPartnerCodeValue] = useState<string>('');
  const [searchTourValue, setSearchTourValue] = useState<string>('');
  const [depositeDate, setDepositeDate] = useState<string[]>([]);
  const [currencyType, setCurrencyType] = useState<string>('');

  const refModalSelectCompany = useRef<BasicFormModalRef>();
  const refModalAccountingCollaboration = useRef<BasicFormModalRef>();
  const refImportCsv = useRef<ImportCsvCommonRef>(null);

  const divHeaderRef = useRef(null);
  const [widthHeader, setWidthHeader] = useState(0);

  const businessPartnerCodeDebounce = useDebounce(searchBusinessPartnerCodeValue, 300);

  useEffect(() => {
    if (currentCompany) {
      setSearchBusinessPartnerCodeValue(currentCompany?.business_partner_code);
    }
  }, [currentCompany]);

  const handleSearchByValue = () => {
    const paramToSearch = { ...paramSearch, page: 1 };
    setParamSearch({
      ...paramToSearch,
      keyword: searchTourValue,
      // tourId: searchTourValue,
      currency_type_master_id: currencyType ?? undefined,
      business_partner_id: currentCompany?.id,
      deposit_start_date: depositeDate?.[0],
      deposit_end_date: depositeDate?.[1],
      business_partner_code: searchBusinessPartnerCodeValue,
    });
  };

  const onExportCsv = () => {
    refModalAccountingCollaboration.current.open();
  };

  useEffect(() => {
    const initDate = [paramSearch?.deposit_start_date, paramSearch?.deposit_end_date];
    setDepositeDate(initDate);
    setSearchTourValue(paramSearch?.keyword);
    setCurrencyType(paramSearch?.currency_type_master_id);
    setSearchBusinessPartnerCodeValue(paramSearch?.business_partner_code);
  }, [
    // paramSearch?.tourId,
    paramSearch?.keyword,
    paramSearch?.deposit_start_date,
    paramSearch?.deposit_end_date,
    paramSearch?.currency_type_master_id,
    paramSearch?.business_partner_code,
  ]);

  const onImportCsv = () => {
    if (refImportCsv) {
      refImportCsv.current.open();
    }
  };

  const newOptionsCurrencyType = useMemo(() => {
    const options = optionsCurrencyType?.map((item) => {
      return {
        value: item.id,
        label: item.currency_type_name,
        disabled: item.status === 0 || !item.target?.includes?.('3'),
      };
    });

    return options;
  }, [optionsCurrencyType]);

  const onOpenModalSelectCompany = () => {
    refModalSelectCompany.current.open();
  };

  const fetchDataDetail = async (id: number) => {
    try {
      const { data, status } = await getDetailBusinessPartner(id);
      const rsData = data.data;
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dataRs = {
          ...rsData,
          qualified_invoice_issuer_number:
            rsData?.qualified_invoice_issuer_number?.toString() ?? '',
          r_rate: rsData?.r_rate?.toString() ?? '',
          target: JSON.parse(rsData?.target as string) ?? undefined,
        };

        setCurrentCompany(dataRs as any);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    if (!Number.isNaN(Number(paramSearch?.business_partner_id))) {
      fetchDataDetail(Number(paramSearch?.business_partner_id));
    }
  }, []);

  const fetchDataCompany = async () => {
    const resData = await getListBusinessPartner({
      page: 1,
      limit: 1,
      business_partner_code: businessPartnerCodeDebounce,
    });

    if (resData?.data?.data) {
      setCurrentCompany(resData.data.data[0]);
      setParamSearch({
        ...paramSearch,
        page: 1,
        business_partner_id: resData.data.data[0]?.id,
        business_partner_code: resData.data.data[0]?.business_partner_code,
      });
    } else {
      setCurrentCompany(undefined);
    }
  };

  useEffect(() => {
    if (businessPartnerCodeDebounce?.length > 2) {
      fetchDataCompany();
    }
  }, [businessPartnerCodeDebounce]);

  useEffect(() => {
    const resizeObserver = new ResizeObserver(() => {
      if (divHeaderRef.current) {
        const newWidth = divHeaderRef.current.offsetWidth;
        setWidthHeader(newWidth);
      }
    });
    if (divHeaderRef.current) {
      resizeObserver.observe(divHeaderRef.current);
    }

    return () => {
      if (divHeaderRef.current) {
        resizeObserver.disconnect();
      }
    };
  }, []);

  return (
    <div ref={divHeaderRef}>
      <div className="flex items-end justify-between gap-4 flex-wrap">
        <div className="flex flex-wrap gap-4 ">
          <BasicDateRangePicker
            value={depositeDate?.map((item) => {
              return item ? moment(item) : undefined;
            })}
            title="入金日"
            className="!h-10"
            onChange={(e) => {
              const deposit_start_date = e?.[0] ? moment(e?.[0])?.format('YYYY/MM/DD') : undefined;
              const deposit_end_date = e?.[1] ? moment(e?.[1])?.format('YYYY/MM/DD') : undefined;

              setDepositeDate([deposit_start_date, deposit_end_date]);
            }}
          />
          <BasicSelect
            value={currencyType ? Number(currencyType) : undefined}
            title="入金種別"
            className="!h-10 w-[240px]"
            placeholder="すべて"
            options={newOptionsCurrencyType}
            allowClear
            onChange={(e) => setCurrencyType(e)}
          />
          <BasicInput
            style={{
              width: '240px',
              height: '40px',
            }}
            // title={`${TEXT_TITLE.Deposit_Id}, ${TEXT_TITLE.Customer_ID_Customer_name}`}
            title={'得意先ID'}
            placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
            value={searchBusinessPartnerCodeValue}
            onChange={(val) => {
              setSearchBusinessPartnerCodeValue(val.target.value);
            }}
          />

          <div className="">
            <p className="mb-2 text-[13px] leading-4 font-medium">{TEXT_TITLE.Customer}</p>
            <BasicButton
              className="!border-[#EBE9FA] !w-[230px] flex justify-between !px-[11px] items-center !text-[rgba(0,0,0,0.3)] !bg-white hover:opacity:40"
              onClick={() => {
                if (currentCompany?.business_partner_name) {
                  setCurrentCompany(undefined);
                  setParamSearch({
                    ...paramSearch,
                    page: 1,
                    business_partner_id: undefined,
                    business_partner_code: undefined,
                  });
                  return;
                }
                onOpenModalSelectCompany();
              }}
            >
              <span className="truncate">{currentCompany?.business_partner_name ?? 'すべて'}</span>
              {currentCompany?.business_partner_name ? (
                <CloseSVG width="18" height="18" />
              ) : (
                <Image src={SelectIcon} alt="select icon" className="w-5 h-5" preview={false} />
              )}
            </BasicButton>
          </div>
          <BasicInput
            style={{
              width: '280px',
              height: '40px',
            }}
            title={'入金伝票ID, 旅行IDまたはツアー名'}
            placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
            value={searchTourValue}
            onChange={(val) => {
              setSearchTourValue(val.target.value);
            }}
          />
          <div className="flex items-end">
            <BasicButton
              icon={<SearchSVG colorSvg="white" />}
              className="flex items-center w-[120px]"
              styleType="accept"
              onClick={handleSearchByValue}
            >
              <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
            </BasicButton>
          </div>
        </div>

        <div
          className={`flex gap-x-[12px]`}
          style={{
            width: widthHeader < MAX_WIDTH_HEADER ? '100%' : undefined,
            justifyContent: widthHeader < MAX_WIDTH_HEADER ? 'end' : undefined,
          }}
        >
          <BasicButton
            icon={<UploadOutlined style={{ color: '#EC980C' }} />}
            className="flex items-center !text-[#EC980C]"
            styleType="noneOutLine"
            onClick={onImportCsv}
          >
            {TEXT_ACTION.CSV_Import}
          </BasicButton>
          <BasicButton
            icon={<DownloadOutlined style={{ color: '#3997C8' }} />}
            className="flex items-center "
            styleType="outline"
            onClick={onExportCsv}
          >
            会計連携
          </BasicButton>
          <BasicButton
            icon={<PlusOutlined />}
            className="flex items-center"
            styleType="accept"
            onClick={() => {
              history.push('/accounting-management/deposit-management/creation');
            }}
          >
            {TEXT_ACTION.CREATE_NEW}
          </BasicButton>
        </div>
      </div>

      <BasicFormModal
        ref={refModalSelectCompany}
        content={
          <FormSelectCompany
            defaultParams={{ is_use_sale: 1 }}
            onSelect={(v) => {
              setCurrentCompany(v);
              setParamSearch({
                ...paramSearch,
                page: 1,
                business_partner_id: v?.id,
                business_partner_code: v?.business_partner_code,
              });
            }}
            onClose={() => refModalSelectCompany.current.close()}
          />
        }
        className="!w-[1200px] [&_.ant-modal-body]:!px-2"
        title="取引先名"
        isValidate={true}
        hideListButton={true}
      />
      <BasicFormModal
        ref={refModalAccountingCollaboration}
        content={
          <FormAccountingCollaboration
            onReloadData={onFetchData}
            onClose={() => refModalAccountingCollaboration.current.close()}
            apiGetList={listReportDepositInvoice}
            apiMarkInvoiceItem={markReportedDepositInvoiceItem}
            type="deposit"
          />
        }
        className="!w-[1400px] [&_.ant-modal-body]:!px-2"
        title="会計連携"
        isValidate={true}
        hideListButton={true}
      />
      <CommonModalImportCsv
        confirm={onFetchData}
        ref={refImportCsv}
        type={EMenuType['deposit-invoice']}
      />
    </div>
  );
};

export default HeaderAction;
