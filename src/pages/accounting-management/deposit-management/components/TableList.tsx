import type { BaseParams } from '@/@types/request';
import { cancelDepositSlip, type DepositSlipListItem } from '@/apis/accounting/depositManagement';
import type { CurrencyMasterDetailType } from '@/apis/master/currencyType';
import IconCancel from '@/assets/imgs/common-icons/cancel.svg';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import IconEdit from '@/assets/imgs/common-icons/edit-blue.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicTable from '@/components/Commons/BasicTable';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { formatMoney } from '@/utils';
import { Image, Popconfirm, Popover } from 'antd';
import moment from 'moment';
import { history } from 'umi';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  isLoading,
  dataSource,
  paramSearch,
  setParamSearch,
  total,
  optionsCurrencyType,
  onFetchData,
}: {
  isLoading: boolean;
  dataSource: DepositSlipListItem[];
  paramSearch?: BaseParams;
  setParamSearch?: (val: BaseParams) => void;
  total: number;
  optionsCurrencyType: CurrencyMasterDetailType[];
  onFetchData?: () => void;
}) => {
  const confirmDeleteRow = async (id: number) => {
    try {
      const resDelete = await cancelDepositSlip(Number(id));
      if (resDelete.status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        onFetchData?.();
      } else if (
        resDelete?.error?.data?.errors?.deposit_id === 'Deposit Slip is linked with accounting'
      ) {
        openNotificationFail(MESSAGE_ALERT.DELETE_NEED_ACCOUNTING_NOT_LINKED);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      console.log('onHandleDelete error', error);
    }
  };

  const onHandleRevert = async (id: number) => {
    try {
      history.push(`/accounting-management/deposit-management/edit/${id}?isRevert=1`);
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      console.log('onHandleRevert error', error);
    }
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'key',
      key: 'key',
      width: 70,
    },
    {
      title: '入金伝票ID',
      dataIndex: 'deposit_slip_id',
      key: 'deposit_slip_id',
      render: (_, record) => <span>{record?.deposit_slip_id}</span>,
      width: 120,
    },
    {
      title: '入金日',
      dataIndex: 'deposit_date',
      key: 'deposit_date',
      render: (_, record) => (
        <span>{record?.deposit_date ? moment(record?.deposit_date).format('YYYY/MM/DD') : ''}</span>
      ),
      width: 120,
    },
    {
      title: '得意先ID',
      dataIndex: 'business_partner_code',
      key: 'business_partner_code',
      width: 120,
    },
    {
      title: '得意先名',
      dataIndex: 'business_partner_name',
      key: 'business_partner_name',
      width: 280,
    },
    {
      title: '入金種別',
      dataIndex: 'currency_type_master_id',
      key: 'currency_type_master_id',
      render: (_, record) => (
        <div>
          {optionsCurrencyType && record.currency_type_master_id
            ? optionsCurrencyType.find((item) => item.id === record.currency_type_master_id)
                ?.currency_type_name
            : ''}
        </div>
      ),
      width: 180,
    },
    {
      title: '入金額',
      dataIndex: 'deposit_amount',
      key: 'deposit_amount',
      render: (_, record) => (
        <div className="text-right">
          {record.deposit_amount !== null ? formatMoney(record?.deposit_amount) : 0}
        </div>
      ),
      width: 140,
    },
    {
      title: '手数料',
      dataIndex: 'fee',
      key: 'fee',
      render: (_, record) => (
        <div className="text-right">{record.fee !== null ? formatMoney(record?.fee) : 0}</div>
      ),
      width: 140,
    },
    {
      title: '合計金額',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (_, record) => (
        <div className="text-right">
          {record.total_amount !== null ? formatMoney(record?.total_amount) : 0}
        </div>
      ),
      width: 140,
    },
    {
      title: '会計連携済',
      dataIndex: 'accounting_linked',
      key: 'accounting_linked',
      render: (_, record) => (
        <p className="text-xs !ml-1">{record.accounting_linked === 1 ? '済' : '未'}</p>
      ),
      width: 140,
    },

    {
      title: '',
      dataIndex: 'groupAction',
      key: 'groupAction',
      width: 280,
      render: (_, record) => (
        <div className="flex gap-4">
          <BasicButton
            onClick={() =>
              history.push(
                `/accounting-management/deposit-management/edit/${record?.id}?exclude_zero_value=${record.is_cancelled}`,
              )
            }
            styleType="danger"
            className="!h-[24px] w-[76px] border-[transparent] hover:!border-[#225DE0] !text-[#225DE0]"
          >
            <Image preview={false} src={IconEdit} width={12} height={13} />
            <p className="text-xs !ml-1">{TEXT_ACTION.EDIT}</p>
          </BasicButton>
          {record.accounting_linked === 1 &&
          !(record.is_cancelled || record.is_inversed || record?.deposit_slip_inverse_id) ? (
            <Popconfirm
              title={
                <div className="whitespace-pre-line">{MESSAGE_ALERT.CONFIRM_CANCEL_DOCUMENT}</div>
              }
              onConfirm={() => onHandleRevert(record.id)}
              okText={TEXT_ACTION.CANCEL_DOCUMENT}
              cancelText={TEXT_ACTION.CANCEL}
            >
              <BasicButton
                styleType="danger"
                className="!h-[24px] w-[98px] border-[transparent] hover:!border-[#FDAF2E] !text-[#FDAF2E]"
              >
                <Image preview={false} src={IconCancel} width={13} height={13} />
                <p className="text-xs !ml-1">{TEXT_ACTION.CANCEL_DOCUMENT}</p>
              </BasicButton>
            </Popconfirm>
          ) : (
            <Popover
              content={
                <>
                  {record.is_inversed === 1 || record?.deposit_slip_inverse_id
                    ? MESSAGE_ALERT.ALREADY_CANCEL
                    : record.is_cancelled === 1
                    ? MESSAGE_ALERT.ALREADY_DELETE
                    : MESSAGE_ALERT.CANCEL_NEED_ACCOUNTING_LINKED}
                </>
              }
              trigger="hover"
            >
              <BasicButton
                styleType="danger"
                className="!h-[24px] w-[98px] !cursor-not-allowed !border-[transparent] !text-[#FDAF2E] !bg-[#f5f5f5]"
              >
                <Image preview={false} src={IconCancel} width={13} height={13} />
                <p className="text-xs !ml-1">{TEXT_ACTION.CANCEL_DOCUMENT}</p>
              </BasicButton>
            </Popover>
          )}
          {record.accounting_linked !== 1 &&
          !(record.is_cancelled || record.is_inversed || record?.deposit_slip_inverse_id) ? (
            <Popconfirm
              title={
                <div className="whitespace-pre-line">{MESSAGE_ALERT.CONFIRM_DELETE_DOCUMENT}</div>
              }
              onConfirm={() => confirmDeleteRow(record.id)}
              okText={TEXT_ACTION.DELETE_DOCUMENT}
              cancelText={TEXT_ACTION.CANCEL}
            >
              <BasicButton
                styleType="danger"
                className="!h-[24px] w-[98px] border-[transparent] hover:!border-[#ff4d4f] !text-[#ff4d4f]"
              >
                <Image preview={false} src={IconDelete} width={15} height={14} />
                <p className="text-xs !ml-1">{TEXT_ACTION.DELETE_DOCUMENT}</p>
              </BasicButton>
            </Popconfirm>
          ) : (
            <Popover
              content={
                <>
                  {record.is_inversed === 1 || record?.deposit_slip_inverse_id
                    ? MESSAGE_ALERT.ALREADY_CANCEL
                    : record.is_cancelled === 1
                    ? MESSAGE_ALERT.ALREADY_DELETE
                    : MESSAGE_ALERT.DELETE_NEED_ACCOUNTING_NOT_LINKED}
                </>
              }
              trigger="hover"
            >
              <BasicButton
                styleType="danger"
                className="!h-[24px] w-[98px]  !cursor-not-allowed !border-[transparent] !text-[#ff4d4f] !bg-[#f5f5f5]"
              >
                <Image preview={false} src={IconDelete} width={15} height={14} />
                <p className="text-xs !ml-1">{TEXT_ACTION.DELETE_DOCUMENT}</p>
              </BasicButton>
            </Popover>
          )}
        </div>
      ),
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1500 },
          loading: isLoading,
          columns: defaultColumns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
        }}
        page={Number(paramSearch.page) as number}
        pageSize={Number(paramSearch.limit) as number}
        onChangePage={(p: number) => {
          setParamSearch({ ...paramSearch, page: p });
        }}
        total={total}
        onSelectPageSize={(v) => setParamSearch({ ...paramSearch, limit: v })}
      />
    </>
  );
};

export default TableList;
