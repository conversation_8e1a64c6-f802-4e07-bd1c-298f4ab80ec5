import type { BusinessPartnerDetailType } from '@/@types/businessPartner';

import type {
  DataDepositSlipDetailType,
  DepositSlipItemType,
} from '@/apis/accounting/depositManagement';
import {
  createDepositSlip,
  updateDepositSlip,
  getDetailDepositSlip,
  getListDepositSlipItem,
  cancelDepositSlip,
  inverseDepositSlip,
} from '@/apis/accounting/depositManagement';
import type { ParamSearchListSaleInvoice } from '@/apis/accounting/saleManagement';
import type { AccountingTravelListType } from '@/apis/accounting/traveList';
import IconBack from '@/assets/imgs/common-icons/back-btn.svg';
import IconCoin from '@/assets/imgs/common-icons/coins-01.svg';
import IconSave from '@/assets/imgs/common-icons/save-white-btn.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import BasicModal from '@/components/Commons/BasicModal';
import PageContainer from '@/components/Commons/Page/Container';
import FormSelectCompany from '@/components/Form/FormSelectCompany';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { Form, Image } from 'antd';
import moment from 'moment';
import { useLocation } from 'react-router';
import { useEffect, useMemo, useRef, useState } from 'react';
import { history, useParams } from 'umi';
import HeaderAction from './components/HeaderAction';
import HeaderInformation from './components/HeaderInformation';
import TableTax from './components/TableTax';
import { BaseParams } from '@/@types/request';

interface RowDataSources {
  key: number;
  tour_name: string;
  departure_date?: string | Date;
  sale_invoice_id: number;
  voucher_posting_date: string | Date;
  total_amount: number;
  business_partner_id: number;
  sales_amount?: null;
  accounting_linked?: number;
  return_date?: string | Date;
  sale_destination?: string | Date;
  deposit_slip_id?: number;
  deposit_amount?: number;
  current_deposit_amount?: number;
  total_deposit_amount?: number;
  unpaid_amount?: number;
  travel?: AccountingTravelListType;
  isDelete?: boolean;
  id?: number;
  status_cancel?: number;
  sale_invoice_code: string;
  travel_code: string;
  previous_deposit_amount?: string | number;
  deposit_slip_item_id?: number;
  is_reversed?: number;
  reverse_item_id?: number;
}

const ITEM_PER_PAGE = 30;

const InvoicesPage = () => {
  const paramUrl = useParams();
  const depositInvoiceId = (paramUrl as { id: string })?.id;
  const { query } = useLocation() as any;
  const excludeZeroValue = (query as { exclude_zero_value: string })?.exclude_zero_value;
  const isRevertQuery = (query as { isRevert: string })?.isRevert;
  const [form] = Form.useForm();
  const [formDeposit] = Form.useForm();
  const depositAmount = Form.useWatch('deposit_amount', formDeposit);
  const depositFee = Form.useWatch('fee', formDeposit);
  const refModalConfirmChangePage = useRef(null);
  const refModalConfirmSaveData = useRef(null);
  const refModalSelectCompany = useRef(null);
  const refModalConfirmChangeCompany = useRef(null);
  const refModalDataChangedBeforeUpdate = useRef(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentCompany, setCurrentCompany] = useState<BusinessPartnerDetailType>();
  const [dataSource, setDataSource] = useState<RowDataSources[]>([]);
  const [listItemChange, setListItemChange] = useState<string[]>([]);
  const [sumTotalAmountReceived, setSumTotalAmountReceived] = useState<number>(0);
  const [sumPreviousDepositAmount, setSumPreviousDepositAmount] = useState<number>(0);
  const [sumCurrentDepositAmount, setSumCurrentDepositAmount] = useState<number>(0);
  const [sumCurrentDepositAmountCurrentPage, setSumCurrentDepositAmountCurrentPage] =
    useState<number>(0);
  const [sumUnpaidAmount, setSumUnpaidAmount] = useState<number>(0);
  const [paramSearch, setParamSearch] = useState<ParamSearchListSaleInvoice>({
    limit: ITEM_PER_PAGE,
    page: 1,
    exclude_zero_value: excludeZeroValue === '1' ? undefined : true,
    is_unpaid_amount: depositInvoiceId ? undefined : true,
  });
  const [params, setParams] = useState<BaseParams>({
    page: 1,
    limit: ITEM_PER_PAGE,
  });

  const [isRevert, setIsRevert] = useState<boolean>(isRevertQuery === '1' ? true : false);

  const [dataDetailDeposit, setDataDetailDeposit] = useState<DataDepositSlipDetailType>();

  const [isFirstTimeLoadData, setIsFirstTimeLoadData] = useState<boolean>(true);
  const [dataWithNoParams, setDataWithNoParams] = useState<RowDataSources[]>([]);
  const [allFormData, setAllFormData] = useState<Record<string, any>>({});

  useEffect(() => {
    formDeposit.setFieldValue('deposit_date', moment());
  }, []);

  const isEditDepositPage = useMemo(() => {
    if (depositInvoiceId) return true;
    return false;
  }, [depositInvoiceId]);

  const isEditPage = useMemo(() => {
    if (dataDetailDeposit?.is_cancelled === 1 || dataDetailDeposit?.is_inversed === 1) return false;
    return true;
  }, [dataDetailDeposit]);

  useEffect(() => {
    const unblock =
      listItemChange.length > 0
        ? history.block((a, b) => {
            if (
              !isEditDepositPage &&
              a.pathname.includes('accounting-management/deposit-management/edit') &&
              b === 'REPLACE'
            ) {
            } else {
              return TEXT_WARNING.leave_page_and_lose_changes;
            }
          })
        : null;

    return () => {
      if (unblock) {
        unblock?.();
      }
    };
  }, [listItemChange, history]);

  const handleChangeCompany = (company: BusinessPartnerDetailType) => {
    if (company?.id === currentCompany?.id) return;
    setIsFirstTimeLoadData(true);
    setCurrentCompany(company);
  };

  const refreshDataItem = () => {
    form.resetFields();
    setListItemChange([]);
  };

  const formatDataDeposit = async (invoice_items: RowDataSources[]) => {
    if (invoice_items) {
      const listBlockNew = [];
      const listCanDeleteAndReverseNew = [];
      const newFormData = {};
      invoice_items.forEach((item, index) => {
        if (item.is_reversed === 1 || item.reverse_item_id) {
          listBlockNew.push(index + 1);
        }
        if (item.accounting_linked === 1 && item?.deposit_slip_item_id) {
          listCanDeleteAndReverseNew.push(item?.deposit_slip_item_id);
        }
        Object.keys(item).forEach((key) => {
          const keyForm = `${item.key}.${key}`;
          form.setFieldValue(keyForm, item[key]);
          newFormData[keyForm] = item[key];
        });
      });
      setAllFormData(newFormData);
    }
  };

  const formatDataSource = ({
    dataListDepositItem,
    isLinked,
    isFirstTimeLoad,
    newSourceAll,
    isHandleRevert = false,
  }: {
    dataListDepositItem: DepositSlipItemType[];
    isLinked?: number | null;
    isFirstTimeLoad?: boolean;
    newSourceAll?: RowDataSources[];
    isHandleRevert?: boolean;
  }) => {
    if (isFirstTimeLoad) {
      const newSource = dataListDepositItem.map((item, index) => {
        const total_amount = item?.total_amount ?? 0;
        const deposit_amount = item?.deposit_amount ?? 0;
        const current_deposit_amount =
          (item?.current_deposit_amount && item?.current_deposit_amount !== 0) || depositInvoiceId
            ? item?.current_deposit_amount * (isHandleRevert ? -1 : 1)
            : undefined;
        const previous_deposit_amount =
          deposit_amount + (isHandleRevert ? (current_deposit_amount ?? 0) * -1 : 0);

        const total_deposit_amount = previous_deposit_amount + deposit_amount;
        const unpaid_amount =
          total_amount - (current_deposit_amount ?? 0) - previous_deposit_amount;

        return {
          ...item,
          isDelete: item.is_reversed === 1 || item.reverse_item_id ? true : false,
          isDisableForm: isLinked,
          key: index + 1,
          tour_name: item?.tour_name,
          total_amount: total_amount,
          previous_deposit_amount: previous_deposit_amount,
          current_deposit_amount: current_deposit_amount,
          total_deposit_amount: total_deposit_amount,
          unpaid_amount: unpaid_amount,
          accounting_linked: isLinked ?? 0,
          sale_accounting_linked: item?.accounting_linked,
        };
      });
      return newSource;
    } else {
      const dataClone = newSourceAll?.length > 0 ? [...newSourceAll] : [...dataWithNoParams];
      const newSource = dataClone.filter((dataAllItem) => {
        const itemIncludeDataSource = dataListDepositItem.find(
          (depositItem) => depositItem.sale_invoice_id === dataAllItem.sale_invoice_id,
        );
        if (itemIncludeDataSource) return true;
        return false;
      });
      return newSource;
    }
  };

  const onFetchListDeposit = async (companyId: number) => {
    setIsLoading(true);
    if (isFirstTimeLoadData) {
      refreshDataItem();
    }
    try {
      let newSourceAll = [];
      //fetch all data - call 1 times
      if (isFirstTimeLoadData) {
        const resGetAllListSaleInvoice = await getListDepositSlipItem({
          business_partner_id: companyId,
        });
        if (resGetAllListSaleInvoice.status === STATUS_CODE.SUCCESSFUL) {
          const resData = resGetAllListSaleInvoice.data?.data;
          [...newSourceAll] = formatDataSource({
            dataListDepositItem: resData,
            isLinked: 0,
            isFirstTimeLoad: true,
          });
          setDataWithNoParams([...newSourceAll]);
          formatDataDeposit([...newSourceAll]);
          setIsFirstTimeLoadData(false);
        } else {
          openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
        }
      }
      //fetch by params
      const resGetListSaleInvoice = await getListDepositSlipItem({
        ...paramSearch,
        business_partner_id: companyId,
      });
      if (resGetListSaleInvoice.status === STATUS_CODE.SUCCESSFUL) {
        const resData = resGetListSaleInvoice.data?.data;
        const newSource = formatDataSource({
          dataListDepositItem: resData,
          isFirstTimeLoad: false,
          newSourceAll,
        });
        setDataSource(newSource);
      } else {
        openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
      }
    } catch (error) {
      console.log('error', error);
      openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
    }
    setIsLoading(false);
  };

  const onFetchDetailDepositInvoice = async (id: string) => {
    setIsLoading(true);

    try {
      let newSourceAll = [];
      //fetch all data - call 1 times

      if (isFirstTimeLoadData) {
        refreshDataItem();
        const detailResponseAll = await getDetailDepositSlip({
          id: Number(id),
        });
        if (detailResponseAll?.data?.data?.id) {
          const dataDepositResponseAll = detailResponseAll?.data?.data;
          setDataDetailDeposit(dataDepositResponseAll);
          newSourceAll = formatDataSource({
            dataListDepositItem: dataDepositResponseAll.items,
            isLinked: isRevert ? 0 : dataDepositResponseAll.accounting_linked,
            isFirstTimeLoad: true,
            isHandleRevert: isRevert,
          });

          setDataWithNoParams([...newSourceAll]);
          formatDataDeposit([...newSourceAll]);
          setIsFirstTimeLoadData(false);
        } else {
          openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
          return;
        }
      }
      //fetch with params
      const detailResponse = await getDetailDepositSlip({
        ...paramSearch,
        id: Number(id),
      });
      if (detailResponse?.data?.data?.id) {
        const dataDepositResponse = detailResponse?.data?.data;
        handleChangeCompany(dataDepositResponse?.business_partner);
        formDeposit.setFieldsValue({
          memo: isRevert ? '取消済み伝票' : dataDepositResponse?.memo,
          fee: (dataDepositResponse?.fee ?? 0) * (isRevert ? -1 : 1),
          deposit_amount: (dataDepositResponse?.deposit_amount ?? 0) * (isRevert ? -1 : 1),
          currency_type_master_id: dataDepositResponse?.currency_type_master_id,
          accounting_linked: dataDepositResponse?.accounting_linked,
          deposit_date: dataDepositResponse?.deposit_date
            ? moment(dataDepositResponse?.deposit_date)
            : moment(),
        });

        const newSource = formatDataSource({
          dataListDepositItem: dataDepositResponse.items,
          isFirstTimeLoad: false,
          newSourceAll,
        });
        setDataSource([...newSource]);
      } else {
        openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
      }
    } catch (error) {
      console.log('error', error);
      openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (currentCompany?.id && !depositInvoiceId) {
      setParams({ ...params, page: 1 });
      onFetchListDeposit(currentCompany?.id);
    }
  }, [
    currentCompany?.id,
    depositInvoiceId,
    paramSearch?.keyword,
    paramSearch?.departure_date,
    paramSearch?.return_date,
    paramSearch?.is_unpaid_amount,
  ]);

  useEffect(() => {
    if (depositInvoiceId) {
      setParams({ ...params, page: 1 });
      onFetchDetailDepositInvoice(depositInvoiceId);
    }
  }, [
    depositInvoiceId,
    paramSearch?.keyword,
    paramSearch?.departure_date,
    paramSearch?.return_date,
    paramSearch?.is_unpaid_amount,
  ]);

  const confirmDeleteRow = async () => {
    try {
      const resDelete = await cancelDepositSlip(Number(depositInvoiceId));
      if (resDelete.status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        history.replace(
          `/accounting-management/deposit-management/edit/${depositInvoiceId}?exclude_zero_value=1`,
        );
        window.location.reload();
      } else if (
        resDelete?.error?.data?.errors?.deposit_id === 'Deposit Slip is linked with accounting'
      ) {
        openNotificationFail(MESSAGE_ALERT.CANCEL_NEED_ACCOUNTING_LINKED);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      console.log('onHandleDelete error', error);
    }
  };

  const onHandleRevert = async () => {
    try {
      const resRevert = await inverseDepositSlip(Number(depositInvoiceId));
      if (resRevert.status === STATUS_CODE.SUCCESSFUL) {
        setIsFirstTimeLoadData(true);
        setIsRevert(false);
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        setTimeout(() => {
          history.push(
            `/accounting-management/deposit-management/edit/${resRevert.data?.data?.deposit_id}`,
          );
        }, 500);
      } else if (
        resRevert?.error?.data?.errors?.deposit_id === 'Deposit Slip is not linked with accounting'
      ) {
        openNotificationFail(MESSAGE_ALERT.CANCEL_NEED_ACCOUNTING_LINKED);
      } else if (
        resRevert?.error?.data?.errors?.deposit_id === 'Deposit Slip is already inversed'
      ) {
        openNotificationFail(MESSAGE_ALERT.CANCEL_NEED_ACCOUNTING_LINKED);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      console.log('onHandleRevert error', error);
    }
  };

  const onSave = async () => {
    setIsLoading(true);
    try {
      if (isRevert) {
        onHandleRevert();
        return;
      }
      const valueDeposit = await formDeposit.validateFields();

      // Start Update
      if (Number(depositAmount ?? 0) + Number(depositFee ?? 0) !== sumCurrentDepositAmount) {
        openNotificationFail(MESSAGE_ALERT.SUM_AMOUNT_AND_FEE_NOT_EQUAL_SUM_CURRENT_AMOUNT);
        setIsLoading(false);
        return;
      }

      await form.validateFields();
      const resultObj = Object.keys(allFormData).reduce((acc, key) => {
        const [index, property] = key.split('.');
        const value = allFormData[key];

        if (!acc[index]) {
          acc[index] = {};
        }

        acc[index][property] = value;

        return acc;
      }, {});

      const newData = [...dataWithNoParams];

      Object.keys(resultObj).forEach((key) => {
        const index = dataWithNoParams.findIndex((item) => item.key === Number(key));
        newData[index] = { ...dataWithNoParams[index], ...resultObj[key] };
      });

      // Filter data change
      const dataChange = newData.map((item) => {
        return {
          deposit_slip_item_id: item?.deposit_slip_item_id,
          sale_invoice_id: item?.sale_invoice_id,
          current_deposit_amount:
            item?.current_deposit_amount !== undefined ? item?.current_deposit_amount : 0,
        };
      });

      let saveItems;
      if (depositInvoiceId) {
        saveItems = {
          updates: [...dataChange],
        };
      } else {
        saveItems = [...dataChange];
      }

      const dataSubmit = {
        ...valueDeposit,
        business_partner_id: currentCompany?.id,
        deposit_date: moment(valueDeposit.deposit_date).format('YYYY-MM-DD'),
        items: saveItems,
        fee: Number(depositFee ?? 0),
      };
      if (depositInvoiceId) {
        dataSubmit.id = Number(depositInvoiceId);
      }
      const handleSaveApi = depositInvoiceId ? updateDepositSlip : createDepositSlip;
      const responseSave = await handleSaveApi(dataSubmit);

      if (
        responseSave.status === STATUS_CODE.SUCCESSFUL ||
        responseSave.status === STATUS_CODE.CREATED
      ) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        // Reset
        setListItemChange([]);
        setIsLoading(false);

        if (depositInvoiceId) {
          onFetchDetailDepositInvoice(depositInvoiceId);
        } else {
          history.replace(
            `/accounting-management/deposit-management/edit/${responseSave.data?.data?.id}`,
          );
        }
      } else if (responseSave.status === STATUS_CODE.CONFLICT) {
        refModalDataChangedBeforeUpdate.current.open();
      } else {
        openNotificationFail(
          responseSave?.error?.data?.errors?.message ?? MESSAGE_ALERT.EDIT_FAILED,
        );
      }
      // End update
    } catch (errInfo) {
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      setIsLoading(false);
      console.log('Save failed:', errInfo);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    let current_deposit_amount: number = 0;
    dataWithNoParams.forEach((item) => {
      const keyIndex = item.key;
      current_deposit_amount += Number(allFormData[`${keyIndex}.current_deposit_amount`] ?? 0);
    });
    setSumCurrentDepositAmount(current_deposit_amount);
  }, [listItemChange, dataWithNoParams, isRevert, allFormData]);

  const dataSourceWithPageLimit = useMemo(() => {
    const dataSplice = [...dataSource].slice(
      (Number(params.page) - 1) * Number(params.limit),
      Number(params.page) * Number(params.limit),
    );
    return dataSplice;
  }, [dataSource, params.page, params.limit]);

  const sumItemNotInDataSource = () => {
    const otherListChangeNotInDataSource = dataWithNoParams.filter((item) => {
      const isSameKey = dataSourceWithPageLimit.findIndex((itemSource) => {
        return Number(itemSource.key) === Number(item.key);
      });
      return isSameKey === -1;
    });
    let result = 0;
    otherListChangeNotInDataSource.forEach((item) => {
      result += Number(allFormData[`${item.key}.current_deposit_amount`] ?? 0);
    });
    return result;
  };

  const handleAutoAllocate = () => {
    const sumOtherItem = sumItemNotInDataSource();
    let totalDepositAmount = Number(depositAmount ?? 0) + Number(depositFee ?? 0) - sumOtherItem;
    const newListItemChange = [...listItemChange];
    let newAllForm = { ...allFormData };
    dataSourceWithPageLimit.forEach((item) => {
      newListItemChange.push(item.key.toString());

      if (item.isDelete) {
        return true;
      }
      const totalAmountReceived = Number(item.total_amount ?? 0);
      const previousDepositAmount = Number(item.previous_deposit_amount ?? 0);
      const unpaidAmount = totalAmountReceived - previousDepositAmount;
      if (unpaidAmount >= 0) {
        const currentDepositAmount = Math.min(totalDepositAmount, unpaidAmount);
        form.setFieldValue(`${item.key}.current_deposit_amount`, currentDepositAmount);
        form.setFieldValue(
          `${item.key}.total_deposit_amount`,
          previousDepositAmount + currentDepositAmount,
        );
        form.setFieldValue(
          `${item.key}.unpaid_amount`,
          totalAmountReceived - (previousDepositAmount + currentDepositAmount),
        );
        newAllForm = {
          ...newAllForm,
          [`${item.key}.current_deposit_amount`]: currentDepositAmount,
        };
        totalDepositAmount -= currentDepositAmount;
      } else {
        const currentDepositAmount = totalDepositAmount;
        form.setFieldValue(
          `${item.key}.current_deposit_amount`,
          currentDepositAmount === 0 ? undefined : currentDepositAmount,
        );
        form.setFieldValue(
          `${item.key}.total_deposit_amount`,
          previousDepositAmount + currentDepositAmount,
        );
        form.setFieldValue(
          `${item.key}.unpaid_amount`,
          totalAmountReceived - (previousDepositAmount + currentDepositAmount),
        );
        newAllForm = {
          ...newAllForm,
          [`${item.key}.current_deposit_amount`]:
            currentDepositAmount === 0 ? undefined : currentDepositAmount,
        };
        totalDepositAmount -= currentDepositAmount;
      }

      return true;
    });
    setAllFormData({
      ...newAllForm,
    });
    setListItemChange([...newListItemChange]);
  };

  const onDaftRevert = () => {
    setIsRevert(true);
    const newSourceAll = formatDataSource({
      dataListDepositItem: dataDetailDeposit.items,
      isLinked: dataDetailDeposit.accounting_linked,
      isFirstTimeLoad: true,
      isHandleRevert: true,
    });
    formDeposit.setFieldsValue({
      fee: dataDetailDeposit?.fee * -1,
      deposit_amount: dataDetailDeposit?.deposit_amount * -1,
      accounting_linked: 0,
      memo: '取消済み伝票',
    });

    formatDataDeposit([...newSourceAll]);
  };

  useEffect(() => {
    let total_amount: number = 0;
    let previous_deposit_amount: number = 0;
    let current_deposit_amount_current_page: number = 0;
    let unpaid_amount: number = 0;
    dataSourceWithPageLimit.forEach((item) => {
      const keyIndex = item.key;
      total_amount += Number(form.getFieldValue(`${keyIndex}.total_amount`) ?? 0);
      previous_deposit_amount += Number(
        form.getFieldValue(`${keyIndex}.previous_deposit_amount`) ?? 0,
      );
      current_deposit_amount_current_page += Number(
        allFormData[`${keyIndex}.current_deposit_amount`] ?? 0,
      );
      unpaid_amount += Number(form.getFieldValue(`${keyIndex}.unpaid_amount`) ?? 0);
    });
    setSumTotalAmountReceived(total_amount);
    setSumPreviousDepositAmount(previous_deposit_amount);
    setSumCurrentDepositAmountCurrentPage(current_deposit_amount_current_page);
    setSumUnpaidAmount(unpaid_amount);
  }, [dataSourceWithPageLimit, allFormData]);

  return (
    <PageContainer>
      <HeaderInformation
        form={formDeposit}
        isEditDepositPage={isEditDepositPage}
        isDisabled={!isEditPage}
        isRevert={isRevert}
        onOpenModalSelectCompany={() => {
          if (listItemChange.length === 0) {
            refModalSelectCompany.current.open();
          } else refModalConfirmChangeCompany.current.open();
        }}
        currentCompany={currentCompany}
        setCurrentCompany={handleChangeCompany}
        sumCurrentDepositAmount={sumCurrentDepositAmount}
        depositSlipID={dataDetailDeposit?.deposit_slip_id}
      />
      <HeaderAction
        listItemChange={listItemChange}
        isLinked={isEditDepositPage && dataDetailDeposit?.accounting_linked === 1}
        isDeleted={dataDetailDeposit?.is_cancelled === 1}
        isCanceled={
          dataDetailDeposit?.is_inversed === 1 || dataDetailDeposit?.deposit_slip_inverse_id
        }
        paramSearch={paramSearch}
        setParamSearch={setParamSearch}
        onDaftRevert={onDaftRevert}
        isRevert={isRevert}
        confirmDeleteRow={confirmDeleteRow}
        depositInvoiceId={depositInvoiceId}
      />
      <div className="p-2 rounded-xl bg-white mt-6">
        <TableTax
          isEditPage={isEditPage}
          isLoading={isLoading}
          form={form}
          dataSource={dataSourceWithPageLimit}
          total={dataSource.length}
          listItemChange={listItemChange}
          setListItemChange={setListItemChange}
          sumTotalAmountReceived={sumTotalAmountReceived}
          sumPreviousDepositAmount={sumPreviousDepositAmount}
          sumCurrentDepositAmountCurrentPage={sumCurrentDepositAmountCurrentPage}
          sumUnpaidAmount={sumUnpaidAmount}
          allFormData={allFormData}
          setAllFormData={setAllFormData}
          params={params}
          setParams={setParams}
        />
      </div>

      <div className="flex gap-6 justify-center mt-10">
        <BasicButton
          styleType="noneOutLine"
          disabled={isLoading}
          className="!w-[190px]  !bg-white flex justify-center items-center"
          onClick={() => {
            history.goBack();
          }}
        >
          <Image preview={false} src={IconBack} width={8} />
          <p className="ml-2">{TEXT_ACTION.RETURN}</p>
        </BasicButton>
        <BasicButton
          disabled={isLoading || !isEditPage || isRevert}
          styleType="accept"
          className={`!w-[190px] flex justify-center items-center !bg-[#225DE0] hover:!bg-[#225DE0] !text-white ${
            isLoading || !isEditPage || isRevert ? '!bg-[gray] hover:!bg-[gray] ' : ''
          }`}
          onClick={handleAutoAllocate}
        >
          <Image preview={false} src={IconCoin} width={16} height={16} />
          <p>{TEXT_ACTION.AUTO_ALLOCATE}</p>
        </BasicButton>
        <BasicButton
          disabled={isLoading}
          styleType="accept"
          className={`!w-[190px] flex justify-center items-center ${
            isLoading ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
          }`}
          onClick={onSave}
        >
          <Image preview={false} src={IconSave} width={12} height={14} />
          <p>{TEXT_ACTION.SAVE}</p>
        </BasicButton>
      </div>

      <BasicFormModal
        ref={refModalSelectCompany}
        content={
          <FormSelectCompany
            defaultParams={{ is_use_sale: 1 }}
            onSelect={handleChangeCompany}
            onClose={() => refModalSelectCompany.current.close()}
            target={3}
          />
        }
        className="!w-[1200px] [&_.ant-modal-body]:!px-2"
        title="取引先名"
        isValidate={true}
        hideListButton={true}
      />
      <BasicModal
        ref={refModalConfirmChangePage}
        title={'警告'}
        content={<>{MESSAGE_ALERT.CONFIRM_CHANGE_PAGE}</>}
        okText="ページを変更する"
        onSubmit={() => {
          setListItemChange([]);
          refModalConfirmChangePage.current?.close();
          history.replace('/accounting-management/deposit-management');
        }}
      />

      <BasicModal
        ref={refModalConfirmChangeCompany}
        title={'警告'}
        content={<>この変更により、入力したすべてのデータが更新されます。続行しますか?</>}
        okText="同意する"
        onSubmit={() => {
          refreshDataItem();
          refModalConfirmChangeCompany.current?.close();
          refModalSelectCompany.current?.open();
        }}
      />
      <BasicModal
        ref={refModalConfirmSaveData}
        title={'警告'}
        content={
          <>
            選択したデータには新たにデータが追加されました。この操作を実行する前に、データの保存を実行してください。
          </>
        }
        okText="理解した"
        hideCloseButton={true}
        onSubmit={() => {
          refModalConfirmSaveData.current?.close();
        }}
      />
      <BasicModal
        ref={refModalDataChangedBeforeUpdate}
        title={'警告'}
        content={<>データが変更されました。続行する前にデータを再読み込みしてください。</>}
        okText="リロード"
        hideCloseButton={true}
        onSubmit={() => {
          history.go(0);
          refModalDataChangedBeforeUpdate.current?.close();
        }}
      />
    </PageContainer>
  );
};

export default InvoicesPage;
