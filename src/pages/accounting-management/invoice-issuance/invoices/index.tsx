import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import type { SaleInvoiceItemType } from '@/apis/accounting/saleManagement';
import {
  getAccountingTravelDetail,
  type AccountingTravelListType,
} from '@/apis/accounting/traveList';
import type { AggregationItemDetailType } from '@/apis/master/AggregationItem';
import { getListAggregationItem } from '@/apis/master/AggregationItem';
import type { CompanyType } from '@/apis/master/companyMaster';
import type { SubjectMasterDetailType } from '@/apis/master/subjectMaster';
import { getListSubjectMaster } from '@/apis/master/subjectMaster';
import type { BasicFormModalRef } from '@/components/Commons/BasicFormModal';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import PageContainer from '@/components/Commons/Page/Container';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_WARNING } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { Form, Spin } from 'antd';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useLocation } from 'react-router';
import { connect, history, useParams } from 'umi';
import HeaderInformation from './components/HeaderInformation';
import ListButtonAction from './components/ListButtonAction';
import ListModal from './components/ListModal';
import PreviewInvoice from './components/PreviewInvoice';
import TableTax from './components/TableTax';
import type { DatumGetListTaxCategory } from '@/apis/master/taxCategory';
import { getListTaxCategory } from '@/apis/master/taxCategory';
import type { DetailInvoiceIssuance } from '@/apis/accounting/invoiceIssuance';
import {
  createInvoiceIssuance,
  generateInvoiceID,
  getDepositAmountByTourAndBP,
  getDetailInvoiceIssuance,
  updateInvoiceIssuance,
} from '@/apis/accounting/invoiceIssuance';
import { getListUsers, UserItem } from '@/apis/users';
import { getListBusinessPartner } from '@/apis/businessPartner';

export interface DetailRowItemSaleInvoiceType {
  key: number;
  subject_id: string;
  product_name: string;
  product_name_en: string;
  sale_date: string;
  use_at: string;
  unit_price: number;
  quantity: number;
  amount_including_tax: number;
  id: number;
  memo?: string;
  subjectName?: string;
  subjectNameEn?: string;
  categoryTaxName?: string;
  sale_invoice_item_id?: string;
  consolidated_invoice_item_id?: number;
  tax_rate?: number;
  is_exists?: number;
  consumption_tax?: number;
  tax_category_code?: number;
}

const InvoicesPage = ({ currentUser }) => {
  const [form] = Form.useForm();
  const [formHeaderInformation] = Form.useForm();
  const refModalSelectTour = useRef<BasicFormModalRef>();
  const refModalPreviewInvoice = useRef<BasicFormModalRef>();
  const refModalSelectSaleItem = useRef<BasicFormModalRef>();
  const refModalSelectSignatureStamp = useRef<BasicFormModalRef>();
  const refModalConfirmSaveData = useRef<BasicFormModalRef>(null);
  const refModalSelectCompany = useRef<BasicFormModalRef>(null);
  const refModalConfirmChangeTour = useRef<BasicFormModalRef>(null);
  const refModalDataChangedBeforeUpdate = useRef(null);

  const [isEditReInvoice, setIsEditReInvoice] = useState<boolean>(false);
  const [isDisableInvoice, setIsDisableInvoice] = useState<boolean>(false);

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [currentTour, setCurrentTour] = useState<AccountingTravelListType>();
  const [currentCompany, setCurrentCompany] = useState<BusinessPartnerDetailType>();
  const [dataSource, setDataSource] = useState<DetailRowItemSaleInvoiceType[]>([]);
  const [listItemChange, setListItemChange] = useState<number[]>([]);
  const [countItemDeleted, setCountItemDeleted] = useState<number>(0);
  const [dataSummarySubject, setDataSummarySubject] = useState<AggregationItemDetailType[]>();
  const [dataAccountCodeList, setDataAccountCodeList] = useState<SubjectMasterDetailType[]>();
  const [dataTaxCategory, setDataTaxCategory] = useState<DatumGetListTaxCategory[]>();
  const [signatureStamp, setSignatureStamp] = useState<{
    seal: string;
    logo: string;
    company: CompanyType;
    language: number;
    optionInvoicePrintf: number;
  }>();
  const paramUrl = useParams();
  const { query } = useLocation() as any;
  const invoiceIssuanceId = (paramUrl as { id: string })?.id;
  const [valueSubmit, setValueSubmit] = useState<any>({});
  const [isItemExist, setIsItemExist] = useState<boolean>(false);
  const [detailIssuanceInvoice, setDetailIssuanceInvoice] = useState<DetailInvoiceIssuance>();
  const [urlFilePDF, setUrlFilePDF] = useState<string>();
  const [invoiceIdByGenerate, setInvoiceIdByGenerate] = useState<string>();
  const [listManager, setListManager] = useState<any[]>([]);

  const handleGetListAccount = async () => {
    try {
      const { data, status } = await getListUsers({ limit: 999, page: 1 });
      if (status === 200) {
        const optionsManager = data?.data?.map((item: UserItem) => {
          return {
            value: item.id,
            label: item.name,
          };
        });
        setListManager(optionsManager);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const handleGetInvoiceID = async () => {
    const resGetInvoiceID = await generateInvoiceID();
    if (resGetInvoiceID.status === STATUS_CODE.SUCCESSFUL) {
      setInvoiceIdByGenerate(resGetInvoiceID?.data?.data);
    }
  };

  const handleGetListSummarySubject = async () => {
    const res = await getListAggregationItem({ limit: 'all' });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setDataSummarySubject(res?.data?.data);
    }
  };
  const handleGetListAccountMaster = async () => {
    const res = await getListSubjectMaster({ limit: 'all', order: 'asc', sort: 'id' });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setDataAccountCodeList(res?.data?.data);
    }
  };
  const handleGetTaxCategoryMaster = async () => {
    const res = await getListTaxCategory({
      limit: 'all',
      order: 'asc',
      sort: 'tax_category_code',
    });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setDataTaxCategory(res?.data?.data);
    }
  };

  const handleGetDepositAmount = async () => {
    const res = await getDepositAmountByTourAndBP({
      travel_id: currentTour?.id,
      business_partner_id: currentCompany?.id,
    });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      formHeaderInformation.setFieldsValue({
        deposit_amount: res?.data?.data,
      });
    }
  };

  const fetchDataBusinessPartnerWithTravel = async () => {
    const resData = await getListBusinessPartner({
      page: 1,
      limit: 1,
      is_use_sale: 1,
      travel_id: currentTour?.id,
    });
    setCurrentCompany(resData?.data?.data?.[0]);
  };

  useEffect(() => {
    if (currentTour?.id && currentCompany?.id) {
      handleGetDepositAmount();
    }
  }, [currentTour, currentCompany, invoiceIssuanceId]);

  useEffect(() => {
    if (currentTour?.id && !invoiceIssuanceId) {
      if (currentTour?.admin_id) {
        formHeaderInformation.setFieldsValue({
          manager_id: currentTour?.admin_id,
        });
      } else {
        formHeaderInformation.setFieldsValue({
          manager_id: undefined,
        });
      }
      fetchDataBusinessPartnerWithTravel();
    } else if (!currentTour?.id) {
      setCurrentCompany(null);
    }
    if (!invoiceIssuanceId) {
      setIsLoading(false);
    }
  }, [currentTour, invoiceIssuanceId]);

  useEffect(() => {
    handleGetListAccount();
    handleGetListSummarySubject();
    handleGetListAccountMaster();
    handleGetTaxCategoryMaster();
    handleGetInvoiceID();
  }, []);

  //effect handle warning change page
  useEffect(() => {
    const unblock =
      listItemChange.length > 0
        ? history.block((a, b) => {
            if (
              !invoiceIssuanceId &&
              a.pathname.includes('accounting-management/invoice-issuance/edit') &&
              b === 'REPLACE'
            ) {
            } else {
              return TEXT_WARNING.leave_page_and_lose_changes;
            }
          })
        : null;

    return () => {
      if (unblock) {
        unblock?.();
      }
    };
  }, [listItemChange, history]);

  const isEditSaleInvoice = useMemo(() => {
    if (invoiceIssuanceId) return true;
    return false;
  }, [invoiceIssuanceId]);

  const formatDataSaleInvoiceItems = async (
    sale_invoice_items: SaleInvoiceItemType[],
    isGetDetail?: boolean,
  ) => {
    if (sale_invoice_items) {
      const dataSourceApi = sale_invoice_items?.map((item, index) => {
        const key = index + 1;
        const duplicateItem = dataSource?.find((itemDataSource) => item.id === itemDataSource.id);
        const subjectMaster = dataAccountCodeList.find(
          (account) => account?.id === Number(item?.subject_id),
        );
        const summaryItem = dataSummarySubject.find(
          (summary) => summary.id === Number(subjectMaster?.summary_item_id),
        );

        const subjectName = summaryItem?.summary_item_name ? summaryItem?.summary_item_name : '';
        const subjectNameEn = summaryItem?.summary_item_name_en ?? subjectName;

        const categoryTaxName = dataTaxCategory.find(
          (tax) => item.tax_category_code === tax?.id,
        )?.tax_category_name;

        return {
          key,
          is_exists: item?.is_exists,
          index: index + 1,
          subject_id: item?.subject_id,
          subjectName: subjectName,
          subjectNameEn: subjectNameEn,
          categoryTaxName: categoryTaxName,
          product_name: item?.product_name,
          product_name_en: item?.product_name_en ?? item?.product_name,
          tax_category_code: item?.tax_category_code,
          consumption_tax: item?.consumption_tax,
          sale_date: item?.sale_date,
          use_at: item?.use_at,
          unit_price: item?.unit_price,
          quantity: item?.quantity,
          amount_including_tax: item?.amount_including_tax,
          id: item?.id,
          tax_rate: item?.tax_rate,
          sale_invoice_item_id: item?.sale_invoice_item_id,
          consolidated_invoice_item_id: item?.consolidated_invoice_item_id,
          memo: duplicateItem
            ? form.getFieldValue(`${duplicateItem.key}.memo`)
            : isGetDetail
            ? item?.memo
            : undefined,
          consolidate_invoice_item_id: item?.consolidate_invoice_item_id,
        };
      });
      setDataSource([...dataSourceApi]);

      dataSourceApi.forEach((item) => {
        Object.keys(item).forEach((key) => {
          const keyForm = `${item.key}.${key}`;
          form.setFieldValue(keyForm, item[key]);
        });
      });
    }
  };

  const fetchAccountingTravelDetail = async (id) => {
    const resTravelDetail = await getAccountingTravelDetail(id);
    if (resTravelDetail.status === STATUS_CODE.SUCCESSFUL) {
      setCurrentTour(resTravelDetail?.data?.data);
    }
  };

  useEffect(() => {
    if (!invoiceIssuanceId && query.tourId) {
      fetchAccountingTravelDetail(query.tourId);
    }
  }, [invoiceIssuanceId, query.tourId]);

  const formatDetailInvoiceIssuance = (data: DetailInvoiceIssuance) => {
    const listItem: SaleInvoiceItemType[] = data?.items?.map((item) => {
      return {
        ...item.sale_invoice_item,
        consolidate_invoice_item_id: data?.consolidated_invoice_id,
        memo: item.memo,
        is_exists: 1,
      };
    });
    formatDataSaleInvoiceItems(listItem, true);
  };

  const handleGetDetailInvoiceIssuance = async (id) => {
    try {
      setIsLoading(true);
      const resDetail = await getDetailInvoiceIssuance(id);
      if (resDetail.status === STATUS_CODE.SUCCESSFUL) {
        const dataDetail = resDetail?.data?.data;
        setCurrentCompany(dataDetail?.business_partner);
        setCurrentTour(dataDetail?.travel);
        setDetailIssuanceInvoice(dataDetail);
        setIsDisableInvoice(dataDetail?.is_disable === 1 ? true : false);
        formHeaderInformation.setFieldsValue({
          title: dataDetail?.title ? Number(dataDetail?.title) : undefined,
          issued_date: dataDetail?.issued_date
            ? moment(dataDetail?.issued_date, 'YYYY-MM-DD')
            : null,
          payment_deadline: dataDetail?.payment_deadline
            ? moment(dataDetail?.payment_deadline, 'YYYY-MM-DD')
            : null,
          billing_category: dataDetail?.billing_category
            ? Number(dataDetail?.billing_category)
            : undefined,
          memo: dataDetail?.memo,
          manager_id: dataDetail?.manager?.id,
          deposit_entered: dataDetail?.deposit_entered,
        });
        formatDetailInvoiceIssuance(dataDetail);
        setIsLoading(false);
      }
    } catch (error) {
      console.error('error handleGetDetailInvoiceIssuance', error);
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (invoiceIssuanceId) {
      if (dataAccountCodeList && dataSummarySubject && dataTaxCategory) {
        handleGetDetailInvoiceIssuance(invoiceIssuanceId);
      }
    } else {
      formHeaderInformation.setFieldValue('issued_date', moment());
    }
  }, [
    invoiceIssuanceId,
    currentUser?.user?.name,
    dataAccountCodeList,
    dataSummarySubject,
    dataTaxCategory,
  ]);

  const removeRowFormData = (key) => {
    form.setFieldsValue({
      [`${key}.key`]: undefined,
      [`${key}.subject_id`]: undefined,
      [`${key}.product_name`]: undefined,
      [`${key}.sale_date`]: undefined,
      [`${key}.use_at`]: undefined,
      [`${key}.unit_price`]: undefined,
      [`${key}.quantity`]: undefined,
      [`${key}.amount_including_tax`]: undefined,
      [`${key}.id`]: undefined,
      [`${key}.memo`]: undefined,
      [`${key}.sale_invoice_item_id`]: undefined,
    });
  };

  const confirmDeleteRow = (key: number) => {
    setDataSource([...dataSource].filter((item) => key !== item.key));
    removeRowFormData(key);
    setListItemChange(listItemChange.filter((item) => key !== item));
    setCountItemDeleted(countItemDeleted + 1);
  };

  const onPreSave = async () => {
    setIsLoading(true);
    try {
      const valueHeaderInformation = await formHeaderInformation.validateFields();
      if (dataSource.length === 0) {
        openNotificationFail('アイテムは作成されていません');
        setIsLoading(false);
        return;
      }

      const valueItem = [...dataSource].map((item) => {
        const valueRe = {
          ...item,
          sale_invoice_item_id: item.id,
          memo: form.getFieldValue(`${item.key}.memo`),
        };
        if (invoiceIssuanceId) {
          valueRe['consolidated_invoice_item_id'] = Number(item.consolidated_invoice_item_id);
        }
        return {
          ...valueRe,
        };
      });

      const managerName = listManager.find(
        (item) => item.value === valueHeaderInformation.manager_id,
      )?.label;
      setValueSubmit({
        valueHeaderInformation,
        valueItem,
        currentCompany,
        currentTour,
        managerName,
      });
      refModalSelectSignatureStamp.current.open();

      // End update
    } catch (errInfo) {
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      setIsLoading(false);
      console.log('Save failed:', errInfo);
    }
    setIsLoading(false);
  };

  const onSave = async (urlPdf?: string) => {
    setIsLoading(true);
    try {
      if (!urlPdf && !urlFilePDF && !detailIssuanceInvoice?.pictures?.[0]?.path) {
        onPreSave();
        return;
      }
      //Start create
      const dataSubmit = {
        items: valueSubmit?.valueItem ?? [],
        billing_category: valueSubmit.valueHeaderInformation?.billing_category,
        manager_id: valueSubmit.valueHeaderInformation?.manager_id,
        travel_id: currentTour?.id,
        business_partner_id: currentCompany?.id,
        issued_date: valueSubmit?.valueHeaderInformation?.issued_date
          ? moment(valueSubmit.valueHeaderInformation.issued_date).format('YYYY-MM-DD')
          : null,
        title: valueSubmit?.valueHeaderInformation?.title,
        payment_deadline: valueSubmit?.valueHeaderInformation?.payment_deadline
          ? moment(valueSubmit.valueHeaderInformation.payment_deadline).format('YYYY-MM-DD')
          : null,
        seal: signatureStamp?.seal,
        logo: signatureStamp?.logo,
        option_invoice_print: signatureStamp?.optionInvoicePrintf,
        option_language: signatureStamp?.language,
        memo: valueSubmit?.valueHeaderInformation?.memo,
        images: [urlPdf ?? urlFilePDF ?? detailIssuanceInvoice?.pictures?.[0]?.path],
        consolidated_invoice_id: invoiceIdByGenerate,
        deposit_amount: valueSubmit?.valueHeaderInformation?.deposit_amount,
        deposit_entered: valueSubmit?.valueHeaderInformation?.deposit_entered,
      };

      if (isEditReInvoice) {
        dataSubmit['re_invoice_id'] = invoiceIssuanceId;
      } else if (invoiceIssuanceId) {
        dataSubmit['id'] = invoiceIssuanceId;
      }

      const handleSaveApi =
        invoiceIssuanceId && !isEditReInvoice ? updateInvoiceIssuance : createInvoiceIssuance;
      const resApi = await handleSaveApi(dataSubmit);
      if (resApi.status === STATUS_CODE.SUCCESSFUL || resApi.status === STATUS_CODE.CREATED) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        refModalPreviewInvoice.current.close();
        if (invoiceIssuanceId && !isEditReInvoice) {
          handleGetDetailInvoiceIssuance(invoiceIssuanceId);
        } else {
          history.replace(`/accounting-management/invoice-issuance/edit/${resApi.data?.data?.id}`);
        }
      } else if (resApi.status === STATUS_CODE.CONFLICT) {
        openNotificationFail('Request No. already exists, please check the data and try again.');
        handleGetInvoiceID();
      } else {
        if (invoiceIssuanceId) {
          openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
        } else {
          openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
        }
      }

      //TODO: Call API create
      // End create
    } catch (errInfo) {
      if (invoiceIssuanceId) {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      } else {
        openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
      }
      setIsLoading(false);
      console.log('Save failed:', errInfo);
    }
    setIsLoading(false);
  };

  const onCreateItem = () => {
    if (!currentCompany || !currentTour) {
      window.alert(MESSAGE_ALERT.CREATE_INVOICE_ITEM_NO_HAVE_TOUR_AND_COMPANY);
      return;
    }
    refModalSelectSaleItem?.current?.open();
  };

  const refreshDataItem = () => {
    if (dataSource.length !== 0) {
      setDataSource([]);
    }
    setListItemChange([]);
  };

  const onSelectSaleItem = (val: SaleInvoiceItemType[]) => {
    formatDataSaleInvoiceItems(val, true);
  };

  useEffect(() => {
    if (dataSource) {
      const isHasItemExist = dataSource.some((item) => item?.is_exists === 1);
      setIsItemExist(isHasItemExist);
    }
  }, [dataSource]);

  const onFetchDataDetail = () => {
    setIsEditReInvoice(false);
    if (invoiceIssuanceId) {
      handleGetDetailInvoiceIssuance(invoiceIssuanceId);
    }
  };

  const handleEditDetailInvoice = () => {
    const VoidCategory = 2;
    formHeaderInformation.setFieldsValue({ billing_category: VoidCategory });
    setIsEditReInvoice(true);
  };

  return (
    <Spin spinning={isLoading}>
      <PageContainer>
        <HeaderInformation
          listManager={listManager}
          setCurrentTour={setCurrentTour}
          setCurrentCompany={setCurrentCompany}
          isEditSaleInvoice={isEditSaleInvoice}
          onOpenModalSelectTour={() => {
            if (listItemChange.length === 0) {
              refModalSelectTour.current.open();
            } else refModalConfirmChangeTour.current.open();
          }}
          currentTour={currentTour}
          onOpenModalSelectCompany={() => {
            refModalSelectCompany.current.open();
          }}
          currentCompany={currentCompany}
          form={formHeaderInformation}
          onCreateItem={onCreateItem}
          detailIssuanceInvoice={detailIssuanceInvoice}
          isEditReInvoice={isEditReInvoice}
          onFetchDataDetail={onFetchDataDetail}
          handleEditDetailInvoice={handleEditDetailInvoice}
          isDisableInvoice={isDisableInvoice}
        />
        <div className="p-2 rounded-xl bg-white mt-6">
          <TableTax
            isLoading={isLoading}
            form={form}
            dataSource={dataSource}
            setDataSource={setDataSource}
            setListItemChange={setListItemChange}
            confirmDeleteRow={confirmDeleteRow}
            isEditReInvoice={isEditReInvoice}
          />
        </div>
        <ListButtonAction
          isLoading={isLoading}
          onSave={onPreSave}
          detailIssuanceInvoice={detailIssuanceInvoice}
          isDisableInvoice={isDisableInvoice}
        />
        <BasicFormModal
          ref={refModalPreviewInvoice}
          content={
            <PreviewInvoice
              invoiceIdByGenerate={invoiceIdByGenerate}
              detailIssuanceInvoice={detailIssuanceInvoice}
              valueSubmit={valueSubmit}
              isItemExist={isItemExist}
              signatureStamp={signatureStamp}
              formHeaderInformation={formHeaderInformation}
              onSelect={(url) => {
                setUrlFilePDF(url);
                onSave(url);
              }}
              onClose={() => {
                refModalPreviewInvoice.current.close();
              }}
              isEditReInvoice={isEditReInvoice}
            />
          }
          className="!w-[1160px] [&_.ant-modal-body]:!px-2 [&_.ant-modal-content]:max-h-[90vh] [&_.ant-modal-content]:overflow-y-auto"
          title=""
          isValidate={true}
          hideListButton={true}
        />

        <ListModal
          refModalSelectSignatureStamp={refModalSelectSignatureStamp}
          refModalSelectSaleItem={refModalSelectSaleItem}
          refModalSelectTour={refModalSelectTour}
          refModalSelectCompany={refModalSelectCompany}
          refModalConfirmChangeTour={refModalConfirmChangeTour}
          refModalConfirmSaveData={refModalConfirmSaveData}
          refModalDataChangedBeforeUpdate={refModalDataChangedBeforeUpdate}
          currentCompany={currentCompany}
          currentTour={currentTour}
          onSelectSaleItem={onSelectSaleItem}
          dataSummarySubject={dataSummarySubject}
          dataAccountCodeList={dataAccountCodeList}
          dataSource={dataSource}
          setCurrentTour={setCurrentTour}
          setCurrentCompany={setCurrentCompany}
          refreshDataItem={refreshDataItem}
          setSignatureStamp={(value) => {
            refModalSelectSignatureStamp.current.close();
            setSignatureStamp(value);
            refModalPreviewInvoice.current.open();
          }}
          detailIssuanceInvoice={detailIssuanceInvoice}
        />
      </PageContainer>
    </Spin>
  );
};

export default connect(({ user }) => ({
  currentUser: user.currentUser,
}))(InvoicesPage);
