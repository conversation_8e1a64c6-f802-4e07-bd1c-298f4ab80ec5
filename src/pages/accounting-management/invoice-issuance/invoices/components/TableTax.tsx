import type { AggregationItemDetailType } from '@/apis/master/AggregationItem';
import type { SubjectMasterDetailType } from '@/apis/master/subjectMaster';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicInput from '@/components/Commons/BasicInput';
import BasicModal from '@/components/Commons/BasicModal';
import BasicTable from '@/components/Commons/BasicTable';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION } from '@/constants/commonText';
import { formatMoney, roundNumber } from '@/utils';
import { MenuOutlined } from '@ant-design/icons';
import type { FormInstance } from 'antd';
import { Image, Popconfirm } from 'antd';
import { arrayMoveImmutable } from 'array-move';
import moment from 'moment';
import React, { useMemo, useRef } from 'react';
import type { SortableContainerProps, SortEnd } from 'react-sortable-hoc';
import { SortableContainer, SortableElement, SortableHandle } from 'react-sortable-hoc';

const DragHandle = SortableHandle(() => <MenuOutlined style={{ cursor: 'grab', color: '#999' }} />);

const SortableItem = SortableElement((props: React.HTMLAttributes<HTMLTableRowElement>) => (
  <tr {...props} />
));
const SortableBody: React.ReactNode = SortableContainer(
  (props: React.HTMLAttributes<HTMLTableSectionElement>) => <tbody {...props} />,
);

interface TableTaxProps {
  isLoading: boolean;
  form: FormInstance<any>;
  dataSource: any[];
  setDataSource: (val: any[]) => void;
  setListItemChange: (val: number[]) => void;
  confirmDeleteRow: (key: number) => void;
  isEditReInvoice: boolean;
}

const TableTax: React.FC<TableTaxProps> = ({
  isLoading,
  form,
  dataSource,
  setDataSource,
  setListItemChange,
  confirmDeleteRow,
  isEditReInvoice,
}) => {
  const refModalConfirmChangePage = useRef(null);

  const onSortEnd = ({ oldIndex, newIndex }: SortEnd) => {
    if (oldIndex !== newIndex) {
      const newData = arrayMoveImmutable(dataSource.slice(), oldIndex, newIndex).filter(
        (el: any) => !!el,
      );
      setDataSource(newData);
    }
  };

  const DraggableContainer = (props: SortableContainerProps) => (
    // @ts-ignore
    <SortableBody
      useDragHandle
      disableAutoscroll
      helperClass="row-dragging"
      onSortEnd={onSortEnd}
      {...props}
    />
  );

  const DraggableBodyRow: React.FC<any> = ({ className, style, ...restProps }) => {
    // function findIndex base on Table rowKey props and should always be a right array index
    const index = dataSource.findIndex((x) => x.index === restProps['data-row-key']);
    // @ts-ignore
    return <SortableItem index={index} {...restProps} />;
  };
  const defaultColumns = [
    {
      title: '',
      dataIndex: 'sort',
      width: 60,
      className: 'drag-visible',
      // @ts-ignore
      render: () => <DragHandle />,
    },
    {
      title: '#',
      dataIndex: 'stt',
      key: 'stt',
      width: 60,
      render: (_, __, index) => {
        return <span>{index + 1}</span>;
      },
    },
    {
      title: '集計科目',
      dataIndex: 'subjectName',
      key: 'subjectName',
      width: 120,
    },
    {
      title: '品名',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 180,
    },
    {
      title: '利用日',
      dataIndex: 'use_at',
      key: 'use_at',
      width: 120,
      sorter: (a, b) => {
        if (!a.use_at) return -1;
        if (!b.use_at) return 1;
        return moment(a.use_at).unix() - moment(b.use_at).unix();
      },
    },
    {
      title: '単価',
      dataIndex: 'unit_price',
      key: 'unit_price',
      width: 100,
      render: (_, record) => (
        <div className="text-right">
          {record.unit_price || record.unit_price === 0 ? formatMoney(record.unit_price) : ''}
        </div>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      render: (_, record) => (
        <div className="text-right">
          {record.quantity || record.quantity === 0 ? formatMoney(record.quantity) : ''}
        </div>
      ),
    },
    {
      title: '金額',
      dataIndex: 'amount_including_tax',
      key: 'amount_including_tax',
      width: 100,
      render: (_, record) => (
        <div className="text-right">
          {record.amount_including_tax || record.amount_including_tax === 0
            ? formatMoney(roundNumber(record.amount_including_tax))
            : ''}
        </div>
      ),
    },

    {
      title: '備考',
      dataIndex: 'memo',
      key: 'memo',
      render: (_, record) => {
        return (
          <div>
            <BasicInput
              defaultValue={form.getFieldValue(`${record.key}.memo`)}
              onBlur={(val) => form.setFieldValue(`${record.key}.memo`, val.currentTarget.value)}
            />
          </div>
        );
      },
    },
    {
      title: '請求書番号',
      dataIndex: 'consolidate_invoice_item_id',
      key: 'consolidate_invoice_item_id',
      width: 120,
    },
  ];

  const columns = useMemo(() => {
    if (!isEditReInvoice) {
      return defaultColumns;
    } else {
      const newColumns = [...defaultColumns];
      newColumns.push({
        title: '',
        dataIndex: 'action',
        key: 'action',
        width: 120,
        render: (_, record) => {
          return (
            <Popconfirm
              title={
                <div className="whitespace-pre-line">このアイテムを削除してもよろしいですか?</div>
              }
              onConfirm={() => confirmDeleteRow(record.key)}
              okText={TEXT_ACTION.DELETE}
              cancelText={TEXT_ACTION.CANCEL}
            >
              <BasicButton
                styleType="danger"
                className="!h-[24px] w-[98px] border-[transparent] hover:!border-[#ff4d4f] !text-[#ff4d4f]"
              >
                <Image preview={false} src={IconDelete} width={15} height={14} />
                <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
              </BasicButton>
            </Popconfirm>
          );
        },
      });
      return newColumns;
    }
  }, [isEditReInvoice, dataSource]);

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1400, y: 800 },
          loading: isLoading,
          components: {
            body: {
              wrapper: DraggableContainer,
              row: DraggableBodyRow,
            },
          },
          columns: columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'key',
        }}
        hasPagination={false}
      />
      <BasicModal
        ref={refModalConfirmChangePage}
        title={'警告'}
        content={<>{MESSAGE_ALERT.CONFIRM_CHANGE_PAGE}</>}
        okText="ページを変更する"
        onSubmit={() => {
          setListItemChange([]);
          refModalConfirmChangePage.current?.close();
        }}
      />
    </>
  );
};

export default TableTax;
