import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import { DetailInvoiceIssuance } from '@/apis/accounting/invoiceIssuance';
import {
  getListAccountingTravel,
  type AccountingTravelListType,
} from '@/apis/accounting/traveList';
import { getListBusinessPartner } from '@/apis/businessPartner';
import IconEdit from '@/assets/imgs/common-icons/edit-blue.svg';
import SelectIcon from '@/assets/imgs/common-icons/select-icon.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import BasicNumbericInput from '@/components/Commons/BasicNumbericInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTextArea from '@/components/Commons/BasicTextArea';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { useDebounce } from '@uidotdev/usehooks';
import type { FormInstance } from 'antd';
import { Form, Image } from 'antd';
import moment from 'moment';
import type { FieldData } from 'rc-field-form/lib/interface';
import { useEffect, useState } from 'react';

const HeaderInformation = ({
  onOpenModalSelectTour,
  onOpenModalSelectCompany,
  currentCompany,
  currentTour,
  isEditSaleInvoice,
  setCurrentTour,
  setCurrentCompany,
  onCreateItem,
  form,
  detailIssuanceInvoice,
  listManager,
  isEditReInvoice,
  onFetchDataDetail,
  handleEditDetailInvoice,
  isDisableInvoice,
}: {
  onOpenModalSelectTour: () => void;
  currentTour: AccountingTravelListType;
  onOpenModalSelectCompany: () => void;
  currentCompany: BusinessPartnerDetailType;
  isEditSaleInvoice: boolean;
  setCurrentTour: (val: AccountingTravelListType) => void;
  setCurrentCompany: (val: BusinessPartnerDetailType) => void;
  onCreateItem: () => void;
  form: FormInstance<any>;
  detailIssuanceInvoice?: DetailInvoiceIssuance;
  listManager: any[];
  isEditReInvoice: boolean;
  onFetchDataDetail: () => void;
  handleEditDetailInvoice: () => void;
  isDisableInvoice: boolean;
}) => {
  const billingCategory = Form.useWatch('billing_category', form);
  const [oldBillingCategory, setOldBillingCategory] = useState<number>(billingCategory);
  const [textSearchTour, setTextSearchTour] = useState<string>(currentTour?.travel_id ?? '');
  const [textSearchCompany, setTextSearchCompany] = useState<string>(
    currentCompany?.business_partner_code ?? '',
  );
  const codeTourToSearch = useDebounce(textSearchTour, 500);
  const codeCompanyToSearch = useDebounce(textSearchCompany, 500);

  useEffect(() => {
    if (currentCompany?.business_partner_code) {
      setTextSearchCompany(currentCompany.business_partner_code);
    } else {
      setTextSearchCompany('');
    }
  }, [currentCompany]);

  useEffect(() => {
    if (currentTour?.travel_id) {
      setTextSearchTour(currentTour.travel_id);
    }
  }, [currentTour]);

  useEffect(() => {
    const currentMemo = form.getFieldValue('memo');
    if (billingCategory === 2 && (!currentMemo || !currentMemo?.includes('は無効とします'))) {
      form.setFieldValue('memo', 'No.〇〇は無効とします。');
    }
    if (billingCategory === 1 && oldBillingCategory === 2) {
      form.setFieldValue('memo', undefined);
    }
    setOldBillingCategory(billingCategory);
  }, [billingCategory]);

  const fetchDataTour = async () => {
    const resData = await getListAccountingTravel({
      page: 1,
      limit: 1,
      keyword: codeTourToSearch,
    });
    if (resData?.data?.data) {
      setCurrentTour(resData.data.data[0]);
    } else {
      setCurrentTour(undefined);
    }
  };

  useEffect(() => {
    if (codeTourToSearch.length > 6) {
      fetchDataTour();
    } else {
      setCurrentTour(undefined);
    }
  }, [codeTourToSearch]);

  const fetchDataCompany = async () => {
    const resData = await getListBusinessPartner({
      page: 1,
      limit: 1,
      business_partner_code: codeCompanyToSearch,
    });
    if (resData?.data?.data && resData?.data?.total === 1) {
      setCurrentCompany(resData.data.data[0]);
    } else {
      setCurrentCompany(undefined);
    }
  };

  useEffect(() => {
    if (codeCompanyToSearch.length > 2) {
      fetchDataCompany();
    }
  }, [codeCompanyToSearch]);

  const optionsBillingCategory = [
    { value: 1, label: '通常' },
    { value: 2, label: 'VOID' },
  ];

  const optionsTitle = [
    { value: 1, label: '御中' },
    { value: 2, label: '様' },
  ];

  const oldFirstSubjectId = Form.useWatch('billing_category', form);

  const handleListItemChange = (changeField: FieldData[]) => {
    const nameFieldChange = changeField?.[0]?.name?.[0];
    const valueChange = changeField?.[0]?.value;
    if (nameFieldChange === 'billing_category') {
      if (valueChange?.toString() === oldFirstSubjectId?.toString()) {
        return;
      }
    }
  };

  return (
    <Form
      form={form}
      component={false}
      onFieldsChange={(changeField: FieldData[]) => handleListItemChange(changeField)}
    >
      <div className="form-header-information grid grid-cols-12 gap-6 ">
        <div className="col-span-2">
          {isEditSaleInvoice ? (
            <>
              <p className="mb-2 text-[13px] leading-4 font-medium">旅行ID</p>
              <span>{currentTour?.travel_id}</span>
            </>
          ) : (
            <BasicInput
              title="旅行ID"
              className="!h-10"
              placeholder="旅行ID"
              value={textSearchTour}
              onChange={(e) => setTextSearchTour(e.target.value)}
            />
          )}
        </div>
        <div className="col-span-4">
          <p className="mb-2 text-[13px] leading-4 font-medium">
            旅行 <span className="text-[red]">*</span>
          </p>
          {isEditSaleInvoice ? (
            <span>{currentTour?.tour_name ?? 'すべて'}</span>
          ) : (
            <BasicButton
              disabled={isEditSaleInvoice}
              className={`
            !border-[#EBE9FA] flex justify-between !px-[11px] items-center !w-full
             ${
               currentTour?.tour_name ? '!text-[rgba(0,0,0,0.85)]' : '!text-[rgba(0,0,0,0.3)] '
             } !bg-white hover:opacity:40 font-normal`}
              onClick={onOpenModalSelectTour}
            >
              <span>{currentTour?.tour_name ?? 'すべて'}</span>
              <Image src={SelectIcon} alt="select icon" preview={false} className="w-5 h-5" />
            </BasicButton>
          )}
        </div>
        <div className="col-span-2">
          <Form.Item
            className="!mb-0"
            name="billing_category"
            rules={[{ required: true, message: 'を入力してください' }]}
            initialValue={1}
          >
            <BasicSelect
              disabled={isDisableInvoice}
              title={TEXT_TITLE.Billing_category}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              options={optionsBillingCategory}
              required={true}
              value={form.getFieldValue('billing_category')}
              // className={`${isDisableInvoice ? '!bg-[#DCDEE3] ' : ''}`}
              style={{ backgroundColor: isDisableInvoice ? '#DCDEE3' : '' }}
            />
          </Form.Item>
        </div>
        <div className="col-span-2">
          <BasicDatePicker
            title="出発日"
            disabled={true}
            className="!h-10 !bg-[#DCDEE3]"
            placeholder="すべて"
            value={moment(currentTour?.departure_date)}
          />
        </div>
        <div className="col-span-2">
          <Form.Item
            className="!mb-0"
            name="manager_id"
            rules={[{ required: true, message: 'を入力してください' }]}
          >
            <BasicSelect
              disabled={isDisableInvoice}
              required
              title="担当者"
              placeholder="すべて"
              className="!h-10"
              options={listManager}
            />
          </Form.Item>
        </div>
        <div className="col-span-2">
          {isEditSaleInvoice ? (
            <>
              <p className="mb-2 text-[13px] leading-4 font-medium">得意先ID</p>
              <span>{currentCompany?.business_partner_code}</span>
            </>
          ) : (
            <BasicInput
              title="得意先ID"
              className="!h-10"
              placeholder="得意先ID"
              value={textSearchCompany}
              onChange={(e) => setTextSearchCompany(e.target.value)}
            />
          )}
        </div>
        <div className="col-span-4">
          <p className="mb-2 text-[13px] leading-4 font-medium">
            得意先名 <span className="text-[red]">*</span>
          </p>
          {isEditSaleInvoice ? (
            <span className="line-clamp-1">
              {currentCompany?.business_partner_name ?? 'すべて'}
            </span>
          ) : (
            <BasicButton
              disabled={isEditSaleInvoice}
              className={`
            !border-[#EBE9FA] flex justify-between !px-[11px] items-center !w-full
            ${
              currentCompany?.business_partner_name
                ? '!text-[rgba(0,0,0,0.85)]'
                : '!text-[rgba(0,0,0,0.3)] '
            } !bg-white hover:opacity:40 font-normal`}
              onClick={onOpenModalSelectCompany}
            >
              <span className="line-clamp-1">
                {currentCompany?.business_partner_name ?? 'すべて'}
              </span>
              <Image src={SelectIcon} alt="select icon" preview={false} className="w-5 h-5" />
            </BasicButton>
          )}
        </div>
        <div className="col-span-2">
          <Form.Item
            className="!mb-0"
            name="title"
            rules={[{ required: true, message: 'を入力してください' }]}
            initialValue={1}
          >
            <BasicSelect
              disabled={isDisableInvoice}
              title={TEXT_TITLE.TitleName}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              options={optionsTitle}
              required={true}
            />
          </Form.Item>
        </div>
        <div className="col-span-2">
          <Form.Item className="!mb-0" name="payment_deadline">
            <BasicDatePicker
              disabled={isDisableInvoice}
              title="支払期限"
              placeholder="YYYY / MM / DD"
              className={`!h-10 ${isDisableInvoice ? '!bg-[#DCDEE3] ' : ''}`}
            />
          </Form.Item>
        </div>
        <div className="col-span-2">
          <Form.Item
            className="!mb-0"
            name="issued_date"
            rules={[{ required: true, message: 'を入力してください' }]}
          >
            <BasicDatePicker
              disabled={isDisableInvoice}
              title="発行日"
              required
              placeholder="YYYY / MM / DD"
              className={`!h-10 ${isDisableInvoice ? '!bg-[#DCDEE3] ' : ''}`}
            />
          </Form.Item>
        </div>
        <div className="col-span-8 ">
          <Form.Item className="!mb-0" name="memo">
            <BasicTextArea
              disabled={isDisableInvoice}
              title={TEXT_TITLE.remarks}
              placeholder={TEXT_PLACEHOLDER.remarks}
              className={`!h-[112px] ${isDisableInvoice ? '!bg-[#DCDEE3] ' : ''}`}
            />
          </Form.Item>
        </div>
        <div className="col-span-4 ">
          <div className="grid grid-cols-2 gap-6">
            <div className="col-span-1 ">
              <Form.Item className="!mb-0" name="deposit_amount" initialValue={0}>
                <BasicNumbericInput
                  title="既入金額"
                  className="!h-10 !text-right !bg-[#DCDEE3]"
                  disabled
                />
              </Form.Item>
            </div>
            <div className="col-span-1 ">
              <Form.Item className="!mb-0" name="deposit_entered" initialValue={0}>
                <BasicNumbericInput
                  disabled={isDisableInvoice}
                  title="お預かり金額"
                  className={`!h-10 !text-right ${isDisableInvoice ? '!bg-[#DCDEE3] ' : ''}`}
                />
              </Form.Item>
            </div>
          </div>
          <div className="grid grid-cols-2 gap-6 mt-6">
            <div className="col-span-1 flex gap-4">
              <div className="">
                <p className="mb-2 text-[13px] leading-4 font-medium">請求書ID</p>
                <span>{detailIssuanceInvoice?.consolidated_invoice_id}</span>
              </div>
              {!isDisableInvoice && isEditSaleInvoice && (
                <>
                  {isEditReInvoice ? (
                    <BasicButton
                      styleType="danger"
                      onClick={onFetchDataDetail}
                      className=" w-[94px] hover:shadow-md hover:opacity-70 !border-[#ff4d4f] !text-[#ff4d4f] !bg-white"
                    >
                      <CloseOutlined style={{ color: '#FF0000', fontSize: 12 }} />
                      <p className="text-xs !ml-1">{TEXT_ACTION.CANCEL}</p>
                    </BasicButton>
                  ) : (
                    <BasicButton
                      onClick={handleEditDetailInvoice}
                      styleType="noneOutLine"
                      className="w-[74px] !h-full flex justify-center items-center !border-[#225DE0] !bg-white hover:shadow-md hover:opacity-70 "
                    >
                      <Image preview={false} src={IconEdit} width={12} height={13} />
                      <p className="text-xs !ml-1 !text-[#225DE0]">編集</p>
                    </BasicButton>
                  )}
                </>
              )}
            </div>
            <div className="col-span-1 flex justify-end">
              <BasicButton
                disabled={!isEditReInvoice && isEditSaleInvoice}
                icon={<PlusOutlined />}
                className={`flex justify-center items-center ${
                  !isEditReInvoice && isEditSaleInvoice
                    ? '!bg-[gray] hover:!bg-[gray] !text-white '
                    : ''
                }`}
                styleType={!isEditSaleInvoice || isEditReInvoice ? 'accept' : undefined}
                onClick={onCreateItem}
              >
                {TEXT_ACTION.ADD_SALE}
              </BasicButton>
            </div>
          </div>
        </div>
      </div>
    </Form>
  );
};

export default HeaderInformation;
