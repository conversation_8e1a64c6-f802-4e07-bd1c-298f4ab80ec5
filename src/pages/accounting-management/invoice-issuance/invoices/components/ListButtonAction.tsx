import IconCancelRed from '@/assets/imgs/common-icons/cancel-red.svg';
import IconTickWhite from '@/assets/imgs/common-icons/tick_white.svg';
import BasicButton from '@/components/Commons/BasicButton';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_TITLE } from '@/constants/commonText';
import { DownloadOutlined } from '@ant-design/icons';
import { Image } from 'antd';
import moment from 'moment';
import { history } from 'umi';

const ListButtonAction = ({ isLoading, detailIssuanceInvoice, onSave, isDisableInvoice }) => {
  const exportPDF = async () => {
    try {
      const urlPdf = detailIssuanceInvoice?.pictures?.[0]?.path;
      if (urlPdf) {
        const a = document.createElement('a');
        a.href = urlPdf;
        a.target = '_blank';
        a.download = `${moment().format('YYYYMMDDHHmmss')}_請求書_${
          detailIssuanceInvoice?.consolidated_invoice_id
        }.pdf`;
        a.click();
        window.URL.revokeObjectURL(urlPdf);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      console.log('onHandleDelete error', error);
    }
  };

  return (
    <div className="flex gap-6 justify-center mt-10">
      <BasicButton
        styleType="noneOutLine"
        disabled={isLoading}
        className="w-[186px] flex items-center justify-center rounded-[4px] shadow-md !bg-white hover:opacity-40 font-medium !text-[#FF3B30] !border-[#DCDEE3]"
        onClick={() => {
          history.goBack();
        }}
      >
        <Image preview={false} src={IconCancelRed} width={18} height={18} />
        <p className="ml-2">{TEXT_ACTION.CANCEL}</p>
      </BasicButton>
      {detailIssuanceInvoice && (
        <BasicButton
          disabled={isLoading}
          styleType="accept"
          className={`w-[186px] flex items-center justify-center rounded-[4px] shadow-md
        hover:opacity-40 font-medium !text-white
       !bg-[#225DE0] hover:!bg-[#225DE0] `}
          onClick={() => exportPDF()}
        >
          <DownloadOutlined style={{ color: 'white' }} />
          <p>{TEXT_ACTION.DOWNLOAD}</p>
        </BasicButton>
      )}
      <BasicButton
        disabled={isLoading || isDisableInvoice}
        styleType={isDisableInvoice ? undefined : 'accept'}
        className={`!w-[186px] flex justify-center items-center ${
          isDisableInvoice ? '!bg-[gray] hover:!bg-[gray] !text-white ' : ''
        }`}
        onClick={onSave}
      >
        <Image preview={false} src={IconTickWhite} width={18} height={18} />
        <p>{TEXT_TITLE.Invoicing}</p>
      </BasicButton>
    </div>
  );
};

export default ListButtonAction;
