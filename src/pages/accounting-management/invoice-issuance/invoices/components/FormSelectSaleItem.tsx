import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import type { BaseParams } from '@/@types/request';
import { getListSaleItemByTourAndBP } from '@/apis/accounting/invoiceIssuance';
import type { SaleInvoiceItemType } from '@/apis/accounting/saleManagement';
import type { AccountingTravelListType } from '@/apis/accounting/traveList';
import type { AggregationItemDetailType } from '@/apis/master/AggregationItem';
import type { SubjectMasterDetailType } from '@/apis/master/subjectMaster';
import IconCancelRed from '@/assets/imgs/common-icons/cancel-red.svg';
import IconTickWhite from '@/assets/imgs/common-icons/tick_white.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicCheckboxGroup from '@/components/Commons/BasicCheckboxGroup';
import BasicInput from '@/components/Commons/BasicInput';
import BasicTable from '@/components/Commons/BasicTable';
import { openNotificationFail } from '@/components/Notification';
import SearchSVG from '@/components/SVG/SearchSVG';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION } from '@/constants/commonText';
import { formatMoney, roundNumber } from '@/utils';
import { Image } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import type { DetailRowItemSaleInvoiceType } from '..';

const FormSelectSaleItem = ({
  onClose,
  onSelect,
  currentCompany,
  currentTour,
  dataSummarySubject,
  dataAccountCodeList,
  listDataSelected,
}: {
  onClose: () => void;
  onSelect: (value: SaleInvoiceItemType[]) => void;
  currentCompany: BusinessPartnerDetailType;
  currentTour: AccountingTravelListType;
  dataSummarySubject: AggregationItemDetailType[];
  dataAccountCodeList: SubjectMasterDetailType[];
  listDataSelected: DetailRowItemSaleInvoiceType[];
}) => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<SaleInvoiceItemType[]>([]);
  const [params, setParams] = useState<BaseParams>({});
  const [isInvoiceNotIssued, setIsInvoiceNotIssued] = useState<boolean>(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  useEffect(() => {
    if (listDataSelected) {
      const listKey = listDataSelected.map((item) => item.id);
      setSelectedRowKeys(listKey);
    }
  }, [listDataSelected]);

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    setSelectedRowKeys([...newSelectedRowKeys]);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };
  const defaultColumns = [
    {
      title: '集計科目名',
      dataIndex: 'subject_id',
      width: 120,
      key: 'subject_id',
      render: (_, record) => {
        const subjectMaster = dataAccountCodeList.find(
          (account) => account?.id === Number(record?.subject_id),
        );
        const summaryItem = dataSummarySubject.find(
          (summary) => summary.id === Number(subjectMaster?.summary_item_id),
        );
        return <span>{summaryItem?.summary_item_name ? summaryItem?.summary_item_name : ''}</span>;
      },
    },
    {
      title: '品名',
      dataIndex: 'product_name',
      key: 'product_name',
    },
    {
      title: '利用日',
      dataIndex: 'use_at',
      key: 'use_at',
      width: 120,
      render: (_, record) => (
        <span>{record.use_at ? moment(record.use_at).format('YYYY/MM/DD') : ''}</span>
      ),
    },
    {
      title: '単価',
      dataIndex: 'unit_price',
      key: 'unit_price',
      width: 110,
      render: (_, record) => (
        <div className="text-right">
          {record.unit_price || record.unit_price === 0 ? formatMoney(record.unit_price) : ''}
        </div>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 80,
      render: (_, record) => (
        <div className="text-right">
          {record.quantity || record.quantity === 0 ? formatMoney(record.quantity) : ''}
        </div>
      ),
    },
    {
      title: '金額',
      dataIndex: 'amount_including_tax',
      key: 'amount_including_tax',
      width: 100,
      render: (_, record) => (
        <div className="text-right">
          {record.amount_including_tax || record.amount_including_tax === 0
            ? formatMoney(roundNumber(record.amount_including_tax))
            : ''}
        </div>
      ),
    },
    {
      title: '備考',
      dataIndex: 'memo',
      key: 'memo',
    },
    {
      title: '請求書番号',
      dataIndex: 'consolidated_invoice_code',
      key: 'consolidated_invoice_code',
      width: 120,
      render: (_, record) => (
        <p className="text-xs">
          {record.consolidated_invoices_is_disable !== 1
            ? record.consolidated_invoice_code ?? ''
            : ''}
        </p>
      ),
    },
  ];

  const fetchData = async () => {
    setIsLoading(true);
    // const resData1 = await getDetailSaleInvoice(47);
    const resData = await getListSaleItemByTourAndBP({
      travel_id: currentTour?.id,
      business_partner_id: currentCompany?.id,
      invoice_linked: isInvoiceNotIssued ? undefined : 1,
    });
    // console.log('resData', resData1);
    const listSaleItem = resData?.data?.data;
    if (listSaleItem) {
      setDataSource([...listSaleItem]);
    } else {
      openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, [params, currentTour?.id, currentCompany?.id]);

  const handleSelect = () => {
    const selectedItems = dataSource.filter((item) => selectedRowKeys.includes(item.id));
    onSelect?.(selectedItems);
    setSelectedRowKeys([]);
    onClose?.();
  };

  return (
    <>
      <div className="flex justify-between px-4">
        <div className="flex gap-4">
          <BasicInput
            style={{
              width: '304px',
              height: '40px',
            }}
            title="旅行"
            value={currentTour?.tour_name}
            disabled
          />
          <BasicInput
            style={{
              width: '304px',
              height: '40px',
            }}
            title="請求先"
            value={currentCompany?.business_partner_name}
            disabled
          />
          <div className="flex flex-col">
            <div className="h-6" />
            <div className="flex flex-1 items-center">
              <BasicCheckboxGroup
                value={isInvoiceNotIssued ? [true] : []}
                onChange={(val) => {
                  if (val.length > 0) {
                    setIsInvoiceNotIssued(true);
                  } else setIsInvoiceNotIssued(false);
                }}
                options={[{ label: '請求書未発行', value: true }]}
              />
            </div>
          </div>
          <div className="flex items-end">
            <BasicButton
              icon={<SearchSVG colorSvg="white" />}
              className="flex items-center w-[120px]"
              styleType="accept"
              onClick={() => setParams({ ...params, page: 1 })}
            >
              <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
            </BasicButton>
          </div>
        </div>
      </div>

      <div className="p-2 border border-[#DCDEE3] rounded-[12px] mt-6">
        <BasicTable
          className="!mt-0"
          tableProps={{
            scroll: { x: 1080, y: 500 },
            loading: isLoading,
            columns: defaultColumns,
            rowSelection: rowSelection,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            rowKey: 'id',
          }}
          hasPagination={false}
        />
      </div>
      <div className="flex justify-center mt-6 gap-5">
        <BasicButton
          className="w-[290px] flex items-center justify-center rounded-[4px] shadow-md !bg-white hover:opacity-40 font-medium !text-[#FF3B30] !border-[#DCDEE3]"
          onClick={onClose}
        >
          <Image preview={false} src={IconCancelRed} width={18} height={18} />
          <p className="pl-2 font-medium">{TEXT_ACTION.CLOSE}</p>
        </BasicButton>
        <BasicButton
          className="w-[290px] flex items-center justify-center rounded-[4px] shadow-md !bg-main-color hover:opacity-40 font-medium !text-white "
          onClick={handleSelect}
        >
          <Image preview={false} src={IconTickWhite} width={18} height={18} />
          <p className="pl-2 font-medium">{TEXT_ACTION.Add_to_invoice}</p>
        </BasicButton>
      </div>
    </>
  );
};

export default FormSelectSaleItem;
