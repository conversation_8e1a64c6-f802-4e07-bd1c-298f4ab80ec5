import BasicFormModal from '@/components/Commons/BasicFormModal';
import BasicModal from '@/components/Commons/BasicModal';
import FormSelectCompany from '@/components/Form/FormSelectCompany';
import FormSelectSignatureStamp from '@/components/Form/FormSelectSignatureStamp';
import { history } from 'umi';
import FormSelectTour from '../../../../../components/Form/FormSelectTour';
import FormSelectSaleItem from '../components/FormSelectSaleItem';

const ListModal = ({
  refModalSelectSaleItem,
  refModalSelectTour,
  refModalSelectCompany,
  refModalConfirmChangeTour,
  refModalConfirmSaveData,
  refModalDataChangedBeforeUpdate,
  refModalSelectSignatureStamp,
  currentCompany,
  currentTour,
  onSelectSaleItem,
  dataSummarySubject,
  dataAccountCodeList,
  dataSource,
  setCurrentTour,
  setCurrentCompany,
  setSignatureStamp,
  refreshDataItem,
  detailIssuanceInvoice,
}) => {
  return (
    <>
      <BasicFormModal
        ref={refModalSelectSignatureStamp}
        content={
          <FormSelectSignatureStamp
            detailIssuanceInvoice={detailIssuanceInvoice}
            onSelect={setSignatureStamp}
            onClose={() => {
              refModalSelectSignatureStamp.current.close();
            }}
          />
        }
        className="!w-[1300px] [&_.ant-modal-body]:!px-2"
        title="設定変更"
        isValidate={true}
        hideListButton={true}
      />
      <BasicFormModal
        ref={refModalSelectSaleItem}
        content={
          <FormSelectSaleItem
            currentCompany={currentCompany}
            currentTour={currentTour}
            onSelect={onSelectSaleItem}
            onClose={() => refModalSelectSaleItem.current.close()}
            dataSummarySubject={dataSummarySubject}
            dataAccountCodeList={dataAccountCodeList}
            listDataSelected={dataSource}
          />
        }
        className="!w-[1300px] [&_.ant-modal-body]:!px-2"
        title=""
        isValidate={true}
        hideListButton={true}
      />
      <BasicFormModal
        ref={refModalSelectTour}
        content={
          <FormSelectTour
            defaultParams={{ is_use_sale: 1 }}
            onSelect={setCurrentTour}
            onClose={() => refModalSelectTour.current.close()}
          />
        }
        className="!w-[1300px] [&_.ant-modal-body]:!px-2"
        title="旅行"
        isValidate={true}
        hideListButton={true}
      />
      <BasicFormModal
        ref={refModalSelectCompany}
        content={
          <FormSelectCompany
            defaultParams={{ is_use_sale: 1, travel_id: currentTour?.id }}
            isCompanyActive={true}
            onSelect={setCurrentCompany}
            onClose={() => refModalSelectCompany.current.close()}
          />
        }
        className="!w-[1200px] [&_.ant-modal-body]:!px-2"
        title="取引先名"
        isValidate={true}
        hideListButton={true}
      />

      <BasicModal
        ref={refModalConfirmChangeTour}
        title={'警告'}
        content={<>この変更により、入力したすべてのデータが更新されます。続行しますか?</>}
        okText="同意する"
        onSubmit={() => {
          refreshDataItem();
          refModalConfirmChangeTour.current?.close();
          refModalSelectTour.current?.open();
        }}
      />
      <BasicModal
        ref={refModalConfirmSaveData}
        title={'警告'}
        content={
          <>
            選択したデータには新たにデータが追加されました。この操作を実行する前に、データの保存を実行してください。
          </>
        }
        okText="理解した"
        hideCloseButton={true}
        onSubmit={() => {
          refModalConfirmSaveData.current?.close();
        }}
      />
      <BasicModal
        ref={refModalDataChangedBeforeUpdate}
        title={'警告'}
        content={<>データが変更されました。続行する前にデータを再読み込みしてください。</>}
        okText="リロード"
        hideCloseButton={true}
        onSubmit={() => {
          history.go(0);
          refModalDataChangedBeforeUpdate.current?.close();
        }}
      />
    </>
  );
};

export default ListModal;
