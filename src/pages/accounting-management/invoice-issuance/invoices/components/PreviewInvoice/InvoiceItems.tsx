import { formatMoney, roundNumber } from '@/utils';
import { useMemo, useState } from 'react';
import { DetailRowItemSaleInvoiceType } from '../..';
import InvoiceFooter from './InvoiceFooter';
import moment from 'moment';

const InvoiceItems = ({
  valueSubmit,
  signatureStamp,
  detailIssuanceInvoice,
  invoiceIdByGenerate,
  isEditReInvoice,
}) => {
  const MAX_LENGTH_PAGE_1 = 12;
  const COUNT_ROW = 31; // max row per page
  const listCode10Tax = [6, 7];
  const listCode08Tax = [4, 5];
  const listCode0Tax = [1, 2, 3];
  const language = signatureStamp?.language;
  const isJapanese = language === 1;
  const [isHasPage2, setIsHasPage2] = useState(false);

  function groupBySubjectId(
    items: DetailRowItemSaleInvoiceType[],
    startIndex?: number,
  ): DetailRowItemSaleInvoiceType[][] {
    const groupedItems: { [key: number]: DetailRowItemSaleInvoiceType[] } = {};
    items.forEach((item) => {
      const subjectId = isJapanese ? item.subjectName : item.subjectNameEn;
      if (!groupedItems[subjectId]) {
        groupedItems[subjectId] = [];
      }
      groupedItems[subjectId].push(item);
    });

    const newArray = Object.values(groupedItems);
    let indexArray = startIndex ?? 0;
    const arrayWhenIndex = newArray.map((item) => {
      return item.map((childItem) => {
        indexArray += 1;
        return { ...childItem, key: indexArray };
      });
    });

    // unlock to test multiPage
    // if (startIndex) {
    //   return [...arrayWhenIndex, ...arrayWhenIndex, ...arrayWhenIndex, ...arrayWhenIndex];
    // }
    return arrayWhenIndex;
  }

  const itemGroupRenderPage1 = useMemo(() => {
    if (valueSubmit?.valueItem) {
      const result = groupBySubjectId(valueSubmit?.valueItem as DetailRowItemSaleInvoiceType[]);
      // check if item length > 11 or group length > 4
      if (valueSubmit?.valueItem?.length > 11 || result.length > 4) {
        const resultPage1 = [];
        let count = 0;
        for (let i = 0; i < result.length; i++) {
          if (count + result[i].length + 2 > MAX_LENGTH_PAGE_1) {
            const resultCut = [...result[i]].slice(0, MAX_LENGTH_PAGE_1 - count);
            if (resultCut.length > 0) {
              resultPage1.push(resultCut);
            }
            setIsHasPage2(true);
            break;
          } else {
            count += result[i].length + 2;
            resultPage1.push(result[i]);
          }
        }
        return resultPage1;
      }
      return result;
    }
    return [];
  }, [valueSubmit?.valueItem]);

  const itemGroupRenderAll = useMemo(() => {
    if (valueSubmit?.valueItem && itemGroupRenderPage1) {
      const sttStart = itemGroupRenderPage1.reduce((acc, cur) => acc + cur.length, 0);
      const newItemPage1 = itemGroupRenderPage1?.flatMap((item) => item);

      const remainingArr = valueSubmit?.valueItem?.filter(
        (item) => !newItemPage1?.some((itemPage1) => itemPage1.index === item.index),
      );

      // calculate item per page
      const groupRenderRemaining = groupBySubjectId(remainingArr, sttStart);

      const pages = [];
      let currentTotal = 0;
      let currentPage = [];
      groupRenderRemaining.forEach((group, indexGroup) => {
        const newLength = currentTotal + group.length + 1;
        if (newLength <= COUNT_ROW) {
          currentPage.push(group);
          if (indexGroup === groupRenderRemaining.length - 1) {
            pages.push(currentPage);
          } else {
            currentTotal += group.length + 2;
          }
        } else {
          const count = COUNT_ROW - currentTotal - 2;
          const firstArray = group.slice(0, count - 1);
          const secondArray = group.slice(count - 1);
          currentPage.push(firstArray);
          pages.push(currentPage);
          //reset for next page
          currentTotal = secondArray.length + 2;
          currentPage = [secondArray];
        }
      });

      return pages;
    }
    return [];
  }, [valueSubmit?.valueItem, itemGroupRenderPage1]);

  const renderTypeByTaxCode = (taxCode) => {
    if (listCode0Tax.includes(taxCode)) {
      return '#';
    }
    if (listCode08Tax.includes(taxCode)) {
      return '＊';
    }
    return '';
  };

  const countGroup = (group) => {
    const count = group.reduce((acc, cur) => acc + cur.length + (cur.length ? 2 : 0), 0);
    return count;
  };

  const arrayToRenderWhenFewItem = useMemo(() => {
    const sttStart = countGroup(itemGroupRenderPage1);
    const COUNT_MAX_BY_LANGUAGE = isJapanese
      ? isHasPage2
        ? MAX_LENGTH_PAGE_1 + 2
        : MAX_LENGTH_PAGE_1 + 1
      : MAX_LENGTH_PAGE_1;
    const count = COUNT_MAX_BY_LANGUAGE - sttStart;

    if (count <= 0) return [];
    return Array.from({ length: count }, (_, index) => index);
  }, [itemGroupRenderPage1, valueSubmit?.valueItem, isJapanese, isHasPage2]);

  return (
    <div>
      <div className="table w-full border">
        <div className="w-full grid grid-cols-18 bg-[#F0F2F5]">
          <div className="col-span-1 flex pb-1 h-[46px] justify-center items-center border-r font-bold">
            No
          </div>
          <div className="col-span-5 flex pb-1 h-[46px] justify-center items-center font-bold ">
            {isJapanese ? '項目' : 'Item'}
          </div>
          <div className="col-span-1 flex h-[46px] justify-center items-center border-r " />
          <div className="col-span-2 flex h-[46px] justify-center items-center border-r font-bold text-center ">
            {isJapanese ? '単価' : 'Unit Price'}
          </div>
          <div className="col-span-2 flex h-[46px] justify-center items-center border-r font-bold text-center ">
            {isJapanese ? '数量' : 'Quantity'}
          </div>
          <div className="col-span-2 flex h-[46px] justify-center items-center border-r font-bold text-center ">
            {isJapanese ? '金額' : 'Amount'}
          </div>
          <div className="col-span-5 flex h-[46px] justify-center items-center font-bold ">
            {isJapanese ? '備考' : 'Remarks'}
          </div>
        </div>

        {itemGroupRenderPage1?.map((groupItem, childIndex) => {
          let itemSum = 0;
          let lengthItem = 0;
          return (
            <div key={`group_item_${childIndex}`}>
              {groupItem.map((childItem, indexChildItem) => {
                if (lengthItem > MAX_LENGTH_PAGE_1) return null;
                lengthItem += 1 + (indexChildItem === 0 ? 2 : 0);
                itemSum += childItem.amount_including_tax;
                const key = `${indexChildItem}_group_item_${childIndex}`;
                return (
                  <div key={key} className="w-full grid grid-cols-18 border-t">
                    <div className="col-span-1 flex min-h-[46px] px-3 items-center border-r justify-end">
                      <p className="text-right">{childItem.key}</p>
                    </div>
                    <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center ">
                      <p className="">
                        {isJapanese ? childItem?.product_name : childItem?.product_name_en}
                      </p>
                    </div>
                    <div className="col-span-1 flex min-h-[46px] px-3 border-r justify-center items-center">
                      <p>{renderTypeByTaxCode(childItem?.tax_category_code)}</p>
                    </div>
                    <div className="col-span-2 flex min-h-[46px] px-3 items-center border-r justify-end">
                      <p>{formatMoney(childItem?.unit_price)}</p>
                    </div>
                    <div className="col-span-2 flex min-h-[46px] px-3 items-center border-r justify-end">
                      <p>{formatMoney(childItem?.quantity)}</p>
                    </div>
                    <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end">
                      <p>{formatMoney(roundNumber(childItem?.amount_including_tax))}</p>
                    </div>
                    <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center">
                      <p>{childItem?.memo}</p>
                    </div>
                  </div>
                );
              })}
              <div className="w-full grid grid-cols-18 border-t">
                <div className="col-span-1 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center justify-center">
                  <p className="text-center font-bold whitespace-pre-line">
                    {groupItem?.[0]?.subjectName
                      ? `- ${
                          isJapanese
                            ? `${groupItem?.[0]?.subjectName} 合計`
                            : `Total ${groupItem?.[0]?.subjectNameEn}`
                        } -`
                      : ''}
                  </p>
                </div>
                <div className="col-span-1 flex pb-1 min-h-[46px] px-3 border-r justify-center items-center" />
                <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end">
                  <p>{groupItem?.length > 0 ? formatMoney(itemSum) : ''}</p>
                </div>
                <div className="col-span-5 flex min-h-[46px] px-3 items-center justify-end" />
              </div>
              {childIndex === itemGroupRenderPage1.length - 1 &&
              countGroup(itemGroupRenderPage1) > MAX_LENGTH_PAGE_1 + 2 ? null : (
                <div className="w-full grid grid-cols-18 border-t">
                  <div className="col-span-1 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                  <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center justify-center " />
                  <div className="col-span-1 flex pb-1 min-h-[46px] px-3 border-r justify-center items-center" />
                  <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                  <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                  <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                  <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center justify-end" />
                </div>
              )}
            </div>
          );
        })}
        {arrayToRenderWhenFewItem.map((index) => {
          return (
            <div key={`empty_${index}`} className="w-full grid grid-cols-18 border-t">
              <div className="col-span-1 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
              <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center justify-center " />
              <div className="col-span-1 flex pb-1 min-h-[46px] px-3 border-r justify-center items-center" />
              <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
              <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
              <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
              <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center justify-end" />
            </div>
          );
        })}
      </div>
      <InvoiceFooter valueSubmit={valueSubmit} signatureStamp={signatureStamp} />
      {isHasPage2 && itemGroupRenderAll?.length > 0 ? (
        <>
          {itemGroupRenderAll?.map((itemPage, index) => (
            <div
              className={`${
                index === 0 ? (isJapanese ? 'pt-[225px]' : 'pt-[233px]') : 'pt-[245px]'
              }`}
              key={`page-${index + 2}`}
            >
              <div className="flex justify-end ">
                <div className="flex flex-col gap-5 w-[220px]">
                  <div className="flex justify-between">
                    <p className="w-[80px]">{isJapanese ? '請求書No.' : 'Invoice No.'}</p>
                    <p>：</p>
                    <p className="flex flex-1 justify-end">
                      {detailIssuanceInvoice?.consolidated_invoice_id && !isEditReInvoice
                        ? detailIssuanceInvoice?.consolidated_invoice_id
                        : invoiceIdByGenerate}
                    </p>
                  </div>
                  <div className="flex justify-between">
                    <p className="w-[80px]">{isJapanese ? '旅行ID' : 'Travel ID'}</p>
                    <p>：</p>
                    <p className="flex flex-1 justify-end">{valueSubmit?.currentTour?.travel_id}</p>
                  </div>
                  <div className="flex justify-between">
                    <p className="w-[80px]">{isJapanese ? '発行日' : 'Issue Date'}</p>
                    <p>：</p>
                    <p className="flex flex-1 justify-end">
                      {valueSubmit?.valueHeaderInformation?.issued_date
                        ? isJapanese
                          ? moment(valueSubmit?.valueHeaderInformation?.issued_date).format(
                              'YYYY年MM月DD日',
                            )
                          : moment(valueSubmit?.valueHeaderInformation?.issued_date).format(
                              'MM/DD/YYYY',
                            )
                        : ''}
                    </p>
                  </div>
                </div>
              </div>
              <div className="table w-full border mt-10">
                <div className="w-full grid grid-cols-18 bg-[#F0F2F5]">
                  <div className="col-span-1 flex flex-col h-[46px] justify-center items-center border-r ">
                    <p className="m-auto">No</p>
                  </div>
                  <div className="col-span-5 flex pb-1 h-[46px] justify-center items-center font-bold ">
                    {isJapanese ? '項目' : 'Item'}
                  </div>
                  <div className="col-span-1 flex h-[46px] justify-center items-center border-r " />
                  <div className="col-span-2 flex h-[46px] justify-center items-center border-r font-bold text-center ">
                    {isJapanese ? '単価' : 'Unit Price'}
                  </div>
                  <div className="col-span-2 flex h-[46px] justify-center items-center border-r font-bold text-center ">
                    {isJapanese ? '数量' : 'Quantity'}
                  </div>
                  <div className="col-span-2 flex h-[46px] justify-center items-center border-r font-bold text-center ">
                    {isJapanese ? '金額' : 'Amount'}
                  </div>
                  <div className="col-span-5 flex h-[46px] justify-center items-center font-bold ">
                    {isJapanese ? '備考' : 'Remarks'}
                  </div>
                </div>

                {itemPage?.map((groupItem, childIndex) => {
                  const itemSum = groupItem.reduce(
                    (sum, item) => sum + item.amount_including_tax,
                    0,
                  );
                  return (
                    <div key={`all_group_item_${childIndex}`}>
                      {groupItem.map((childItem, indexChildItem) => {
                        const key = `all_${indexChildItem}_group_item_${childIndex}`;
                        return (
                          <div key={key} className="w-full grid grid-cols-18 border-t">
                            <div className="col-span-1 flex min-h-[46px] px-3 items-center border-r justify-end">
                              <p className="text-right">{childItem.key}</p>
                            </div>
                            <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center ">
                              <p className="">
                                {isJapanese ? childItem?.product_name : childItem?.product_name_en}
                              </p>
                            </div>
                            <div className="col-span-1 flex min-h-[46px] px-3 border-r justify-center items-center">
                              <p>{renderTypeByTaxCode(childItem?.tax_category_code)}</p>
                            </div>
                            <div className="col-span-2 flex min-h-[46px] px-3 items-center border-r justify-end">
                              <p>{formatMoney(childItem?.unit_price)}</p>
                            </div>
                            <div className="col-span-2 flex min-h-[46px] px-3 items-center border-r justify-end">
                              <p>{formatMoney(childItem?.quantity)}</p>
                            </div>
                            <div className="col-span-2 flex min-h-[46px] px-3 items-center border-r justify-end">
                              <p>{formatMoney(roundNumber(childItem?.amount_including_tax))}</p>
                            </div>
                            <div className="col-span-5 flex min-h-[46px] px-3 items-center">
                              <p>{childItem?.memo}</p>
                            </div>
                          </div>
                        );
                      })}
                      {groupItem.length > 0 && (
                        <>
                          <div className="w-full grid grid-cols-18 border-t">
                            <div className="col-span-1 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                            <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center justify-center ">
                              <p className="text-center font-bold whitespace-pre-line">
                                {groupItem?.[0]?.subjectName
                                  ? `- ${
                                      isJapanese
                                        ? `${groupItem?.[0]?.subjectName} 合計`
                                        : `Total ${groupItem?.[0]?.subjectNameEn}`
                                    } -`
                                  : ''}
                              </p>
                            </div>
                            <div className="col-span-1 flex min-h-[46px] px-3 border-r justify-center items-center" />
                            <div className="col-span-2 flex min-h-[46px] px-3 items-center border-r justify-end" />
                            <div className="col-span-2 flex min-h-[46px] px-3 items-center border-r justify-end" />
                            <div className="col-span-2 flex min-h-[46px] px-3 items-center border-r justify-end">
                              <p>{formatMoney(itemSum)}</p>
                            </div>
                            <div className="col-span-5 flex min-h-[46px] px-3 items-center justify-end" />
                          </div>
                          <div className="w-full grid grid-cols-18 border-t">
                            <div className="col-span-1 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                            <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center justify-center " />
                            <div className="col-span-1 flex pb-1 min-h-[46px] px-3 border-r justify-center items-center" />
                            <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                            <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                            <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                            <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center justify-end" />
                          </div>
                        </>
                      )}
                    </div>
                  );
                })}
                {countGroup(itemPage) < COUNT_ROW + 1 && index !== itemGroupRenderAll.length - 1
                  ? Array.from(
                      { length: COUNT_ROW + 1 - countGroup(itemPage) },
                      (_, indexGroup) => indexGroup,
                    ).map((_, indexEmpty) => (
                      <div
                        key={`page_${index + 2}_empty_${indexEmpty}`}
                        className="w-full grid grid-cols-18 border-t"
                      >
                        <div className="col-span-1 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                        <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center justify-center " />
                        <div className="col-span-1 flex pb-1 min-h-[46px] px-3 border-r justify-center items-center" />
                        <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                        <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                        <div className="col-span-2 flex pb-1 min-h-[46px] px-3 items-center border-r justify-end" />
                        <div className="col-span-5 flex pb-1 min-h-[46px] px-3 items-center justify-end" />
                      </div>
                    ))
                  : null}
              </div>
            </div>
          ))}
        </>
      ) : null}
    </div>
  );
};
export default InvoiceItems;
