import moment from 'moment';
import { Image } from 'antd';

const InvoiceHeader = ({
  sealUrl,
  valueSubmit,
  signatureStamp,
  detailIssuanceInvoice,
  invoiceIdByGenerate,
  isEditReInvoice,
}) => {
  const language = signatureStamp.language;
  const isJapanese = language === 1;
  return (
    <div className="mb-1">
      <p className="h-1 text-center font-bold text-[40px]">{isJapanese ? '御請求書' : 'INVOICE'}</p>
      <div className="flex justify-end ">
        <div className="flex flex-col gap-5 w-[220px]">
          <div className="flex justify-between">
            <p className="w-[80px]">{isJapanese ? '請求書No.' : 'Invoice No.'}</p>
            <p>：</p>
            <p className="flex flex-1 justify-end">
              {detailIssuanceInvoice?.consolidated_invoice_id && !isEditReInvoice
                ? detailIssuanceInvoice?.consolidated_invoice_id
                : invoiceIdByGenerate}
            </p>
          </div>
          <div className="flex justify-between">
            <p className="w-[80px]">{isJapanese ? '旅行ID' : 'Travel ID'}</p>
            <p>：</p>
            <p className="flex flex-1 justify-end">{valueSubmit?.currentTour?.travel_id}</p>
          </div>
          <div className="flex justify-between">
            <p className="w-[80px]">{isJapanese ? '発行日' : 'Issue Date'}</p>
            <p>：</p>
            <p className="flex flex-1 justify-end">
              {valueSubmit?.valueHeaderInformation?.issued_date
                ? isJapanese
                  ? moment(valueSubmit?.valueHeaderInformation?.issued_date).format(
                      'YYYY年MM月DD日',
                    )
                  : moment(valueSubmit?.valueHeaderInformation?.issued_date).format('MM/DD/YYYY')
                : ''}
            </p>
          </div>
        </div>
      </div>
      <div className="flex justify-between">
        <div className="flex flex-col">
          <div className="mb-[60px] flex flex-col">
            <p className="mb-4">
              {isJapanese && '〒'} {valueSubmit?.currentCompany?.postal_code}
            </p>
            <p className="mb-4">
              {isJapanese || !valueSubmit?.currentCompany?.address_en
                ? valueSubmit?.currentCompany?.address
                : valueSubmit?.currentCompany?.address_en}
            </p>
            <p className="h-[60px] text-[22px] whitespace-pre-line max-w-[600px]">
              {isJapanese || !valueSubmit?.currentCompany?.business_partner_name_en
                ? valueSubmit?.currentCompany?.business_partner_name
                : valueSubmit?.currentCompany?.business_partner_name_en}

              {valueSubmit?.valueHeaderInformation?.title && isJapanese ? (
                valueSubmit?.valueHeaderInformation?.title === 1 ? (
                  <span className="ml-3">{'御中'}</span>
                ) : (
                  <span className="ml-3">{'様'}</span>
                )
              ) : (
                ''
              )}
            </p>
            <p className="">TEL: {valueSubmit?.currentCompany?.phone_number ?? ''}</p>
          </div>
          <p className="mb-[40px]">
            {isJapanese ? '出発日' : 'Departure Date'}：
            {valueSubmit?.currentTour?.departure_date
              ? isJapanese
                ? moment(valueSubmit?.currentTour?.departure_date).format('YYYY年MM月DD日 (dd）')
                : moment(valueSubmit?.currentTour?.departure_date).format('MM/DD/YYYY')
              : ''}
          </p>
          <div className="h-7">{isJapanese && <p>下記料金のご請求を申し上げます。</p>}</div>
        </div>
        <div className="flex flex-col mt-[56px] mr-10 relative">
          <div className="absolute right-0 top-[10px]">
            <Image preview={false} src={sealUrl} width={120} height={120} />
          </div>

          <p className="whitespace-pre">
            {isJapanese
              ? signatureStamp?.company?.travel_agency_license_number
              : 'Registered Travel Agency No. 2124'}
          </p>
          <p className="text-xl">
            {/* {signatureStamp.optionInvoicePrintf === 1
              ? signatureStamp?.company?.invoice_print_account_name
              : signatureStamp?.company?.invoice_print_account_name_2} */}
            {isJapanese
              ? signatureStamp?.company?.company_name
              : signatureStamp?.company?.company_name_en}
          </p>
          <p className="whitespace-pre">
            {isJapanese
              ? signatureStamp?.company?.business_name
              : signatureStamp?.company?.business_name_en}
          </p>
          <p className="whitespace-pre">
            {isJapanese && (
              <span>
                {isJapanese && '〒'}
                {signatureStamp?.company?.postal_code} {'  '}
              </span>
            )}
            {isJapanese ? signatureStamp?.company?.address : signatureStamp?.company?.address_en}
          </p>
          <p className="whitespace-pre">
            TEL: {signatureStamp?.company?.phone_number} {'  '} FAX: {signatureStamp?.company?.fax}
          </p>
          <p>E-mail: {signatureStamp?.company?.email}</p>
          <p className="whitespace-pre">
            {isJapanese ? '適格請求書発行事業者登録番号：' : 'Invoice system registration number：'}{' '}
            {signatureStamp?.company?.qualified_invoice_issuer_number}
          </p>
          <br />
          <p className="text-right leading-10">
            {isJapanese ? '担当者' : 'In Charge'}： {valueSubmit?.managerName}
          </p>
        </div>
      </div>
    </div>
  );
};
export default InvoiceHeader;
