import moment from 'moment';
import type { DetailRowItemSaleInvoiceType } from '../..';
import { useMemo } from 'react';
import { formatMoney, roundNumber } from '@/utils';

const InvoiceFooter = ({ valueSubmit, signatureStamp }) => {
  const language = signatureStamp.language;
  const isJapanese = language === 1;

  function groupByTaxRate(
    items: DetailRowItemSaleInvoiceType[],
  ): Record<number, { amount: number; tax_amount: number }> | null {
    const groupedItems: Record<number, DetailRowItemSaleInvoiceType[]> = {};
    try {
      items.forEach((item) => {
        const tax_rate = item.tax_rate;
        if (!groupedItems[tax_rate]) {
          groupedItems[tax_rate] = [];
        }
        groupedItems[tax_rate].push(item);
      });

      const taxRate10 = groupedItems['10']?.reduce(
        (acc, item) => {
          acc.amount += item.amount_including_tax;
          acc.tax_amount += roundNumber(item.consumption_tax);
          return acc;
        },
        { amount: 0, tax_amount: 0 },
      );
      const taxRate8 = groupedItems['8']?.reduce(
        (acc, item) => {
          acc.amount += item.amount_including_tax;
          acc.tax_amount += roundNumber(item.consumption_tax);
          return acc;
        },
        { amount: 0, tax_amount: 0 },
      );
      const taxRate0 = groupedItems['0']?.reduce(
        (acc, item) => {
          acc.amount += item.amount_including_tax;
          return acc;
        },
        { amount: 0, tax_amount: 0 },
      );
      const result = {
        10: taxRate10,
        8: taxRate8,
        0: taxRate0,
      };
      return result;
    } catch (error) {
      return null;
    }
  }

  const taxRateInformation = useMemo(() => {
    if (valueSubmit?.valueItem) {
      const result = groupByTaxRate(valueSubmit?.valueItem as DetailRowItemSaleInvoiceType[]);
      return result;
    }
    return null;
  }, [valueSubmit?.valueItem]);

  return (
    <div>
      <div className="flex justify-between py-4 gap-[120px]">
        <div className="flex flex-col flex-1 justify-between gap-7">
          <div className="flex flex-col border flex-1">
            <p className="font-bold text-center border-b py-2 bg-[#F0F2F5]">
              {isJapanese ? '備考' : 'Remarks'}
            </p>
            <div className="py-2 px-4">
              <span className="mr-8 font-medium whitespace-pre-line">
                {valueSubmit?.valueHeaderInformation?.memo
                  ? valueSubmit?.valueHeaderInformation?.memo
                  : ''}
              </span>
            </div>
          </div>
          <p>
            {valueSubmit?.valueHeaderInformation?.payment_deadline
              ? isJapanese
                ? `お支払いは弊社下記口座へ${moment(
                    valueSubmit?.valueHeaderInformation?.payment_deadline,
                  ).format('YYYY年MM月DD日')}までにお振込みください。`
                : `Please transfer the payment to our account below by ${moment(
                    valueSubmit?.valueHeaderInformation?.payment_deadline,
                  ).format('MM/DD/YYYY')}`
              : ''}
          </p>
        </div>
        <div className="flex flex-1 flex-col border max-w-[424px]">
          <div className="grid grid-cols-10 border-b">
            <div className="col-span-6 px-4 border-r bg-[#F0F2F5] pb-1">
              <p className="text-center py-1"> {isJapanese ? '10%対象' : 'Sub Total (10%)'}</p>
              <p className="text-right py-1 ">{isJapanese ? '内消費税' : 'Consumption Tax'} </p>
            </div>
            <div className="col-span-4 px-4 pb-1">
              <p className="text-right py-1">
                ￥
                {taxRateInformation?.['10']?.amount
                  ? formatMoney(taxRateInformation?.['10']?.amount)
                  : 0}
              </p>
              <p className="text-right py-1 pb-2">
                ￥
                {taxRateInformation?.['10']?.tax_amount
                  ? formatMoney(taxRateInformation?.['10']?.tax_amount)
                  : 0}
              </p>
            </div>
          </div>
          <div className="grid grid-cols-10 border-b">
            <div className="col-span-6 px-4 pb-1 border-r bg-[#F0F2F5]">
              <p className="text-center py-1">{isJapanese ? '8%対象' : 'Sub Total (8%)'}</p>
              <p className="text-right py-1">{isJapanese ? '内消費税' : 'Consumption Tax'} </p>
            </div>
            <div className="col-span-4 px-4 pb-1">
              <p className="text-right py-1">
                ￥
                {taxRateInformation?.['8']?.amount
                  ? formatMoney(taxRateInformation?.['8']?.amount)
                  : 0}
              </p>
              <p className="text-right py-1">
                ￥
                {taxRateInformation?.['8']?.tax_amount
                  ? formatMoney(taxRateInformation?.['8']?.tax_amount)
                  : 0}
              </p>
            </div>
          </div>
          <div className="grid grid-cols-10 border-b ">
            <div className="col-span-6 px-4 pb-1 border-r bg-[#F0F2F5]">
              <p className="text-center py-1">{isJapanese ? '非課税等計' : 'Tax Exemption'}</p>
              <p className="text-center py-1" />
            </div>
            <div className="col-span-4 px-4 pb-1">
              <p className="text-right py-1">
                ￥
                {taxRateInformation?.['0']?.amount
                  ? formatMoney(taxRateInformation?.['0']?.amount)
                  : 0}
              </p>
              <p className="text-right py-1">-</p>
            </div>
          </div>
          <div className="grid grid-cols-10 border-b">
            <div className="col-span-6 px-4 pb-1 border-r bg-[#F0F2F5]">
              <p className="text-center font-bold py-1">{isJapanese ? '合計' : 'TOTAL'} </p>
              <p className="text-right py-1">{isJapanese ? '内消費税' : 'Consumption Tax'} </p>
            </div>
            <div className="col-span-4 px-4 pb-1">
              <p className="text-right py-1">
                ￥
                {formatMoney(
                  (taxRateInformation?.['10']?.amount ?? 0) +
                    (taxRateInformation?.['8']?.amount ?? 0) +
                    (taxRateInformation?.['0']?.amount ?? 0),
                )}
              </p>
              <p className="text-right py-1">
                ￥
                {formatMoney(
                  (taxRateInformation?.['10']?.tax_amount ?? 0) +
                    (taxRateInformation?.['8']?.tax_amount ?? 0) +
                    (taxRateInformation?.['0']?.tax_amount ?? 0),
                )}
              </p>
            </div>
          </div>
          <div className="grid grid-cols-10 border-b">
            <div className="col-span-6 px-4  border-r bg-[#F0F2F5]">
              <p className="text-center py-3">{isJapanese ? 'お預り金額' : 'Deposit'}</p>
            </div>
            <div className="col-span-4 px-4  ">
              <p className="text-right py-3">
                ￥{formatMoney(Number(valueSubmit?.valueHeaderInformation?.deposit_entered ?? 0))}
              </p>
            </div>
          </div>
          <div className="grid grid-cols-10">
            <div className="col-span-6 px-4 py-2 border-r bg-[#F0F2F5]">
              <p className="text-center font-semibold text-[20px]">
                {isJapanese ? 'ご請求金額' : 'Total Amount'}
              </p>
            </div>
            <div className="col-span-4 px-4 py-2">
              <p className="text-right py-1" />
              <p className="text-right py-1 font-bold">
                ￥
                {formatMoney(
                  (taxRateInformation?.['10']?.amount ?? 0) +
                    (taxRateInformation?.['8']?.amount ?? 0) +
                    (taxRateInformation?.['0']?.amount ?? 0) -
                    Number(valueSubmit?.valueHeaderInformation?.deposit_entered ?? 0),
                )}
              </p>
            </div>
          </div>
        </div>
      </div>
      <div className="flex flex-col">
        <p className="font-bold">{isJapanese ? '【お振込先】' : '[Bank transfer details]'}</p>

        {isJapanese ? (
          <>
            <span className="mr-8 font-medium">
              {signatureStamp.optionInvoicePrintf === 1
                ? signatureStamp?.company?.invoice_print_account_number
                : signatureStamp?.company?.invoice_print_account_number_2}
            </span>
            <p className="font-medium">
              {signatureStamp.optionInvoicePrintf === 1
                ? signatureStamp?.company?.invoice_print_account_name
                : signatureStamp?.company?.invoice_print_account_name_2}
            </p>
          </>
        ) : (
          <div className="whitespace-pre-line mb-2">
            <div className="flex">
              <p className={isJapanese ? 'w-[140px]' : 'w-[200px]'}>
                {isJapanese ? '社名' : 'ACCOUNT NAME'}
              </p>
              <p>: {signatureStamp?.company?.company_name_beneficiary}</p>
            </div>
            <div className="flex">
              <p className={isJapanese ? 'w-[140px]' : 'w-[200px]'}>SWIFT CODE</p>
              <p>: {signatureStamp?.company?.swift_code} </p>
            </div>
            <div className="flex">
              <p className={isJapanese ? 'w-[140px]' : 'w-[200px]'}>
                {isJapanese ? '金融機関名称' : 'PAYING BANK AND BRANCH'}
              </p>
              <p>: {signatureStamp?.company?.financial_institution_name}</p>
            </div>
            <div className="flex">
              <p className={isJapanese ? 'w-[140px]' : 'w-[200px]'}>
                {isJapanese ? '金融機関住所' : 'BANK ADDRESS'}{' '}
              </p>
              <p>: {signatureStamp?.company?.financial_institution_address}</p>
            </div>
            <div className="flex">
              <p className={isJapanese ? 'w-[140px]' : 'w-[200px]'}>
                {isJapanese ? '口座番号' : 'ACCOUNT NO'}
              </p>
              <p>: {signatureStamp?.company?.account_number}</p>
            </div>
          </div>
        )}

        <div className={`${isJapanese ? 'mt-7' : 'mt-3'} whitespace-pre-line text-xs pb-5`}>
          {isJapanese ? (
            <>
              <p className="mb-3">
                ※誠に恐れ入りますが、お振込手数料はお客様負担にてお願いいたします。
              </p>
              （注１）立替金におけるインボイス免税事業者からの仕入金額は、合計欄の消費税10％、もしくは、消費税8％に合算されております。
              <br />
              　　　　また、備考欄に登録番号の記載がない立替先は、インボイス免税事業者からの仕入となります。
              <br />
              （注２）項目欄の「＊」は軽減税率対象の取引となります。
              <br />
              （注３）項目欄の「＃」は非課税等（非課税/不課税/免税）対象の取引となります。
            </>
          ) : (
            <>
              <p className="mb-1">Notes :</p>
              （１）The purchase amount from an Invoice-exempt taxpayer for advances is included in
              the total column under either the 10% or 8% consumption tax. In addition, any advances
              without a 　　　registration number listed in the remarks column is considered to be a
              purchase from an invoice tax-exempt business.
              <br />
              （２）Transactions marked with「＊」in the item column are subject to the reduced tax
              rate category.
              <br />
              （３）Transactions marked with「＃」in the item column are subject to non-taxable
              categories (exempt, non-taxable, or tax-free).
            </>
          )}
        </div>
      </div>
    </div>
  );
};
export default InvoiceFooter;
