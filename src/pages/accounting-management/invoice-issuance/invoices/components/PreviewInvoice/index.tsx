import {
  getBase64Image,
  uploadFilePDFInvoice,
  type DetailInvoiceIssuance,
} from '@/apis/accounting/invoiceIssuance';
import type { CompanyType } from '@/apis/master/companyMaster';
import IconCancelRed from '@/assets/imgs/common-icons/cancel-red.svg';
import IconTickWhite from '@/assets/imgs/common-icons/tick_white.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicModal from '@/components/Commons/BasicModal';
import { openNotificationFail } from '@/components/Notification';
import { TEXT_ACTION } from '@/constants/commonText';
import { notosanjpRegularBase64, robotoRegularBase64 } from '@/constants/fontBase64';
import STATUS_CODE from '@/constants/statusCode';
import type { FormInstance } from 'antd';
import { Image, Spin } from 'antd';
import html2canvas from 'html2canvas';
import pdfMake from 'pdfmake/build/pdfmake';
import pdfFonts from 'pdfmake/build/vfs_fonts';
import { useEffect, useRef, useState } from 'react';
import InvoiceHeader from './InvoiceHeader';
import InvoiceItems from './InvoiceItems';
import moment from 'moment';

const PreviewInvoice = ({
  onSelect,
  onClose,
  valueSubmit,
  detailIssuanceInvoice,
  invoiceIdByGenerate,
  formHeaderInformation,
  isItemExist,
  signatureStamp,
  isEditReInvoice,
}: {
  onSelect: (val: string) => void;
  onClose: () => void;
  signatureStamp: {
    seal: string;
    logo: string;
    company: CompanyType;
    language: number;
  };
  valueSubmit: any;
  detailIssuanceInvoice: DetailInvoiceIssuance;
  invoiceIdByGenerate: string;
  formHeaderInformation: FormInstance<any>;
  isItemExist: boolean;
  isEditReInvoice: boolean;
}) => {
  const htmlRef = useRef(null);
  const refModalConfirmForNormalInvoice = useRef<any>(null);
  const refModalConfirmForVOIDInvoice = useRef<any>(null);
  const [sealUrl, setSealUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  pdfMake.vfs = {
    ...(pdfFonts?.pdfMake?.vfs || {}),
    'Roboto-Regular.ttf': robotoRegularBase64,
    'NotoSansJP-Regular.ttf': notosanjpRegularBase64,
  };

  pdfMake.fonts = {
    NotoSansJP: {
      normal: 'NotoSansJP-Regular.ttf',
      bold: 'NotoSansJP-Regular.ttf',
      italics: 'NotoSansJP-Regular.ttf',
      bolditalics: 'NotoSansJP-Regular.ttf',
    },
    Roboto: {
      normal: 'Roboto-Regular.ttf',
      bold: 'Roboto-Regular.ttf',
      italics: 'Roboto-Regular.ttf',
      bolditalics: 'Roboto-Regular.ttf',
    },
  };

  const handleSendPDF = async () => {
    if (htmlRef.current) {
      try {
        setLoading(true);

        const canvas = await html2canvas(htmlRef.current, {
          useCORS: true,
          logging: true,
          scale: 2,
          windowWidth: htmlRef.current.scrollWidth,
          windowHeight: htmlRef.current.scrollHeight,
        });

        const imgData = canvas.toDataURL('image/png');
        const imgWidth = 500;
        const pageHeight = 860; //signatureStamp?.language === 1 ? 830 : 860; // A4 size in points (72 points per inch)
        const paddingBottom = 0; // Padding bottom in points
        const effectivePageHeight = pageHeight - paddingBottom;
        const imgHeight = (canvas.height * imgWidth) / canvas.width;

        const content = [];

        const pageCount = Math.ceil(imgHeight / effectivePageHeight);

        for (let i = 0; i < pageCount; i++) {
          content.push({
            image: imgData,
            width: imgWidth,
            margin: [0, i === 0 ? 0 : -i * effectivePageHeight, 0, 0],
          });
        }

        const documentDefinition = {
          // pageSize: { width: imgWidth, height: pageHeight },
          pageSize: 'A4',
          pageMargins: [40, 20, 40, 0],
          content: content,
          // defaultStyle: {
          //   font: 'NotoSansJP',
          // },
          footer: function (currentPage: number, totalPage: number) {
            return {
              text: `${currentPage} / ${totalPage}`,
              alignment: 'center',
              margin: [0, 0, 0, paddingBottom],
              fontSize: 8,
            };
          },
        };

        const pdfDocGenerator = pdfMake.createPdf(documentDefinition);
        pdfDocGenerator.getBlob(async (blob: Blob) => {
          const bpCode = valueSubmit.currentCompany.business_partner_code;
          const bpName = valueSubmit.currentCompany.business_partner_name;
          const sumAmount = valueSubmit.valueItem.reduce(
            (acc, cur) => acc + cur.amount_including_tax,
            0,
          );
          const currentTime = moment().format('YYYYMMDDHHmmss');
          const formData = new FormData();
          formData.append('file', blob, 'invoice.pdf');
          formData.append('type', 'invoice');
          formData.append('fileName', `${bpCode}_${bpName}_${sumAmount}_${currentTime}.pdf`);

          // test preview pdf
          // const url = URL.createObjectURL(blob);
          // window.open(url, '_blank');

          const resUpload = await uploadFilePDFInvoice(formData);
          if (resUpload.status === STATUS_CODE.SUCCESSFUL && resUpload.data.data) {
            onSelect(resUpload.data.data);
          } else {
            openNotificationFail({ message: 'Upload Error' });
          }
          setLoading(false);
        });
      } catch (error) {
        setLoading(false);
        openNotificationFail({ message: 'Upload: Syntax Error' });
      }
    }
  };

  const handleCheckConfirm = () => {
    if (formHeaderInformation.getFieldValue('billing_category') === 1 && isItemExist) {
      refModalConfirmForNormalInvoice.current.open();
      return;
    } else if (formHeaderInformation.getFieldValue('billing_category') === 2) {
      refModalConfirmForVOIDInvoice.current.open();
      return;
    }
    handleSendPDF();
  };

  useEffect(() => {
    const fetchSealUrl = async () => {
      setLoading(true);
      if (signatureStamp?.seal) {
        try {
          const result = await getBase64Image({ file_link: signatureStamp?.seal });
          if (result.data.base64) {
            const base64Image = `data:image/png;base64,${result.data.base64}`;
            setSealUrl(base64Image);
          } else {
            openNotificationFail({ message: 'Failed to fetch seal' });
          }
        } catch (error) {
          setLoading(false);
          setSealUrl(null);
        }
      } else {
        setSealUrl(null);
      }
      setLoading(false);
    };

    fetchSealUrl();
  }, [signatureStamp?.seal]);

  return (
    <>
      <Spin spinning={loading}>
        <div className="p-6 !pt-0" ref={htmlRef}>
          <InvoiceHeader
            sealUrl={sealUrl}
            valueSubmit={valueSubmit}
            signatureStamp={signatureStamp}
            detailIssuanceInvoice={detailIssuanceInvoice}
            invoiceIdByGenerate={invoiceIdByGenerate}
            isEditReInvoice={isEditReInvoice}
          />
          <InvoiceItems
            valueSubmit={valueSubmit}
            signatureStamp={signatureStamp}
            detailIssuanceInvoice={detailIssuanceInvoice}
            invoiceIdByGenerate={invoiceIdByGenerate}
            isEditReInvoice={isEditReInvoice}
          />
        </div>
        <div className="flex justify-center mt-6 gap-5">
          <BasicButton
            className="w-[290px] flex items-center justify-center rounded-[4px] shadow-md !bg-white hover:opacity-40 font-medium !text-[#FF3B30] !border-[#DCDEE3]"
            onClick={onClose}
          >
            <Image preview={false} src={IconCancelRed} width={18} height={18} />
            <p className="pl-2 font-medium">{TEXT_ACTION.CLOSE}</p>
          </BasicButton>
          <BasicButton
            className="w-[290px] flex items-center justify-center rounded-[4px] shadow-md !bg-main-color hover:opacity-40 font-medium !text-white "
            onClick={handleCheckConfirm}
            disabled={loading}
            loading={loading}
          >
            <Image preview={false} src={IconTickWhite} width={18} height={18} />
            <p className="pl-2 font-medium">{TEXT_ACTION.Add_to_invoice}</p>
          </BasicButton>
        </div>
        <BasicModal
          ref={refModalConfirmForNormalInvoice}
          title={'請求書作成'}
          content={
            <>
              『通常』区分が選択されていますが、発行済みの明細があります。
              このまま請求書を発行しますか？
            </>
          }
          buttonSubmitTitle="はい"
          buttonCloseTitle="いいえ"
          styleTypeButtonSubmit="accept"
          styleTypeButtonClose="danger"
          iconButtonSubmit={<Image preview={false} src={IconTickWhite} width={18} height={18} />}
          iconButtonClose={<Image preview={false} src={IconCancelRed} width={18} height={18} />}
          onSubmit={() => {
            refModalConfirmForNormalInvoice.current?.close();
            handleSendPDF();
          }}
        />
        <BasicModal
          ref={refModalConfirmForVOIDInvoice}
          title={'請求書発行'}
          content={
            <>
              『ＶＯＩＤ』区分が選択されています。備考欄は入力済みですか？
              このまま請求書を発行しますか？
            </>
          }
          iconButtonSubmit={<Image preview={false} src={IconTickWhite} width={18} height={18} />}
          iconButtonClose={<Image preview={false} src={IconCancelRed} width={18} height={18} />}
          buttonSubmitTitle="はい"
          buttonCloseTitle="いいえ"
          styleTypeButtonSubmit="accept"
          styleTypeButtonClose="danger"
          onSubmit={() => {
            refModalConfirmForVOIDInvoice.current?.close();
            handleSendPDF();
          }}
        />
      </Spin>
    </>
  );
};
export default PreviewInvoice;
