import type { GetListInvoiceIssuance } from '@/apis/accounting/invoiceIssuance';
import { getListInvoiceIssuance } from '@/apis/accounting/invoiceIssuance';
import PageContainer from '@/components/Commons/Page/Container';
import STATUS_CODE from '@/constants/statusCode';
import { ITEM_PER_PAGE } from '@/utils/constants';
import { useEffect, useState } from 'react';
import { useUrlSearchParams } from 'use-url-search-params';
import HeaderAction from './components/HeaderAction';
import TableListDeposit from './components/TableList';

const initSearchParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};

const InvoiceIssuance = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<GetListInvoiceIssuance[]>([]);
  const [total, setTotal] = useState(0);

  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);

  const onFetchData = async () => {
    setIsLoading(true);
    try {
      const resGetListPurchaseInvoice = await getListInvoiceIssuance(parameter);
      if (resGetListPurchaseInvoice.status === STATUS_CODE.SUCCESSFUL) {
        const resData = resGetListPurchaseInvoice.data;
        if (resData.data) {
          setDataSource(
            [...resData.data].map((item, index) => {
              return {
                ...item,
                tour_id: item.travel?.travel_id,
                tour_name: item.travel?.tour_name,
                business_partner_name: item.business_partner?.business_partner_name,
                business_partner_code: item.business_partner?.business_partner_code,
                contact_person: item?.manager?.name,
                key: Number(parameter.limit) * (Number(parameter.page) - 1) + index + 1,
              };
            }),
          );
        }
        setTotal(resData.total);
      }
    } catch (error) {
      console.log('error', error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    onFetchData();
  }, [
    parameter,
    parameter?.keyword,
    parameter?.page,
    parameter?.limit,
    parameter?.tourId,
    parameter?.buiness_partner_id,
    parameter?.buiness_partner_code,
  ]);

  return (
    <PageContainer>
      <HeaderAction paramSearch={parameter} setParamSearch={setParameter} />
      <div className="p-2 rounded-xl bg-white mt-6">
        <TableListDeposit
          total={total}
          isLoading={isLoading}
          dataSource={dataSource}
          paramSearch={parameter}
          setParamSearch={setParameter}
        />
      </div>
    </PageContainer>
  );
};

export default InvoiceIssuance;
