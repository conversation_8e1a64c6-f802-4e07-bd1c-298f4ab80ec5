import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import { getDetailBusinessPartner, getListBusinessPartner } from '@/apis/businessPartner';
import SelectIcon from '@/assets/imgs/common-icons/select-icon.svg';
import BasicButton from '@/components/Commons/BasicButton';
import type { BasicFormModalRef } from '@/components/Commons/BasicFormModal';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import BasicInput from '@/components/Commons/BasicInput';
import FormSelectCompany from '@/components/Form/FormSelectCompany';
import CloseSVG from '@/components/SVG/CloseSVG';
import SearchSVG from '@/components/SVG/SearchSVG';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { PlusOutlined } from '@ant-design/icons';
import { useDebounce } from '@uidotdev/usehooks';
import { Image } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { history } from 'umi';

const HeaderAction = ({ paramSearch, setParamSearch }) => {
  const [currentCompany, setCurrentCompany] = useState<BusinessPartnerDetailType>();
  const [searchValue, setSearchValue] = useState<string>('');
  const [businessPartnerIdValue, setBusinessPartnerIdValue] = useState('');

  const refModalSelectCompany = useRef<BasicFormModalRef>();
  const businessPartnerCodeDebounce = useDebounce(businessPartnerIdValue, 300);

  useEffect(() => {
    if (currentCompany) {
      setBusinessPartnerIdValue(currentCompany?.business_partner_code);
    }
  }, [currentCompany]);

  const updateParameter = (key, value) => {
    if (value === undefined || value === null) {
      const searchParams = new URLSearchParams(window.location.search);
      searchParams.delete(key);
      const newUrl = `${window.location.pathname}?${searchParams.toString()}`;
      window.history.pushState({}, '', newUrl);
    } else {
      setParamSearch(key, value);
    }
  };
  const handleSearchByValue = () => {
    const paramToSearch = {
      ...paramSearch,
      page: 1,
      keyword: searchValue,
      business_partner_code: businessPartnerIdValue,
    };
    delete paramToSearch.tourId;
    updateParameter('tourId', null);
    setParamSearch({
      ...paramToSearch,
    });
  };

  useEffect(() => {
    setSearchValue(paramSearch?.keyword);
  }, [paramSearch?.keyword]);

  const onOpenModalSelectCompany = () => {
    refModalSelectCompany.current.open();
  };

  const fetchDataDetail = async (id: number) => {
    try {
      const { data, status } = await getDetailBusinessPartner(id);
      const rsData = data.data;
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dataRs = {
          ...rsData,
          qualified_invoice_issuer_number:
            rsData?.qualified_invoice_issuer_number?.toString() ?? '',
          r_rate: rsData?.r_rate?.toString() ?? '',
          target: JSON.parse(rsData?.target as string) ?? undefined,
        };

        setCurrentCompany(dataRs as any);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    if (!Number.isNaN(Number(paramSearch?.business_partner_id))) {
      fetchDataDetail(Number(paramSearch?.business_partner_id));
    }
  }, []);

  const fetchDataCompany = async () => {
    const resData = await getListBusinessPartner({
      page: 1,
      limit: 1,
      business_partner_code: businessPartnerCodeDebounce,
    });

    if (resData?.data?.data) {
      setCurrentCompany(resData.data.data[0]);
      setParamSearch({
        ...paramSearch,
        page: 1,
        business_partner_id: resData.data.data[0]?.id,
        business_partner_code: resData.data.data[0]?.business_partner_code,
      });
    } else {
      setCurrentCompany(undefined);
    }
  };

  useEffect(() => {
    if (businessPartnerCodeDebounce?.length > 2) {
      fetchDataCompany();
    }
  }, [businessPartnerCodeDebounce]);

  return (
    <>
      <div className="flex items-end justify-between gap-4 flex-wrap">
        <div className="flex flex-wrap gap-4 ">
          <BasicInput
            style={{
              width: '140px',
              height: '40px',
            }}
            value={businessPartnerIdValue}
            title={`得意先ID`}
            placeholder={'得意先ID'}
            onChange={(val) => {
              setBusinessPartnerIdValue(val.target.value);
            }}
          />

          <div className="">
            <p className="mb-2 text-[13px] leading-4 font-medium">{'得意先名'}</p>
            <BasicButton
              className="!border-[#EBE9FA] !w-[230px] flex justify-between !px-[11px] items-center !text-[rgba(0,0,0,0.3)] !bg-white hover:opacity:40"
              onClick={() => {
                if (currentCompany?.business_partner_name) {
                  setCurrentCompany(undefined);
                  setParamSearch({
                    ...paramSearch,
                    page: 1,
                    business_partner_id: undefined,
                    business_partner_code: undefined,
                  });
                  return;
                }
                onOpenModalSelectCompany();
              }}
            >
              <span className="truncate">{currentCompany?.business_partner_name ?? 'すべて'}</span>
              {currentCompany?.business_partner_name ? (
                <CloseSVG width="18" height="18" />
              ) : (
                <Image src={SelectIcon} alt="select icon" className="w-5 h-5" preview={false} />
              )}
            </BasicButton>
          </div>

          <BasicInput
            style={{
              width: '280px',
              height: '40px',
            }}
            title="請求書ID, 旅行IDまたはツアー名"
            placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
            value={searchValue}
            onChange={(val) => {
              setSearchValue(val.target.value);
            }}
          />
          <div className="flex items-end">
            <BasicButton
              icon={<SearchSVG colorSvg="white" />}
              className="flex items-center w-[120px]"
              styleType="accept"
              onClick={handleSearchByValue}
            >
              <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
            </BasicButton>
          </div>
        </div>

        <div className="flex gap-x-[12px]">
          <BasicButton
            icon={<PlusOutlined />}
            className="flex items-center"
            styleType="accept"
            onClick={() => {
              history.push('/accounting-management/invoice-issuance/creation');
            }}
          >
            {TEXT_ACTION.CREATE_NEW}
          </BasicButton>
        </div>
      </div>
      <BasicFormModal
        ref={refModalSelectCompany}
        content={
          <FormSelectCompany
            defaultParams={{ is_use_sale: 1 }}
            onSelect={(v) => {
              setCurrentCompany(v);
              setParamSearch({
                ...paramSearch,
                page: 1,
                business_partner_id: v?.id,
                business_partner_code: v?.business_partner_code,
              });
            }}
            onClose={() => refModalSelectCompany.current.close()}
          />
        }
        className="!w-[1200px] [&_.ant-modal-body]:!px-2"
        title="取引先名"
        isValidate={true}
        hideListButton={true}
      />
    </>
  );
};

export default HeaderAction;
