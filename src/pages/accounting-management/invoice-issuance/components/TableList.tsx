import type { BaseParams } from '@/@types/request';
import type { GetListInvoiceIssuance } from '@/apis/accounting/invoiceIssuance';
import IconEye from '@/assets/imgs/common-icons/view-eye.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicTable from '@/components/Commons/BasicTable';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_TITLE } from '@/constants/commonText';
import { formatMoney } from '@/utils';
import { DownloadOutlined, PrinterOutlined } from '@ant-design/icons';
import moment from 'moment';
import { history } from 'umi';
import IconCancelRed from '@/assets/imgs/common-icons/cancel-red.svg';
import IconTickWhite from '@/assets/imgs/common-icons/tick_white.svg';
import { Image } from 'antd';
import BasicModal from '@/components/Commons/BasicModal';
import { useRef, useState } from 'react';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  isLoading,
  dataSource,
  paramSearch,
  setParamSearch,
  total,
}: {
  isLoading: boolean;
  dataSource: GetListInvoiceIssuance[];
  paramSearch?: BaseParams;
  setParamSearch?: (val: BaseParams) => void;
  total: number;
}) => {
  const [recordIdSelected, setRecordIdSelected] = useState<number | null>(null);
  const refModalConfirmForNormalInvoice = useRef(null);

  const exportPDF = async (dataRecord: GetListInvoiceIssuance) => {
    try {
      const urlPdf = dataRecord?.pictures?.[0]?.path;
      if (urlPdf) {
        const a = document.createElement('a');
        a.href = urlPdf;
        a.target = '_blank';
        a.download = `${moment().format('YYYYMMDDHHmmss')}_請求書_${
          dataRecord?.consolidated_invoice_id
        }.pdf`;
        a.click();
        window.URL.revokeObjectURL(urlPdf);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      console.log('onHandleDelete error', error);
    }
  };
  const handleReissue = (record: GetListInvoiceIssuance) => {
    // if (record?.billing_category === '1') {
    //   setRecordIdSelected(record?.id);
    //   refModalConfirmForNormalInvoice?.current?.open();
    // } else {
    history.push(`/accounting-management/invoice-issuance/edit/${record?.id}`);
    // }
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'key',
      key: 'key',
      width: 70,
    },
    {
      title: '請求書ID',
      dataIndex: 'consolidated_invoice_id',
      key: 'consolidated_invoice_id',
      width: 100,
    },
    {
      title: '旅行ID',
      dataIndex: 'tour_id',
      key: 'tour_id',
      width: 100,
    },
    {
      title: 'ツアー名',
      dataIndex: 'tour_name',
      key: 'tour_name',
      width: 200,
    },
    {
      title: '得意先ID',
      dataIndex: 'business_partner_code',
      key: 'business_partner_code',
      width: 200,
    },
    {
      title: '得意先名',
      dataIndex: 'business_partner_name',
      key: 'business_partner_name',
      width: 200,
    },
    {
      title: '作成日',
      dataIndex: 'issued_date',
      key: 'issued_date',
      width: 120,
      render: (_, record) => (
        <span>{record?.issued_date ? moment(record?.issued_date).format('YYYY/MM/DD') : ''}</span>
      ),
    },
    {
      title: '請求金額',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 140,
      render: (_, record) => (
        <div className="text-right">
          {record?.total_amount ? formatMoney(record?.total_amount) : ''}
        </div>
      ),
    },
    {
      title: '担当者',
      dataIndex: 'contact_person',
      key: 'contact_person',
      width: 140,
    },
    {
      title: '',
      dataIndex: 'groupAction',
      key: 'groupAction',
      width: 240,
      render: (_, record) => (
        <div className="flex gap-4">
          <BasicButton
            onClick={() => exportPDF(record)}
            type="text"
            className="!h-[24px] w-[124px] flex !border-none !text-main-color "
          >
            <DownloadOutlined style={{ color: '#3997C8' }} />
            <p className="text-xs !ml-1">{TEXT_ACTION.DOWNLOAD}</p>
          </BasicButton>

          <BasicButton
            type="text"
            className=" flex items-center justify-center !h-[24px] w-[112px] hover:border-[#3997C8] !border-none !text-main-color"
            onClick={() => handleReissue(record)}
          >
            <Image preview={false} src={IconEye} width={16} height={16} />
            <p className="text-xs !ml-1">詳細を見る</p>
          </BasicButton>
        </div>
      ),
    },
  ];

  return (
    <>
      <BasicModal
        ref={refModalConfirmForNormalInvoice}
        title={'請求書作成'}
        content={
          <>
            『通常』区分が選択されていますが、発行済みの明細があります。
            このまま請求書を発行しますか？
          </>
        }
        buttonSubmitTitle="はい"
        buttonCloseTitle="いいえ"
        styleTypeButtonSubmit="accept"
        styleTypeButtonClose="danger"
        iconButtonSubmit={<Image preview={false} src={IconTickWhite} width={18} height={18} />}
        iconButtonClose={<Image preview={false} src={IconCancelRed} width={18} height={18} />}
        onSubmit={() => {
          history.push(`/accounting-management/invoice-issuance/edit/${recordIdSelected}`);
        }}
      />
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1500 },
          loading: isLoading,
          columns: defaultColumns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
        }}
        page={paramSearch.page as number}
        pageSize={paramSearch.limit as number}
        onChangePage={(p: number) => {
          setParamSearch({ ...paramSearch, page: p });
        }}
        total={total}
        onSelectPageSize={(v) => setParamSearch({ ...paramSearch, limit: v })}
      />
    </>
  );
};

export default TableList;
