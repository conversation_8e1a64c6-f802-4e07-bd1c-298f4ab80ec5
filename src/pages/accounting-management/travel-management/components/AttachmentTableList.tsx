import { ItemFileItineraryType } from '@/apis/itineraries';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import IconEye from '@/assets/imgs/common-icons/view-eye.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicTable from '@/components/Commons/BasicTable';
import { UploadImageType } from '@/components/Commons/BasicUploads';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import { Image, Popconfirm } from 'antd';
import { useEffect, useState } from 'react';

interface Props {
  listAttachedFile: ItemFileItineraryType[];
  handleDeleteAttachedFile: (id: number) => void;
  loading: boolean;
}

const AttachmentTableList: React.FC<Props> = ({
  loading,
  listAttachedFile,
  handleDeleteAttachedFile,
}) => {
  const [dataSource, setDataSource] = useState<any>([]);
  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      render: (_, __, index) => index + 1,
    },
    {
      title: 'ファイル名',
      dataIndex: 'file_name',
      key: 'file_name',
      width: '50%',
      render: (_, { file_name }) => {
        const textFileName = file_name ?? 'file';
        return <>{textFileName}</>;
      },
    },
    {
      title: '',
      dataIndex: 'preview',
      key: 'preview',
      render: (_, record) => {
        return (
          <div className="flex items-center justify-center">
            <BasicButton
              styleType="outline"
              className=" flex items-center justify-center !h-[24px] w-[112px] !bg-[transparent] !shadow-none !border-none text-[#3997C8]"
              onClick={() => {
                window.open(record?.file_link, '_blank');
              }}
            >
              <Image preview={false} src={IconEye} width={16} height={16} />
              <p className="text-xs !ml-1">{TEXT_ACTION.PREVIEW}</p>
            </BasicButton>
          </div>
        );
      },
    },
    {
      title: '',
      dataIndex: 'deleteFile',
      key: 'deleteFile',
      render: (_, record) => (
        <Popconfirm
          title={TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
          onConfirm={() => handleDeleteAttachedFile(record?.id)}
          okText={TEXT_ACTION.DELETE}
          cancelText={TEXT_ACTION.CANCEL}
          placement="topRight"
        >
          <BasicButton
            styleType="danger"
            className="!h-[24px] w-[84px] hover:shadow-md hover:opacity-70 !border-none !shadow-none !bg-[transparent]"
          >
            <Image preview={false} src={IconDelete} width={15} height={14} />
            <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
          </BasicButton>
        </Popconfirm>
      ),
    },
  ];

  useEffect(() => {
    if (listAttachedFile) {
      const newDataSource = [...listAttachedFile];
      setDataSource(newDataSource);
    }
  }, [listAttachedFile]);

  return (
    <BasicTable
      className="!mt-0"
      tableProps={{
        scroll: { x: 300, y: 300 },
        columns: defaultColumns,
        dataSource: dataSource,
        bordered: false,
        pagination: false,
        loading: loading,
        rowKey: 'id',
      }}
      hasPagination={false}
    />
  );
};

export default AttachmentTableList;
