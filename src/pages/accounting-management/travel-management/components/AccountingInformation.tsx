import type { AccountingTravelListDetailType } from '@/apis/accounting/traveList';
import { uploadImage } from '@/apis/common';
import type { ItemFileItineraryType } from '@/apis/itineraries';
import {
  createAttachedFileItinerary,
  deleteAttachedFileItinerary,
  getListAttachedFileItinerary,
} from '@/apis/itineraries';
import BasicButton from '@/components/Commons/BasicButton';
import { UploadImageType } from '@/components/Commons/BasicUploads';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { PlusOutlined } from '@ant-design/icons';
import { notification, Upload } from 'antd';
import { useEffect, useState } from 'react';
import AccountingInformationTableDetail from './AccountingInformationTableDetail';
import AttachmentTableList from './AttachmentTableList';

interface Props {
  data: AccountingTravelListDetailType;
  travelId: string;
}
const AccountingInformation = ({ data, travelId }: Props) => {
  const MAX_FILE_SIZE_MB = 10;
  const acceptTypes = 'image/*,application/pdf';
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [listAttachedFile, setListAttachedFile] = useState<ItemFileItineraryType[]>([]);

  const fetchListAttachedFile = async () => {
    const resGetList = await getListAttachedFileItinerary(travelId);
    if (resGetList.status === STATUS_CODE.SUCCESSFUL) {
      const list = resGetList.data.data;
      setListAttachedFile(list);
    }
  };

  useEffect(() => {
    if (travelId) {
      fetchListAttachedFile();
    }
  }, [travelId]);

  const handleBeforeUpload = async (file: File) => {
    const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

    if (file.size > MAX_FILE_SIZE_BYTES) {
      notification.error({
        message: `※1ファイル${MAX_FILE_SIZE_MB}MBまでとすること`,
      });
      return;
    }
    setIsLoading(true);
    try {
      const config = {
        headers: { 'Content-Type': 'multipart/form-data' },
      };

      const formData = new FormData();
      formData.append('type', UploadImageType.travels);
      formData.append('file', file);

      const { data: dataFile, status, error } = await uploadImage(formData, config);
      if (status === STATUS_CODE.SUCCESSFUL) {
        const urlFile = dataFile.data;
        const resCreate = await createAttachedFileItinerary({ id: travelId, file_link: urlFile });
        if (resCreate.status === STATUS_CODE.SUCCESSFUL) {
          fetchListAttachedFile();
          openNotificationSuccess(MESSAGE_ALERT.UPLOAD_SUCCESS);
        } else {
          openNotificationFail(MESSAGE_ALERT.UPLOAD_FAILED);
        }
      } else {
        const errors = error.data.errors;
        if (errors && Object.keys(errors).length) {
          Object.keys(errors).forEach((key) => {
            const message = errors[key][0];
            openNotificationFail(message);
          });
        } else {
          openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
        }
      }
      setIsLoading(false);
    } catch (error) {
      console.log('upload fail', error);
      setIsLoading(false);
    }
  };

  const handleDeleteAttachedFile = async (id: number) => {
    setIsLoading(true);
    try {
      const resDelete = await deleteAttachedFileItinerary(id);
      if (resDelete.status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.DELETE_SUCCESS);
        fetchListAttachedFile();
      } else {
        openNotificationFail(MESSAGE_ALERT.DELETE_FAILED);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  return (
    <div className="grid grid-cols-2 gap-6 mt-6">
      <div className="flex-1 bg-white rounded-md mb-4 p-4">
        <p className="font-bold text-[18px] mb-4">2. 会計情報</p>
        <AccountingInformationTableDetail data={data} />
      </div>
      <div className="flex-1 bg-white rounded-md mb-4 p-4">
        <div className="flex justify-between mb-4">
          <p className="font-bold text-[18px] ">3. 資料</p>
          <div className="flex flex-col justify-end items-end">
            <span className="text-xs text-[#363840] mb-1">
              ※1ファイル{MAX_FILE_SIZE_MB}MBまでとすること
            </span>
            <Upload
              accept={acceptTypes}
              name="attachedFile"
              showUploadList={false}
              beforeUpload={handleBeforeUpload}
            >
              <BasicButton
                icon={<PlusOutlined />}
                className="flex justify-center items-center w-[112px] h-7 border border-[#DCDEE3]"
              >
                <span>{TEXT_ACTION.CREATE_NEW}</span>
              </BasicButton>
            </Upload>
          </div>
        </div>
        <AttachmentTableList
          handleDeleteAttachedFile={handleDeleteAttachedFile}
          listAttachedFile={listAttachedFile}
          loading={isLoading}
        />
      </div>
    </div>
  );
};

export default AccountingInformation;
