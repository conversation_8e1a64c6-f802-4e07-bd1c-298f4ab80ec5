import type { SelectItemType } from '@/@types/common';
import type { AccountingTravelListDetailType } from '@/apis/accounting/traveList';
import { getListTravelTypeMaster } from '@/apis/travelTypeMaster';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import BasicNumbericInput from '@/components/Commons/BasicNumbericInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import STATUS_CODE from '@/constants/statusCode';
import { Form } from 'antd';
import type { FormInstance } from 'antd/es/form';
import { forwardRef, useEffect, useImperativeHandle, useState } from 'react';

interface Props {
  submit: (v: AccountingTravelListDetailType) => void;
}

export type TripOverviewFormRef = {
  form: FormInstance<any>;
};

const TripOverview = forwardRef<TripOverviewFormRef, Props>(({ submit }, ref) => {
  const [form] = Form.useForm();

  const [optionTravelType, setOptionTravelType] = useState<SelectItemType[]>([]);

  const fetchDataOption = async () => {
    let arrOption: SelectItemType[] = [];
    const { data, status } = await getListTravelTypeMaster({
      limit: 'all',
      status: 1,
      sort: 'travel_code',
      order: 'asc',
    });
    if (status === STATUS_CODE.SUCCESSFUL) {
      const resData = data.data;

      arrOption = resData?.map((item) => ({
        label: item?.travel_name,
        value: item?.id,
      }));
    }

    setOptionTravelType(arrOption);
  };

  useEffect(() => {
    fetchDataOption();
  }, []);

  useImperativeHandle(ref, () => ({
    form,
  }));

  return (
    <Form form={form} onFinish={submit}>
      <div className="bg-white rounded-md p-4 ">
        <p className="font-bold text-[18px] mb-4">1. 旅行概要</p>
        <div className="grid grid-cols-5 gap-4">
          <div className="col-span-1">
            <Form.Item name={'travel_id'} noStyle>
              <BasicInput title="旅行ID" placeholder="xxxxxx" disabled />
            </Form.Item>
          </div>
          <div className="col-span-3">
            <Form.Item name={'tour_name'} noStyle>
              <BasicInput disabled title="ツアー名" placeholder="" />
            </Form.Item>
          </div>
          <div className="col-span-1">
            <Form.Item name={'travel_type'} noStyle>
              <BasicSelect title="旅行種別" options={optionTravelType} placeholder="-" />
            </Form.Item>
          </div>

          <div className="col-span-1">
            <Form.Item name="departure_date" noStyle>
              <BasicDatePicker className="!h-10" title="出発日" disabled />
            </Form.Item>
          </div>
          <div className="col-span-1">
            <Form.Item name={'return_date'} noStyle>
              <BasicDatePicker className="!h-10" title="帰着日" disabled />
            </Form.Item>
          </div>
          <div className="col-span-1">
            <Form.Item name={'gross_profit_margin'} noStyle>
              <BasicInput className="text-right" title="粗利率" disabled />
            </Form.Item>
          </div>
          <div className="col-span-1">
            <Form.Item
              name={'adult_count'}
              rules={[
                () => ({
                  validator: async (_, value: string) => {
                    const reg = /^[0-9]*$/;
                    if (value && !reg.test(value)) {
                      return Promise.reject('整数のみを入力してください');
                    } else {
                      return Promise.resolve();
                    }
                  },
                }),
              ]}
            >
              <BasicNumbericInput isRightAlign title="人数 (大人)" placeholder="xxxxxx" />
            </Form.Item>
          </div>
          <div className="col-span-1">
            <Form.Item
              name={'children_count'}
              rules={[
                () => ({
                  validator: async (_, value: string) => {
                    const reg = /^[0-9]*$/;
                    if (value && !reg.test(value)) {
                      return Promise.reject('整数のみを入力してください');
                    } else {
                      return Promise.resolve();
                    }
                  },
                }),
              ]}
            >
              <BasicNumbericInput isRightAlign title="人数 (子供)" placeholder="xxxxxx" />
            </Form.Item>
          </div>
        </div>
      </div>
    </Form>
  );
});

export default TripOverview;
