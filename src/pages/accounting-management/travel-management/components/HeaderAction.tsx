import { exportTravelList } from '@/apis/accounting/traveList';
import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInputSearch from '@/components/Commons/BasicInputSearch';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { DownloadOutlined, SearchOutlined, UploadOutlined } from '@ant-design/icons';
import { Form } from 'antd';
import moment from 'moment';
import { useEffect } from 'react';

const HeaderAction = ({ paramSearch, setParamSearch, onImportCsv }) => {
  const [form] = Form.useForm();

  const onExportCsv = async () => {
    const { data, status } = await exportTravelList({ ...paramSearch, limit: 'all', page: 1 });
    if (status === STATUS_CODE.SUCCESSFUL) {
      const urlCsv = data?.file_link;
      if (urlCsv) {
        const a = document.createElement('a');
        a.href = urlCsv;
        a.download = 'csv_travel_list_management.csv';
        a.click();
        window.URL.revokeObjectURL(urlCsv);
      }
    } else {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  const onSubmit = (values) => {
    const newSearchParams = {
      ...paramSearch,
      keyword: values.keyword ?? undefined,
      departure_date: values?.departure_date
        ? moment(values?.departure_date).format('YYYY/MM/DD')
        : undefined,
      return_date: values?.return_date
        ? moment(values?.return_date).format('YYYY/MM/DD')
        : undefined,
      page: 1,
    };

    setParamSearch(newSearchParams);
  };

  const startDate = Form.useWatch('departure_date', form);
  const endDate = Form.useWatch('return_date', form);

  useEffect(() => {
    if (paramSearch) {
      form?.setFieldsValue({
        keyword: paramSearch?.keyword,
        departure_date: paramSearch?.departure_date ? moment(paramSearch?.departure_date) : null,
        return_date: paramSearch?.return_date ? moment(paramSearch?.return_date) : null,
      });
    }
  }, []);

  return (
    <div className="flex items-end justify-between">
      <Form form={form} onFinish={onSubmit}>
        <div className="flex items-end gap-4">
          <Form.Item name={'keyword'} noStyle>
            <BasicInputSearch
              style={{
                width: '280px',
                height: '40px',
              }}
              title={TEXT_TITLE.Trip_ID_or_Tour_Name}
              placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
            />
          </Form.Item>

          <Form.Item name={'departure_date'} noStyle>
            <BasicDatePicker
              disabledDate={(date) => (endDate ? date > endDate : null)}
              title={TEXT_TITLE.Departure_date}
              placeholder="YYYY/MM/DD"
              className="!h-10"
              allowClear={true}
            />
          </Form.Item>

          <Form.Item name={'return_date'} noStyle>
            <BasicDatePicker
              disabledDate={(date) => (startDate ? date < startDate : null)}
              title={TEXT_TITLE.Return_Date}
              placeholder="YYYY/MM/DD"
              className="!h-10"
              allowClear
            />
          </Form.Item>

          <BasicButton
            onClick={() => form.submit()}
            icon={<SearchOutlined />}
            styleType="accept"
            className="flex items-center min-w-[120px]"
          >
            {TEXT_ACTION.SEARCH}
          </BasicButton>
        </div>
      </Form>

      <div className="flex gap-x-[12px]">
        {/* <BasicButton
          icon={<UploadOutlined style={{ color: '#EC980C' }} />}
          className="flex items-center !text-[#EC980C] !bg-white"
          styleType="noneOutLine"
          onClick={onImportCsv}
        >
          {TEXT_ACTION.CSV_Import}
        </BasicButton> */}
        <BasicButton
          icon={<DownloadOutlined style={{ color: '#3997C8' }} />}
          className="flex items-center !text-main-color !bg-white"
          styleType="noneOutLine"
          onClick={onExportCsv}
        >
          {TEXT_ACTION.CSV_Export}
        </BasicButton>
      </div>
    </div>
  );
};

export default HeaderAction;
