import type { AccountingTravelListDetailType } from '@/apis/accounting/traveList';
import BasicTable from '@/components/Commons/BasicTable';
import { formatMoney } from '@/utils';
import { Typography } from 'antd';
const { Link } = Typography;

interface Props {
  data: AccountingTravelListDetailType;
}

const AccountingInformationTableDetail = ({ data }: Props) => {
  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: '項目',
      dataIndex: 'item',
      key: 'item',
      width: 120,
    },
    {
      title: '金額',
      dataIndex: 'amount',
      key: 'amount',
      render: (_, record) => <p className="text-right">{formatMoney(record?.amount)}</p>,
    },
    {
      title: '',
      dataIndex: 'list',
      key: 'list',
      render: (_, record) => {
        return (
          <Link href={record?.list?.href} className="">
            {record?.list?.title}
          </Link>
        );
      },
    },
    {
      title: '',
      dataIndex: 'create',
      key: 'create',
      render: (_, record) => (
        <Link href={record?.create?.href} className="">
          {record?.create?.title}
        </Link>
      ),
    },
  ];

  const queryUrl = () => {
    const encodedURL = `?tourId=${data?.id}&keyword=${data?.travel_id}`;

    return encodedURL;
  };

  const dataSource = [
    {
      id: 1,
      item: '売上',
      amount: data?.sale ?? 0,
      list: { title: '売上一覧 ', href: '/accounting-management/sales-management' + queryUrl() },
      create: {
        title: '売上作成',
        href: '/accounting-management/sales-management/invoices' + queryUrl(),
      },
    },
    {
      id: 2,
      item: '請求',
      amount: data?.consolidated_invoice ?? 0,
      list: {
        title: '請求一覧',
        href: '/accounting-management/invoice-issuance' + '?tourId=' + data?.travel_id,
      }, //accounting-management/invoice-issuance
      create: {
        title: '請求作成',
        href: '/accounting-management/invoice-issuance/creation' + queryUrl(),
      }, //accounting-management/invoice-issuance/creation
    },
    {
      id: 3,
      item: '入金',
      amount: data?.deposit ?? 0,
      list: {
        title: '入金一覧',
        href: '/accounting-management/deposit-management' + `?tourId=${data?.travel_id}`,
      }, //accounting-management/deposit-management
      create: { title: '入金作成', href: '/accounting-management/deposit-management/creation' }, //accounting-management/deposit-management/creation
    },
    {
      id: 4,
      item: '仕入',
      amount: data?.purchase ?? 0,
      list: { title: '仕入一覧', href: '/accounting-management/purchase-management' + queryUrl() },
      create: {
        title: '仕入作成',
        href: '/accounting-management/purchase-management/invoices' + queryUrl(),
      },
    },
    {
      id: 5,
      item: '支払',
      amount: data?.payment ?? 0,
      list: {
        title: '支払一覧',
        href: '/accounting-management/payment-management' + `?tourId=${data?.travel_id}`,
      }, //accounting-management/payment-management
      create: { title: '支払作成', href: '/accounting-management/payment-management/creation' }, //accounting-management/payment-management/creation
    },
  ];

  return (
    <BasicTable
      className="!mt-0"
      tableProps={{
        scroll: { x: 550 },
        columns: defaultColumns,
        dataSource: dataSource,
        bordered: false,
        pagination: false,
        rowKey: 'id',
      }}
      hasPagination={false}
    />
  );
};

export default AccountingInformationTableDetail;
