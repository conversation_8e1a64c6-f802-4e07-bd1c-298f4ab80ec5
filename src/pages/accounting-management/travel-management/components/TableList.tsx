import type { TravelListSearchParams } from '@/apis/accounting/traveList';
import IconEye from '@/assets/imgs/common-icons/view-eye.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicTable from '@/components/Commons/BasicTable';
import { TEXT_TITLE } from '@/constants/commonText';
import { formatMoney } from '@/utils';
import { ITEM_PER_PAGE } from '@/utils/constants';
import type { FormInstance } from 'antd';
import { Form, Image } from 'antd';
import moment from 'moment';
import { history } from 'umi';

const TableListTravelManagement = ({
  form,
  isLoading,
  dataSource,
  paramSearch,
  setParamSearch,
  total,
}: {
  isLoading: boolean;
  form: FormInstance<any>;
  dataSource: any;
  paramSearch?: TravelListSearchParams;
  setParamSearch?: (val: TravelListSearchParams) => void;
  total: number;
}) => {
  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'id',
      key: 'id',
      width: 60,
      render: (_, {}, index) => {
        const order = (Number(paramSearch?.page) - 1) * Number(paramSearch?.limit ?? 0) + 1 + index;
        return <>{order}</>;
      },
    },
    {
      title: TEXT_TITLE.Travel_Id,
      dataIndex: 'travel_id',
      key: 'travel_id',
      width: 120,
    },
    {
      title: TEXT_TITLE.Tour_Name,
      dataIndex: 'tour_name',
      key: 'tour_name',
    },
    {
      title: TEXT_TITLE.Departure_Date,
      dataIndex: 'departure_date',
      key: 'departure_date',
      render: (_, { departure_date }) => (
        <>{departure_date && moment(departure_date).format('YYYY/MM/DD')}</>
      ),
    },
    {
      title: TEXT_TITLE.Return_Date_Trv,
      dataIndex: 'return_date',
      key: 'return_date',
      render: (_, { return_date }) => (
        <>{return_date && moment(return_date).format('YYYY/MM/DD')}</>
      ),
    },
    {
      title: TEXT_TITLE.Sale,
      dataIndex: 'sale',
      key: 'sale',
      render: (_, record) => (
        <p className="text-right">{record?.sale ? formatMoney(record?.sale) : ''}</p>
      ),
    },
    {
      title: TEXT_TITLE.Total_Billing_Amount,
      dataIndex: 'consolidated_invoice',
      key: 'consolidated_invoice',
      render: (_, record) => (
        <p className="text-right">
          {record?.consolidated_invoice ? formatMoney(record?.consolidated_invoice) : ''}
        </p>
      ),
    },
    {
      title: TEXT_TITLE.Deposit,
      dataIndex: 'deposit',
      key: 'deposit',
      render: (_, record) => (
        <p className="text-right">{record?.deposit ? formatMoney(record?.deposit) : ''}</p>
      ),
    },
    {
      title: TEXT_TITLE.Purchase,
      dataIndex: 'purchase',
      key: 'purchase',
      render: (_, record) => (
        <p className="text-right">{record?.purchase ? formatMoney(record?.purchase) : ''}</p>
      ),
    },
    {
      title: TEXT_TITLE.Payment,
      dataIndex: 'payment',
      key: 'payment',
      render: (_, record) => (
        <p className="text-right">{record?.payment ? formatMoney(record?.payment) : ''}</p>
      ),
    },
    {
      title: '粗利率',
      dataIndex: 'gross_profit_margin',
      key: 'gross_profit_margin',
      render: (_, record) => (
        <p className="text-right">
          {record?.gross_profit_margin === 0
            ? '0%'
            : record?.gross_profit_margin
            ? `${record?.gross_profit_margin?.toFixed(2)}%`
            : ''}
        </p>
      ),
    },

    {
      title: '',
      dataIndex: 'viewItem',
      key: 'viewItem',
      width: 112,
      render: (_, record) => (
        <BasicButton
          styleType="outline"
          className=" flex items-center justify-center !h-[24px] w-[112px] hover:border-[#3997C8] text-main-color"
          onClick={() => {
            history.push(`/accounting-management/travel-management/${record.id}`);
          }}
        >
          <Image preview={false} src={IconEye} width={16} height={16} />
          <p className="text-xs !ml-1">詳細を見る</p>
        </BasicButton>
      ),
    },
  ];

  return (
    <>
      <Form form={form} component={false}>
        <BasicTable
          className="!mt-0"
          tableProps={{
            scroll: { x: 1500 },
            loading: isLoading,
            columns: defaultColumns,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            rowKey: 'id',
          }}
          page={Number(paramSearch?.page) ?? 1}
          pageSize={Number(paramSearch?.limit) ?? Number(ITEM_PER_PAGE)}
          onChangePage={(p: number) => {
            setParamSearch({ ...paramSearch, page: p });
            // setParameter({ page: p, limit: paramSearch?.limit });
          }}
          total={total}
          onSelectPageSize={(v) => {
            setParamSearch({ ...paramSearch, limit: v, page: 1 });
            // setParameter({ page: 1, limit: v });
          }}
        />
      </Form>
    </>
  );
};

export default TableListTravelManagement;
