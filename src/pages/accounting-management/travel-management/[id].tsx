import PageContainer from '@/components/Commons/Page/Container';
import type { TripOverviewFormRef } from './components/TripOverview';
import TripOverview from './components/TripOverview';
import AccountingInformation from './components/AccountingInformation';
import { useParams } from 'react-router';
import type { AccountingTravelListDetailType } from '@/apis/accounting/traveList';
import {
  getAccountingTravelDetail,
  updateAccountingTravelDetail,
} from '@/apis/accounting/traveList';
import { useEffect, useRef, useState } from 'react';
import STATUS_CODE from '@/constants/statusCode';
import moment from 'moment';
import BasicButton from '@/components/Commons/BasicButton';
import { LeftOutlined, SaveOutlined } from '@ant-design/icons';
import {
  openNotificationApprove,
  openNotificationBlock,
  openNotificationFail,
} from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { history } from 'umi';
import { TEXT_ACTION } from '@/constants/commonText';

const TravelManagementDetailPage = () => {
  const { id } = useParams() as { id: string };

  const refTripOverview = useRef<TripOverviewFormRef>(null);

  const [detail, setDetail] = useState<AccountingTravelListDetailType>();
  const [listAttachedFile, setListAttachedFile] = useState<string[]>([]);

  const fetchDataDetail = async () => {
    try {
      const { data, status } = await getAccountingTravelDetail(Number(id));
      if (status === STATUS_CODE.SUCCESSFUL) {
        const rs = data.data;
        setDetail(rs);
        setListAttachedFile(rs.files ?? []);
        const gross_profit_margin =
          rs?.purchase && rs?.sale && rs.sale !== 0
            ? ((rs.sale - rs?.purchase) / rs.sale) * 100
            : undefined;
        const fieldsValueForm = {
          ...rs,
          travel_type: rs.travel_type ? Number(rs.travel_type) : undefined,
          departure_date: moment(rs.departure_date),
          return_date: moment(rs.return_date),
          gross_profit_margin:
            gross_profit_margin === 0
              ? '0%'
              : gross_profit_margin
              ? `${gross_profit_margin?.toFixed(2)}%`
              : '',
        };
        refTripOverview?.current?.form?.setFieldsValue(fieldsValueForm);
      } else {
        openNotificationBlock(MESSAGE_ALERT.SERVER_ERROR);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  const onSave = async (values) => {
    try {
      const payload = {
        ...values,
        adult_count: values?.adult_count,
        children_count: values?.children_count,
        departure_date: moment(values?.departure_date)?.format('YYYY-MM-DD'),
        return_date: moment(values?.return_date)?.format('YYYY-MM-DD'),
        files: listAttachedFile,
      };

      const { status } = await updateAccountingTravelDetail(Number(id), payload);
      if (status === STATUS_CODE.SUCCESSFUL) {
        openNotificationApprove(MESSAGE_ALERT.EDIT_SUCCESS);
      } else {
        openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    fetchDataDetail();
  }, [id]);

  return (
    <PageContainer>
      <TripOverview ref={refTripOverview} submit={onSave} />
      <AccountingInformation data={detail} travelId={id} />

      <div className="flex items-center justify-center gap-x-[20px]">
        <BasicButton
          className="flex items-center justify-center min-w-[190px]"
          styleType="noneOutLine"
          onClick={() => history.goBack()}
          icon={<LeftOutlined />}
        >
          {TEXT_ACTION.RETURN}
        </BasicButton>
        <BasicButton
          className="flex items-center min-w-[190px]"
          icon={<SaveOutlined />}
          onClick={() => {
            refTripOverview?.current?.form?.submit();
          }}
          styleType="accept"
        >
          {TEXT_ACTION.SAVE}
        </BasicButton>
      </div>
    </PageContainer>
  );
};

export default TravelManagementDetailPage;
