import type { ImportCsvCommonRef } from '@/components/CommonModalImportCsv';
import type { AccountingTravelListType } from '@/apis/accounting/traveList';
import CommonModalImportCsv, { EMenuType } from '@/components/CommonModalImportCsv';
import PageContainer from '@/components/Commons/Page/Container';
import STATUS_CODE from '@/constants/statusCode';
import { Form } from 'antd';
import { useEffect, useRef, useState } from 'react';
import HeaderAction from './components/HeaderAction';
import TableListTravelManagement from './components/TableList';
import { getListAccountingTravel } from '@/apis/accounting/traveList';
import { useUrlSearchParams } from 'use-url-search-params';
import { ITEM_PER_PAGE } from '@/utils/constants';

const initSearchParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};

const TravelManagementPage = () => {
  const [form] = Form.useForm();
  const refImportCsv = useRef<ImportCsvCommonRef>(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<AccountingTravelListType[]>([]);
  const [total, setTotal] = useState(0);
  // const [paramSearch, setParamSearch] = useState<TravelListSearchParams>({
  //   limit: query?.limit ? Number(query?.limit) : ITEM_PER_PAGE,
  //   page: query?.page ? Number(query?.page) : 1,
  // });

  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);

  const onFetchData = async () => {
    setIsLoading(true);
    try {
      const resGetListMaster = await getListAccountingTravel(parameter);
      if (resGetListMaster.status === STATUS_CODE.SUCCESSFUL) {
        const resData = resGetListMaster.data;
        setDataSource(
          resData.data.map((item) => ({
            ...item,
            gross_profit_margin:
              item?.purchase && item?.sale && item.sale !== 0
                ? ((item.sale - item?.purchase) / item.sale) * 100
                : undefined,
            key: item.id,
          })),
        );
        setTotal(Number(resData.total));
      }
    } catch (error) {
      console.log('error', error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    onFetchData();
  }, [
    parameter?.keyword,
    parameter?.limit,
    parameter?.page,
    parameter?.departure_date,
    parameter?.return_date,
  ]);

  useEffect(() => {
    dataSource.forEach((item) => {
      Object.keys(item).forEach((key) => {
        const keyForm = `${item.id}.${key}`;
        form.setFieldValue(keyForm, item[key]);
      });
    });
  }, [dataSource]);

  const onImportCsv = () => {
    if (refImportCsv) {
      refImportCsv.current.open();
    }
  };

  return (
    <PageContainer>
      <HeaderAction
        paramSearch={parameter}
        setParamSearch={setParameter}
        onImportCsv={onImportCsv}
      />
      <div className="p-2 rounded-xl bg-white mt-6">
        <TableListTravelManagement
          total={total}
          isLoading={isLoading}
          form={form}
          dataSource={dataSource}
          paramSearch={parameter}
          setParamSearch={setParameter}
        />
      </div>

      <CommonModalImportCsv
        ref={refImportCsv}
        type={EMenuType['travel-list']}
        refetch={onFetchData}
      />
    </PageContainer>
  );
};

export default TravelManagementPage;
