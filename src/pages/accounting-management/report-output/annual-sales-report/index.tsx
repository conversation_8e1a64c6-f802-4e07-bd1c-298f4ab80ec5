import type {
  ResponseDataReportTravelTypeByYear,
  ResponseDataReportTravelTypeForLastYear,
  ResponseReportTravelTypeByYear,
  ResponseReportTravelTypeForLastYear,
} from '@/apis/reportOutput/travelTypeByTime';
import { getReportTravelByYear } from '@/apis/reportOutput/travelTypeByTime';
import BasicButton from '@/components/Commons/BasicButton';
import PageContainer from '@/components/Commons/Page/Container';
import { TEXT_ACTION } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { formatMoney, formatPercent } from '@/utils';
import { DownloadOutlined } from '@ant-design/icons';
import { Form } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { useUrlSearchParams } from 'use-url-search-params';
import HeaderAction from './components/HeaderAction';
import TableByCurrentYear from './components/TableByCurrentYear';
import TableForLastYear from './components/TableForLastYear';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';

const initSearchParams = {};

const InvoicesPage = () => {
  const [formHeader] = Form.useForm();
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);
  const [isLoading, setIsLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);

  const formatDataForYear = (data: ResponseDataReportTravelTypeByYear[]) => {
    const dataRender: any[] = [];
    data.forEach((item, index) => {
      const saleColumns = {};
      const purchaseColumns = {};
      const rAmountColumns = {};
      const grossProfitTotalColumns = {};
      const grossProfitRateColumns = {};
      let lineData = item?.monthly_data;
      if (!item?.monthly_data && item?.summary) {
        lineData = item?.summary;
      }
      Object.keys(lineData).forEach((key) => {
        //sale
        saleColumns[`month.${key}`] = formatMoney(lineData[key].total_sales);
        //purchase
        purchaseColumns[`month.${key}`] = formatMoney(lineData[key].total_purchases);
        //rAmount
        rAmountColumns[`month.${key}`] = formatMoney(lineData[key].total_commission);
        //grossProfitTotal
        grossProfitTotalColumns[`month.${key}`] = formatMoney(lineData[key].total_gross_profit);
        //grossProfitRate
        grossProfitRateColumns[`month.${key}`] =
          lineData[key].total_gross_profit_rate !== 0
            ? formatPercent(lineData[key].total_gross_profit_rate, 2, '%')
            : '0.00%';
      });
      saleColumns['total'] = formatMoney(
        Object.values(lineData).reduce((acc, itemMonth) => acc + itemMonth.total_sales, 0),
      );
      purchaseColumns['total'] = formatMoney(
        Object.values(lineData).reduce((acc, itemMonth) => acc + itemMonth.total_purchases, 0),
      );
      rAmountColumns['total'] = formatMoney(
        Object.values(lineData).reduce((acc, itemMonth) => acc + itemMonth.total_commission, 0),
      );
      grossProfitTotalColumns['total'] = formatMoney(
        Object.values(lineData).reduce((acc, itemMonth) => acc + itemMonth.total_gross_profit, 0),
      );
      grossProfitRateColumns['total'] = formatPercent(
        (Object.values(lineData).reduce((acc, itemMonth) => acc + itemMonth.total_gross_profit, 0) /
          Object.values(lineData).reduce((acc, itemMonth) => acc + itemMonth.total_sales, 0)) *
          100,
        2,
        '%',
      );

      //売上金額
      dataRender.push({
        index: 1 + index * 5,
        key: !item?.monthly_data && item?.summary ? '' : 1 + index,
        category_id: !item?.monthly_data && item?.summary ? '' : item.id,
        travel_code: !item?.monthly_data && item?.summary ? '' : item.travel_code,
        category_name: !item?.monthly_data && item?.summary ? '合計' : item.travel_name,
        item: '売上金額',
        ...saleColumns,
      });
      dataRender.push({
        index: 2 + index * 5,
        item: '仕入金額',
        ...purchaseColumns,
      });
      dataRender.push({
        index: 3 + index * 5,
        item: '手数料 (Ｒ額)',
        ...rAmountColumns,
      });
      dataRender.push({
        index: 4 + index * 5,
        item: '粗利合計',
        ...grossProfitTotalColumns,
      });
      dataRender.push({
        index: 5 + index * 5,
        item: '粗利率',
        ...grossProfitRateColumns,
      });
    });
    return dataRender;
  };

  const formatDataForLastYear = (data: ResponseDataReportTravelTypeForLastYear[]) => {
    const dataRender: any[] = [];
    data.forEach((item, index) => {
      const saleColumns: any = {};
      const lastYearSaleColumns: any = {};
      const lastYearChangeSaleColumns: any = {};
      const rAmountColumns: any = {};
      const grossProfitTotalColumns: any = {};
      const lastYearGrossProfitTotalColumns: any = {};
      const lastYearChangeGrossProfitTotalColumns: any = {};
      let lineData = item?.monthly_data;
      if (!item?.monthly_data && item?.summary) {
        lineData = item?.summary;
      }
      Object.keys(lineData).forEach((key) => {
        //sale
        saleColumns[`month.${key}`] = formatMoney(lineData[key].total_sales);
        lastYearSaleColumns[`month.${key}`] = formatMoney(lineData[key].past_year_total_sales);
        lastYearChangeSaleColumns[`month.${key}`] = formatPercent(
          lineData[key].compare_total_sales,
          2,
          '%',
        );
        //rAmount
        rAmountColumns[`month.${key}`] = formatMoney(lineData[key].total_commission);
        //grossProfitTotal
        grossProfitTotalColumns[`month.${key}`] = formatMoney(lineData[key].total_gross_profit);
        lastYearGrossProfitTotalColumns[`month.${key}`] = formatMoney(
          lineData[key].past_year_total_gross_profit,
        );
        lastYearChangeGrossProfitTotalColumns[`month.${key}`] = formatPercent(
          lineData[key].compare_total_gross_profit,
          2,
          '%',
        );
      });
      saleColumns['total'] = formatMoney(
        Object.values(lineData).reduce((acc, itemMonth) => acc + itemMonth.total_sales, 0),
      );
      lastYearSaleColumns['total'] = formatMoney(
        Object.values(lineData).reduce(
          (acc, itemMonth) => acc + itemMonth.past_year_total_sales,
          0,
        ),
      );
      const totalSaleCurrentYear = Object.values(lineData).reduce(
        (acc, itemMonth) => acc + itemMonth.total_sales,
        0,
      );

      const totalSalePastYear = Object.values(lineData).reduce(
        (acc, itemMonth) => acc + itemMonth.past_year_total_sales,
        0,
      );

      lastYearChangeSaleColumns['total'] =
        totalSalePastYear === 0
          ? '0.00%'
          : formatPercent((totalSaleCurrentYear / totalSalePastYear) * 100, 2, '%');

      rAmountColumns['total'] = formatMoney(
        Object.values(lineData).reduce((acc, itemMonth) => acc + itemMonth.total_commission, 0),
      );
      const totalGrossProfit = Object.values(lineData).reduce(
        (acc, itemMonth) => acc + itemMonth.total_gross_profit,
        0,
      );
      grossProfitTotalColumns['total'] = formatMoney(totalGrossProfit);

      const totalGrossProfitPastYear = Object.values(lineData).reduce(
        (acc, itemMonth) => acc + itemMonth.past_year_total_gross_profit,
        0,
      );
      lastYearGrossProfitTotalColumns['total'] = formatMoney(totalGrossProfitPastYear);

      lastYearChangeGrossProfitTotalColumns['total'] =
        totalGrossProfitPastYear === 0
          ? '0.00%'
          : formatPercent((totalGrossProfit / totalGrossProfitPastYear) * 100, 2, '%');

      //売上金額
      dataRender.push({
        index: 1 + index * 7,
        key: !item?.monthly_data && item?.summary ? '' : 1 + index,
        category_id: !item?.monthly_data && item?.summary ? '' : item.id,
        travel_code: !item?.monthly_data && item?.summary ? '' : item.travel_code,
        category_name: !item?.monthly_data && item?.summary ? '合計' : item.travel_name,
        item: '売上金額',
        ...saleColumns,
      });
      dataRender.push({
        index: 2 + index * 7,
        item: '前年実績売上金額',
        ...lastYearSaleColumns,
      });
      dataRender.push({
        index: 3 + index * 7,
        item: '前年比 (売上金額)',
        ...lastYearChangeSaleColumns,
      });
      dataRender.push({
        index: 4 + index * 7,
        item: '手数料 (Ｒ額)',
        ...rAmountColumns,
      });
      dataRender.push({
        index: 5 + index * 7,
        item: '粗利合計',
        ...grossProfitTotalColumns,
      });
      dataRender.push({
        index: 6 + index * 7,
        item: '前年実績粗利合計',
        ...lastYearGrossProfitTotalColumns,
      });
      dataRender.push({
        index: 7 + index * 7,
        item: '前年比 (粗利合計)',
        ...lastYearChangeGrossProfitTotalColumns,
      });
    });
    return dataRender;
  };

  const onFetchData = async (isExport?: boolean) => {
    setIsLoading(true);
    try {
      const reportType = parameter.report_type;
      if (!reportType) {
        setIsLoading(false);
        return;
      }
      let travelTypeListParam = undefined;
      try {
        if (parameter?.travel_type && Array.isArray(parameter.travel_type)) {
          travelTypeListParam = parameter?.travel_type?.map((item: string) => Number(item));
        } else if (parameter?.travel_type && typeof parameter.travel_type === 'string') {
          travelTypeListParam = [Number(parameter?.travel_type)];
        }
      } catch (error) {
        console.log('travelTypeListParam error', error);
      }
      const valueSearch: { [key: string]: any } = {
        start: parameter.startDate
          ? moment(parameter.startDate as string).format('YYYY-MM')
          : moment(parameter.endDate as string)
              .subtract(11, 'months')
              .startOf('month')
              .format('YYYY-MM'),
        finish: parameter.endDate
          ? moment(parameter.endDate as string).format('YYYY-MM')
          : undefined,
        limit: parameter.limit ? (parameter.limit as string) : 'all',
        report_type: reportType as string,
        is_last_year: reportType === 'report_with_past_year' ? 1 : 0,
        travel_type: travelTypeListParam,
        is_excluding_tax: parameter?.is_excluding_tax
          ? JSON.parse(parameter?.is_excluding_tax as string)
          : 1,
        type_date: (parameter.type_date as string) || 'voucher_posting_date',
      };
      if (isExport) {
        valueSearch.is_export_excel = 1;
        const resExport = await getReportTravelByYear(valueSearch);
        window.open(resExport.data?.data?.file_url);
        setIsLoading(false);
        return;
      }
      setDataSource([]);
      const resGetList = await getReportTravelByYear(valueSearch);
      if (resGetList.status === STATUS_CODE.SUCCESSFUL) {
        let dataRender: any[] = [];
        if (reportType === 'report_by_year') {
          dataRender = formatDataForYear(
            resGetList.data?.data?.data as ResponseReportTravelTypeByYear,
          );
        } else if (reportType === 'report_with_past_year') {
          dataRender = formatDataForLastYear(
            resGetList.data?.data?.data as ResponseReportTravelTypeForLastYear,
          );
        }
        setDataSource(dataRender);
      } else {
        openNotificationFail({ message: MESSAGE_ALERT.FAILED_TO_GET_DATA });
      }
    } catch (error) {
      console.log('error', error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    // set Form Header
    if (formHeader) {
      let travelTypeListParam = undefined;
      try {
        if (parameter?.travel_type && Array.isArray(parameter.travel_type)) {
          travelTypeListParam = parameter?.travel_type?.map((item: string) => Number(item));
        } else if (parameter?.travel_type && typeof parameter.travel_type === 'string') {
          travelTypeListParam = [Number(parameter?.travel_type)];
        }
      } catch (error) {
        console.log('travelTypeListParam error', error);
      }
      formHeader.setFieldsValue({
        ...parameter,
        type_date: (parameter.type_date as string) ?? 'voucher_posting_date',
        startDate: parameter.startDate ? moment(parameter.startDate as string) : undefined,
        endDate: parameter.endDate
          ? moment(parameter.endDate as string)
          : moment().subtract(1, 'months').endOf('month'),
        travel_type: travelTypeListParam,
        is_excluding_tax: parameter?.is_excluding_tax
          ? JSON.parse(parameter?.is_excluding_tax as string)
          : 1,
      });
    }
    onFetchData();
  }, [parameter]);

  const onSubmit = async () => {
    const valueSubmit = await formHeader.validateFields();
    setParameter({
      ...valueSubmit,
      startDate: valueSubmit.startDate && moment(valueSubmit.startDate).format('YYYY-MM-DD'),
      endDate: valueSubmit.endDate && moment(valueSubmit.endDate).format('YYYY-MM-DD'),
    });
  };

  return (
    <PageContainer>
      <HeaderAction form={formHeader} onSubmit={onSubmit} />
      <div className="p-2 rounded-xl bg-white mt-6">
        {(!parameter.report_type || parameter.report_type == 'report_by_year') && (
          <TableByCurrentYear
            isLoading={isLoading}
            dataSource={dataSource}
            year={formHeader?.getFieldValue('startDate')?.format('YYYY年')}
          />
        )}
        {parameter.report_type == 'report_with_past_year' && (
          <TableForLastYear
            year={formHeader?.getFieldValue('startDate')?.format('YYYY年')}
            isLoading={isLoading}
            dataSource={dataSource}
          />
        )}
      </div>

      <div className="flex gap-6 justify-center mt-10">
        <BasicButton
          disabled={isLoading}
          styleType="accept"
          className={`!w-[190px] flex justify-center items-center ${
            isLoading ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
          }`}
          onClick={() => {
            onFetchData(true);
          }}
        >
          <DownloadOutlined style={{ color: 'white' }} />
          <p>{TEXT_ACTION.Report_output}</p>
        </BasicButton>
      </div>
    </PageContainer>
  );
};

export default InvoicesPage;
