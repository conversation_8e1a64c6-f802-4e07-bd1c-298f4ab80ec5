import BasicTable from '@/components/Commons/BasicTable';
import EditableCell from '@/components/Commons/EditableCell';
import { useMemo } from 'react';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  year,
  isLoading,
  dataSource,
}: {
  year: string;
  isLoading: boolean;
  dataSource: any[];
}) => {
  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };

  const columns = useMemo(() => {
    if (!dataSource || dataSource?.length < 1) return [];
    const listMonth = Object.keys(dataSource[0]).filter((item) => item.includes('month'));
    const listMonthColumns = listMonth.map((item) => ({
      title: item.split('.')[1].split('-')[0],
      dataIndex: 'year' + year,
      key: 'year' + year,
      width: 120,
      children: [
        {
          title: item.split('-')[1] + '月',
          dataIndex: item,
          key: item,
          width: 120,

          render: (_, record) => <p className="text-right !ml-1">{record[item]}</p>,
        },
      ],
    }));
    return [
      {
        title: '',
        dataIndex: 'key',
        key: 'key',
        width: 70,
      },
      {
        title: '旅行種別コード',
        dataIndex: 'travel_code',
        key: 'travel_code',
        width: 140,
      },
      {
        title: '旅行種別名称',
        dataIndex: 'category_name',
        key: 'category_name',
        width: 200,
      },
      {
        title: '項目',
        dataIndex: 'item',
        key: 'item',
        width: 160,
      },
      ...listMonthColumns,
      {
        title: '合計',
        dataIndex: 'total',
        key: 'total',
        width: 160,
        render: (_, { total }) => <p className="text-right !ml-1">{total}</p>,
      },
    ];
  }, [dataSource]);
  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1600, y: 800 },
          loading: isLoading,
          components: {
            body: {
              cell: EditableCell,
            },
          },
          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'index',
          rowClassName: getRowClassName,
        }}
        hasPagination={false}
      />
    </>
  );
};

export default TableList;
