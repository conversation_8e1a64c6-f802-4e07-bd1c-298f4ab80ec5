import BasicTable from '@/components/Commons/BasicTable';
import { formatMoney } from '@/utils';
import { Table } from 'antd';
import moment from 'moment';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  isLoading,
  dataSource,
  AmountIncludingTax,
  RAmount,
  CurrentPaymentAmount,
  PaymentBalance,
  PaymentAmountAfterRAamount,
}: {
  isLoading: boolean;
  dataSource: any[];
  AmountIncludingTax: number;
  RAmount: number;
  CurrentPaymentAmount: number;
  PaymentBalance: number;
  PaymentAmountAfterRAamount: number;
}) => {
  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };

  const columns = [
    {
      title: '',
      dataIndex: 'key',
      key: 'key',
      width: 75,
    },

    {
      title: '仕入先ID',
      dataIndex: 'business_partner_code',
      key: 'business_partner_code',
      width: 120,
      children: [
        {
          title: '仕入先名',
          dataIndex: 'business_partner_name',
          key: 'business_partner_name',
          width: 120,
          render: (_, record) => (
            <p className={`${record.type === 'total' ? 'font-medium' : ''} !ml-1`}>
              {record.business_partner_code}
              <br />
              {record.business_partner_name}
            </p>
          ),
        },
      ],
    },
    {
      title: '旅行ID',
      dataIndex: 'travel_code',
      key: 'travel_code',
      width: 120,
      children: [
        {
          title: '仕入伝票ID',
          dataIndex: 'purchase_invoice_code',
          key: 'purchase_invoice_code',
          width: 120,
          render: (_, record) => {
            if (record.type === 'total') {
              return <></>;
            }
            return (
              <p className=" !ml-1">
                {record.travel_code}
                <br />
                {record.purchase_invoice_code}
              </p>
            );
          },
        },
      ],
    },
    {
      title: 'ツアー名',
      dataIndex: 'tour_name',
      key: 'tour_name',
      width: 120,
      children: [
        {
          title: '集計科目名',
          dataIndex: 'summary_subject_name',
          key: 'summary_subject_name',
          width: 120,
          render: (_, record) => {
            if (record.type === 'total') {
              return <></>;
            }
            return (
              <p className=" !ml-1">
                {record.tour_name}
                <br />
                {record.summary_subject_name}
              </p>
            );
          },
        },
      ],
    },

    {
      title: '計上日',
      dataIndex: 'purchase_date',
      key: 'purchase_date',
      width: 120,
      children: [
        {
          title: '品名',
          dataIndex: 'product_name',
          key: 'product_name',
          width: 120,
          render: (_, record) => {
            if (record.type === 'total') {
              return <>{record.title}</>;
            }
            return (
              <p className=" !ml-1">
                {record.purchase_date ? moment(record.purchase_date).format('YYYY/MM/DD') : ''}
                <br />
                {record.product_name}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '仕入',
      dataIndex: 'purchase',
      key: 'purchase',
      width: 120,
      children: [
        {
          title: '仕入金額',
          dataIndex: 'amount_including_tax',
          key: 'amount_including_tax',
          width: 120,
          render: (_, record) => {
            return <p className="text-right">{formatMoney(record.amount_including_tax)}</p>;
          },
        },
      ],
    },
    {
      title: '支払',
      dataIndex: 'payment',
      key: 'payment',
      width: 120,
      children: [
        {
          title: '手数料（R額）',
          dataIndex: 'r_amount',
          key: 'r_amount',
          width: 120,
          render: (_, record) => {
            return <p className="text-right">{formatMoney(record.r_amount)}</p>;
          },
        },
        {
          title: '支払金額',
          dataIndex: 'current_payment_amount',
          key: 'current_payment_amount',
          width: 120,
          render: (_, record) => {
            return <p className="text-right">{formatMoney(record?.current_payment_amount)}</p>;
          },
        },
        {
          title: '支払残額',
          dataIndex: 'payment_balance',
          key: 'payment_balance',
          width: 120,
          render: (_, record) => {
            return <p className="text-right">{formatMoney(record.payment_balance)}</p>;
          },
        },
      ],
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1400, y: 800 },
          loading: isLoading,
          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'index',
          rowClassName: getRowClassName,
          summary: (pageData) => (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={4} />
              <Table.Summary.Cell index={5} className="font-medium">
                出力期間　合計
              </Table.Summary.Cell>
              <Table.Summary.Cell index={5} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(AmountIncludingTax)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={6} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(RAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={7} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(CurrentPaymentAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={8} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(PaymentBalance)}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          ),
        }}
        hasPagination={false}
      />
    </>
  );
};

export default TableList;
