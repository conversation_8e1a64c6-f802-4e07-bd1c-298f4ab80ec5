import BasicTable from '@/components/Commons/BasicTable';
import { formatMoney } from '@/utils';
import { Table } from 'antd';
import moment from 'moment';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  isLoading,
  dataSource,
  CurrentPaymentAmount,
  FeeAmount,
}: {
  isLoading: boolean;
  dataSource: any[];
  CurrentPaymentAmount: number;
  FeeAmount: number;
}) => {
  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };

  const columns = [
    {
      title: '',
      dataIndex: 'key',
      key: 'key',
      width: 75,
    },

    {
      title: '支払日',
      dataIndex: 'payment_date',
      key: 'payment_date',
      width: 120,
      children: [
        {
          title: '支払伝票ID',
          dataIndex: 'payment_slip_id',
          key: 'payment_slip_id',
          width: 120,
          render: (_, record) => (
            <p className=" !ml-1">
              {record.payment_date ? moment(record.payment_date).format('YYYY/MM/DD') : ''}
              <br />
              {record.payment_slip_id}
            </p>
          ),
        },
      ],
    },

    {
      title: '仕入先ID',
      dataIndex: 'business_partner_code',
      key: 'business_partner_code',
      width: 120,
      children: [
        {
          title: '仕入先名',
          dataIndex: 'business_partner_name',
          key: 'business_partner_name',
          width: 120,
          render: (_, record) => (
            <p className=" !ml-1">
              {record.business_partner_code}
              <br />
              {record.business_partner_name}
            </p>
          ),
        },
      ],
    },
    {
      title: '旅行ID',
      dataIndex: 'travel_code',
      key: 'travel_code',
      width: 120,
      children: [
        {
          title: '仕入伝票ID',
          dataIndex: 'purchase_invoice_code',
          key: 'purchase_invoice_code',
          width: 120,
          render: (_, record) => {
            if (record.type === 'total') {
              return <></>;
            }
            return (
              <p className=" !ml-1">
                {record.travel_code}
                <br />
                {record.purchase_invoice_code}
              </p>
            );
          },
        },
      ],
    },
    {
      title: 'ツアー名',
      dataIndex: 'tour_name',
      key: 'tour_name',
      width: 160,
      children: [
        {
          title: '品名',
          dataIndex: 'summary_subject_name',
          key: 'summary_subject_name',
          width: 160,
          render: (_, record) => {
            if (record.type === 'total') {
              return <p className="whitespace-pre">{record.title}</p>;
            }
            return (
              <p className=" !ml-1">
                {record.tour_name}
                <br />
                {record.product_name}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '支払',
      dataIndex: 'payment',
      key: 'payment',
      width: 120,
      children: [
        {
          title: '支払金種',
          dataIndex: 'currency_type_master',
          key: 'currency_type_master',
          width: 120,
          render: (_, record) => <p className=" !ml-1">{record.currency_type_master}</p>,
        },
        {
          title: '支払金額',
          dataIndex: 'current_payment_amount',
          key: 'current_payment_amount',
          width: 120,
          render: (_, record) => (
            <p className="text-right !ml-1">{formatMoney(record.current_payment_amount)}</p>
          ),
        },
        {
          title: '振込手数料',
          dataIndex: 'fee',
          key: 'fee',
          width: 120,
          render: (_, record) => <p className="text-right !ml-1">{formatMoney(record.fee)}</p>,
        },
      ],
    },
    {
      title: '備考',
      dataIndex: 'remark',
      key: 'remark',
      width: 120,
      children: [
        {
          title: '支払内訳',
          dataIndex: 'memo',
          key: 'memo',
          width: 120,
          render: (_, record) => {
            if (record.type === 'total') {
              return <p className=" !ml-1">{record.remark}</p>;
            }
            return <p className=" !ml-1">{record.memo}</p>;
          },
        },
      ],
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1400, y: 800 },
          loading: isLoading,
          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'index',
          rowClassName: getRowClassName,
          summary: (pageData) => (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={4} />
              <Table.Summary.Cell index={5} className="font-medium">
                出力期間　合計
              </Table.Summary.Cell>
              <Table.Summary.Cell index={6} colSpan={1} />
              <Table.Summary.Cell index={5} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(CurrentPaymentAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={6} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(FeeAmount)}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          ),
        }}
        hasPagination={false}
      />
    </>
  );
};

export default TableList;
