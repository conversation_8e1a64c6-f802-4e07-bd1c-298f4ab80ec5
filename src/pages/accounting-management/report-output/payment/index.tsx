import { BusinessPartnerDetailType } from '@/@types/businessPartner';
import { getListBusinessPartner } from '@/apis/businessPartner';
import type {
  ResponseReportPaymentType,
  ResponseReportPaymentUnpaidType,
} from '@/apis/reportOutput/payment';
import { getReportPayment } from '@/apis/reportOutput/payment';
import BasicButton from '@/components/Commons/BasicButton';
import PageContainer from '@/components/Commons/Page/Container';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { DownloadOutlined } from '@ant-design/icons';
import { Form } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { useUrlSearchParams } from 'use-url-search-params';
import HeaderAction from './components/HeaderAction';
import TablePayment from './components/TablePayment';
import TablePaymentUnpaid from './components/TablePaymentUnpaid';
import { BaseOptionType } from '@/utils';

const initSearchParams = {};

const InvoicesPage = () => {
  const [formHeader] = Form.useForm();
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);
  const [isLoading, setIsLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [FeeAmount, setFeeAmount] = useState(0);
  const [AmountIncludingTax, setAmountIncludingTax] = useState(0);
  const [RAmount, setRAmount] = useState(0);
  const [CurrentPaymentAmount, setCurrentPaymentAmount] = useState(0);
  const [PaymentBalance, setPaymentBalance] = useState(0);
  const [PaymentAmountAfterRAamount, setPaymentAmountAfterRAamount] = useState(0);

  const [optionsBusinessPartner, setOptionsBusinessPartner] = useState<BaseOptionType[]>();
  const [firstCodeBusinessPartner, setFirstCodeBusinessPartner] = useState<string>();
  const [secondCodeBusinessPartner, setSecondCodeBusinessPartner] = useState<string>();

  const formatOptionBusinessPartner = async () => {
    const resData = await getListBusinessPartner({
      page: 1,
      limit: 'all',
      order: 'desc',
      order_by: 'business_partner_code',
    });
    const listCompany = resData?.data?.data;
    if (listCompany) {
      const newList = listCompany?.map((item: BusinessPartnerDetailType) => {
        return {
          label: item.business_partner_name,
          value: item.business_partner_code?.toString(),
          id: item.id,
        };
      });
      newList.sort((a, b) => Number(a.value) - Number(b.value));
      setOptionsBusinessPartner(newList);
      return;
    }
    setOptionsBusinessPartner([]);
  };

  useEffect(() => {
    formatOptionBusinessPartner();
  }, []);

  const formatDataPayment = (data: ResponseReportPaymentType[][]) => {
    const dataRender: any[] = [];
    let key = 1;
    let feeTotalAmount = 0;
    let currentPaymentTotalAmount = 0;
    data.forEach((childrenDate, indexChildren) => {
      let feeTotalGroup = 0;
      let currentPaymentTotalGroup = 0;
      childrenDate.map((groupData, indexGroup) => {
        groupData.items.forEach((itemDetail, indexDetail) => {
          dataRender.push({
            index: `payment_${indexChildren}_${indexDetail}_${indexGroup} `,
            key: indexGroup === 0 && indexDetail === 0 && key,
            payment_date: indexGroup === 0 && indexDetail === 0 && groupData?.payment_date,
            payment_slip_id: indexGroup === 0 && indexDetail === 0 && groupData?.payment_slip_id,
            business_partner_code:
              indexGroup === 0 &&
              indexDetail === 0 &&
              groupData?.business_partner?.business_partner_code,
            business_partner_name:
              indexGroup === 0 &&
              indexDetail === 0 &&
              groupData?.business_partner?.business_partner_name,
            travel_code: itemDetail?.travel_code,
            purchase_invoice_code: itemDetail?.purchase_invoice_code,
            tour_name: itemDetail?.tour_name,
            product_name: itemDetail?.product_name,
            currency_type_master: groupData?.currency_type_master?.currency_type_name,
            current_payment_amount: itemDetail?.current_payment_amount,
            payment_amount_after_r_amount: itemDetail?.payment_amount_after_r_amount,
            fee: childrenDate.length === 1 && groupData.items.length === 1 && (groupData.fee ?? 0),
            memo:
              childrenDate.length === 1 && groupData.items.length === 1 && (groupData.memo ?? ''),
          });
        });
        feeTotalGroup += groupData.fee;
        currentPaymentTotalGroup += groupData.payment_amount;
        feeTotalAmount += groupData.fee;
        currentPaymentTotalAmount += groupData.payment_amount;
      });
      const subTotalCategory = parameter?.sub_total;
      let title = '';
      switch (subTotalCategory) {
        case 'category_payment_slip':
          title = `支払伝票別   計`;
          break;
        case 'category_payment_date':
          title = `支払日別   計`;
          break;
        case 'category_payment_recipient':
          title = `支払先別   計`;
          break;
        case 'category_monthly':
          title = `月   計`;
          break;
        case 'category_payment_type':
          title = `支払種別   計`;
          break;
        default:
          break;
      }
      if (
        childrenDate.length > 1 ||
        (childrenDate.length === 1 && childrenDate[0].items.length > 1)
      ) {
        dataRender.push({
          index: `total_${key}_${indexChildren}`,
          type: 'total',
          fee: feeTotalGroup,
          remark: childrenDate?.[0]?.memo ?? '',
          title,
          current_payment_amount: currentPaymentTotalGroup,
          currency_type_master:
            subTotalCategory === 'category_payment_type' &&
            childrenDate?.[0]?.currency_type_master?.currency_type_name,
          product_name:
            subTotalCategory === 'category_payment_slip' && childrenDate?.[0]?.payment_slip_id,
          payment_date:
            subTotalCategory === 'category_payment_date' && childrenDate?.[0]?.payment_date,
          payment_slip_id:
            subTotalCategory === 'category_payment_slip' && childrenDate?.[0]?.payment_slip_id,
          business_partner_code:
            subTotalCategory === 'category_payment_recipient' &&
            childrenDate?.[0]?.business_partner?.business_partner_code,
          business_partner_name:
            subTotalCategory === 'category_payment_recipient' &&
            childrenDate?.[0]?.business_partner?.business_partner_name,
        });
      }
      key++;
    });

    setFeeAmount(feeTotalAmount);
    setCurrentPaymentAmount(currentPaymentTotalAmount);
    return dataRender;
  };

  const formatDataPaymentUnpaid = (data: ResponseReportPaymentUnpaidType[]) => {
    const dataRender: any[] = [];
    let key = 1;
    let totalAmountIncludingTax = 0;
    let totalRAmount = 0;
    let totalCurrentPaymentAmount = 0;
    let totalPaymentAmountAfterRAmount = 0;
    let totalPaymentBalance = 0;
    data.forEach((childrenDate, indexChildren) => {
      childrenDate.items.forEach((itemDetail, indexDetail) => {
        dataRender.push({
          index: `payment_${indexChildren}_${indexDetail}_${indexDetail} `,
          key: indexDetail === 0 && indexDetail === 0 && key,
          business_partner_code:
            indexDetail === 0 && indexDetail === 0
              ? childrenDate?.business_partner?.business_partner_code
              : '',
          business_partner_name:
            indexDetail === 0 && indexDetail === 0
              ? childrenDate?.business_partner?.business_partner_name
              : '',
          travel_code: itemDetail?.travel_code,
          purchase_invoice_code: itemDetail?.purchase_invoice_code,
          tour_name: itemDetail?.tour_name,
          product_name: itemDetail?.product_name,
          purchase_date: itemDetail?.purchase_date,
          // currency_type_master: childrenDate?.currency_type_master?.currency_type_name,
          amount_including_tax: itemDetail?.amount_including_tax,
          r_amount: itemDetail?.r_amount,
          summary_subject_name: itemDetail?.summary_subject_name,
          current_payment_amount: itemDetail?.current_payment_amount,
          payment_amount_after_r_amount: itemDetail?.amount_including_tax - itemDetail?.r_amount,
          payment_balance:
            itemDetail?.amount_including_tax -
            itemDetail?.r_amount -
            itemDetail?.current_payment_amount,
        });
      });

      totalAmountIncludingTax += childrenDate?.total_amount_tax;
      totalRAmount += childrenDate?.total_r_amount;
      totalCurrentPaymentAmount += childrenDate?.total_current_payment_amount;
      totalPaymentBalance += childrenDate?.total_payment_balance;
      totalPaymentAmountAfterRAmount += childrenDate?.total_payment_amount_after_r_amount;

      dataRender.push({
        index: `total_${key}_${indexChildren}`,
        type: 'total',
        title: `${childrenDate?.business_partner?.business_partner_name || ''}　合計`,
        business_partner_code: childrenDate?.business_partner?.business_partner_code,
        business_partner_name: childrenDate?.business_partner?.business_partner_name,
        amount_including_tax: childrenDate.total_amount_tax,
        r_amount: childrenDate.total_r_amount,
        current_payment_amount: childrenDate.total_current_payment_amount,
        payment_amount_after_r_amount: childrenDate.total_payment_balance,
        payment_balance: childrenDate.total_payment_balance,
      });
      key++;
    });

    setAmountIncludingTax(totalAmountIncludingTax);
    setRAmount(totalRAmount);
    setCurrentPaymentAmount(totalCurrentPaymentAmount);
    setPaymentBalance(totalPaymentBalance);
    setPaymentAmountAfterRAamount(totalPaymentAmountAfterRAmount);
    return dataRender;
  };

  const onFetchData = async (isExport?: boolean) => {
    setIsLoading(true);
    try {
      const reportType = parameter.report_type;
      if (!reportType) {
        setIsLoading(false);
        return;
      }
      let currencyTypeListParam = undefined;
      try {
        if (
          parameter?.currency_type_master_ids &&
          Array.isArray(parameter.currency_type_master_ids)
        ) {
          currencyTypeListParam = parameter?.currency_type_master_ids?.map((item: string) =>
            Number(item),
          );
        } else if (
          parameter?.currency_type_master_ids &&
          typeof parameter.currency_type_master_ids === 'string'
        ) {
          currencyTypeListParam = [Number(parameter?.currency_type_master_ids)];
        }
      } catch (error) {
        console.log('currencyTypeListParam error', error);
      }
      const valueSearch: { [key: string]: any } = {
        start: parameter.startDate
          ? moment(parameter.startDate as string).format('YYYY-MM-DD')
          : moment(parameter.endDate as string)
              .subtract(11, 'months')
              .startOf('month')
              .format('YYYY-MM-DD'),
        finish: parameter.endDate
          ? moment(parameter.endDate as string).format('YYYY-MM-DD')
          : undefined,
        report_type: reportType as string,
        currency_type_master_ids: currencyTypeListParam,
        type_date: parameter.type_date as string,
        sub_total: reportType !== 'report_payment_list' ? undefined : parameter?.sub_total,
        limit: parameter.limit ? (parameter.limit as string) : 'all',
        bp_code_start: parameter.bp_code_start,
        bp_code_end: parameter.bp_code_end,
      };
      if (isExport) {
        valueSearch.is_export_excel = 1;
        const resExport = await getReportPayment(valueSearch);
        if (resExport.status !== STATUS_CODE.SUCCESSFUL) {
          openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
          setIsLoading(false);
          return;
        }
        window.open(resExport.data?.data?.file_url);
        setIsLoading(false);
        return;
      }
      setDataSource([]);
      const resGetList = await getReportPayment(valueSearch);
      if (resGetList.status === STATUS_CODE.SUCCESSFUL) {
        let dataRender: any[] = [];
        if (reportType === 'report_payment_list') {
          dataRender = formatDataPayment(resGetList.data as ResponseReportPaymentType[][]);
        } else if (reportType === 'report_payment_list_unpaid') {
          dataRender = formatDataPaymentUnpaid(
            resGetList.data as ResponseReportPaymentUnpaidType[],
          );
        }
        setDataSource(dataRender);
      } else {
        openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
      }
    } catch (error) {
      console.log('error', error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    // set Form Header
    if (formHeader) {
      setFirstCodeBusinessPartner(parameter.bp_code_start as string | undefined);
      setSecondCodeBusinessPartner(parameter.bp_code_end as string | undefined);
      let currencyTypeListParam = undefined;
      try {
        if (
          parameter?.currency_type_master_ids &&
          Array.isArray(parameter.currency_type_master_ids)
        ) {
          currencyTypeListParam = parameter?.currency_type_master_ids?.map((item: string) =>
            Number(item),
          );
        } else if (
          parameter?.currency_type_master_ids &&
          typeof parameter.currency_type_master_ids === 'string'
        ) {
          currencyTypeListParam = [Number(parameter?.currency_type_master_ids)];
        }
      } catch (error) {
        console.log('currencyTypeListParam error', error);
      }
      formHeader.setFieldsValue({
        ...parameter,
        startDate: parameter.startDate ? moment(parameter.startDate as string) : undefined,
        endDate: parameter.endDate
          ? moment(parameter.endDate as string)
          : moment().subtract(1, 'months').endOf('month'),
        currency_type_master_ids: currencyTypeListParam,
        bp_code_start: parameter.bp_code_start,
        bp_code_end: parameter.bp_code_end,
      });
    }
    onFetchData();
  }, [parameter]);

  const onSubmit = async () => {
    const valueSubmit = await formHeader.validateFields();
    let startDate = valueSubmit.startDate && moment(valueSubmit.startDate).format('YYYY-MM-DD');
    let endDate = valueSubmit.endDate && moment(valueSubmit.endDate).format('YYYY-MM-DD');
    const reportType = valueSubmit.report_type;
    if (reportType !== 'report_payment_list') {
      startDate =
        valueSubmit.startDate &&
        moment(valueSubmit.startDate).startOf('months').format('YYYY-MM-DD');
      endDate =
        valueSubmit.endDate && moment(valueSubmit.endDate).endOf('months').format('YYYY-MM-DD');
    }
    setParameter({
      ...valueSubmit,
      startDate,
      endDate,
      type_date: valueSubmit.type_date ? (valueSubmit.type_date as string) : 'voucher_posting_date',
      bp_code_start: firstCodeBusinessPartner,
      bp_code_end: secondCodeBusinessPartner,
    });
  };

  return (
    <PageContainer>
      <HeaderAction
        form={formHeader}
        onSubmit={onSubmit}
        optionsBusinessPartner={optionsBusinessPartner}
        firstCodeBusinessPartner={firstCodeBusinessPartner}
        secondCodeBusinessPartner={secondCodeBusinessPartner}
        setFirstCodeBusinessPartner={setFirstCodeBusinessPartner}
        setSecondCodeBusinessPartner={setSecondCodeBusinessPartner}
        subTotalParam={parameter.sub_total}
      />
      <div className="p-2 rounded-xl bg-white mt-6">
        {(!parameter.report_type || parameter.report_type === 'report_payment_list') && (
          <TablePayment
            isLoading={isLoading}
            dataSource={dataSource}
            CurrentPaymentAmount={CurrentPaymentAmount}
            FeeAmount={FeeAmount}
          />
        )}
        {parameter.report_type === 'report_payment_list_unpaid' && (
          <TablePaymentUnpaid
            isLoading={isLoading}
            dataSource={dataSource}
            AmountIncludingTax={AmountIncludingTax}
            RAmount={RAmount}
            CurrentPaymentAmount={CurrentPaymentAmount}
            PaymentBalance={PaymentBalance}
            PaymentAmountAfterRAamount={PaymentAmountAfterRAamount}
          />
        )}
      </div>

      <div className="flex gap-6 justify-center mt-10">
        <BasicButton
          disabled={isLoading}
          styleType="accept"
          className={`!w-[190px] flex justify-center items-center ${
            isLoading ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
          }`}
          onClick={() => {
            onFetchData(true);
          }}
        >
          <DownloadOutlined style={{ color: 'white' }} />
          <p>{TEXT_ACTION.Report_output}</p>
        </BasicButton>
      </div>
    </PageContainer>
  );
};

export default InvoicesPage;
