import type {
  ResponseDataReportTravelTypeBy<PERSON>onth,
  ResponseReportDataTravelTypeByMonthWithLastYear,
  ResponseReportTravelTypeByMonth,
  ResponseReportTravelTypeByMonthWithLastYear,
} from '@/apis/reportOutput/travelTypeByTime';
import { getReportTravelByMonth } from '@/apis/reportOutput/travelTypeByTime';
import BasicButton from '@/components/Commons/BasicButton';
import PageContainer from '@/components/Commons/Page/Container';
import { TEXT_ACTION } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { formatMoney, formatPercent } from '@/utils';
import { DownloadOutlined } from '@ant-design/icons';
import { Form } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { useUrlSearchParams } from 'use-url-search-params';
import HeaderAction from './components/HeaderAction';
import TableByMonth from './components/TableByMonth';
import TableByYear from './components/TableByYear';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';

const initSearchParams = {};

const InvoicesPage = () => {
  const [formHeader] = Form.useForm();
  const reportTypeForm = Form.useWatch('report_type', formHeader);
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);
  const [isLoading, setIsLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [SalesAmount, setSalesAmount] = useState(0);
  const [PurchaseAmount, setPurchaseAmount] = useState(0);
  const [GrossProfitAmount, setGrossProfitAmount] = useState(0);
  const [RAmount, setRAmount] = useState(0);
  const [GrossProfitTotal, setGrossProfitTotal] = useState(0);
  const [GrossProfitRate, setGrossProfitRate] = useState(0);

  const formatDataByMonth = (data: ResponseDataReportTravelTypeByMonth[]) => {
    const Sales_amount = data.reduce((acc, item) => acc + item.total_sale_amount, 0);
    const Purchase_amount = data.reduce((acc, item) => acc + item.total_purchase_amount, 0);
    const Gross_profit_amount = data.reduce((acc, item) => acc + item.gross_profit_amount, 0);
    const R_amount = data.reduce((acc, item) => acc + item.commission, 0);
    const Gross_profit_total = data.reduce((acc, item) => acc + item.total_gross_profit, 0);
    setSalesAmount(Sales_amount);
    setPurchaseAmount(Purchase_amount);
    setGrossProfitAmount(Gross_profit_amount);
    setRAmount(R_amount);
    setGrossProfitTotal(Gross_profit_total);
    const intGrossProfitRate = Sales_amount === 0 ? 0 : (Gross_profit_total / Sales_amount) * 100;
    setGrossProfitRate(intGrossProfitRate);

    const dataRender: any[] = [];
    data.forEach((item, index) => {
      dataRender.push({
        key: 1 + index,
        travel_code: item.travel_code,
        category_name: item.category_name,
        Sales_amount: formatMoney(item.total_sale_amount),
        Purchase_amount: formatMoney(item.total_purchase_amount),
        Gross_profit_amount: formatMoney(item.gross_profit_amount),
        R_amount: formatMoney(item.commission),
        Gross_profit_total: formatMoney(item.total_gross_profit),
        Gross_profit_rate: formatPercent(item.gross_profit_rate, 2, '%'),
      });
    });
    return dataRender;
  };
  const formatDataByYear = (data: ResponseReportTravelTypeByMonthWithLastYear) => {
    const dataRender: any[] = [];
    data.data.forEach((item, index) => {
      dataRender.push({
        index: 1 + index * 3,
        key: 1 + index,
        travel_code: item.travel_code,
        category_name: item.category_name,
        collection_by_month: formHeader.getFieldValue('startDate').format('YYYY年MM月'),
        Sales_amount: formatMoney(item.total_sale_amount),
        Purchase_amount: formatMoney(item.total_purchase_amount),
        Gross_profit_amount: formatMoney(item.gross_profit_amount),
        R_amount: formatMoney(item.commission),
        Gross_profit_total: formatMoney(item.total_gross_profit),
        Gross_profit_rate: formatPercent(item.gross_profit_rate, 2, '%'),
      });
      dataRender.push({
        index: 2 + index * 3,
        collection_by_month: '前年実績',
        Sales_amount: formatMoney(item.total_sale_amount_last_year),
        Purchase_amount: formatMoney(item.total_purchase_amount_last_year),
        Gross_profit_amount: formatMoney(item.gross_profit_amount_last_year),
        R_amount: formatMoney(item.commission_last_year),
        Gross_profit_total: formatMoney(item.total_gross_profit_last_year),
        Gross_profit_rate: formatPercent(item.gross_profit_rate_last_year, 2, '%'),
      });
      dataRender.push({
        index: 3 + index * 3,
        collection_by_month: '前年比',
        Sales_amount: formatPercent(item.total_sale_amount_diff, 2, '%'),
        Purchase_amount: formatPercent(item.total_purchase_amount_diff, 2, '%'),
        Gross_profit_amount: formatPercent(item.gross_profit_amount_diff, 2, '%'),
        R_amount: formatPercent(item.commission_diff, 2, '%'),
        Gross_profit_total: formatPercent(item.total_gross_profit_diff, 2, '%'),
        Gross_profit_rate: formatPercent(item.gross_profit_rate_diff, 2, '%'),
      });
    });

    dataRender.push({
      index: 'summary_1',
      collection_by_month: formHeader.getFieldValue('startDate').format('YYYY年MM月'),
      category_name: '合計',
      Sales_amount: formatMoney(data.summary?.total_sale_amount),
      Purchase_amount: formatMoney(data.summary?.total_purchase_amount),
      Gross_profit_amount: formatMoney(data.summary?.gross_profit_amount),
      R_amount: formatMoney(data.summary?.commission),
      Gross_profit_total: formatMoney(data.summary?.total_gross_profit),
      Gross_profit_rate: formatPercent(data.summary?.gross_profit_rate, 2, '%'),
    });
    dataRender.push({
      index: 'summary_2',
      collection_by_month: '前年実績',
      Sales_amount: formatMoney(data.summary?.total_sale_amount_last_year),
      Purchase_amount: formatMoney(data.summary?.total_purchase_amount_last_year),
      Gross_profit_amount: formatMoney(data.summary?.gross_profit_amount_last_year),
      R_amount: formatMoney(data.summary?.commission_last_year),
      Gross_profit_total: formatMoney(data.summary?.total_gross_profit_last_year),
      Gross_profit_rate: formatPercent(data.summary?.gross_profit_rate_last_year, 2, '%'),
    });
    dataRender.push({
      index: 'summary_3',
      collection_by_month: '前年比',
      Sales_amount: formatPercent(data.summary?.total_sale_amount_diff, 2, '%'),
      Purchase_amount: formatPercent(data.summary?.total_purchase_amount_diff, 2, '%'),
      Gross_profit_amount: formatPercent(data.summary?.gross_profit_amount_diff, 2, '%'),
      R_amount: formatPercent(data.summary?.commission_diff, 2, '%'),
      Gross_profit_total: formatPercent(data.summary?.total_gross_profit_diff, 2, '%'),
      Gross_profit_rate: formatPercent(data.summary?.gross_profit_rate_diff, 2, '%'),
    });
    return dataRender;
  };

  const onFetchData = async (isExport?: boolean) => {
    setIsLoading(true);
    try {
      const reportType = parameter.report_type;
      if (!reportType) {
        setIsLoading(false);
        return;
      }
      let travelTypeListParam = undefined;
      try {
        if (parameter?.travel_type && Array.isArray(parameter.travel_type)) {
          travelTypeListParam = parameter?.travel_type?.map((item: string) => Number(item));
        } else if (parameter?.travel_type && typeof parameter.travel_type === 'string') {
          travelTypeListParam = [Number(parameter?.travel_type)];
        }
      } catch (error) {
        console.log('travelTypeListParam error', error);
      }
      const valueSearch: { [key: string]: any } = {
        start: parameter.startDate
          ? moment(parameter.startDate as string)
              .startOf('month')
              .format('YYYY-MM-DD')
          : undefined,
        finish: parameter.startDate
          ? moment(parameter.startDate as string)
              .endOf('month')
              .format('YYYY-MM-DD')
          : undefined,
        limit: parameter.limit ? (parameter.limit as string) : 'all',
        report_type: reportType as string,
        is_last_year: reportType === 'report_by_year' ? 1 : 0,
        travel_type: travelTypeListParam,
        is_excluding_tax: parameter?.is_excluding_tax
          ? JSON.parse(parameter?.is_excluding_tax as string)
          : 1,
        type_date: parameter.type_date as string,
      };

      if (isExport) {
        valueSearch.is_export_excel = 1;
        const resExport = await getReportTravelByMonth(valueSearch);
        window.open(resExport.data?.data?.file_url);
        setIsLoading(false);
        return;
      }
      setDataSource([]);
      const resGetList = await getReportTravelByMonth(valueSearch);
      if (resGetList.status === STATUS_CODE.SUCCESSFUL) {
        let dataRender: any[] = [];
        if (reportType === 'report_by_month') {
          dataRender = formatDataByMonth(
            (resGetList.data?.data as ResponseReportTravelTypeByMonth)?.data,
          );
        } else if (reportType === 'report_by_year') {
          dataRender = formatDataByYear(
            resGetList.data?.data as ResponseReportTravelTypeByMonthWithLastYear,
          );
        }
        setDataSource(dataRender);
      } else {
        openNotificationFail({ message: MESSAGE_ALERT.FAILED_TO_GET_DATA });
      }
    } catch (error) {
      console.log('error', error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    // set Form Header
    if (formHeader) {
      let travelTypeListParam = undefined;
      try {
        if (parameter?.travel_type && Array.isArray(parameter.travel_type)) {
          travelTypeListParam = parameter?.travel_type?.map((item: string) => Number(item));
        } else if (parameter?.travel_type && typeof parameter.travel_type === 'string') {
          travelTypeListParam = [Number(parameter?.travel_type)];
        }
      } catch (error) {
        console.log('travelTypeListParam error', error);
      }
      formHeader.setFieldsValue({
        ...parameter,
        startDate: parameter.startDate
          ? moment(parameter.startDate as string)
          : moment().subtract(1, 'months'),
        travel_type: travelTypeListParam,
        is_excluding_tax: parameter?.is_excluding_tax
          ? JSON.parse(parameter?.is_excluding_tax as string)
          : 1,
      });
    }
    onFetchData();
  }, [parameter]);

  const onSubmit = async () => {
    const valueSubmit = await formHeader.validateFields();
    setParameter({
      ...valueSubmit,
      startDate: valueSubmit.startDate && moment(valueSubmit.startDate).format('YYYY-MM-DD'),
      endDate: valueSubmit.endDate && moment(valueSubmit.endDate).format('YYYY-MM-DD'),
    });
  };

  return (
    <PageContainer>
      <HeaderAction form={formHeader} onSubmit={onSubmit} />
      <div className="p-2 rounded-xl bg-white mt-6">
        {(!parameter.report_type || parameter.report_type == 'report_by_month') && (
          <TableByMonth
            isLoading={isLoading}
            dataSource={dataSource}
            SalesAmount={SalesAmount}
            PurchaseAmount={PurchaseAmount}
            GrossProfitAmount={GrossProfitAmount}
            RAmount={RAmount}
            GrossProfitTotal={GrossProfitTotal}
            GrossProfitRate={GrossProfitRate}
          />
        )}
        {parameter.report_type == 'report_by_year' && (
          <TableByYear isLoading={isLoading} dataSource={dataSource} />
        )}
      </div>

      <div className="flex gap-6 justify-center mt-10">
        <BasicButton
          disabled={isLoading}
          styleType="accept"
          className={`!w-[190px] flex justify-center items-center ${
            isLoading ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
          }`}
          onClick={() => {
            onFetchData(true);
          }}
        >
          <DownloadOutlined style={{ color: 'white' }} />
          <p>{TEXT_ACTION.Report_output}</p>
        </BasicButton>
      </div>
    </PageContainer>
  );
};

export default InvoicesPage;
