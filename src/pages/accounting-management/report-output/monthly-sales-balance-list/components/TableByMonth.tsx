import BasicTable from '@/components/Commons/BasicTable';
import { formatMoney, formatPercent } from '@/utils';
import { Table } from 'antd';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  isLoading,
  dataSource,
  SalesAmount,
  PurchaseAmount,
  GrossProfitAmount,
  RAmount,
  GrossProfitTotal,
  GrossProfitRate,
}: {
  isLoading: boolean;
  dataSource: any[];
  SalesAmount: number;
  PurchaseAmount: number;
  GrossProfitAmount: number;
  RAmount: number;
  GrossProfitTotal: number;
  GrossProfitRate: number;
}) => {
  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };

  const columns = [
    {
      title: '',
      dataIndex: 'key',
      key: 'key',
      width: 70,
    },
    {
      title: '旅行種別コード',
      dataIndex: 'travel_code',
      key: 'travel_code',
      width: 140,
    },
    {
      title: '旅行種別名称',
      dataIndex: 'category_name',
      key: 'category_name',
      width: 200,
    },
    {
      title: '売上金額',
      dataIndex: 'Sales_amount',
      key: 'Sales_amount',
      width: 160,
      render: (_, record) => <p className="text-right">{record.Sales_amount}</p>,
    },
    {
      title: '仕入金額',
      dataIndex: 'Purchase_amount',
      key: 'Purchase_amount',
      width: 160,
      render: (_, record) => <p className="text-right">{record.Purchase_amount}</p>,
    },
    {
      title: '粗利額',
      dataIndex: 'Gross_profit_amount',
      key: 'Gross_profit_amount',
      width: 160,
      render: (_, record) => <p className="text-right">{record.Gross_profit_amount}</p>,
    },
    {
      title: '手数料 (R額)',
      dataIndex: 'R_amount',
      key: 'R_amount',
      width: 160,
      render: (_, record) => <p className="text-right">{record.R_amount}</p>,
    },
    {
      title: '粗利合計',
      dataIndex: 'Gross_profit_total',
      key: 'Gross_profit_total',
      width: 160,
      render: (_, record) => <p className="text-right">{record.Gross_profit_total}</p>,
    },
    {
      title: '粗利率',
      dataIndex: 'Gross_profit_rate',
      key: 'Gross_profit_rate',
      width: 160,
      render: (_, record) => (
        <p className="text-right">{formatPercent(record.Gross_profit_rate, 2, '%')}</p>
      ),
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1400, y: 800 },
          loading: isLoading,
          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'index',
          rowClassName: getRowClassName,
          summary: (pageData) => (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={2} />
              <Table.Summary.Cell index={3} className="font-medium">
                合計
              </Table.Summary.Cell>
              <Table.Summary.Cell index={4} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(SalesAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={5} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(PurchaseAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={6} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(GrossProfitAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={7} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(RAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={8} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(GrossProfitTotal)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={9} className="text-right font-medium">
                {dataSource.length > 0 && formatPercent(GrossProfitRate, 2, '%')}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          ),
        }}
        hasPagination={false}
      />
    </>
  );
};

export default TableList;
