import BasicTable from '@/components/Commons/BasicTable';
import EditableCell from '@/components/Commons/EditableCell';
import { formatPercent } from '@/utils';

// eslint-disable-next-line max-lines-per-function
const TableList = ({ isLoading, dataSource }: { isLoading: boolean; dataSource: any[] }) => {
  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };

  const columns = [
    {
      title: '',
      dataIndex: 'key',
      key: 'key',
      width: 70,
    },
    {
      title: '旅行種別コード',
      dataIndex: 'travel_code',
      key: 'travel_code',
      width: 140,
    },
    {
      title: '旅行種別名称',
      dataIndex: 'category_name',
      key: 'category_name',
      width: 200,
    },
    {
      title: '集計年月',
      dataIndex: 'collection_by_month',
      key: 'collection_by_month',
      width: 160,
    },
    {
      title: '売上金額',
      dataIndex: 'Sales_amount',
      key: 'Sales_amount',
      width: 160,
      render: (_, record) => <p className="text-right">{record.Sales_amount}</p>,
    },
    {
      title: '仕入金額',
      dataIndex: 'Purchase_amount',
      key: 'Purchase_amount',
      width: 160,
      render: (_, record) => <p className="text-right">{record.Purchase_amount}</p>,
    },
    {
      title: '粗利額',
      dataIndex: 'Gross_profit_amount',
      key: 'Gross_profit_amount',
      width: 160,
      render: (_, record) => <p className="text-right">{record.Gross_profit_amount}</p>,
    },
    {
      title: '手数料 (R額)',
      dataIndex: 'R_amount',
      key: 'R_amount',
      width: 160,
      render: (_, record) => <p className="text-right">{record.R_amount}</p>,
    },
    {
      title: '粗利合計',
      dataIndex: 'Gross_profit_total',
      key: 'Gross_profit_total',
      width: 160,
      render: (_, record) => <p className="text-right">{record.Gross_profit_total}</p>,
    },
    {
      title: '粗利率',
      dataIndex: 'Gross_profit_rate',
      key: 'Gross_profit_rate',
      width: 120,
      render: (_, record) => <p className="text-right">{record.Gross_profit_rate}</p>,
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1400, y: 800 },
          loading: isLoading,
          components: {
            body: {
              cell: EditableCell,
            },
          },
          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'index',
          rowClassName: getRowClassName,
        }}
        hasPagination={false}
      />
    </>
  );
};

export default TableList;
