import { getListCurrencyTypeMaster } from '@/apis/master/currencyType';
import ArrowRight from '@/assets/imgs/svg/arrow-right-02.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import SearchSVG from '@/components/SVG/SearchSVG';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import {
  optionReportDepositType,
  optionReportDepositTypeDate,
  optionSubTotalCategoryDeposit,
} from '@/constants/data';
import STATUS_CODE from '@/constants/statusCode';
import { BaseOptionType, filterBusinessPartner } from '@/utils';
import type { FormInstance } from 'antd';
import { Form, Image, Select } from 'antd';
import { useEffect, useMemo, useState } from 'react';

const HeaderAction = ({
  form,
  onSubmit,
  optionsBusinessPartner,
  firstCodeBusinessPartner,
  secondCodeBusinessPartner,
  setFirstCodeBusinessPartner,
  setSecondCodeBusinessPartner,
  subTotalParam,
}: {
  form: FormInstance<any>;
  onSubmit: () => void;
  optionsBusinessPartner: BaseOptionType[];
  firstCodeBusinessPartner: string;
  secondCodeBusinessPartner: string;
  setFirstCodeBusinessPartner: (val: string) => void;
  setSecondCodeBusinessPartner: (val: string) => void;
  subTotalParam;
}) => {
  const [optionsCurrencyType, setOptionsCurrencyType] = useState<{ value: any; label: string }[]>(
    [],
  );
  const [firstSearchSelect, setFirstSearchSelect] = useState<string>('');
  const [secondSearchSelect, setSecondSearchSelect] = useState<string>('');
  const reportType = Form.useWatch('report_type', form);
  const endDate = Form.useWatch('endDate', form);
  const handleGetTaxCategoryMaster = async () => {
    const res = await getListCurrencyTypeMaster({
      limit: 'all',
      order: 'asc',
      sort: 'currency_type_code',
    });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      // setOptionsCurrencyType(res?.data?.data);
      const options = res?.data?.data?.map((item) => {
        return {
          value: item.id,
          label: item.currency_type_name,
          disabled: item.status === 0,
        };
      });
      setOptionsCurrencyType(options);
    }
  };

  useEffect(() => {
    handleGetTaxCategoryMaster();
  }, []);

  const validateEndDate = (_, value) => {
    const startDate = form.getFieldValue('startDate');
    if (!startDate || !value) {
      return Promise.resolve();
    }

    if (value.isBefore(startDate)) {
      return Promise.reject('終了日は開始日より後でなければなりません');
    }
    const diffInDays = value.diff(startDate, 'days');
    if (diffInDays > 365) {
      return Promise.reject('開始日より1年以内の日付を選択してください。');
    }

    return Promise.resolve();
  };

  const highlightText = (text: string, search: string) => {
    if (!search) return text;
    const regex = new RegExp(`(${search})`, 'gi');
    try {
      return text?.split?.(regex).map((part, index) =>
        regex.test(part) ? (
          <span key={index} style={{ color: 'red' }}>
            {part}
          </span>
        ) : (
          part
        ),
      );
    } catch (error) {
      console.log('highlightText error', error);
      return text;
    }
  };

  const secondRenderOption = useMemo(() => {
    if (!optionsBusinessPartner) return [];
    return optionsBusinessPartner.filter(
      (item) => !firstCodeBusinessPartner || Number(item.value) >= Number(firstCodeBusinessPartner),
    );
  }, [optionsBusinessPartner, firstCodeBusinessPartner]);

  const optionSubTotalByType = useMemo(() => {
    const reportList = 'report_deposit_list';
    if (reportType === reportList) {
      return optionSubTotalCategoryDeposit;
    }
    return [{ value: 'business_partner', label: '得意先ID' }];
  }, [reportType]);

  useEffect(() => {
    if (reportType !== 'report_deposit_list') {
      form.setFieldsValue({ sub_total: 'business_partner' });
    } else if (
      !subTotalParam ||
      subTotalParam === 'business_partner' ||
      subTotalParam !== form.getFieldValue('sub_total')
    ) {
      form.setFieldsValue({ sub_total: 'category_deposit_slip' });
    }
  }, [reportType, subTotalParam]);

  const textSortOrder = useMemo(() => {
    let text = '入金日 / 得意先名（カナ）/旅行ID（昇順）';
    if (reportType === 'report_deposit_list_unpaid_exp') {
      text = '得意先ID / 旅行ID / 計上日（昇順）';
    } else if (reportType === 'report_deposit_list_unpaid_collection_plan') {
      text = '得意先ID / 旅行ID（昇順）';
    }
    return text;
  }, [reportType]);

  return (
    <>
      <Form form={form} component={false}>
        <div className="grid grid-cols-6 gap-4 ">
          <Form.Item
            name="report_type"
            className="col-span-2 !mb-0"
            rules={[{ required: true, message: 'を入力してください' }]}
          >
            <BasicSelect
              title={TEXT_TITLE.Report_type}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              required={true}
              options={optionReportDepositType}
            />
          </Form.Item>
          <div className="col-span-2 !mb-0">
            <BasicInput
              className=" !bg-[#DCDEE3] !text-[#9499A5]"
              disabled
              title={TEXT_TITLE.Sort_oder}
              value={textSortOrder}
            />
          </div>
          <Form.Item name="sub_total" className="col-span-2 !mb-0">
            <BasicSelect
              allowClear
              title={TEXT_TITLE.subtotal_category}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              className={`${
                reportType !== 'report_deposit_list'
                  ? ' [&_.ant-select-selector]:!bg-[#DCDEE3] [&_.ant-select-selector]:!text-[#9499A5]'
                  : ''
              }`}
              options={optionSubTotalByType}
              disabled={reportType !== 'report_deposit_list'}
            />
          </Form.Item>

          <div className="flex items-center gap-1 col-span-2  h-fit">
            <div className="firstSelectBP flex-1">
              <div className="mb-2 text-[13px] leading-4 font-medium line-clamp-1">
                取引先コード(取引先名)
              </div>
              <Select
                showSearch
                allowClear
                className="[&_.ant-select-selector]:!rounded-lg
                    [&_.ant-select-selection-search-input]:!min-h-[40px]
                    [&_.ant-select-selector]:!min-h-10 max-w-[350px]
                    [&_.ant-select-selection-placeholder]:flex
                    [&_.ant-select-selection-placeholder]:items-center
                    !h-fit [&_.ant-select-selector]:!h-fit w-full"
                filterOption={(input, option) =>
                  filterBusinessPartner(input, option as BaseOptionType)
                }
                placeholder={TEXT_PLACEHOLDER.Please_select}
                onChange={setFirstCodeBusinessPartner}
                onSearch={setFirstSearchSelect}
                searchValue={firstSearchSelect}
                value={firstCodeBusinessPartner}
              >
                {optionsBusinessPartner?.map((item) => (
                  <Select.Option key={item.value} value={item.value} label={item.label}>
                    <span className="text-sm font-semibold">
                      {highlightText(item.value, firstSearchSelect)}
                    </span>
                    <br />
                    <span className="text-xs">{highlightText(item.label, firstSearchSelect)}</span>
                  </Select.Option>
                ))}
              </Select>
            </div>
            <div className="">
              <div className="h-6" />
              <div className="flex items-center">
                <Image preview={false} src={ArrowRight} alt="arrow right" />
              </div>
            </div>
            <div className="firstSelectBP flex-1">
              <div className="mb-2 text-[13px] leading-4 font-medium line-clamp-1">
                取引先コード(取引先名)
              </div>
              <Select
                showSearch
                allowClear
                className="[&_.ant-select-selector]:!rounded-lg
                    [&_.ant-select-selection-search-input]:!min-h-[40px]
                    [&_.ant-select-selector]:!min-h-10 max-w-[350px]
                    [&_.ant-select-selection-placeholder]:flex
                    [&_.ant-select-selection-placeholder]:items-center
                    !h-fit [&_.ant-select-selector]:!h-fit w-full"
                filterOption={(input, option) =>
                  filterBusinessPartner(input, option as BaseOptionType)
                }
                placeholder={TEXT_PLACEHOLDER.Please_select}
                onChange={setSecondCodeBusinessPartner}
                searchValue={secondSearchSelect}
                onSearch={setSecondSearchSelect}
                value={secondCodeBusinessPartner}
              >
                {secondRenderOption?.map((item) => (
                  <Select.Option key={item.value} value={item.value} label={item.label}>
                    <span className="text-sm font-semibold">
                      {highlightText(item.value, secondSearchSelect)}
                    </span>
                    <br />
                    <span className="text-xs">{highlightText(item.label, secondSearchSelect)}</span>
                  </Select.Option>
                ))}
              </Select>
            </div>
          </div>
          <Form.Item name="currency_type_master_ids" className="col-span-2 !mb-0">
            <BasicSelect
              allowClear
              className="[&_.ant-select-selection-overflow]:!flex-nowrap [&_.ant-select-selection-overflow]:!overflow-hidden"
              title={TEXT_TITLE.Deposit_Type}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              options={optionsCurrencyType}
              mode="multiple"
            />
          </Form.Item>
          {/* 5.2 5.3 */}
          {reportType !== 'report_deposit_list' && (
            <Form.Item
              name="type_date"
              initialValue="voucher_posting_date"
              className="col-span-2 !mb-0"
            >
              <BasicSelect
                title={TEXT_TITLE.Add_output_condition}
                placeholder={TEXT_PLACEHOLDER.Please_select}
                options={optionReportDepositTypeDate}
              />
            </Form.Item>
          )}
          <Form.Item name="startDate" className="col-span-1 !mb-0">
            <BasicDatePicker
              picker={reportType === 'report_deposit_list' ? 'date' : 'month'}
              className="h-10"
              title={TEXT_TITLE.Departure_date}
            />
          </Form.Item>
          <Form.Item
            name="endDate"
            className="col-span-1 !mb-0"
            rules={[
              { required: true, message: 'を入力してください' },
              { validator: validateEndDate },
            ]}
          >
            <BasicDatePicker
              picker={reportType === 'report_deposit_list' ? 'date' : 'month'}
              required
              className="h-10"
              title={TEXT_TITLE.Return_Date}
            />
          </Form.Item>
        </div>
        <div className="flex items-end mt-4">
          <BasicButton
            icon={<SearchSVG colorSvg="white" />}
            className="flex items-center w-[120px]"
            styleType="accept"
            onClick={onSubmit}
          >
            <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
          </BasicButton>
        </div>
      </Form>
    </>
  );
};

export default HeaderAction;
