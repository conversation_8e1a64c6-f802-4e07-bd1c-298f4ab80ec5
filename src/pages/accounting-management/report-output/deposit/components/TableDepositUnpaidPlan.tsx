import BasicTable from '@/components/Commons/BasicTable';
import { formatMoney } from '@/utils';
import { Table } from 'antd';
import moment from 'moment';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  isLoading,
  dataSource,
  totalAmount,
  previousPaid,
  currentPaid,
  remainingAmount,
}: {
  isLoading: boolean;
  dataSource: any[];
  totalAmount: number;
  previousPaid: number;
  currentPaid: number;
  remainingAmount: number;
}) => {
  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };

  const columns = [
    {
      title: '',
      dataIndex: 'key',
      key: 'key',
      width: 60,
    },

    {
      title: '得意先ID',
      dataIndex: 'business_partner_code',
      key: 'business_partner_code',
      width: 150,
      children: [
        {
          title: '得意先名',
          dataIndex: 'tour_name',
          key: 'tour_name',
          width: 150,
          render: (_, record) => {
            return (
              <p className=" !ml-1">
                {record.business_partner_code}
                <br />
                {record.business_partner_name}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '旅行ID',
      dataIndex: 'travel_id',
      key: 'travel_id',
      width: 100,
      children: [
        {
          title: '売上伝票ID',
          width: 100,
          dataIndex: 'sale_invoice_id',
          key: 'sale_invoice_id',
          render: (_, record) => {
            return (
              <p className=" !ml-1">
                {record.travel_id}
                <br />
                {record.sale_invoice_id}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '出発日',
      dataIndex: 'departure_date',
      key: 'departure_date',
      width: 140,
      children: [
        {
          title: 'ツアー名',
          dataIndex: 'tour_name',
          key: 'tour_name',
          width: 140,
          render: (_, record) => {
            if (record.type === 'total') {
              return null;
            }
            return (
              <p className=" !ml-1">
                {record.departure_date ? moment(record.departure_date).format('YY/MM/DD') : ''}
                <br />
                {record.tour_name}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '帰着日',
      dataIndex: 'return_date',
      key: 'return_date',
      width: 140,
      children: [
        {
          title: '',
          dataIndex: 'return_date_no_return',
          key: 'return_date_no_return',
          width: 140,
          render: (_, record) => {
            if (record.type === 'total') {
              return null;
            }
            return (
              <p className=" !ml-1">
                {record.return_date ? moment(record.return_date).format('YY/MM/DD') : ''}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '計上日',
      dataIndex: 'voucher_posting_date',
      key: 'voucher_posting_date',
      width: 140,
      children: [
        {
          title: '',
          dataIndex: 'deposit_date_no_return',
          key: 'deposit_date_no_return',
          width: 140,
          render: (_, record) => {
            if (record.type === 'total') {
              return null;
            }
            return (
              <p className=" !ml-1">
                {record.voucher_posting_date
                  ? moment(record.voucher_posting_date).format('YY/MM/DD')
                  : ''}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '請求書発行日',
      dataIndex: 'issued_date',
      key: 'issued_date',
      width: 140,
      children: [
        {
          title: '',
          dataIndex: 'issued_date_no_return',
          key: 'issued_date_no_return',
          width: 140,
          render: (_, record) => {
            if (record.type === 'total') {
              return '';
            }
            return (
              <p className=" !ml-1">
                {record.issued_date ? moment(record.issued_date).format('YY/MM/DD') : ''}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '請求書ID',
      dataIndex: 'consolidated_invoice_id',
      key: 'consolidated_invoice_id',
      width: 160,
      children: [
        {
          title: '',
          dataIndex: 'consolidated_invoice_id_no_return',
          key: 'consolidated_invoice_id_no_return',
          width: 140,
          render: (_, record) => {
            if (record.type === 'total') {
              return `${record.business_partner_name}　合計`;
            }
            return <p className=" !ml-1">{record.consolidated_invoice_id}</p>;
          },
        },
      ],
    },
    {
      title: '売上',
      dataIndex: 'payment',
      key: 'payment',
      width: 140,
      children: [
        {
          title: '売上金額',
          dataIndex: 'total_amount',
          key: 'total_amount',
          width: 140,
          render: (_, record) => {
            return <p className="text-right !ml-1">{formatMoney(record.total_amount)}</p>;
          },
        },
      ],
    },
    {
      title: '入金',
      dataIndex: 'payment',
      key: 'payment',
      width: 140,
      children: [
        {
          title: '前月までの入金額',
          dataIndex: 'previous_paid',
          key: 'previous_paid',
          width: 140,
          render: (_, record) => {
            return <p className="text-right !ml-1">{formatMoney(record.previous_paid)}</p>;
          },
        },
        {
          title: '当月入金額',
          dataIndex: 'current_paid',
          key: 'current_paid',
          width: 140,
          render: (_, record) => {
            return <p className="text-right !ml-1">{formatMoney(record.current_paid)}</p>;
          },
        },
        {
          title: '残額',
          dataIndex: 'remaining_amount',
          key: 'remaining_amount',
          width: 140,
          render: (_, record) => {
            return <p className="text-right !ml-1">{formatMoney(record.remaining_amount)}</p>;
          },
        },
      ],
    },
    {
      title: '入金期日',
      dataIndex: 'payment_deadline',
      key: 'payment_deadline',
      width: 140,
      children: [
        {
          title: '回収時期',
          dataIndex: 'payment_deadline_to_day',
          key: 'payment_deadline_to_day',
          width: 140,
          render: (_, record) => {
            return (
              <p className=" !ml-1">
                {record.payment_deadline ? moment(record.payment_deadline).format('YY/MM/DD') : ''}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '',
      dataIndex: 'remaining_amount_title',
      key: 'remaining_amount_title',
      width: 140,
      children: [
        {
          title: '入金予定額',
          dataIndex: 'remaining_amount_2',
          key: 'remaining_amount_2',
          width: 140,
          render: (_, record) => {
            return <p className="text-right !ml-1">{formatMoney(record.remaining_amount)}</p>;
          },
        },
      ],
    },
    {
      title: '経過日数',
      dataIndex: 'expired_days',
      key: 'expired_days',
      width: 140,
      children: [
        {
          title: '備考',
          dataIndex: 'note',
          key: 'note',
          width: 140,
          render: (_, record) => {
            return (
              <p className="">
                {formatMoney(record.expired_days)}
                <br />
                {record.note}
              </p>
            );
          },
        },
      ],
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 2200, y: 800 },
          loading: isLoading,
          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'index',
          rowClassName: getRowClassName,
          summary: (pageData) => (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={7} />
              <Table.Summary.Cell index={7} className="font-medium">
                未入金　合計
              </Table.Summary.Cell>
              <Table.Summary.Cell index={8} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(totalAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={9} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(previousPaid)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={10} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(currentPaid)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={11} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(remainingAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={12} colSpan={1} />

              <Table.Summary.Cell index={13} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(remainingAmount)}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          ),
        }}
        hasPagination={false}
      />
    </>
  );
};

export default TableList;
