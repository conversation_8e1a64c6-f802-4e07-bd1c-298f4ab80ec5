import BasicTable from '@/components/Commons/BasicTable';
import { formatMoney } from '@/utils';
import { Table } from 'antd';
import moment from 'moment';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  isLoading,
  dataSource,
  totalAmount,
  feeAmount,
}: {
  isLoading: boolean;
  dataSource: any[];
  totalAmount: number;
  feeAmount: number;
}) => {
  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };

  const columns = [
    {
      title: '',
      dataIndex: 'key',
      key: 'key',
      width: 60,
    },

    {
      title: '入金日',
      dataIndex: 'deposit_date',
      key: 'deposit_date',
      width: 120,
      children: [
        {
          title: '入金伝票ID',
          dataIndex: 'deposit_id',
          key: 'deposit_id',
          width: 120,
          render: (_, record) => (
            <p className="">
              {record.deposit_date ? moment(record.deposit_date).format('YY/MM/DD') : ''}
              <br />
              {record.deposit_id}
            </p>
          ),
        },
      ],
    },
    {
      title: '得意先ID',
      dataIndex: 'business_partner_code',
      key: 'business_partner_code',
      width: 150,
      children: [
        {
          title: '得意先名',
          dataIndex: 'business_partner_name',
          key: 'business_partner_name',
          width: 150,
          render: (_, record) => {
            return (
              <p className="">
                {record.business_partner_code}
                <br />
                {record.business_partner_name}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '旅行ID',
      dataIndex: 'travel_id',
      key: 'travel_id',
      width: 100,
      children: [
        {
          title: '売上伝票ID',
          width: 100,
          dataIndex: 'sale_invoice_id',
          key: 'sale_invoice_id',
          render: (_, record) => {
            return (
              <p className="">
                {record.travel_id}
                <br />
                {record.sale_invoice_id}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '出発日',
      dataIndex: 'departure_date',
      key: 'departure_date',
      width: 140,
      children: [
        {
          title: 'ツアー名',
          dataIndex: 'tour_name',
          key: 'tour_name',
          width: 140,
          render: (_, record) => {
            if (record.type === 'total') {
              return null;
            }
            return (
              <p className="">
                {record.departure_date ? moment(record.departure_date).format('YY/MM/DD') : ''}
                <br />
                {record.tour_name}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '帰着日',
      dataIndex: 'return_date',
      key: 'return_date',
      width: 140,
      children: [
        {
          title: '',
          dataIndex: 'return_date_no_return',
          key: 'return_date_no_return',
          width: 140,
          render: (_, record) => {
            if (record.type === 'total') {
              return null;
            }
            return (
              <p className="">
                {record.return_date ? moment(record.return_date).format('YY/MM/DD') : ''}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '請求書発行日',
      dataIndex: 'voucher_posting_date',
      key: 'voucher_posting_date',
      width: 140,
      children: [
        {
          title: '',
          dataIndex: 'voucher_posting_date_no_return',
          key: 'voucher_posting_date_no_return',
          width: 140,
          render: (_, record) => {
            if (record.type === 'total') {
              return record.title;
            }
            return (
              <p className="">
                {record.voucher_posting_date
                  ? moment(record.voucher_posting_date).format('YY/MM/DD')
                  : ''}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '入金',
      dataIndex: 'payment',
      key: 'payment',
      width: 140,
      children: [
        {
          title: '入金金種',
          dataIndex: 'currency_type_master_id',
          key: 'currency_type_master_id',
          width: 140,
          render: (_, record) => {
            if (record.type === 'total') {
              return record.currency_type_master_id;
            }
            return <p className="">{record.currency_type_master_id}</p>;
          },
        },
        {
          title: '今回入金額',
          dataIndex: 'current_deposit_amount',
          key: 'current_deposit_amount',
          width: 140,
          render: (_, record) => {
            return <p className="text-right ">{formatMoney(record.current_deposit_amount)}</p>;
          },
        },
        {
          title: '(内、手数料)',
          dataIndex: 'fee',
          key: 'fee',
          width: 140,
          render: (_, record) => {
            if (record.type === 'total') {
              return <p className="text-right ">{formatMoney(record.fee)}</p>;
            }
            return null;
          },
        },
      ],
    },
    {
      title: '備考',
      dataIndex: 'memo',
      key: 'memo',
      width: 140,
      children: [
        {
          title: '',
          dataIndex: 'memo_no_return',
          key: 'memo_no_return',
          width: 140,
          render: (_, record) => {
            if (record.type === 'total') {
              return <p className="">{record.memo}</p>;
            }
            return null;
          },
        },
      ],
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1800, y: 800 },
          loading: isLoading,
          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'index',
          rowClassName: getRowClassName,
          summary: (pageData) => (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={6} />
              <Table.Summary.Cell index={6} className="font-medium">
                合計
              </Table.Summary.Cell>
              <Table.Summary.Cell index={7} colSpan={1} />
              <Table.Summary.Cell index={8} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(totalAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={9} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(feeAmount)}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          ),
        }}
        hasPagination={false}
      />
    </>
  );
};

export default TableList;
