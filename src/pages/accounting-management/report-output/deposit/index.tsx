import { BusinessPartnerDetailType } from '@/@types/businessPartner';
import { getListBusinessPartner } from '@/apis/businessPartner';
import type {
  ResponseReportDepositType,
  ResponseReportDepositTypeOther,
  ResponseReportUnpaidDepositType,
} from '@/apis/reportOutput/deposit';
import { getReportDeposit } from '@/apis/reportOutput/deposit';
import BasicButton from '@/components/Commons/BasicButton';
import PageContainer from '@/components/Commons/Page/Container';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { DownloadOutlined } from '@ant-design/icons';
import { Form } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { useUrlSearchParams } from 'use-url-search-params';
import HeaderAction from './components/HeaderAction';
import TableDeposit from './components/TableDeposit';
import TableDepositUnpaid from './components/TableDepositUnpaid';
import TableDepositUnpaidPlan from './components/TableDepositUnpaidPlan';
import { BaseOptionType } from '@/utils';

const initSearchParams = {};

const InvoicesPage = () => {
  const [formHeader] = Form.useForm();
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);
  const [isLoading, setIsLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [totalAmount, setTotalAmount] = useState(0);
  const [previousPaid, setPreviousPaid] = useState(0);
  const [currentPaid, setCurrentPaid] = useState(0);
  const [remainingAmount, setRemainingAmount] = useState(0);
  const [feeAmount, setFeeAmount] = useState(0);

  const [optionsBusinessPartner, setOptionsBusinessPartner] = useState<BaseOptionType[]>();
  const [firstCodeBusinessPartner, setFirstCodeBusinessPartner] = useState<string>();
  const [secondCodeBusinessPartner, setSecondCodeBusinessPartner] = useState<string>();

  const formatOptionBusinessPartner = async () => {
    const resData = await getListBusinessPartner({
      page: 1,
      limit: 'all',
      order: 'desc',
      order_by: 'business_partner_code',
    });
    const listCompany = resData?.data?.data;
    if (listCompany) {
      const newList = listCompany?.map((item: BusinessPartnerDetailType) => {
        return {
          label: item.business_partner_name,
          value: item.business_partner_code?.toString(),
          id: item.id,
        };
      });
      newList.sort((a, b) => Number(a.value) - Number(b.value));
      setOptionsBusinessPartner(newList);
      return;
    }
    setOptionsBusinessPartner([]);
  };

  useEffect(() => {
    formatOptionBusinessPartner();
  }, []);

  const formatDataPaymentTypeCategorySlip = (data: ResponseReportDepositType[]) => {
    const dataRender: any[] = [];
    let key = 1;
    let sumTotalAmount = 0;
    let sumFee = 0;
    data.forEach((groupData, index) => {
      groupData.deposit_items.forEach((itemDetail, indexDetail) => {
        dataRender.push({
          index: `item_` + indexDetail + key,
          key: indexDetail === 0 && key + indexDetail,
          deposit_date: groupData.deposit_date,
          deposit_id: groupData.deposit_id,
          business_partner_code: groupData.business_partner_code,
          business_partner_name: groupData.business_partner_name,
          travel_id: itemDetail.travel_id,
          sale_invoice_id: itemDetail.sale_invoice_id,
          departure_date: itemDetail.departure_date,
          tour_name: itemDetail.tour_name,
          return_date: itemDetail.return_date,
          voucher_posting_date: itemDetail.voucher_posting_date,
          currency_type_master_id: groupData.currency_type_master.currency_type_name,
          current_deposit_amount: itemDetail.current_deposit_amount,
        });
      });
      key++;
      sumTotalAmount += groupData.total_deposit_amount;
      sumFee += groupData.fee;
      const title = `入金伝票別: ${groupData.deposit_id} 計`;
      dataRender.push({
        index: 'total' + key + index,
        type: 'total',
        deposit_date: groupData.deposit_date,
        deposit_id: groupData.deposit_id,
        title,
        business_partner_code: groupData.business_partner_code,
        business_partner_name: groupData.business_partner_name,
        current_deposit_amount: groupData.total_deposit_amount,
        currency_type_master_id: groupData.currency_type_master.currency_type_name,
        fee: groupData.fee,
        memo: groupData.note,
      });
    });
    setTotalAmount(sumTotalAmount);
    setFeeAmount(sumFee);
    return dataRender;
  };

  const formatDataPaymentTypeOther = (data: ResponseReportDepositTypeOther) => {
    const dataRender: any[] = [];
    let key = 1;
    let sumTotalAmount = 0;
    let sumFee = 0;
    const listKeyGroup = Object.keys(data);
    const listValuesGroup = Object.values(data);
    listValuesGroup.forEach((valueGroup, indexKey) => {
      let sumTotalAmountGroup = 0;
      let sumFeeGroup = 0;
      valueGroup.forEach((groupData, index) => {
        groupData.deposit_items.forEach((itemDetail, indexDetail) => {
          dataRender.push({
            index: `item_` + indexDetail + key,
            key: indexDetail === 0 && index === 0 && key + indexDetail,
            deposit_date: groupData.deposit_date,
            deposit_id: groupData.deposit_id,
            business_partner_code: groupData.business_partner_code,
            business_partner_name: groupData.business_partner_name,
            travel_id: itemDetail.travel_id,
            sale_invoice_id: itemDetail.sale_invoice_id,
            departure_date: itemDetail.departure_date,
            tour_name: itemDetail.tour_name,
            return_date: itemDetail.return_date,
            voucher_posting_date: itemDetail.voucher_posting_date,
            currency_type_master_id: groupData.currency_type_master.currency_type_name,
            current_deposit_amount: itemDetail.current_deposit_amount,
          });
        });
        sumTotalAmount += groupData.total_deposit_amount;
        sumFee += groupData.fee;
        sumTotalAmountGroup += groupData.total_deposit_amount;
        sumFeeGroup += groupData.fee;
      });
      key++;

      let title = '';
      const subTotalCategory = parameter?.sub_total;
      switch (subTotalCategory) {
        case 'category_deposit_date':
          title = `入金日別: ${moment(listKeyGroup[indexKey]).format('YY/MM/DD')} 計`;
          break;
        case 'category_deposit_recipient':
          title = `得意先別: ${listKeyGroup[indexKey]} 計`;
          break;
        case 'category_monthly':
          title = `月計: ${moment(listKeyGroup[indexKey], 'YYYY-MM').format('YY/MM')} 計`;
          break;
        case 'category_deposit_type':
          title = `入金種別: ${valueGroup?.[0]?.currency_type_master.currency_type_name} 計`;
          break;
        default:
          break;
      }
      dataRender.push({
        index: 'total' + key + indexKey,
        type: 'total',
        deposit_date: subTotalCategory === 'category_deposit_date' && valueGroup?.[0].deposit_date,
        // deposit_id: valueGroup?.[0].deposit_id,
        title,
        business_partner_code:
          subTotalCategory === 'category_deposit_recipient' &&
          valueGroup?.[0].business_partner_code,
        business_partner_name:
          subTotalCategory === 'category_deposit_recipient' &&
          valueGroup?.[0].business_partner_name,
        current_deposit_amount: sumTotalAmountGroup,
        fee: sumFeeGroup,
        // memo: valueGroup?.[0].note,
        currency_type_master_id:
          subTotalCategory === 'category_deposit_type' &&
          valueGroup?.[0].currency_type_master.currency_type_name,
      });
    });
    setTotalAmount(sumTotalAmount);
    setFeeAmount(sumFee);
    return dataRender;
  };
  const formatDataPaymentUnpaid = (data: ResponseReportUnpaidDepositType[]) => {
    const dataRender: any[] = [];
    let key = 1;
    let sumTotalAmount = 0;
    let sumPreviousPaid = 0;
    let sumCurrentPaid = 0;
    let sumRemainingAmount = 0;
    data.forEach((groupData, index) => {
      groupData.travels.forEach((itemDetail, indexDetail) => {
        dataRender.push({
          index: `item_` + indexDetail + key,
          key: indexDetail === 0 && key + indexDetail,

          business_partner_code: indexDetail === 0 && groupData.business_partner_code,
          business_partner_name: indexDetail === 0 && groupData.business_partner_name,
          travel_id: itemDetail.travel_id,
          sale_invoice_id: itemDetail.sale_invoice_id,
          departure_date: itemDetail.departure_date,
          tour_name: itemDetail.tour_name,
          return_date: itemDetail.return_date,
          voucher_posting_date: itemDetail.voucher_posting_date,
          deposit_date: itemDetail.deposit_date,
          deposit_slip_id: itemDetail.deposit_slip_id,
          total_amount: itemDetail.total_amount,
          previous_paid: itemDetail.previous_paid,
          current_paid: itemDetail.current_paid,
          remaining_amount: itemDetail.remaining_amount,
          payment_deadline: itemDetail.payment_deadline,
          sale_date: itemDetail.sale_date,
          issued_date: itemDetail.issued_date,
          consolidated_invoice_id: itemDetail.consolidated_invoice_id,
          expired_days: itemDetail.expired_days,
          note: itemDetail.note,
        });
      });
      key++;
      if (groupData.travels.length > 0) {
        sumTotalAmount += groupData.travels.reduce((total, item) => total + item.total_amount, 0);
        sumPreviousPaid += groupData.travels.reduce((total, item) => total + item.previous_paid, 0);
        sumCurrentPaid += groupData.travels.reduce((total, item) => total + item.current_paid, 0);
        sumRemainingAmount += groupData.travels.reduce(
          (total, item) => total + item.remaining_amount,
          0,
        );
        dataRender.push({
          index: 'total' + key + index,
          type: 'total',
          business_partner_code: groupData.business_partner_code,
          business_partner_name: groupData.business_partner_name,
          total_amount: groupData.travels.reduce((total, item) => total + item.total_amount, 0),
          previous_paid: groupData.travels.reduce((total, item) => total + item.previous_paid, 0),
          current_paid: groupData.travels.reduce((total, item) => total + item.current_paid, 0),
          remaining_amount: groupData.travels.reduce(
            (total, item) => total + item.remaining_amount,
            0,
          ),
        });
      }
    });
    setTotalAmount(sumTotalAmount);
    setPreviousPaid(sumPreviousPaid);
    setCurrentPaid(sumCurrentPaid);
    setRemainingAmount(sumRemainingAmount);
    return dataRender;
  };

  const onFetchData = async (isExport?: boolean) => {
    setIsLoading(true);
    try {
      const reportType = parameter.report_type;
      if (!reportType) {
        setIsLoading(false);
        return;
      }
      let currencyTypeListParam = undefined;
      try {
        if (
          parameter?.currency_type_master_ids &&
          Array.isArray(parameter.currency_type_master_ids)
        ) {
          currencyTypeListParam = parameter?.currency_type_master_ids?.map((item: string) =>
            Number(item),
          );
        } else if (
          parameter?.currency_type_master_ids &&
          typeof parameter.currency_type_master_ids === 'string'
        ) {
          currencyTypeListParam = [Number(parameter?.currency_type_master_ids)];
        }
      } catch (error) {
        console.log('currencyTypeListParam error', error);
      }
      const valueSearch: { [key: string]: any } = {
        start: parameter.startDate
          ? moment(parameter.startDate as string).format('YYYY-MM-DD')
          : moment(parameter.endDate as string)
              .subtract(11, 'months')
              .startOf('month')
              .format('YYYY-MM-DD'),
        finish: parameter.endDate
          ? moment(parameter.endDate as string).format('YYYY-MM-DD')
          : undefined,
        limit: parameter.limit ? (parameter.limit as string) : 'all',
        report_type: reportType as string,
        currency_type_master_ids: currencyTypeListParam,
        bp_code_start: parameter.bp_code_start,
        bp_code_end: parameter.bp_code_end,
        sub_total: reportType !== 'report_deposit_list' ? undefined : parameter?.sub_total,
        type_date: parameter.type_date ? (parameter.type_date as string) : 'payment_date',
      };
      if (isExport) {
        valueSearch.is_export_excel = 1;
        const resExport = await getReportDeposit(valueSearch);
        window.open(resExport.data?.data?.file_url);
        setIsLoading(false);
        return;
      }
      setDataSource([]);
      const resGetList = await getReportDeposit(valueSearch);
      if (resGetList.status === STATUS_CODE.SUCCESSFUL) {
        let dataRender: any[] = [];
        if (reportType === 'report_deposit_list') {
          const subTotalCategory = parameter?.sub_total === 'category_deposit_slip';
          if (subTotalCategory) {
            dataRender = formatDataPaymentTypeCategorySlip(
              resGetList.data?.data?.data as ResponseReportDepositType[],
            );
          } else {
            dataRender = formatDataPaymentTypeOther(
              resGetList.data?.data?.data as ResponseReportDepositTypeOther,
            );
          }
        } else if (
          reportType === 'report_deposit_list_unpaid_exp' ||
          reportType === 'report_deposit_list_unpaid_collection_plan'
        ) {
          dataRender = formatDataPaymentUnpaid(
            resGetList.data?.data?.data as ResponseReportUnpaidDepositType[],
          );
        }
        setDataSource(dataRender);
      } else {
        openNotificationFail({ message: MESSAGE_ALERT.FAILED_TO_GET_DATA });
      }
    } catch (error) {
      console.log('error', error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    // set Form Header
    if (formHeader) {
      let currencyTypeListParam = undefined;
      try {
        if (
          parameter?.currency_type_master_ids &&
          Array.isArray(parameter.currency_type_master_ids)
        ) {
          currencyTypeListParam = parameter?.currency_type_master_ids?.map((item: string) =>
            Number(item),
          );
        } else if (
          parameter?.currency_type_master_ids &&
          typeof parameter.currency_type_master_ids === 'string'
        ) {
          currencyTypeListParam = [Number(parameter?.currency_type_master_ids)];
        }
      } catch (error) {
        console.log('currencyTypeListParam error', error);
      }
      setFirstCodeBusinessPartner(parameter.bp_code_start as string | undefined);
      setSecondCodeBusinessPartner(parameter.bp_code_end as string | undefined);
      formHeader.setFieldsValue({
        ...parameter,
        startDate: parameter.startDate ? moment(parameter.startDate as string) : undefined,
        endDate: parameter.endDate
          ? moment(parameter.endDate as string)
          : moment().subtract(1, 'months').endOf('month'),
        currency_type_master_ids: currencyTypeListParam,
        bp_code_start: parameter.bp_code_start,
        bp_code_end: parameter.bp_code_end,
        type_date: parameter.type_date ? (parameter.type_date as string) : 'voucher_posting_date',
      });
    }
    onFetchData();
  }, [parameter]);

  const onSubmit = async () => {
    const valueSubmit = await formHeader.validateFields();
    let startDate = valueSubmit.startDate && moment(valueSubmit.startDate).format('YYYY-MM-DD');
    let endDate = valueSubmit.endDate && moment(valueSubmit.endDate).format('YYYY-MM-DD');
    const reportType = valueSubmit.report_type;
    if (reportType !== 'report_deposit_list') {
      startDate =
        valueSubmit.startDate &&
        moment(valueSubmit.startDate).startOf('months').format('YYYY-MM-DD');
      endDate =
        valueSubmit.endDate && moment(valueSubmit.endDate).endOf('months').format('YYYY-MM-DD');
    }
    setParameter({
      ...valueSubmit,
      startDate,
      endDate,
      bp_code_start: firstCodeBusinessPartner,
      bp_code_end: secondCodeBusinessPartner,
    });
  };

  return (
    <PageContainer>
      <HeaderAction
        form={formHeader}
        onSubmit={onSubmit}
        optionsBusinessPartner={optionsBusinessPartner}
        firstCodeBusinessPartner={firstCodeBusinessPartner}
        secondCodeBusinessPartner={secondCodeBusinessPartner}
        setFirstCodeBusinessPartner={setFirstCodeBusinessPartner}
        setSecondCodeBusinessPartner={setSecondCodeBusinessPartner}
        subTotalParam={parameter.sub_total}
      />
      <div className="p-2 rounded-xl bg-white mt-6">
        {(!parameter.report_type || parameter.report_type === 'report_deposit_list') && (
          <TableDeposit
            isLoading={isLoading}
            dataSource={dataSource}
            totalAmount={totalAmount}
            feeAmount={feeAmount}
          />
        )}
        {parameter.report_type === 'report_deposit_list_unpaid_exp' && (
          <TableDepositUnpaid
            isLoading={isLoading}
            dataSource={dataSource}
            totalAmount={totalAmount}
            previousPaid={previousPaid}
            currentPaid={currentPaid}
            remainingAmount={remainingAmount}
          />
        )}
        {parameter.report_type === 'report_deposit_list_unpaid_collection_plan' && (
          <TableDepositUnpaidPlan
            isLoading={isLoading}
            dataSource={dataSource}
            totalAmount={totalAmount}
            previousPaid={previousPaid}
            currentPaid={currentPaid}
            remainingAmount={remainingAmount}
          />
        )}
      </div>

      <div className="flex gap-6 justify-center mt-10">
        <BasicButton
          disabled={isLoading}
          styleType="accept"
          className={`!w-[190px] flex justify-center items-center ${
            isLoading ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
          }`}
          onClick={() => {
            onFetchData(true);
          }}
        >
          <DownloadOutlined style={{ color: 'white' }} />
          <p>{TEXT_ACTION.Report_output}</p>
        </BasicButton>
      </div>
    </PageContainer>
  );
};

export default InvoicesPage;
