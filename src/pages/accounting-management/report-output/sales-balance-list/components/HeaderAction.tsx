import { getListTravelTypeMaster } from '@/apis/travelTypeMaster';
import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import SelectLocalAgent from '@/components/Commons/SelectLocalAgent';
import SelectManager from '@/components/Commons/SelectManager';
import SearchSVG from '@/components/SVG/SearchSVG';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import { amountOptions, optionReportSaleType, optionReportTypeDate } from '@/constants/data';
import STATUS_CODE from '@/constants/statusCode';
import type { FormInstance } from 'antd';
import { Form } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';

const HeaderAction = ({ form, onSubmit }: { form: FormInstance<any>; onSubmit: () => void }) => {
  const [optionTravelTypeMaster, setOptionTravelTypeMaster] = useState<any[]>();
  const endDate = Form.useWatch('endDate', form);

  const validateEndDate = (_, value) => {
    const startDate = form.getFieldValue('startDate');
    if (!startDate || !value) {
      return Promise.resolve();
    }

    if (value.isBefore(startDate)) {
      return Promise.reject('終了日は開始日より後でなければなりません');
    }

    const diffInDays = value.diff(startDate, 'days');
    if (diffInDays > 365) {
      return Promise.reject('開始日より1年以内の日付を選択してください。');
    }

    return Promise.resolve();
  };

  const handleGetTravelTypeMaster = async () => {
    const res = await getListTravelTypeMaster({
      limit: 'all',
      order: 'asc',
      sort: 'travel_code',
    });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      const options = [
        // { value: 'all', label: 'すべて選択' },
        ...res?.data?.data?.map((item) => {
          return {
            value: item.id,
            label: item.travel_name,
          };
        }),
      ];
      setOptionTravelTypeMaster(options);
    }
  };

  useEffect(() => {
    handleGetTravelTypeMaster();
  }, []);

  return (
    <>
      <Form form={form} component={false}>
        <div className="grid grid-cols-6 gap-4 ">
          <Form.Item
            name="report_type"
            className="col-span-2 !mb-0"
            rules={[{ required: true, message: 'を入力してください' }]}
          >
            <BasicSelect
              title={TEXT_TITLE.Report_type}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              required={true}
              options={optionReportSaleType}
            />
          </Form.Item>
          <div className="col-span-2 !mb-0">
            <BasicInput
              className=" !bg-[#DCDEE3] !text-[#9499A5]"
              disabled
              title={TEXT_TITLE.Sort_oder}
              value="出発日 > 旅行ID > 得意先（カナ）"
            />
          </div>

          <Form.Item name="is_excluding_tax" className="col-span-2 !mb-0">
            <BasicSelect
              title={TEXT_TITLE.Amount}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              options={amountOptions}
            />
          </Form.Item>
          <Form.Item name="business_partner_id" className="col-span-2 !mb-0">
            <SelectLocalAgent
              allowClear
              title={<div className="flex items-center gap-[6px]">{TEXT_TITLE.Customer}</div>}
              className="!w-full"
            />
          </Form.Item>
          {optionTravelTypeMaster && (
            <Form.Item name="travel_type" className="col-span-2 !mb-0">
              <BasicSelect
                className="[&_.ant-select-selection-overflow]:!flex-nowrap [&_.ant-select-selection-overflow]:!overflow-hidden"
                title={TEXT_TITLE.Trip_type}
                placeholder={'選択してください'}
                allowClear
                options={optionTravelTypeMaster}
                mode="multiple"
              />
            </Form.Item>
          )}
          <Form.Item name="admin_id" className="col-span-2 !mb-0">
            <SelectManager
              allowClear
              title={<div className="flex items-center gap-[6px]">{TEXT_TITLE.Salesperson}</div>}
              className="!w-full"
            />
          </Form.Item>
          <Form.Item
            name="type_date"
            initialValue="voucher_posting_date"
            className="col-span-2 !mb-0"
          >
            <BasicSelect
              title={TEXT_TITLE.Add_output_condition}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              options={optionReportTypeDate}
            />
          </Form.Item>
          <Form.Item name="startDate" className="col-span-1">
            <BasicDatePicker className="h-10" title={TEXT_TITLE.Departure_date} />
          </Form.Item>
          <Form.Item
            name="endDate"
            className="col-span-1"
            rules={[
              { required: true, message: 'を入力してください' },
              { validator: validateEndDate },
            ]}
          >
            <BasicDatePicker required className="h-10" title={TEXT_TITLE.Return_Date} />
          </Form.Item>
        </div>
        <div className="flex items-end">
          <BasicButton
            icon={<SearchSVG colorSvg="white" />}
            className="flex items-center w-[120px]"
            styleType="accept"
            onClick={onSubmit}
          >
            <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
          </BasicButton>
        </div>
      </Form>
    </>
  );
};

export default HeaderAction;
