import BasicTable from '@/components/Commons/BasicTable';
import EditableCell from '@/components/Commons/EditableCell';
import { formatMoney, formatPercent } from '@/utils';
import { Table } from 'antd';
import { useRef } from 'react';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  isLoading,
  dataSource,
  SalesAmount,
  PurchaseAmount,
  GrossProfitAmount,
  RAmount,
  GrossProfitTotal,
  GrossProfitRate,
}: {
  isLoading: boolean;
  dataSource: any[];
  SalesAmount: number;
  PurchaseAmount: number;
  GrossProfitAmount: number;
  RAmount: number;
  GrossProfitTotal: number;
  GrossProfitRate: number;
}) => {
  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };

  const columns = [
    {
      title: '#',
      dataIndex: 'key',
      key: 'key',
      width: 70,
    },

    {
      title: '旅行ID',
      dataIndex: 'travel_code',
      key: 'travel_code',
      width: 360,
      children: [
        {
          title: 'ツアー名',
          dataIndex: 'tour_name',
          key: 'tour_name',
          width: 360,
          render: (_, record) => {
            if (record.type === 'total') {
              return <p className="font-medium">{record.title}</p>;
            }
            return (
              <p className=" !ml-1">
                {record.travel_code}
                <br />
                {record.tour_name}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '集計科目',
      dataIndex: 'summary_subject_name',
      key: 'summary_subject_name',
      width: 140,
      children: [
        {
          dataIndex: 'xxxx',
          key: 'xxx',
          width: 140,
          render: (_, record) => {
            return <p className="">{record.summary_subject_name}</p>;
          },
        },
      ],
    },
    {
      title: '売上収支',
      dataIndex: 'Sales_income_expense',
      key: 'Sales_income_expense',
      width: 720,
      children: [
        {
          title: '売上金額',
          dataIndex: 'Sales_amount',
          key: 'Sales_amount',
          width: 120,
          render: (_, record) => <p className="text-right">{record.Sales_amount}</p>,
        },
        {
          title: '仕入金額',
          dataIndex: 'Purchase_amount',
          key: 'Purchase_amount',
          width: 120,
          render: (_, record) => <p className="text-right">{record.Purchase_amount}</p>,
        },
        {
          title: '粗利額',
          dataIndex: 'Gross_profit_amount',
          width: 120,
          key: 'Gross_profit_amount',
          render: (_, record) => <p className="text-right">{record.Gross_profit_amount}</p>,
        },
        {
          title: '手数料（R額）',
          dataIndex: 'R_amount',
          key: 'R_amount',
          width: 120,
          render: (_, record) => <p className="text-right">{record.R_amount}</p>,
        },
        {
          title: '粗利合計',
          dataIndex: 'Gross_profit_total',
          key: 'Gross_profit_total',
          width: 120,
          render: (_, record) => <p className="text-right">{record.Gross_profit_total}</p>,
        },
        {
          title: '粗利率',
          dataIndex: 'Gross_profit_rate',
          key: 'Gross_profit_rate',
          width: 120,
          render: (_, record) => (
            <p className="text-right">{formatPercent(record.Gross_profit_rate, 2, '%')}</p>
          ),
        },
      ],
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1280, y: 800 },
          loading: isLoading,

          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'index',
          rowClassName: getRowClassName,
          summary: (pageData) => (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={1} />
              <Table.Summary.Cell index={2} className="font-medium">
                合計
              </Table.Summary.Cell>
              <Table.Summary.Cell index={3} colSpan={1} />
              <Table.Summary.Cell index={4} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(SalesAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={5} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(PurchaseAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={6} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(GrossProfitAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={7} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(RAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={8} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(GrossProfitTotal)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={9} className="text-right font-medium">
                {dataSource.length > 0 && formatPercent(GrossProfitRate, 2, '%')}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          ),
        }}
        hasPagination={false}
      />
    </>
  );
};

export default TableList;
