import BasicTable from '@/components/Commons/BasicTable';
import { formatMoney, formatPercent } from '@/utils';
import { Table } from 'antd';
import moment from 'moment';
import { useMemo } from 'react';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  isLoading,
  dataSource,
  SalesAmount,
  PurchaseAmount,
  GrossProfitAmount,
  RAmount,
  GrossProfitTotal,
  GrossProfitRate,
  typeDate,
}: {
  isLoading: boolean;
  dataSource: any[];
  SalesAmount: number;
  PurchaseAmount: number;
  GrossProfitAmount: number;
  RAmount: number;
  GrossProfitTotal: number;
  GrossProfitRate: number;
  typeDate: string;
}) => {
  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };

  const columns = useMemo(() => {
    return [
      {
        title: '',
        dataIndex: 'key',
        key: 'key',
        width: 40,
      },
      {
        title:
          typeDate == 'departure_date'
            ? '出発日'
            : typeDate === 'return_date'
            ? '帰着日'
            : '計上日',
        dataIndex: 'departure_date',
        key: 'departure_date',
        width: 120,
        children: [
          {
            title: '旅行ID',
            dataIndex: 'travel_code',
            key: 'travel_code',
            width: 120,
            render: (_, record) => {
              const date =
                typeDate === 'departure_date'
                  ? record.departure_date
                  : typeDate === 'return_date'
                  ? record.return_date
                  : record.sale_date;
              return (
                <p className="">
                  {date ? moment(date).format('YY/MM/DD') : ''}
                  <br />
                  {record.travel_code}
                </p>
              );
            },
          },
        ],
      },
      {
        title: '得意先名',
        dataIndex: 'business_partner_name',
        key: 'business_partner_name',
        width: 240,
        children: [
          {
            title: 'ツアー名',
            dataIndex: 'tour_name',
            key: 'tour_name',
            width: 240,
            render: (_, record) => {
              if (record.type === 'total') {
                return <p className="font-medium">{record.title}</p>;
              }
              return (
                <p className=" !ml-1">
                  {record.business_partner_name}
                  <br />
                  {record.tour_name}
                </p>
              );
            },
          },
        ],
      },
      {
        title: '得意先ID',
        dataIndex: 'business_partner_code',
        key: 'business_partner_code',
        width: 100,
        children: [
          {
            title: '',
            width: 100,
            dataIndex: 'business_partner_code',
            key: 'business_partner_code',
          },
        ],
      },
      {
        title: '営業担当者',
        dataIndex: 'Salesperson',
        key: 'Salesperson',
        width: 140,
        children: [
          {
            title: '計上日',
            dataIndex: 'sale_date',
            key: 'sale_date',
            width: 140,
            render: (_, record) => {
              const date = record.sale_date;
              if (record.type === 'total') {
                return null;
              }
              return (
                <p className=" !ml-1">
                  {record.Salesperson}
                  <br />
                  {date ? moment(date).format('YY/MM/DD') : ''}
                </p>
              );
            },
          },
        ],
      },
      {
        title: '売上収支',
        dataIndex: 'Sales_income_expense',
        key: 'Sales_income_expense',
        width: 660,
        children: [
          {
            title: '売上金額',
            dataIndex: 'Sales_amount',
            key: 'Sales_amount',
            width: 110,
            render: (_, record) => <p className="text-right">{record.Sales_amount}</p>,
          },
          {
            title: '仕入金額',
            dataIndex: 'Purchase_amount',
            key: 'Purchase_amount',
            width: 110,
            render: (_, record) => <p className="text-right">{record.Purchase_amount}</p>,
          },
          {
            title: '粗利額',
            dataIndex: 'Gross_profit_amount',
            key: 'Gross_profit_amount',
            width: 110,
            render: (_, record) => <p className="text-right">{record.Gross_profit_amount}</p>,
          },
          {
            title: '手数料（R額）',
            dataIndex: 'R_amount',
            key: 'R_amount',
            width: 110,
            render: (_, record) => <p className="text-right">{record.R_amount}</p>,
          },
          {
            title: '粗利合計',
            dataIndex: 'Gross_profit_total',
            key: 'Gross_profit_total',
            width: 110,
            render: (_, record) => <p className="text-right">{record.Gross_profit_total}</p>,
          },
          {
            title: '粗利率',
            dataIndex: 'Gross_profit_rate',
            key: 'Gross_profit_rate',
            width: 110,
            render: (_, record) => (
              <p className="text-right">{formatPercent(record.Gross_profit_rate, 2, '%')}</p>
            ),
          },
        ],
      },
    ];
  }, [typeDate]);

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1600, y: 800 },
          loading: isLoading,
          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'index',
          rowClassName: getRowClassName,
          summary: () => (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={2} />
              <Table.Summary.Cell index={3} className="font-medium">
                合計
              </Table.Summary.Cell>
              <Table.Summary.Cell index={4} colSpan={2} />
              <Table.Summary.Cell index={5} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(SalesAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={6} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(PurchaseAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={7} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(GrossProfitAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={8} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(RAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={9} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(GrossProfitTotal)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={10} className="text-right font-medium">
                {dataSource.length > 0 && formatPercent(GrossProfitRate, 2, '%')}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          ),
        }}
        hasPagination={false}
      />
    </>
  );
};

export default TableList;
