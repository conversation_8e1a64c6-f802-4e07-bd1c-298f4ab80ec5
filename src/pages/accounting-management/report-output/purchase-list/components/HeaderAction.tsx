import { getListAggregationItem } from '@/apis/master/AggregationItem';
import ArrowRight from '@/assets/imgs/svg/arrow-right-02.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import BasicNumbericInput from '@/components/Commons/BasicNumbericInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import SearchSVG from '@/components/SVG/SearchSVG';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import { optionReportPurchaseType, optionReportTypeDate } from '@/constants/data';
import STATUS_CODE from '@/constants/statusCode';
import { BaseOptionType, filterBusinessPartner } from '@/utils';
import type { FormInstance } from 'antd';
import { Form, Image, Select } from 'antd';
import { useEffect, useMemo, useState } from 'react';

const HeaderAction = ({
  form,
  onSubmit,
  reportType,
  optionsBusinessPartner,
  firstCodeBusinessPartner,
  secondCodeBusinessPartner,
  setFirstCodeBusinessPartner,
  setSecondCodeBusinessPartner,
}: {
  form: FormInstance<any>;
  onSubmit: () => void;
  reportType: string | number | boolean | Object | string[] | Date;
  optionsBusinessPartner: BaseOptionType[];
  firstCodeBusinessPartner: string;
  secondCodeBusinessPartner: string;
  setFirstCodeBusinessPartner: (val: string) => void;
  setSecondCodeBusinessPartner: (val: string) => void;
}) => {
  const [summaryItemMasterOptions, setSummaryItemMasterOptions] = useState<any[]>([]);
  const typeRankingAmount = [
    { value: 'amount_excluding_tax', label: '仕入金額（税抜）' },
    { value: 'amount_including_tax', label: '仕入金額（税込）' },
    { value: 'r_amount', label: '手数料（Ｒ額）' },
    { value: 'payment_amount', label: '支払金額' },
  ];

  const [firstSearchSelect, setFirstSearchSelect] = useState<string>('');
  const [secondSearchSelect, setSecondSearchSelect] = useState<string>('');

  const onFetchDataSummaryItemMaster = async () => {
    try {
      const payload = {
        limit: 'all',
        sort: 'summary_item_code',
        order: 'asc',
      };
      const resGetListMaster = await getListAggregationItem(payload);
      if (resGetListMaster.status === STATUS_CODE.SUCCESSFUL) {
        const resData = resGetListMaster.data;
        setSummaryItemMasterOptions(
          resData.data.map((item) => ({
            label: item?.summary_item_name,
            value: item?.id,
          })),
        );
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    onFetchDataSummaryItemMaster();
  }, []);

  const validateEndDate = (_, value) => {
    const startDate = form.getFieldValue('startDate');
    if (!startDate || !value) {
      return Promise.resolve();
    }

    if (value.isBefore(startDate)) {
      return Promise.reject('終了日は開始日より後でなければなりません');
    }

    const diffInDays = value.diff(startDate, 'days');
    if (diffInDays > 365) {
      return Promise.reject('開始日より1年以内の日付を選択してください。');
    }

    return Promise.resolve();
  };

  const highlightText = (text: string, search: string) => {
    if (!search) return text;
    const regex = new RegExp(`(${search})`, 'gi');
    try {
      return text?.split?.(regex).map((part, index) =>
        regex.test(part) ? (
          <span key={index} style={{ color: 'red' }}>
            {part}
          </span>
        ) : (
          part
        ),
      );
    } catch (error) {
      return text;
    }
  };

  const secondRenderOption = useMemo(() => {
    if (!optionsBusinessPartner) return [];
    return optionsBusinessPartner.filter(
      (item) => !firstCodeBusinessPartner || Number(item.value) >= Number(firstCodeBusinessPartner),
    );
  }, [optionsBusinessPartner, firstCodeBusinessPartner]);

  return (
    <>
      <Form form={form} component={false}>
        <div className="grid grid-cols-6 gap-4 ">
          <Form.Item
            name="report_type"
            className="col-span-2 !mb-0"
            rules={[{ required: true, message: 'を入力してください' }]}
          >
            <BasicSelect
              title={TEXT_TITLE.Report_type}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              required={true}
              options={optionReportPurchaseType}
            />
          </Form.Item>
          <div className="col-span-2 !mb-0">
            <BasicInput
              className=" !bg-[#DCDEE3] !text-[#9499A5]"
              disabled
              title={TEXT_TITLE.Sort_oder}
              value="仕入先名（カナ）> 仕入先コード > 旅行ID"
            />
          </div>
          {reportType === 'report_by_ranking_amount' ? (
            <>
              <Form.Item name="type_ranking_amount" className="col-span-2 !mb-0">
                <BasicSelect
                  title={TEXT_TITLE.Ranking_Target}
                  placeholder={TEXT_PLACEHOLDER.Please_select}
                  options={typeRankingAmount}
                />
              </Form.Item>
              <Form.Item name="limit" className="col-span-2 !mb-0">
                <BasicNumbericInput
                  title={TEXT_TITLE.Ranking}
                  placeholder={TEXT_PLACEHOLDER.Enter_number_digit}
                />
              </Form.Item>
              <Form.Item name="summary_item_master_id" className="col-span-2 !mb-0">
                <BasicSelect
                  title={TEXT_TITLE.Aggregation_category}
                  placeholder={TEXT_PLACEHOLDER.Please_select}
                  options={summaryItemMasterOptions}
                />
              </Form.Item>
            </>
          ) : (
            <div className="flex items-center gap-1">
              <div className="firstSelectBP">
                <div className="mb-2 text-[13px] leading-4 font-medium">取引先コード(取引先名)</div>
                <Select
                  showSearch
                  allowClear
                  className="min-w-[200px] [&_.ant-select-selector]:!rounded-lg
                    [&_.ant-select-selection-search-input]:!min-h-[40px]
                    [&_.ant-select-selector]:!min-h-10 max-w-[350px]
                    [&_.ant-select-selection-placeholder]:flex
                    [&_.ant-select-selection-placeholder]:items-center
                    !h-fit [&_.ant-select-selector]:!h-fit"
                  filterOption={(input, option) =>
                    filterBusinessPartner(input, option as BaseOptionType)
                  }
                  placeholder={TEXT_PLACEHOLDER.Please_select}
                  onChange={setFirstCodeBusinessPartner}
                  onSearch={setFirstSearchSelect}
                  searchValue={firstSearchSelect}
                  value={firstCodeBusinessPartner}
                >
                  {optionsBusinessPartner?.map((item) => (
                    <Select.Option key={item.value} value={item.value} label={item.label}>
                      <span className="text-sm font-semibold">
                        {highlightText(item.value, firstSearchSelect)}
                      </span>
                      <br />
                      <span className="text-xs">
                        {highlightText(item.label, firstSearchSelect)}
                      </span>
                    </Select.Option>
                  ))}
                </Select>
              </div>
              <div className="">
                <div className="h-6" />
                <div className="flex items-center">
                  <Image preview={false} src={ArrowRight} alt="arrow right" />
                </div>
              </div>
              <div className="firstSelectBP">
                <div className="mb-2 text-[13px] leading-4 font-medium">取引先コード(取引先名)</div>
                <Select
                  showSearch
                  allowClear
                  className="min-w-[200px] [&_.ant-select-selector]:!rounded-lg
                    [&_.ant-select-selection-search-input]:!min-h-[40px]
                    [&_.ant-select-selector]:!min-h-10 max-w-[350px]
                    [&_.ant-select-selection-placeholder]:flex
                    [&_.ant-select-selection-placeholder]:items-center
                    !h-fit [&_.ant-select-selector]:!h-fit"
                  filterOption={(input, option) =>
                    filterBusinessPartner(input, option as BaseOptionType)
                  }
                  placeholder={TEXT_PLACEHOLDER.Please_select}
                  onChange={setSecondCodeBusinessPartner}
                  searchValue={secondSearchSelect}
                  onSearch={setSecondSearchSelect}
                  value={secondCodeBusinessPartner}
                >
                  {secondRenderOption?.map((item) => (
                    <Select.Option key={item.value} value={item.value} label={item.label}>
                      <span className="text-sm font-semibold">
                        {highlightText(item.value, secondSearchSelect)}
                      </span>
                      <br />
                      <span className="text-xs">
                        {highlightText(item.label, secondSearchSelect)}
                      </span>
                    </Select.Option>
                  ))}
                </Select>
              </div>
            </div>
          )}
          <Form.Item
            name="type_date"
            initialValue="voucher_posting_date"
            className="col-span-2 !mb-0"
          >
            <BasicSelect
              title={TEXT_TITLE.Add_output_condition}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              options={optionReportTypeDate}
            />
          </Form.Item>
          <Form.Item name="startDate" className="col-span-1">
            <BasicDatePicker
              picker={reportType === 'report_by_ranking_amount' ? 'month' : 'date'}
              className="h-10"
              title={TEXT_TITLE.Departure_date}
            />
          </Form.Item>
          <Form.Item
            name="endDate"
            className="col-span-1"
            rules={[
              { required: true, message: 'を入力してください' },
              { validator: validateEndDate },
            ]}
          >
            <BasicDatePicker
              picker={reportType === 'report_by_ranking_amount' ? 'month' : 'date'}
              required
              className="h-10"
              title={TEXT_TITLE.Return_Date}
            />
          </Form.Item>
        </div>
        <div className="flex items-end">
          <BasicButton
            icon={<SearchSVG colorSvg="white" />}
            className="flex items-center w-[120px]"
            styleType="accept"
            onClick={onSubmit}
          >
            <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
          </BasicButton>
        </div>
      </Form>
    </>
  );
};

export default HeaderAction;
