import BasicTable from '@/components/Commons/BasicTable';
import EditableCell from '@/components/Commons/EditableCell';
import { formatMoney, formatPercent } from '@/utils';

// eslint-disable-next-line max-lines-per-function
const TableList = ({ isLoading, dataSource }: { isLoading: boolean; dataSource: any[] }) => {
  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };
  const columns = [
    {
      title: '順位',
      dataIndex: 'ranking',
      key: 'ranking',
      width: 100,
    },
    {
      title: '仕入先ID',
      dataIndex: 'business_partner_code',
      key: 'business_partner_code1',
      width: 140,
    },
    {
      title: '仕入先名称',
      dataIndex: 'business_partner_name',
      key: 'business_partner_name',
      width: 200,
    },
    {
      title: '仕入明細数',
      dataIndex: 'total_item_quantity',
      key: 'total_item_quantity',
      width: 120,
      render: (_, record) => (
        <p className="text-right">{formatMoney(record.total_item_quantity)}</p>
      ),
    },
    {
      title: '仕入金額 (税抜)',
      dataIndex: 'total_amount_excluding_tax',
      key: 'total_amount_excluding_tax',
      width: 140,
      render: (_, record) => (
        <p className="text-right">{formatMoney(record.total_amount_excluding_tax)}</p>
      ),
    },
    {
      title: '消費税額',
      dataIndex: 'total_consumption_tax',
      key: 'total_consumption_tax',
      width: 140,
      render: (_, record) => (
        <p className="text-right">{formatMoney(record.total_consumption_tax)}</p>
      ),
    },
    {
      title: '仕入金額 (税込)',
      dataIndex: 'total_amount_including_tax',
      key: 'total_amount_including_tax',
      width: 140,
      render: (_, record) => (
        <p className="text-right">{formatMoney(record.total_amount_including_tax)}</p>
      ),
    },
    {
      title: '手数料 (R額)',
      dataIndex: 'total_r_amount',
      key: 'total_r_amount',
      width: 120,
      render: (_, record) => <p className="text-right">{formatMoney(record.total_r_amount)}</p>,
    },
    {
      title: '支払金額',
      dataIndex: 'total_payment_amount',
      key: 'total_payment_amount',
      width: 140,
      render: (_, record) => (
        <p className="text-right">{formatMoney(record.total_payment_amount)}</p>
      ),
    },
    {
      title: '占有率',
      dataIndex: 'occupancy_rate',
      key: 'occupancy_rate',
      width: 140,
      render: (_, record) => (
        <p className="text-right">{formatPercent(record.occupancy_rate, 2, '%')}</p>
      ),
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1300, y: 800 },
          loading: isLoading,
          components: {
            body: {
              cell: EditableCell,
            },
          },
          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'index',
          rowClassName: getRowClassName,
        }}
        hasPagination={false}
      />
    </>
  );
};

export default TableList;
