import BasicTable from '@/components/Commons/BasicTable';
import { formatMoney, formatPercent } from '@/utils';
import { Table } from 'antd';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  isLoading,
  dataSource,
  AmountExcludingTax,
  ConsumptionTax,
  AmountIncludingTax,
  RAmount,
  TotalPayment,
}: {
  isLoading: boolean;
  dataSource: any[];
  AmountExcludingTax: number;
  ConsumptionTax: number;
  AmountIncludingTax: number;
  RAmount: number;
  TotalPayment: number;
}) => {
  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };

  const columns = [
    {
      title: '',
      dataIndex: 'key',
      key: 'key',
      width: 75,
    },
    {
      title: '旅行ID',
      dataIndex: 'travel_code',
      key: 'travel_code',
      width: 200,
      children: [
        {
          title: 'ツアー名',
          width: 200,
          dataIndex: 'tour_name',
          key: 'tour_name',
          render: (_, record) => {
            return (
              <p className=" !ml-1">
                {record.travel_code}
                <br />
                {record.tour_name}
              </p>
            );
          },
        },
      ],
    },
    {
      title: '仕入先ID',
      dataIndex: 'business_partner_code',
      key: 'business_partner_code',
      width: 200,
      children: [
        {
          title: '仕入先名',
          dataIndex: 'business_partner_name',
          key: 'business_partner_name',
          width: 200,
          render: (_, record) => (
            <p className=" !ml-1">
              {record.business_partner_code}
              <br />
              {record.business_partner_name}
            </p>
          ),
        },
      ],
    },
    // {
    //   title: '旅行ID',
    //   dataIndex: 'travel_code',
    //   key: 'travel_code',
    //   width: 120,
    //   children: [
    //     {
    //       title: '利用日',
    //       dataIndex: 'date_of_use',
    //       key: 'date_of_use',
    //       width: 120,
    //       render: (_, record) => (
    //         <p className=" !ml-1">
    //           {record.travel_code}
    //           <br />
    //           {record.date_of_use}
    //         </p>
    //       ),
    //     },
    //   ],
    // },
    {
      title: '仕入伝票ID',
      dataIndex: 'purchase_id',
      key: 'purchase_id',
      width: 100,
      children: [
        {
          title: '仕入伝票種別',
          width: 100,
          dataIndex: 'purchase_type',
          key: 'purchase_type',
          render: (_, record) => {
            if (record.type === 'total') {
              return <>合計</>;
            }
            return (
              <p className=" !ml-1">
                {record.purchase_id}
                <br />
                {record.purchase_type}
              </p>
            );
          },
        },
      ],
    },

    {
      title: '仕入',
      dataIndex: 'business_partner_items',
      key: 'business_partner_items',
      width: 720,
      children: [
        {
          title: '計上日',
          dataIndex: 'purchase_date',
          key: 'purchase_date',
          width: 120,
        },
        {
          title: '税区分',
          dataIndex: 'tax_category_name',
          key: 'tax_category_name',
          width: 120,
        },
        {
          title: '税率',
          dataIndex: 'tax_rate',
          key: 'tax_rate',
          width: 120,
          render: (_, record) => (
            <p className="text-right !ml-1">{formatPercent(record.tax_rate, 2, '%')}</p>
          ),
        },
        {
          title: '仕入金額 (税抜)',
          width: 120,
          dataIndex: 'amount_excluding_tax',
          key: 'amount_excluding_tax',
          render: (_, record) => (
            <p className="text-right !ml-1">{formatMoney(record.amount_excluding_tax)}</p>
          ),
        },
        {
          title: '消費税額',
          width: 120,
          dataIndex: 'consumption_tax',
          key: 'consumption_tax',
          render: (_, record) => (
            <p className="text-right !ml-1">{formatMoney(record.consumption_tax)}</p>
          ),
        },
        {
          title: '仕入金額 (税込)',
          width: 120,
          dataIndex: 'amount_including_tax',
          key: 'amount_including_tax',
          render: (_, record) => (
            <p className="text-right !ml-1">{formatMoney(record.amount_including_tax)}</p>
          ),
        },
      ],
    },
    {
      title: '手数料',
      dataIndex: 'discount',
      key: 'discount',
      width: 100,
      children: [
        {
          title: 'R額',
          width: 100,
          dataIndex: 'r_amount',
          key: 'r_amount',
          render: (_, record) => <p className="text-right !ml-1">{formatMoney(record.r_amount)}</p>,
        },
      ],
    },
    {
      title: '手数料相殺後',
      dataIndex: 'payment_offset',
      key: 'payment_offset',
      width: 100,
      children: [
        {
          title: '支払額',
          width: 100,
          dataIndex: 'payment_offset',
          key: 'payment_offset',
          render: (_, record) => (
            <p className="text-right !ml-1">{formatMoney(record.payment_offset)}</p>
          ),
        },
      ],
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1900, y: 800 },
          loading: isLoading,
          columns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'index',
          rowClassName: getRowClassName,
          summary: (pageData) => (
            <Table.Summary.Row>
              <Table.Summary.Cell index={0} colSpan={3} />
              <Table.Summary.Cell index={4} className="font-medium">
                合計
              </Table.Summary.Cell>
              <Table.Summary.Cell index={5} colSpan={3} />
              <Table.Summary.Cell index={9} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(AmountExcludingTax)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={10} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(ConsumptionTax)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={11} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(AmountIncludingTax)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={12} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(RAmount)}
              </Table.Summary.Cell>
              <Table.Summary.Cell index={13} className="text-right font-medium">
                {dataSource.length > 0 && formatMoney(TotalPayment)}
              </Table.Summary.Cell>
            </Table.Summary.Row>
          ),
        }}
        hasPagination={false}
      />
    </>
  );
};

export default TableList;
