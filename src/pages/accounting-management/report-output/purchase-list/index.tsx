import { BusinessPartnerDetailType } from '@/@types/businessPartner';
import { getListBusinessPartner } from '@/apis/businessPartner';
import type {
  ResponseOtherItemReportPurchaseTypeByBP,
  ResponseReportPurchaseTypeByBP,
  ResponseReportPurchaseTypeForBP,
  ResponseReportPurchaseTypeForTravel,
} from '@/apis/reportOutput/purchase';
import { getReportPurchase } from '@/apis/reportOutput/purchase';
import BasicButton from '@/components/Commons/BasicButton';
import PageContainer from '@/components/Commons/Page/Container';
import { openNotificationFail } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { DownloadOutlined } from '@ant-design/icons';
import { Form } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';
import { useUrlSearchParams } from 'use-url-search-params';
import HeaderAction from './components/HeaderAction';
import TableByCustomer from './components/TableByCustomer';
import TableForBusinessPartner from './components/TableForBusinessPartner';
import TableForTripID from './components/TableForTripID';
import { BaseOptionType } from '@/utils';

const initSearchParams = {};

const InvoicesPage = () => {
  const [formHeader] = Form.useForm();
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);
  const [isLoading, setIsLoading] = useState(false);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [AmountExcludingTax, setAmountExcludingTax] = useState(0);
  const [ConsumptionTax, setConsumptionTax] = useState(0);
  const [AmountIncludingTax, setAmountIncludingTax] = useState(0);
  const [RAmount, setRAmount] = useState(0);
  const [TotalPayment, setTotalPayment] = useState(0);
  const [optionsBusinessPartner, setOptionsBusinessPartner] = useState<BaseOptionType[]>();
  const [firstCodeBusinessPartner, setFirstCodeBusinessPartner] = useState<string>();
  const [secondCodeBusinessPartner, setSecondCodeBusinessPartner] = useState<string>();

  const formatOptionBusinessPartner = async () => {
    const resData = await getListBusinessPartner({
      page: 1,
      limit: 'all',
      order: 'desc',
      order_by: 'business_partner_code',
    });
    const listCompany = resData?.data?.data;
    if (listCompany) {
      const newList = listCompany?.map((item: BusinessPartnerDetailType) => {
        return {
          label: item.business_partner_name,
          value: item.business_partner_code?.toString(),
          id: item.id,
        };
      });
      newList.sort((a, b) => Number(a.value) - Number(b.value));
      setOptionsBusinessPartner(newList);
      return;
    }
    setOptionsBusinessPartner([]);
  };

  useEffect(() => {
    formatOptionBusinessPartner();
  }, []);

  const reportTypeForm = Form.useWatch('report_type', formHeader);
  const typeRankingAmount = Form.useWatch('type_ranking_amount', formHeader);

  const checkHasOtherTypeRankingAmount = (dataCheck: ResponseOtherItemReportPurchaseTypeByBP) => {
    if (typeRankingAmount === 'payment_amount' && dataCheck.total_payment_amount !== 0) {
      return true;
    } else if (typeRankingAmount === 'r_amount' && dataCheck.total_r_amount !== 0) {
      return true;
    } else if (
      typeRankingAmount === 'amount_including_tax' &&
      dataCheck.total_amount_including_tax !== 0
    ) {
      return true;
    } else if (
      typeRankingAmount === 'amount_excluding_tax' &&
      dataCheck.total_amount_excluding_tax !== 0
    ) {
      return true;
    }
    return false;
  };

  const formatDataTypeToTravel = (data: ResponseReportPurchaseTypeForTravel[]) => {
    const dataRender: any[] = [];
    let key = 1;
    data.forEach((groupData, index) => {
      groupData.business_partner_items.forEach((BPItems, indexBP) => {
        BPItems.map((itemDetail, indexDetail) => {
          dataRender.push({
            index: 'for_travel_' + key + index + indexBP + indexDetail,
            key: indexDetail === 0 ? key : undefined,
            travel_code: indexDetail === 0 && groupData?.travel_code,
            tour_name: indexDetail === 0 && groupData.tour_name,
            business_partner_code: itemDetail.business_partner_code,
            business_partner_name: itemDetail.business_partner_name,
            tax_category_code: itemDetail.tax_category_code,
            tax_rate: itemDetail.tax_rate,
            amount_excluding_tax: itemDetail.amount_excluding_tax,
            consumption_tax: itemDetail.consumption_tax,
            amount_including_tax: itemDetail.amount_including_tax,
            r_amount: itemDetail.r_amount,
            total_payment: itemDetail.total_payment,
            payment_offset: itemDetail?.payment_offset,
            purchase_id: itemDetail.purchase_invoice_code,
            purchase_type: itemDetail.item_type,
            purchase_date: itemDetail.purchase_date
              ? moment(itemDetail.purchase_date).format('YY/MM/DD')
              : '',
            tax_category_name: itemDetail?.tax_category_name,
          });
        });
      });
      dataRender.push({
        index: 'total_for_travel_' + key + index,
        type: 'total',
        amount_excluding_tax: groupData.total_amount_excluding_tax,
        consumption_tax: groupData.total_consumption_tax,
        amount_including_tax: groupData.total_amount_including_tax,
        r_amount: groupData.total_r_amount,
        total_payment: groupData.total_payment,
        payment_offset: groupData.payment_offset,
      });
      key++;
    });
    setAmountExcludingTax(data.reduce((acc, item) => acc + item.total_amount_excluding_tax, 0));
    setConsumptionTax(data.reduce((acc, item) => acc + item.total_consumption_tax, 0));
    setAmountIncludingTax(data.reduce((acc, item) => acc + item.total_amount_including_tax, 0));
    setRAmount(data.reduce((acc, item) => acc + item.total_r_amount, 0));
    // setTotalPayment(data.reduce((acc, item) => acc + item.total_payment, 0));
    setTotalPayment(data.reduce((acc, item) => acc + item.payment_offset, 0));
    return dataRender;
  };
  const formatDataTypeToBusinessPartner = (data: ResponseReportPurchaseTypeForBP[]) => {
    const dataRender: any[] = [];
    let key = 1;
    data.forEach((groupData, index) => {
      groupData.travel_items.forEach((travelItem, indexTravel) => {
        travelItem.forEach((itemDetail, indexDetail) => {
          dataRender.push({
            index: 'for_BP_' + key + index + indexTravel,
            key: indexDetail === 0 && indexTravel === 0 ? key : undefined,
            travel_code: itemDetail?.travel_code,
            tour_name: itemDetail.tour_name,
            business_partner_code:
              indexDetail === 0 && indexTravel === 0 && groupData.business_partner_code,
            business_partner_name:
              indexDetail === 0 && indexTravel === 0 && groupData.business_partner_name,
            tax_category_code: itemDetail.tax_category_code,
            tax_rate: itemDetail.tax_rate,
            amount_excluding_tax: itemDetail.amount_excluding_tax,
            consumption_tax: itemDetail.consumption_tax,
            amount_including_tax: itemDetail.amount_including_tax,
            r_amount: itemDetail.r_amount,
            total_payment: itemDetail.total_payment,
            purchase_date: itemDetail.purchase_date
              ? moment(itemDetail.purchase_date).format('YY/MM/DD')
              : '',
            item_type: itemDetail?.item_type,
            purchase_invoice_code: itemDetail?.purchase_invoice_code,
            tax_category_name: itemDetail?.tax_category_name,
            product_name: itemDetail?.product_name,
            payment_offset: itemDetail?.payment_offset,
          });
        });
      });
      dataRender.push({
        index: 'total_for_travel_' + key + index,
        type: 'total',
        amount_excluding_tax: groupData.total_amount_excluding_tax,
        consumption_tax: groupData.total_consumption_tax,
        amount_including_tax: groupData.total_amount_including_tax,
        r_amount: groupData.total_r_amount,
        total_payment: groupData.total_payment,
        payment_offset: groupData.payment_offset,
      });
      key++;
    });

    setAmountExcludingTax(data.reduce((acc, item) => acc + item.total_amount_excluding_tax, 0));
    setConsumptionTax(data.reduce((acc, item) => acc + item.total_consumption_tax, 0));
    setAmountIncludingTax(data.reduce((acc, item) => acc + item.total_amount_including_tax, 0));
    setRAmount(data.reduce((acc, item) => acc + item.total_r_amount, 0));
    setTotalPayment(data.reduce((acc, item) => acc + item.payment_offset, 0));
    return dataRender;
  };

  const formatDataTypeByRanking = (data: ResponseReportPurchaseTypeByBP) => {
    const dataRender: any[] = [];
    data.items_ranking.forEach((itemData, index) => {
      dataRender.push({
        index: 'by_BP_' + index + 1,
        ranking: index + 1,
        business_partner_name: itemData?.business_partner_name,
        business_partner_code: itemData?.business_partner_code,
        total_amount_excluding_tax: itemData?.total_amount_excluding_tax,
        total_consumption_tax: itemData?.total_consumption_tax,
        total_amount_including_tax: itemData?.total_amount_including_tax,
        total_r_amount: itemData?.total_r_amount,
        total_payment_amount: itemData?.total_payment_amount,
        total_item_quantity: itemData?.total_item_quantity,
        occupancy_rate: itemData?.occupancy_rate,
      });
    });
    const sumItemRate = data.items_ranking.reduce((acc, item) => acc + item.occupancy_rate, 0);
    const resultCheckOtherItem = checkHasOtherTypeRankingAmount(data.other_item);
    if (resultCheckOtherItem) {
      dataRender.push({
        index: 'by_BP_other',
        ranking: `その他\n(orther)`,
        business_partner_name: `その他\n(orther)`,
        total_amount_excluding_tax: data.other_item?.total_amount_excluding_tax,
        total_consumption_tax: data.other_item?.total_consumption_tax,
        total_amount_including_tax: data.other_item?.total_amount_including_tax,
        total_r_amount: data.other_item?.total_r_amount,
        total_payment_amount: data.other_item?.total_payment_amount,
        total_item_quantity: data.other_item?.total_item_quantity,
        occupancy_rate: 100 - (sumItemRate ?? 0),
      });
    }
    dataRender.push({
      index: 'by_BP_sum',
      business_partner_name: `合計`,
      total_amount_excluding_tax: data.total_items?.total_amount_excluding_tax,
      total_consumption_tax: data.total_items?.total_consumption_tax,
      total_amount_including_tax: data.total_items?.total_amount_including_tax,
      total_r_amount: data.total_items?.total_r_amount,
      total_payment_amount: data.total_items?.total_payment_amount,
      occupancy_rate: 100,
    });
    return dataRender;
  };

  const onFetchData = async (isExport?: boolean) => {
    setIsLoading(true);
    try {
      const reportType = parameter.report_type;
      if (!reportType) {
        setIsLoading(false);
        return;
      }
      const valueSearch: { [key: string]: any } = {
        start: parameter.startDate
          ? moment(parameter.startDate as string).format('YYYY-MM-DD')
          : moment(parameter?.endDate as string)
              .subtract(11, 'months')
              .startOf('month')
              .format('YYYY-MM-DD'),
        finish: parameter.endDate
          ? moment(parameter.endDate as string).format('YYYY-MM-DD')
          : undefined,
        report_type: reportType as string,
        bp_code_start: parameter.bp_code_start,
        bp_code_end: parameter.bp_code_end,
        type_date: parameter.type_date as string,
        type_ranking_amount:
          reportType === 'report_by_ranking_amount'
            ? parameter.type_ranking_amount
              ? (parameter.type_ranking_amount as string)
              : 'payment_amount'
            : undefined,
        limit: parameter.limit ? (parameter.limit as string) : 'all',
      };
      if (isExport) {
        valueSearch.is_export_excel = 1;
        const resExport = await getReportPurchase(valueSearch);
        window.open(resExport.data?.data?.file_url);
        setIsLoading(false);
        return;
      }
      setDataSource([]);
      const resGetList = await getReportPurchase(valueSearch);
      if (resGetList.status === STATUS_CODE.SUCCESSFUL) {
        let dataRender: any[] = [];
        if (reportType === 'report_travel_to_business_partner') {
          dataRender = formatDataTypeToTravel(
            resGetList.data?.data?.data as ResponseReportPurchaseTypeForTravel[],
          );
        } else if (reportType === 'report_by_ranking_amount') {
          dataRender = formatDataTypeByRanking(
            resGetList.data as unknown as ResponseReportPurchaseTypeByBP,
          );
        } else if (reportType === 'report_business_partner_to_travel') {
          dataRender = formatDataTypeToBusinessPartner(
            resGetList.data?.data?.data as ResponseReportPurchaseTypeForBP[],
          );
        }
        setDataSource(dataRender);
      } else {
        openNotificationFail({ message: MESSAGE_ALERT.FAILED_TO_GET_DATA });
      }
    } catch (error) {
      console.log('error', error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    // set Form Header
    if (formHeader) {
      setFirstCodeBusinessPartner(parameter.bp_code_start as string | undefined);
      setSecondCodeBusinessPartner(parameter.bp_code_end as string | undefined);
      formHeader.setFieldsValue({
        ...parameter,
        startDate: parameter.startDate ? moment(parameter.startDate as string) : undefined,
        endDate: parameter.endDate
          ? moment(parameter.endDate as string)
          : moment().subtract(1, 'months').endOf('month'),
        bp_code_start: parameter.bp_code_start,
        bp_code_end: parameter.bp_code_end,
        type_ranking_amount: parameter.type_ranking_amount
          ? (parameter.type_ranking_amount as string)
          : 'payment_amount',
        limit: parameter.limit ? (parameter.limit as string) : undefined,
        summary_item_master_id: parameter.summary_item_master_id
          ? Number(parameter.summary_item_master_id as string)
          : undefined,
      });
    }
    onFetchData();
  }, [parameter]);

  const onSubmit = async () => {
    const valueSubmit = await formHeader.validateFields();
    let startDate = valueSubmit.startDate && moment(valueSubmit.startDate).format('YYYY-MM-DD');
    let endDate = valueSubmit.endDate && moment(valueSubmit.endDate).format('YYYY-MM-DD');
    const reportType = valueSubmit.report_type;
    if (reportType === 'report_by_ranking_amount') {
      startDate =
        valueSubmit.startDate &&
        moment(valueSubmit.startDate).startOf('months').format('YYYY-MM-DD');
      endDate =
        valueSubmit.endDate && moment(valueSubmit.endDate).endOf('months').format('YYYY-MM-DD');
    }
    setParameter({
      ...valueSubmit,
      startDate,
      endDate,
      bp_code_start: firstCodeBusinessPartner,
      bp_code_end: secondCodeBusinessPartner,
    });
  };

  return (
    <PageContainer>
      <HeaderAction
        reportType={reportTypeForm}
        form={formHeader}
        onSubmit={onSubmit}
        optionsBusinessPartner={optionsBusinessPartner}
        firstCodeBusinessPartner={firstCodeBusinessPartner}
        secondCodeBusinessPartner={secondCodeBusinessPartner}
        setFirstCodeBusinessPartner={setFirstCodeBusinessPartner}
        setSecondCodeBusinessPartner={setSecondCodeBusinessPartner}
      />
      <div className="p-2 rounded-xl bg-white mt-6">
        {(!parameter.report_type ||
          parameter.report_type === 'report_travel_to_business_partner') && (
          <TableForTripID
            isLoading={isLoading}
            dataSource={dataSource}
            AmountExcludingTax={AmountExcludingTax}
            ConsumptionTax={ConsumptionTax}
            AmountIncludingTax={AmountIncludingTax}
            RAmount={RAmount}
            TotalPayment={TotalPayment}
          />
        )}
        {parameter.report_type === 'report_business_partner_to_travel' && (
          <TableForBusinessPartner
            isLoading={isLoading}
            dataSource={dataSource}
            AmountExcludingTax={AmountExcludingTax}
            ConsumptionTax={ConsumptionTax}
            AmountIncludingTax={AmountIncludingTax}
            RAmount={RAmount}
            TotalPayment={TotalPayment}
          />
        )}
        {parameter.report_type == 'report_by_ranking_amount' && (
          <TableByCustomer isLoading={isLoading} dataSource={dataSource} />
        )}
      </div>

      <div className="flex gap-6 justify-center mt-10">
        <BasicButton
          disabled={isLoading}
          styleType="accept"
          className={`!w-[190px] flex justify-center items-center ${
            isLoading ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
          }`}
          onClick={() => {
            onFetchData(true);
          }}
        >
          <DownloadOutlined style={{ color: 'white' }} />
          <p>{TEXT_ACTION.Report_output}</p>
        </BasicButton>
      </div>
    </PageContainer>
  );
};

export default InvoicesPage;
