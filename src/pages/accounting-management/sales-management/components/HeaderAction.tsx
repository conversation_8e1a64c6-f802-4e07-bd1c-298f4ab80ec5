import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import SelectIcon from '@/assets/imgs/common-icons/select-icon.svg';
import CloseSVG from '@/components/SVG/CloseSVG';
import BasicButton from '@/components/Commons/BasicButton';
import type { BasicFormModalRef } from '@/components/Commons/BasicFormModal';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import BasicInput from '@/components/Commons/BasicInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import FormSelectCompany from '@/components/Form/FormSelectCompany';
import SearchSVG from '@/components/SVG/SearchSVG';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import { DownloadOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import { Image } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { history } from 'umi';
import FormAccountingCollaboration from '@/components/Form/FormAccountingCollaboration';
import {
  listReportSaleInvoice,
  markReportedSaleInvoiceItem,
} from '@/apis/accounting/saleManagement';
import { invoiceIssueOptions } from '@/constants/data';
import STATUS_CODE from '@/constants/statusCode';
import { getDetailBusinessPartner, getListBusinessPartner } from '@/apis/businessPartner';
import type { ImportCsvCommonRef } from '@/components/CommonModalImportCsv';
import CommonModalImportCsv, { EMenuType } from '@/components/CommonModalImportCsv';
import { useDebounce } from '@uidotdev/usehooks';

const HeaderAction = ({ paramSearch, setParamSearch, tourIdSame, onFetchData }) => {
  const [currentCompany, setCurrentCompany] = useState<BusinessPartnerDetailType>();
  const [searchValue, setSearchValue] = useState<string>('');
  const [businessPartnerIdValue, setBusinessPartnerIdValue] = useState('');
  const refModalSelectCompany = useRef<BasicFormModalRef>();
  const refModalAccountingCollaboration = useRef<BasicFormModalRef>();
  const refImportCsv = useRef<ImportCsvCommonRef>(null);
  const businessPartnerCodeDebounce = useDebounce(businessPartnerIdValue, 300);

  useEffect(() => {
    if (currentCompany) {
      setBusinessPartnerIdValue(currentCompany?.business_partner_code);
    }
  }, [currentCompany]);

  useEffect(() => {
    setSearchValue(paramSearch?.keyword?.toString());
    setBusinessPartnerIdValue(paramSearch?.business_partner_code);
  }, [paramSearch]);

  const handleSearchByValue = () => {
    const paramToSearch = { ...paramSearch, page: 1, keyword: searchValue };
    setParamSearch({
      ...paramToSearch,
      keyword: searchValue,
      business_partner_id: currentCompany?.id,
      business_partner_code: businessPartnerIdValue,
    });
  };

  const onOpenModalSelectCompany = () => {
    refModalSelectCompany.current.open();
  };

  const onExportCsv = () => {
    refModalAccountingCollaboration.current.open();
  };

  const fetchDataDetail = async (id: number) => {
    try {
      const { data, status } = await getDetailBusinessPartner(id);
      const rsData = data.data;
      if (status === STATUS_CODE.SUCCESSFUL) {
        const dataRs = {
          ...rsData,
          qualified_invoice_issuer_number:
            rsData?.qualified_invoice_issuer_number?.toString() ?? '',
          r_rate: rsData?.r_rate?.toString() ?? '',
          target: JSON.parse(rsData?.target as string) ?? undefined,
        };

        setCurrentCompany(dataRs as any);
      }
    } catch (error) {
      console.log('error', error);
    }
  };

  useEffect(() => {
    if (!Number.isNaN(Number(paramSearch?.business_partner_id))) {
      fetchDataDetail(Number(paramSearch?.business_partner_id));
    }
  }, []);

  const onImportCsv = () => {
    if (refImportCsv) {
      refImportCsv.current.open();
    }
  };

  const fetchDataCompany = async () => {
    const resData = await getListBusinessPartner({
      page: 1,
      limit: 1,
      business_partner_code: businessPartnerCodeDebounce,
    });

    if (resData?.data?.data) {
      setCurrentCompany(resData.data.data[0]);
      setParamSearch({
        ...paramSearch,
        page: 1,
        business_partner_id: resData.data.data[0]?.id,
        business_partner_code: resData.data.data[0]?.business_partner_code,
      });
    } else {
      setCurrentCompany(undefined);
    }
  };

  useEffect(() => {
    if (businessPartnerCodeDebounce?.length > 2) {
      fetchDataCompany();
    }
  }, [businessPartnerCodeDebounce]);

  return (
    <>
      <div className="flex items-end justify-between gap-4 flex-wrap">
        <div className="flex flex-wrap gap-4 ">
          <BasicInput
            style={{
              width: '140px',
              height: '40px',
            }}
            value={businessPartnerIdValue}
            title={`得意先ID`}
            placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
            onChange={(val) => {
              setBusinessPartnerIdValue(val.target.value);
            }}
          />

          <div className="">
            <p className="mb-2 text-[13px] leading-4 font-medium">{TEXT_TITLE.Customer}</p>
            <BasicButton
              className="!border-[#EBE9FA] !w-[230px] flex justify-between !px-[11px] items-center !text-[rgba(0,0,0,0.3)] !bg-white hover:opacity:40"
              onClick={() => {
                if (currentCompany?.business_partner_name) {
                  setCurrentCompany(undefined);
                  setParamSearch({
                    ...paramSearch,
                    page: 1,
                    business_partner_id: undefined,
                    business_partner_code: undefined,
                  });
                  return;
                }
                onOpenModalSelectCompany();
              }}
            >
              <span className="truncate">{currentCompany?.business_partner_name ?? 'すべて'}</span>
              {currentCompany?.business_partner_name ? (
                <CloseSVG width="18" height="18" />
              ) : (
                <Image src={SelectIcon} alt="select icon" className="w-5 h-5" preview={false} />
              )}
            </BasicButton>
          </div>

          <BasicInput
            style={{
              width: '280px',
              height: '40px',
            }}
            value={searchValue}
            title={`${TEXT_TITLE.Sales_slip_ID}, ${TEXT_TITLE.Trip_ID_or_Tour_Name}`}
            placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
            onChange={(val) => {
              setSearchValue(val.target.value);
            }}
          />
          <BasicSelect
            title={TEXT_TITLE.Invoicing}
            className="w-[140px]"
            allowClear
            placeholder={TEXT_PLACEHOLDER.All}
            defaultValue={
              !Number.isNaN(Number(paramSearch?.invoice_issued))
                ? Number(paramSearch?.invoice_issued)
                : undefined
            }
            options={invoiceIssueOptions}
            onChange={(value) => {
              setParamSearch({
                ...paramSearch,
                invoice_issued: value,
                page: 1,
              });
            }}
          />

          <div className="flex items-end">
            <BasicButton
              icon={<SearchSVG colorSvg="white" />}
              className="flex items-center w-[120px]"
              styleType="accept"
              onClick={handleSearchByValue}
            >
              <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
            </BasicButton>
          </div>
        </div>

        <div className="flex gap-x-[12px]">
          <BasicButton
            icon={<UploadOutlined style={{ color: '#EC980C' }} />}
            className="flex items-center !text-[#EC980C]"
            styleType="noneOutLine"
            onClick={onImportCsv}
          >
            {TEXT_ACTION.CSV_Import}
          </BasicButton>
          <BasicButton
            icon={<DownloadOutlined style={{ color: '#3997C8' }} />}
            className="flex items-center "
            styleType="outline"
            onClick={onExportCsv}
          >
            会計連携
          </BasicButton>
          <BasicButton
            icon={<PlusOutlined />}
            className="flex items-center"
            styleType="accept"
            onClick={() => {
              if (tourIdSame) {
                history.push(
                  `/accounting-management/sales-management/invoices?tourId=${tourIdSame}`,
                );
                return;
              }
              history.push('/accounting-management/sales-management/invoices');
            }}
          >
            {TEXT_ACTION.CREATE_NEW}
          </BasicButton>
        </div>
      </div>
      <BasicFormModal
        ref={refModalSelectCompany}
        content={
          <FormSelectCompany
            onSelect={(v) => {
              setCurrentCompany(v);
              setParamSearch({
                ...paramSearch,
                page: 1,
                business_partner_id: v?.id,
                business_partner_code: v?.business_partner_code,
              });
            }}
            onClose={() => refModalSelectCompany.current.close()}
          />
        }
        className="!w-[1200px] [&_.ant-modal-body]:!px-2"
        title="取引先名"
        isValidate={true}
        hideListButton={true}
      />
      <BasicFormModal
        ref={refModalAccountingCollaboration}
        content={
          <FormAccountingCollaboration
            onReloadData={onFetchData}
            onClose={() => refModalAccountingCollaboration.current.close()}
            apiGetList={listReportSaleInvoice}
            apiMarkInvoiceItem={markReportedSaleInvoiceItem}
            type="sale"
          />
        }
        className="!w-[1400px] [&_.ant-modal-body]:!px-2"
        title="会計連携"
        isValidate={true}
        hideListButton={true}
      />
      <CommonModalImportCsv
        confirm={onFetchData}
        ref={refImportCsv}
        type={EMenuType['sale-invoice']}
      />
    </>
  );
};

export default HeaderAction;
