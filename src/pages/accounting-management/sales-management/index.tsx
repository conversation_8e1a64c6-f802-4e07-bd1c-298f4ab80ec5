import { getListSaleInvoice } from '@/apis/accounting/saleManagement';
import PageContainer from '@/components/Commons/Page/Container';
import STATUS_CODE from '@/constants/statusCode';
import { ITEM_PER_PAGE } from '@/utils/constants';
import { Form } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { useUrlSearchParams } from 'use-url-search-params';
import HeaderAction from './components/HeaderAction';
import TableTax from './components/TableList';

const initSearchParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};

const SaleManagement = () => {
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState([]);
  const [listItemChange, setListItemChange] = useState<string[]>([]);
  const [total, setTotal] = useState(0);

  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);

  const onFetchData = async () => {
    setIsLoading(true);
    try {
      const payload: any = {
        ...parameter,
        keyword: parameter?.tourName?.toString() ?? parameter?.keyword?.toString(),
        sort: 'id',
        order: 'desc',
      };

      delete payload?.tourName;
      delete payload?.tourId;

      const resGetListSaleInvoice = await getListSaleInvoice(payload);
      if (resGetListSaleInvoice.status === STATUS_CODE.SUCCESSFUL) {
        const resData = resGetListSaleInvoice.data;
        setDataSource(
          resData.data.map((item, index) => ({
            ...item,
            travel_id: item?.travel?.travel_id,
            invoice_id: item?.sale_invoice_id,
            key: (Number(parameter?.page) - 1) * Number(parameter?.limit) + index + 1,
            business_partner_name: item?.business_partner?.business_partner_name,
            business_partner_code: item?.business_partner?.business_partner_code,
            tour_name: item?.travel?.tour_name,
          })),
        );
        setTotal(resData.total);
      }
    } catch (error) {
      console.log('error', error);
    }
    setIsLoading(false);
  };

  const tourIdSame: number | undefined = useMemo(() => {
    // if (dataSource.length > 0) {
    //   const tourId = dataSource[0]?.travel?.id;
    //   const isSame = dataSource.every((item) => item?.travel?.id === tourId);
    //   if (isSame) return tourId;
    // }
    return undefined;
  }, [dataSource]);

  useEffect(() => {
    onFetchData();
  }, [
    parameter?.keyword,
    parameter?.limit,
    parameter?.page,
    parameter?.tourId,
    parameter?.business_partner_id,
    parameter?.invoice_issued,
    parameter?.accounting_linked,
    parameter?.business_partner_code,
  ]);

  useEffect(() => {
    dataSource.forEach((item) => {
      Object.keys(item).forEach((key) => {
        const keyForm = `${item.id}.${key}`;
        form.setFieldValue(keyForm, item[key]);
      });
    });
  }, [dataSource]);

  return (
    <PageContainer>
      <HeaderAction
        tourIdSame={tourIdSame}
        onFetchData={onFetchData}
        paramSearch={parameter}
        setParamSearch={setParameter}
      />
      <div className="p-2 rounded-xl bg-white mt-6">
        <TableTax
          total={total}
          isLoading={isLoading}
          form={form}
          dataSource={dataSource}
          listItemChange={listItemChange}
          setListItemChange={setListItemChange}
          paramSearch={parameter}
          setParamSearch={setParameter}
        />
      </div>
    </PageContainer>
  );
};

export default SaleManagement;
