import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import type { SaleInvoiceItemType, SaleInvoiceType } from '@/apis/accounting/saleManagement';
import {
  createSaleInvoiceItem,
  deleteSaleInvoiceItems,
  getDetailSaleInvoice,
  updateSaleInvoice,
} from '@/apis/accounting/saleManagement';
import {
  getAccountingTravelDetail,
  type AccountingTravelListType,
} from '@/apis/accounting/traveList';
import type { AggregationItemDetailType } from '@/apis/master/AggregationItem';
import { getListAggregationItem } from '@/apis/master/AggregationItem';
import type { SubjectMasterDetailType } from '@/apis/master/subjectMaster';
import { getListSubjectMaster } from '@/apis/master/subjectMaster';
import type { DatumGetListTaxCategory } from '@/apis/master/taxCategory';
import { getListTaxCategory } from '@/apis/master/taxCategory';
import IconBack from '@/assets/imgs/common-icons/back-btn.svg';
import IconSave from '@/assets/imgs/common-icons/save-white-btn.svg';
import BasicButton from '@/components/Commons/BasicButton';
import type { BasicFormModalRef } from '@/components/Commons/BasicFormModal';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import BasicModal from '@/components/Commons/BasicModal';
import PageContainer from '@/components/Commons/Page/Container';
import FormSelectCompany from '@/components/Form/FormSelectCompany';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import STATUS_CODE from '@/constants/statusCode';
import { Form, Image, notification, Space } from 'antd';
import type { Moment } from 'moment';
import { useLocation } from 'react-router';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { history, useParams } from 'umi';
import FormSelectTour from '../../../../components/Form/FormSelectTour';
import HeaderAction from './components/HeaderAction';
import HeaderInformation from './components/HeaderInformation';
import TableTax from './components/TableTax';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import { formatInvoiceNumber, roundNumber } from '@/utils';
import { optionTaxInclusionType } from '@/constants/data';

interface DetailRowItemSaleInvoiceType {
  id?: number;
  key: number;
  subject_id: string | number;
  summary_subject_name: string;
  product_name: string;
  product_name_en: string;
  unit_price: number;
  quantity: number;
  tax_inclusion_type: number;
  tax_category_code: number;
  tax_rate: number;
  amount_excluding_tax?: number;
  amount_including_tax?: number;
  consumption_tax: number;
  invoice_number?: string;
  accounting_linked?: number;
  memo: string;
  status?: string;
  isDelete?: boolean;
  voucher_posting_date?: string | Date;
  sale_invoice_id?: number;
  status_cancel?: number | null;
  status_update?: number | null;
  created_by?: number | null;
  updated_by?: number | null;
  deleted_by?: number | null;
  created_at?: string | Date | null;
  updated_at?: string | Date | null;
  deleted_at?: string | Date | null;
  index_column?: number | null;
  isDisableForm?: boolean;
  line_change?: number;
  use_at?: Moment;
  sale_date?: Moment;
  reverse_sale_date?: string;
  isRemoveToSubmitForm?: boolean;
}

const InvoicesPage = () => {
  const isEditPage = true;
  const [form] = Form.useForm();
  const refModalSelectTour = useRef<BasicFormModalRef>();
  // const refModalConfirmChangePage = useRef(null);
  const refModalConfirmSaveData = useRef(null);
  const refModalSelectCompany = useRef(null);
  const refModalConfirmChangeTour = useRef(null);
  const refModalDataChangedBeforeUpdate = useRef(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [currentTour, setCurrentTour] = useState<AccountingTravelListType>();
  const [currentCompany, setCurrentCompany] = useState<BusinessPartnerDetailType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<DetailRowItemSaleInvoiceType[]>([]);
  const [listItemChange, setListItemChange] = useState<string[]>([]);
  const [listItemCreate, setListItemCreate] = useState<string[]>([]);
  const [listItemBlock, setListItemBlock] = useState<number[]>([]);
  const [listItemDelete, setListItemDelete] = useState<number[]>([]);
  const [countItemDeleted, setCountItemDeleted] = useState<number>(0);
  const [invoiceIssue, setInvoiceIssue] = useState<Moment>();
  const [detailDataSaleInvoice, setDetailDataSaleInvoice] = useState<SaleInvoiceType>();
  const [sumAmountExcludingTax, setSumAmountExcludingTax] = useState<number>(0);
  const [sumAmountConsumptionTax, setSumAmountConsumptionTax] = useState<number>(0);
  const [sumAmountIncludingTax, setSumAmountIncludingTax] = useState<number>(0);
  const [listItemAccountingLinked, setListItemAccountingLinked] = useState<number[]>([]);
  const [api, contextHolder] = notification.useNotification();
  const paramUrl = useParams();
  const { query } = useLocation() as any;
  const saleInvoiceId = (paramUrl as { id: string })?.id;

  const [optionsTaxCategory, setOptionsTaxCategory] = useState<{ value: any; label: string }[]>([]);
  const [dataTaxCategory, setDataTaxCategory] = useState<DatumGetListTaxCategory[]>();
  const [optionsAccountCodeList, setOptionsAccountCodeList] = useState<
    { value: any; label: string }[]
  >([]);
  const [dataAccountCodeList, setDataAccountCodeList] = useState<SubjectMasterDetailType[]>();
  const [dataSummarySubject, setDataSummarySubject] = useState<AggregationItemDetailType[]>();

  const handleGetTaxCategoryMaster = async () => {
    const res = await getListTaxCategory({ limit: 'all', order: 'asc', sort: 'tax_category_code' });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setDataTaxCategory(res?.data?.data);
      const options = res?.data?.data?.map((item) => {
        return {
          value: item.id,
          label: item.tax_category_abbreviation,
          disabled: item.status === 0 || !item.target?.includes?.('1'),
        };
      });
      setOptionsTaxCategory(options);
    }
  };

  const handleGetListAccountMaster = async () => {
    const res = await getListSubjectMaster({ limit: 'all', order: 'asc', sort: 'id' });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setDataAccountCodeList(res?.data?.data);
      const options = res?.data?.data?.map((item) => {
        return {
          value: item.id,
          label: item.subject_name,
          disabled: item.status === 0 || !item.target?.includes?.('1'),
        };
      });
      setOptionsAccountCodeList(options);
    }
  };
  const handleGetListSummarySubject = async () => {
    const res = await getListAggregationItem({ limit: 'all' });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setDataSummarySubject(res?.data?.data);
    }
  };

  //effect get list
  useEffect(() => {
    handleGetTaxCategoryMaster();
    handleGetListAccountMaster();
    handleGetListSummarySubject();
  }, []);

  //effect handle warning change page
  useEffect(() => {
    const unblock =
      listItemChange.length > 0
        ? history.block((a, b) => {
            if (
              !saleInvoiceId &&
              a.pathname.includes('accounting-management/sales-management/invoices') &&
              b === 'REPLACE'
            ) {
            } else {
              return TEXT_WARNING.leave_page_and_lose_changes;
            }
          })
        : null;

    return () => {
      if (unblock) {
        unblock?.();
      }
    };
  }, [listItemChange, history]);

  const isEditSaleInvoice = useMemo(() => {
    if (saleInvoiceId) return true;
    setIsLoading(false);
    return false;
  }, [saleInvoiceId]);

  const formatDataSaleInvoiceItems = async (sale_invoice_items: SaleInvoiceItemType[]) => {
    if (sale_invoice_items) {
      let amountExcludingTax: number = 0;
      let consumptionTax: number = 0;
      let amountIncludingTax: number = 0;
      const listBlockNew = [];
      const listItemAccountingLinkedNew = [];

      const dataSourceApi = sale_invoice_items?.map((item, index) => {
        const subjectMaster = dataAccountCodeList.find(
          (account) => account?.id === Number(item?.subject_id),
        );
        const summaryItem = dataSummarySubject.find(
          (summary) => summary.id === Number(subjectMaster?.summary_item_id),
        );

        amountExcludingTax += roundNumber(item?.amount_excluding_tax);
        consumptionTax += roundNumber(item?.consumption_tax);
        amountIncludingTax += roundNumber(item?.amount_including_tax);
        if (item.status_cancel !== 0 || item.status_update !== 0) {
          listBlockNew.push(index + 1);
        }
        if (item.accounting_linked === 1) {
          listItemAccountingLinkedNew.push(item.id);
        }
        return {
          id: item.id,
          key: index + 1,
          accounting_linked: item.accounting_linked,
          subject_id: item?.subject_id ? Number(item?.subject_id) : undefined,
          tax_inclusion_type: Number(item?.tax_inclusion_type) ?? 1,
          summary_subject_name: summaryItem?.summary_item_name,
          product_name: item?.product_name,
          product_name_en: item?.product_name_en,
          unit_price: item?.unit_price,
          quantity: item?.quantity,
          tax_rate: item?.tax_rate,
          tax_category_code: item?.tax_category_code,
          memo: item?.memo,
          amount_excluding_tax: item?.amount_excluding_tax,
          consumption_tax: item?.consumption_tax,
          amount_including_tax: item?.amount_including_tax,
          isDelete: item.status_cancel !== 0 || item.status_update !== 0,
          isDisableForm: item.accounting_linked === 1,
          use_at: item?.use_at ? moment(item?.use_at) : moment(currentTour?.return_date),
          sale_date: item?.sale_date ? moment(item?.sale_date) : moment(currentTour?.return_date),
          sale_invoice_item_id: item?.sale_invoice_item_id,
          consolidated_invoice_id: item?.consolidated_invoice_id,
          consolidated_invoices_is_disable: item?.consolidated_invoices_is_disable,
        };
      });

      setListItemBlock(listBlockNew);
      setListItemAccountingLinked(listItemAccountingLinkedNew);
      setDataSource([...dataSourceApi]);
      setSumAmountExcludingTax(amountExcludingTax);
      setSumAmountConsumptionTax(consumptionTax);
      setSumAmountIncludingTax(amountIncludingTax);

      dataSourceApi.forEach((item) => {
        Object.keys(item).forEach((key) => {
          const keyForm = `${item.key}.${key}`;
          form.setFieldValue(keyForm, item[key]);
        });
      });
    }
  };

  const getSaleInvoice = async (id) => {
    setIsLoading(true);
    form.resetFields();
    // Reset
    setListItemChange([]);
    setListItemCreate([]);
    const resGetDetailSaleInvoice = await getDetailSaleInvoice(id);
    if (resGetDetailSaleInvoice.status === STATUS_CODE.SUCCESSFUL) {
      setCurrentTour(resGetDetailSaleInvoice.data.data?.travel);
      setCurrentCompany(resGetDetailSaleInvoice.data.data?.business_partner);
      formatDataSaleInvoiceItems(resGetDetailSaleInvoice.data.data?.sale_invoice_item);
      if (resGetDetailSaleInvoice?.data?.data) {
        setDetailDataSaleInvoice(resGetDetailSaleInvoice?.data?.data);
        setInvoiceIssue(moment(resGetDetailSaleInvoice?.data?.data?.voucher_posting_date));
      }
    } else {
      openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
    }
    setIsLoading(false);
  };

  const fetchAccountingTravelDetail = async (id) => {
    const resTravelDetail = await getAccountingTravelDetail(id);
    if (resTravelDetail.status === STATUS_CODE.SUCCESSFUL) {
      setCurrentTour(resTravelDetail?.data?.data);
    }
  };

  useEffect(() => {
    if (currentTour?.return_date && !isEditSaleInvoice) {
      setInvoiceIssue(moment(currentTour?.return_date));
    }
  }, [currentTour, isEditSaleInvoice]);

  useEffect(() => {
    if (saleInvoiceId && dataAccountCodeList && dataTaxCategory && dataSummarySubject) {
      getSaleInvoice(saleInvoiceId);
    }
  }, [saleInvoiceId, dataAccountCodeList, dataTaxCategory, dataSummarySubject, query]);

  useEffect(() => {
    if (!saleInvoiceId && query.tourId) {
      fetchAccountingTravelDetail(query.tourId);
    }
  }, [saleInvoiceId, query.tourId]);

  const itemCreateNewInListItemSelected = () => {
    if (selectedRowKeys.length > 0 && listItemCreate.length > 0) {
      return selectedRowKeys.some((item) => listItemCreate.includes(item.toString()));
    }
    return false;
  };

  const removeRowFormData = (key) => {
    form.setFieldsValue({
      [`${key}.subject_id`]: undefined,
      [`${key}.summary_subject_name`]: undefined,
      [`${key}.product_name`]: undefined,
      [`${key}.product_name_en`]: undefined,
      [`${key}.unit_price`]: undefined,
      [`${key}.quantity`]: undefined,
      [`${key}.tax_inclusion_type`]: undefined,
      [`${key}.tax_category_code`]: undefined,
      [`${key}.tax_rate`]: undefined,
      [`${key}.amount_excluding_tax`]: undefined,
      [`${key}.consumption_tax`]: undefined,
      [`${key}.accounting_linked`]: undefined,
      [`${key}.invoice_number`]: undefined,
      [`${key}.memo`]: undefined,
      [`${key}.status`]: undefined,
    });
  };

  const checkWhenReverse = () => {
    const newListIdToReverse = [...dataSource]
      .filter((item) => selectedRowKeys.includes(item.key))
      .map((itemMap) => itemMap.id);
    if (newListIdToReverse.every((item) => !listItemAccountingLinked.includes(item))) {
      window.alert('会計未連携は削除のみで修正と取消の操作できません。\n再度ご確認ください。');
      return true;
    }
    return false;
  };

  const checkWhenUpdateOrDelete = () => {
    const newListIdToCheck = [...dataSource]
      .filter((item) => {
        return (
          selectedRowKeys.includes(item.key) &&
          !listItemCreate.includes(item.key.toString()) &&
          item.id
        );
      })
      .map((itemMap) => itemMap.id);
    if (newListIdToCheck.some((item) => listItemAccountingLinked.includes(item))) {
      return false;
    }
    return newListIdToCheck;
  };

  const confirmDeleteRow = () => {
    const listDelete = checkWhenUpdateOrDelete();
    if (listDelete === false) {
      window.alert('会計連携済の記録があるため、削除できません。\n再度ご確認ください。');
      return;
    }
    setIsLoading(true);
    setDataSource([...dataSource].filter((item) => !selectedRowKeys.includes(item.key)));
    selectedRowKeys.forEach((key) => {
      removeRowFormData(key);
    });
    setListItemBlock(listItemBlock.filter((item) => !selectedRowKeys.includes(item)));
    setListItemCreate(listItemCreate.filter((item) => !selectedRowKeys.includes(item)));
    setListItemChange(listItemChange.filter((item) => !selectedRowKeys.includes(item)));
    setListItemDelete([...listItemDelete, ...listDelete]);
    setCountItemDeleted(countItemDeleted + selectedRowKeys.length);
    setSelectedRowKeys([]);

    setIsLoading(false);
  };

  const onSave = async () => {
    setIsLoading(true);
    try {
      const valueBeforeValidate = form.getFieldsValue();
      const values = { ...valueBeforeValidate };
      await form.validateFields();
      if (dataSource.length === 0) {
        openNotificationFail('アイテムは作成されていません');
        setIsLoading(false);
        return;
      }

      // Start Update
      if (listItemChange.length > 0) {
        const resultObj = Object.keys(values).reduce((acc, key) => {
          const [index, property] = key.split('.');
          const value = values[key];

          if (!acc[index]) {
            acc[index] = {};
          }

          acc[index][property] = value;

          return acc;
        }, {});

        const newData = [...dataSource];

        Object.keys(resultObj).forEach((key) => {
          const index = dataSource.findIndex((item) => item.key === Number(key));
          newData[index] = { ...dataSource[index], ...resultObj[key] };
          const dataSourceIndex = { ...dataSource[index] };
          if (dataSourceIndex.status === 'reverse') {
            const indexItemCancel = dataSource.findIndex(
              (item) => item.id === dataSourceIndex.id && item.isDelete,
            );
            const indexItemUpdate = dataSource.findIndex(
              (item) =>
                item.id === dataSourceIndex.id && item.status === 'update' && !item.isDelete,
            );
            if (indexItemUpdate >= 0) {
              newData[indexItemUpdate].reverse_sale_date = form
                .getFieldValue(`${key}.sale_date`)
                ?.format('YYYY-MM-DD');
            } else if (indexItemCancel >= 0) {
              newData[indexItemCancel].sale_date = form
                .getFieldValue(`${key}.sale_date`)
                ?.format('YYYY-MM-DD');
            }
          }
        });

        // Filter data change
        const dataChange = newData
          .filter((item) => listItemChange.includes(item.key.toString()))
          .filter((item) => item.status !== 'reverse' && !item.isRemoveToSubmitForm === true)
          .map((item) => {
            if (!item.status) {
              item.status = 'edit';
            }
            delete item.sale_invoice_id;
            delete item.status_cancel;
            delete item.status_update;
            delete item.created_by;
            delete item.updated_by;
            delete item.deleted_by;
            delete item.created_at;
            delete item.updated_at;
            delete item.deleted_at;
            delete item.index_column;
            return {
              ...item,
              use_at: moment(item.use_at).format('YYYY-MM-DD'),
              sale_date: moment(item.sale_date).format('YYYY-MM-DD'),
            };
          });

        const responseUpdate = await createSaleInvoiceItem({
          data: {
            sale_invoice: {
              travel_id: currentTour?.id,
              business_partner_id: currentCompany?.id,
              id: saleInvoiceId ? Number(saleInvoiceId) : undefined,
              voucher_posting_date: invoiceIssue.format('YYYY-MM-DD'),
            },
            sale_invoice_item: dataChange,
          },
        });
        if (
          responseUpdate.status === STATUS_CODE.SUCCESSFUL ||
          responseUpdate.status === STATUS_CODE.CREATED
        ) {
          openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
          // Reset
          setListItemChange([]);
          setListItemCreate([]);
          setIsLoading(false);

          if (!saleInvoiceId) {
            history.replace(
              `/accounting-management/sales-management/invoices/${responseUpdate.data.data}`,
            );
          }
        } else if (responseUpdate.status === STATUS_CODE.CONFLICT) {
          refModalDataChangedBeforeUpdate.current.open();
        } else {
          openNotificationFail(
            responseUpdate?.error?.data?.errors?.message || MESSAGE_ALERT.EDIT_FAILED,
          );
        }
      } else if (
        saleInvoiceId &&
        invoiceIssue.format('YYYYMMDD') !=
          moment(detailDataSaleInvoice.voucher_posting_date).format('YYYYMMDD')
      ) {
        const resUpdateSaleInvoice = await updateSaleInvoice({
          id: saleInvoiceId,
          voucher_posting_date: invoiceIssue.format('YYYY-MM-DD'),
        });

        if (
          resUpdateSaleInvoice.status === STATUS_CODE.SUCCESSFUL ||
          resUpdateSaleInvoice.status === STATUS_CODE.CREATED
        ) {
          setListItemChange([]);
          openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        } else {
          openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
        }
      } else if (listItemDelete.length > 0) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
      }
      // End update

      // Start delete
      if (listItemDelete.length > 0) {
        const responseDelete = await deleteSaleInvoiceItems({ id: [...listItemDelete] });
        if (responseDelete.status === STATUS_CODE.SUCCESSFUL) {
          setListItemDelete([]);
          setCountItemDeleted(0);
        } else if (responseDelete.status === STATUS_CODE.CONFLICT) {
          refModalDataChangedBeforeUpdate.current.open();
          return;
        } else {
          setIsLoading(false);
          openNotificationFail(
            responseDelete.error?.data?.errors?.message ||
              responseDelete.error?.data?.message ||
              MESSAGE_ALERT.DELETE_FAILED,
          );
          return;
        }
      }
      // End delete

      // Call api reload data
      if (saleInvoiceId) {
        getSaleInvoice(saleInvoiceId);
      }
    } catch (errInfo) {
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      setIsLoading(false);
      console.log('Save failed:', errInfo);
    }
    setIsLoading(false);
  };

  const handleCheckBeforeSave = async () => {
    try {
      await form.validateFields();
      if (detailDataSaleInvoice?.total_sale && detailDataSaleInvoice?.total_sale > 0) {
        const key = `open${Date.now()}`;
        const btn = (
          <Space>
            <BasicButton
              onClick={() => {
                notification.close(key);
              }}
              styleType={'back'}
              className={`w-[164px] flex items-center space-x-[8px] justify-center`}
            >
              {TEXT_ACTION.NO}
            </BasicButton>
            <BasicButton
              onClick={() => {
                notification.close(key);
                onSave();
              }}
              styleType="accept"
              className="w-[164px] flex items-center space-x-[8px] justify-center"
            >
              {TEXT_ACTION.YES}
            </BasicButton>
          </Space>
        );
        if (sumAmountIncludingTax > detailDataSaleInvoice?.total_amount_received) {
          notification.open({
            message: '売上増加',
            description: '入金処理済みの伝票ですが、本当に修正してもいいですか？',
            className: 'notification-confirm-center',
            btn,
            key,
            duration: 0,
            placement: 'top',
          });
        } else if (
          sumAmountIncludingTax < detailDataSaleInvoice?.total_amount_received &&
          sumAmountIncludingTax >= detailDataSaleInvoice?.total_sale
        ) {
          notification.open({
            message: '売上の減少',
            description: '入金処理済みの伝票ですが、本当に修正してもいいですか？',
            className: 'notification-confirm-center',
            btn,
            key,
            duration: 0,
            placement: 'top',
          });
        } else if (sumAmountIncludingTax < detailDataSaleInvoice?.total_sale) {
          openNotificationFail('入金済みの金額を下回っています。入金伝票から修正してください');
        } else {
          onSave();
        }
      } else {
        onSave();
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      console.log('error', error);
    }
  };

  const onCreateItem = () => {
    if (!currentCompany || !currentTour) {
      window.alert(MESSAGE_ALERT.CREATE_INVOICE_ITEM_NO_HAVE_TOUR_AND_COMPANY);
      return;
    }
    const lastIndex = dataSource.length + 1 + countItemDeleted;
    try {
      const newDataSource = [...dataSource];
      newDataSource.push({
        key: lastIndex,
        subject_id: undefined,
        summary_subject_name: undefined,
        product_name: undefined,
        product_name_en: undefined,
        unit_price: undefined,
        quantity: undefined,
        tax_inclusion_type: 1,
        tax_category_code: undefined,
        tax_rate: undefined,
        amount_excluding_tax: undefined,
        consumption_tax: undefined,
        invoice_number: moment().format('YYMM') + formatInvoiceNumber(lastIndex),
        accounting_linked: 0,
        memo: undefined,
        status: 'create',
        use_at: moment(currentTour?.return_date),
        sale_date: moment(currentTour?.return_date),
      });
      form.setFieldValue(`${lastIndex}.tax_inclusion_type`, 1);
      if (currentTour?.return_date) {
        form.setFieldValue(`${lastIndex}.use_at`, moment(currentTour?.return_date));
        form.setFieldValue(`${lastIndex}.sale_date`, moment(currentTour?.return_date));
      }
      setListItemCreate([...listItemCreate, lastIndex.toString()]);
      setListItemChange([...listItemChange, lastIndex.toString()]);
      setDataSource([...newDataSource]);
    } catch (error) {
      console.log('onCreateItem error', error);
    }
  };

  const onHandleRevert = () => {
    try {
      const isCheckingReverse = checkWhenReverse();
      if (isCheckingReverse) {
        return;
      }
      const isChecking = itemCreateNewInListItemSelected();
      if (isChecking) {
        refModalConfirmSaveData.current.open();
        return;
      }
      const newDataSource = [...dataSource];
      const listIndexItemChange = [...selectedRowKeys].map((item) =>
        newDataSource.findIndex((data) => data.key === item),
      );
      const listBlockNew = [...listItemBlock];
      const listItemChangeNew = [...listItemChange];
      listIndexItemChange.forEach((indexItemChange, index) => {
        newDataSource[indexItemChange] = {
          ...dataSource[indexItemChange],
          status: 'cancel',
          line_change: dataSource[indexItemChange].key,
          isDelete: true,
        };
        listItemChangeNew.push((indexItemChange + 1).toString());

        listBlockNew.push(indexItemChange + 1);

        Object.keys(newDataSource[indexItemChange]).forEach((key) => {
          const keyForm = `${newDataSource[indexItemChange].key}.${key}`;
          form.setFieldValue(keyForm, newDataSource[indexItemChange][key]);
        });
        //reverse item
        const amount_excluding_tax = Number(dataSource[indexItemChange]?.amount_excluding_tax) * -1;
        const consumption_tax = Number(dataSource[indexItemChange]?.consumption_tax) * -1;
        const amount_including_tax = Number(dataSource[indexItemChange]?.amount_including_tax) * -1;
        const lastIndex = dataSource.length + 1 + countItemDeleted;
        newDataSource.push({
          ...dataSource[indexItemChange],
          isDisableForm: true,
          isRemoveToSubmitForm: true,
          status: 'reverse',
          key: lastIndex + index * 1,
          quantity: dataSource[indexItemChange].quantity * -1,
          memo: dataSource[indexItemChange].key + '行目の取消',
          amount_excluding_tax,
          consumption_tax,
          amount_including_tax,
          accounting_linked: 0,
        });
        Object.keys(newDataSource[newDataSource.length - 1]).forEach((key) => {
          const keyForm = `${newDataSource[newDataSource.length - 1].key}.${key}`;
          form.setFieldValue(keyForm, newDataSource[newDataSource.length - 1][key]);
        });
        listBlockNew.push(lastIndex + index * 1);
      });
      setListItemChange([...listItemChangeNew]);
      setListItemBlock([...listBlockNew]);
      setSelectedRowKeys([]);
      setDataSource([...newDataSource]);
    } catch (error) {
      console.log('onHandleRevert error', error);
    }
  };

  const onHandleModify = () => {
    try {
      const isCheckingReverse = checkWhenReverse();
      if (isCheckingReverse) {
        return;
      }
      const isChecking = itemCreateNewInListItemSelected();
      if (isChecking) {
        refModalConfirmSaveData.current.open();
        return;
      }
      const newDataSource = [...dataSource];
      const listIndexItemChange = [...selectedRowKeys].map((item) =>
        newDataSource.findIndex((data) => data.key === item),
      );
      const listBlockNew = [...listItemBlock];
      const listItemChangeNew = [...listItemChange];
      const listItemCreateNew = [...listItemCreate];

      listIndexItemChange.forEach((indexItemChange, index) => {
        newDataSource[indexItemChange] = {
          ...dataSource[indexItemChange],
          isRemoveToSubmitForm: true,
          isDelete: true,
        };

        listItemChangeNew.push((indexItemChange + 1).toString());
        listBlockNew.push(dataSource[indexItemChange].key);

        const lastIndex = dataSource.length + 1 + countItemDeleted;
        newDataSource.push({
          ...dataSource[indexItemChange],
          isDelete: true,
          key: lastIndex + index * 2,
          status: 'reverse',
          quantity: dataSource[indexItemChange].quantity * -1,
          memo: dataSource[indexItemChange].key + '行目の修正',
          amount_excluding_tax: dataSource[indexItemChange].amount_excluding_tax * -1,
          consumption_tax: dataSource[indexItemChange].consumption_tax * -1,
          amount_including_tax: dataSource[indexItemChange].amount_including_tax * -1,
          accounting_linked: 0,
        });
        listBlockNew.push(lastIndex + index * 2);
        // new value reverse
        Object.keys(newDataSource[newDataSource.length - 1]).forEach((key) => {
          const keyForm = `${newDataSource[newDataSource.length - 1].key}.${key}`;
          form.setFieldValue(keyForm, newDataSource[newDataSource.length - 1][key]);
        });
        newDataSource.push({
          ...dataSource[indexItemChange],
          key: lastIndex + 1 + index * 2,
          memo: dataSource[indexItemChange].key + '行目の修正',
          isDisableForm: false,
          status: 'update',
          line_change: dataSource[indexItemChange].key,
          accounting_linked: 0,
        });
        listItemCreateNew.push((lastIndex + 1 + index * 2).toString());
        listItemChangeNew.push((lastIndex + 1 + index * 2).toString());
        // save new value
        Object.keys(newDataSource[newDataSource.length - 1]).forEach((key) => {
          const keyForm = `${newDataSource[newDataSource.length - 1].key}.${key}`;
          const keyFormNewValue = `${newDataSource[indexItemChange].key}.${key}`;
          const newValue = form.getFieldValue(keyFormNewValue);
          form.setFieldValue(keyForm, newValue);
        });
        form.setFieldValue(
          `${newDataSource[newDataSource.length - 1].key}.memo`,
          dataSource[indexItemChange].key + '行目の修正',
        );
        form.setFieldValue(`${newDataSource[newDataSource.length - 1].key}.status`, 'update');
        // new value back to old
        Object.keys(newDataSource[indexItemChange]).forEach((key) => {
          const keyForm = `${newDataSource[indexItemChange].key}.${key}`;
          form.setFieldValue(keyForm, newDataSource[indexItemChange][key]);
        });
      });
      setListItemCreate([...listItemCreateNew]);
      setListItemChange([...listItemChangeNew]);
      setListItemBlock([...listBlockNew]);
      setSelectedRowKeys([]);
      setDataSource([...newDataSource]);
    } catch (error) {
      console.log('onHandleModify error', error);
    }
  };

  useEffect(() => {
    let amount_excluding_tax: number = 0;
    let consumption_tax: number = 0;
    let amount_including_tax: number = 0;
    dataSource.forEach((item) => {
      amount_excluding_tax += roundNumber(
        Number(form.getFieldValue(`${item.key}.amount_excluding_tax`) || 0),
      );
      consumption_tax += roundNumber(
        Number(form.getFieldValue(`${item.key}.consumption_tax`) || 0),
      );
      amount_including_tax += roundNumber(
        Number(form.getFieldValue(`${item.key}.amount_including_tax`) || 0),
      );
    });

    setSumAmountExcludingTax(amount_excluding_tax);
    setSumAmountConsumptionTax(consumption_tax);
    setSumAmountIncludingTax(amount_including_tax);
  }, [listItemChange, listItemCreate, listItemDelete]);

  const isNoChange = useMemo(() => {
    const result =
      listItemDelete.length === 0 &&
      listItemChange.length === 0 &&
      invoiceIssue &&
      detailDataSaleInvoice &&
      invoiceIssue?.format('YYYYMMDD') ===
        moment(detailDataSaleInvoice?.voucher_posting_date).format('YYYYMMDD');
    return result;
  }, [listItemDelete, dataSource, listItemChange, invoiceIssue, detailDataSaleInvoice]);

  const refreshDataItem = () => {
    setDataSource([]);
    setListItemBlock([]);
    setListItemCreate([]);
    setListItemChange([]);
    setListItemDelete([]);
    setSelectedRowKeys([]);
  };

  return (
    <PageContainer>
      {contextHolder}
      <HeaderInformation
        invoiceIssue={invoiceIssue}
        setInvoiceIssue={setInvoiceIssue}
        setCurrentTour={setCurrentTour}
        setCurrentCompany={setCurrentCompany}
        isEditSaleInvoice={isEditSaleInvoice}
        onOpenModalSelectTour={() => {
          if (listItemChange.length === 0) {
            refModalSelectTour.current.open();
          } else refModalConfirmChangeTour.current.open();
        }}
        currentTour={currentTour}
        onOpenModalSelectCompany={() => {
          refModalSelectCompany.current.open();
        }}
        currentCompany={currentCompany}
        saleSlipId={detailDataSaleInvoice?.sale_invoice_id}
        sumAmountIncludingTax={detailDataSaleInvoice?.total_sale ?? 0}
      />
      <div className="mt-6 flex justify-end">
        <HeaderAction
          selectedRowKeys={selectedRowKeys}
          onHandleModify={onHandleModify}
          onHandleRevert={onHandleRevert}
          confirmDeleteRow={confirmDeleteRow}
          onCreateItem={onCreateItem}
        />
      </div>
      <div className="p-2 rounded-xl bg-white mt-6">
        <TableTax
          isEditPage={isEditPage}
          isLoading={isLoading}
          form={form}
          dataSource={dataSource}
          listItemChange={listItemChange}
          setListItemChange={setListItemChange}
          selectedRowKeys={selectedRowKeys}
          setSelectedRowKeys={setSelectedRowKeys}
          listItemBlock={listItemBlock}
          optionsTaxCategory={optionsTaxCategory}
          dataTaxCategory={dataTaxCategory}
          optionsAccountCodeList={optionsAccountCodeList}
          dataAccountCodeList={dataAccountCodeList}
          dataSummarySubject={dataSummarySubject}
          sumAmountExcludingTax={sumAmountExcludingTax}
          sumAmountConsumptionTax={sumAmountConsumptionTax}
          sumAmountIncludingTax={sumAmountIncludingTax}
          optionTaxInclusionType={optionTaxInclusionType}
        />
      </div>
      <div className="flex gap-6 justify-center mt-10">
        <BasicButton
          styleType="noneOutLine"
          disabled={isLoading}
          className="!w-[290px]  !bg-white flex justify-center items-center"
          onClick={() => {
            history.goBack();
          }}
        >
          <Image preview={false} src={IconBack} width={8} />
          <p className="ml-2">{TEXT_ACTION.RETURN}</p>
        </BasicButton>
        <BasicButton
          disabled={isLoading || isNoChange}
          styleType="accept"
          className={`!w-[290px] flex justify-center items-center ${
            isLoading || isNoChange ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
          }`}
          onClick={handleCheckBeforeSave}
        >
          <Image preview={false} src={IconSave} width={12} height={14} />
          <p>{TEXT_ACTION.SAVE}</p>
        </BasicButton>
      </div>
      <BasicFormModal
        ref={refModalSelectTour}
        content={
          <FormSelectTour
            onSelect={setCurrentTour}
            onClose={() => refModalSelectTour.current.close()}
          />
        }
        className="!w-[1300px] [&_.ant-modal-body]:!px-2"
        title="旅行"
        isValidate={true}
        hideListButton={true}
      />
      <BasicFormModal
        ref={refModalSelectCompany}
        content={
          <FormSelectCompany
            target={1}
            isCompanyActive={true}
            onSelect={setCurrentCompany}
            onClose={() => refModalSelectCompany.current.close()}
          />
        }
        className="!w-[1200px] [&_.ant-modal-body]:!px-2"
        title="取引先名"
        isValidate={true}
        hideListButton={true}
      />

      <BasicModal
        ref={refModalConfirmChangeTour}
        title={'警告'}
        content={<>この変更により、入力したすべてのデータが更新されます。続行しますか?</>}
        okText="同意する"
        onSubmit={() => {
          refreshDataItem();
          refModalConfirmChangeTour.current?.close();
          refModalSelectTour.current?.open();
        }}
      />

      <BasicModal
        ref={refModalConfirmSaveData}
        title={'警告'}
        content={
          <>
            選択したデータには新たにデータが追加されました。この操作を実行する前に、データの保存を実行してください。
          </>
        }
        okText="理解した"
        hideCloseButton={true}
        onSubmit={() => {
          refModalConfirmSaveData.current?.close();
        }}
      />
      <BasicModal
        ref={refModalDataChangedBeforeUpdate}
        title={'警告'}
        content={<>データが変更されました。続行する前にデータを再読み込みしてください。</>}
        okText="リロード"
        hideCloseButton={true}
        onSubmit={() => {
          history.go(0);
          refModalDataChangedBeforeUpdate.current?.close();
        }}
      />
    </PageContainer>
  );
};

export default InvoicesPage;
