import type { BaseParams } from '@/@types/request';
import IconEdit from '@/assets/imgs/common-icons/edit-blue.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicTable from '@/components/Commons/BasicTable';
import { TEXT_TITLE } from '@/constants/commonText';
import { invoiceIssueOptions } from '@/constants/data';
import { formatMoney } from '@/utils';
import { ITEM_PER_PAGE } from '@/utils/constants';
import type { FormInstance } from 'antd';
import { Form, Image } from 'antd';
import type { FieldData } from 'rc-field-form/lib/interface';
import { history } from 'umi';

// eslint-disable-next-line max-lines-per-function
const TableList = ({
  form,
  isLoading,
  dataSource,
  listItemChange,
  setListItemChange,
  paramSearch,
  setParamSearch,
  total,
}: {
  isLoading: boolean;
  form: FormInstance<any>;
  dataSource: any;
  listItemChange: string[];
  setListItemChange: (val: string[]) => void;
  paramSearch?: BaseParams;
  setParamSearch?: (val: BaseParams) => void;
  total: number;
}) => {
  const handleListItemChange = (changeField: FieldData[]) => {
    const nameFieldChange = changeField?.[0]?.name?.[0];
    const keyChange = nameFieldChange.split('.')[0];
    setListItemChange([...listItemChange, keyChange]);
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'key',
      key: 'key',
      width: 70,
    },
    {
      title: '仕入伝票ID',
      dataIndex: 'invoice_id',
      key: 'invoice_id',
      width: 120,
    },
    {
      title: '旅行ID',
      dataIndex: 'travel_id',
      key: 'travel_id',
      width: 120,
    },
    {
      title: 'ツアー名',
      dataIndex: 'tour_name',
      key: 'tour_name',
    },
    {
      title: '仕入先ID',
      dataIndex: 'business_partner_code',
      key: 'business_partner_code',
      width: 120,
    },
    {
      title: TEXT_TITLE.Supplier,
      dataIndex: 'business_partner_name',
      key: 'business_partner_name',
    },
    {
      title: '仕入金額',
      dataIndex: 'total_payment',
      key: 'total_payment',
      formType: 'input',
      width: 160,
      render: (_, record) => (
        <p className="text-right">
          {record.total_payment !== null ? formatMoney(record.total_payment) : ''}
        </p>
      ),
    },
    {
      title: '会計連携済',
      dataIndex: 'accounting_linked',
      key: 'accounting_linked',
      width: 120,
      render: (_, record) => (
        <p className="text-center">
          {record.accounting_linked !== null
            ? invoiceIssueOptions.find((item) => item.value === record.accounting_linked)?.label
            : ''}
        </p>
      ),
    },
    {
      title: '',
      dataIndex: 'deleteItem',
      key: 'deleteItem',
      width: 80,
      render: (_, record) => (
        <BasicButton
          onClick={() =>
            history.push(`/accounting-management/purchase-management/invoices/${record.id}`)
          }
          styleType="danger"
          className="!h-[24px] w-[76px] border-[transparent] hover:!border-[#225DE0] !text-[#225DE0]"
        >
          <Image preview={false} src={IconEdit} width={12} height={13} />
          <p className="text-xs !ml-1">編集</p>
        </BasicButton>
      ),
    },
  ];

  return (
    <>
      <Form
        form={form}
        component={false}
        onFieldsChange={(changeField: FieldData[]) => handleListItemChange(changeField)}
      >
        <BasicTable
          className="!mt-0"
          tableProps={{
            scroll: { x: 1200 },
            loading: isLoading,
            columns: defaultColumns,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            rowKey: 'id',
          }}
          page={Number(paramSearch?.page) ?? 1}
          pageSize={Number(paramSearch?.limit) ?? Number(ITEM_PER_PAGE)}
          onChangePage={(p: number) => {
            setParamSearch({ ...paramSearch, page: p });
          }}
          total={total}
          onSelectPageSize={(v) => {
            setParamSearch({ ...paramSearch, limit: v, page: 1 });
          }}
        />
      </Form>
    </>
  );
};

export default TableList;
