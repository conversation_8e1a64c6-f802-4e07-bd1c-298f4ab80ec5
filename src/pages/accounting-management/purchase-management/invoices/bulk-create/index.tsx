import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import { createMultiPurchaseInvoiceItemWithBusinessPartner } from '@/apis/accounting/purchaseManagement';
import { getListAccountingTravel } from '@/apis/accounting/traveList';
import type { AggregationItemDetailType } from '@/apis/master/AggregationItem';
import { getListAggregationItem } from '@/apis/master/AggregationItem';
import type { SubjectMasterDetailType } from '@/apis/master/subjectMaster';
import { getListSubjectMaster } from '@/apis/master/subjectMaster';
import type { DatumGetListTaxCategory } from '@/apis/master/taxCategory';
import { getListTaxCategory } from '@/apis/master/taxCategory';
import IconBack from '@/assets/imgs/common-icons/back-btn.svg';
import IconSave from '@/assets/imgs/common-icons/save-white-btn.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import BasicModal from '@/components/Commons/BasicModal';
import PageContainer from '@/components/Commons/Page/Container';
import FormSelectCompany from '@/components/Form/FormSelectCompany';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import { optionQualifiedBusinessCategory, optionTaxInclusionType } from '@/constants/data';
import STATUS_CODE from '@/constants/statusCode';
import { formatInvoiceNumber, roundNumber } from '@/utils';
import { Form, Image, Spin } from 'antd';
import type { Moment } from 'moment';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { history } from 'umi';
import HeaderAction from './components/HeaderAction';
import HeaderInformation from './components/HeaderInformation';
import TableTax from './components/TableTax';

interface DetailRowItemPurchaseInvoiceType {
  id?: number;
  key: number;
  subject_id: string | number;
  summary_subject_name: string;
  product_name: string;
  product_name_en: string;
  unit_price: number;
  quantity: number;
  tax_inclusion_type: number;
  tax_category_code: number;
  tax_rate: number;
  amount_excluding_tax?: number;
  amount_including_tax?: number;
  consumption_tax: number;
  invoice_number?: string;
  accounting_linked?: number;
  memo: string;
  status?: string;
  isDelete?: boolean;
  voucher_posting_date?: string | Date;
  purchase_invoice_id?: number;
  status_cancel?: number | null;
  status_update?: number | null;
  created_by?: number | null;
  updated_by?: number | null;
  deleted_by?: number | null;
  created_at?: string | Date | null;
  updated_at?: string | Date | null;
  deleted_at?: string | Date | null;
  index_column?: number | null;
  r_rate?: number | null;
  r_amount?: number | null;
  qualified_business_type?: number | null;
  isDisableForm?: boolean;
  line_change?: number;
  purchase_date?: Moment;
  travel_id: number;
}

const InvoicesPage = () => {
  const isEditPage = true;
  const [form] = Form.useForm();
  // const refModalConfirmChangePage = useRef(null);
  const refModalConfirmSaveData = useRef(null);
  const refModalSelectCompany = useRef(null);
  const refModalDataChangedBeforeUpdate = useRef(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentCompany, setCurrentCompany] = useState<BusinessPartnerDetailType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<DetailRowItemPurchaseInvoiceType[]>([]);
  const [listItemChange, setListItemChange] = useState<string[]>([]);
  const [countItemDeleted, setCountItemDeleted] = useState<number>(0);
  const [sumAmountExcludingTax, setSumAmountExcludingTax] = useState<number>(0);
  const [sumAmountConsumptionTax, setSumAmountConsumptionTax] = useState<number>(0);
  const [sumAmountIncludingTax, setSumAmountIncludingTax] = useState<number>(0);
  const [sumRAmount, setSumRAmount] = useState<number>(0);
  const [optionsTaxCategory, setOptionsTaxCategory] = useState<{ value: any; label: string }[]>([]);
  const [dataTaxCategory, setDataTaxCategory] = useState<DatumGetListTaxCategory[]>();
  const [optionsAccountCodeList, setOptionsAccountCodeList] = useState<
    { value: any; label: string }[]
  >([]);
  const [dataAccountCodeList, setDataAccountCodeList] = useState<SubjectMasterDetailType[]>();
  const [dataSummarySubject, setDataSummarySubject] = useState<AggregationItemDetailType[]>();
  const [optionTravels, setOptionTravels] = useState<
    {
      value: number;
      label: string;
      tourName: string;
      returnDate: string;
    }[]
  >([]);
  const refModalConfirmChangeCompany = useRef(null);

  const handleGetTravels = async () => {
    const res = await getListAccountingTravel({
      limit: 'all',
      page: 1,
    });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      const dataOption = res.data.data.map((item) => {
        return {
          value: item.id,
          label: item.travel_id,
          tourName: item.tour_name,
          returnDate: item.return_date as string,
        };
      });
      setOptionTravels(dataOption);
    }
  };

  useEffect(() => {
    if (currentCompany?.id) {
      handleGetTravels();
    }
  }, [currentCompany?.id]);

  const handleGetTaxCategoryMaster = async () => {
    const res = await getListTaxCategory({ limit: 'all', order: 'asc', sort: 'tax_category_code' });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setDataTaxCategory(res?.data?.data);
      const options = res?.data?.data?.map((item) => {
        return {
          value: item.id,
          label: item.tax_category_abbreviation,
          disabled: item.status === 0 || !item.target?.includes?.('2'),
        };
      });
      setOptionsTaxCategory(options);
    }
  };

  const handleGetListAccountMaster = async () => {
    const res = await getListSubjectMaster({ limit: 'all', order: 'asc', sort: 'id' });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setDataAccountCodeList(res?.data?.data);
      const options = res?.data?.data.map((item) => {
        return {
          value: item.id,
          label: item.subject_name,
          disabled: item.status === 0 || !item.target?.includes?.('2'),
        };
      });
      setOptionsAccountCodeList(options);
    }
  };
  const handleGetListSummarySubject = async () => {
    const res = await getListAggregationItem({ limit: 'all' });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setDataSummarySubject(res?.data?.data);
    }
  };

  //effect get list
  useEffect(() => {
    handleGetTaxCategoryMaster();
    handleGetListAccountMaster();
    handleGetListSummarySubject();
  }, []);

  //effect handle warning change page
  useEffect(() => {
    const unblock =
      listItemChange.length > 0
        ? history.block((a, b) => {
            if (
              a.pathname.includes('accounting-management/purchase-management') &&
              b === 'REPLACE'
            ) {
            } else {
              return TEXT_WARNING.leave_page_and_lose_changes;
            }
          })
        : null;

    return () => {
      if (unblock) {
        unblock?.();
      }
    };
  }, [listItemChange, history]);

  const removeRowFormData = (key) => {
    form.setFieldsValue({
      [`${key}.subject_id`]: undefined,
      [`${key}.summary_subject_name`]: undefined,
      [`${key}.product_name`]: undefined,
      [`${key}.product_name_en`]: undefined,
      [`${key}.unit_price`]: undefined,
      [`${key}.quantity`]: undefined,
      [`${key}.tax_inclusion_type`]: undefined,
      [`${key}.tax_category_code`]: undefined,
      [`${key}.tax_rate`]: undefined,
      [`${key}.amount_excluding_tax`]: undefined,
      [`${key}.consumption_tax`]: undefined,
      [`${key}.accounting_linked`]: undefined,
      [`${key}.r_rate`]: undefined,
      [`${key}.r_amount`]: undefined,
      [`${key}.qualified_business_type`]: undefined,
      [`${key}.invoice_number`]: undefined,
      [`${key}.memo`]: undefined,
      [`${key}.status`]: undefined,
    });
  };

  const confirmDeleteRow = () => {
    setIsLoading(true);
    setDataSource([...dataSource].filter((item) => !selectedRowKeys.includes(item.key)));
    selectedRowKeys.forEach((key) => {
      removeRowFormData(key);
    });
    setListItemChange(listItemChange.filter((item) => !selectedRowKeys.includes(item)));

    setCountItemDeleted(countItemDeleted + selectedRowKeys.length);
    setSelectedRowKeys([]);

    setIsLoading(false);
  };

  const onSave = async () => {
    setIsLoading(true);
    try {
      const valueBeforeValidate = form.getFieldsValue();
      const values = { ...valueBeforeValidate };
      await form.validateFields();
      if (dataSource.length === 0) {
        openNotificationFail('アイテムは作成されていません');
        setIsLoading(false);
        return;
      }

      // Start Update
      if (listItemChange.length > 0) {
        const resultObj = Object.keys(values).reduce((acc, key) => {
          const [index, property] = key.split('.');
          const value = values[key];

          if (!acc[index]) {
            acc[index] = {};
          }

          acc[index][property] = value;

          return acc;
        }, {});

        const newData = [...dataSource];

        Object.keys(resultObj).forEach((key) => {
          const index = dataSource.findIndex((item) => item.key === Number(key));
          const dataSourceIndex = { ...dataSource[index] };
          if (dataSourceIndex.status === 'reverse') {
            const indexItemCancel = dataSource.findIndex((item) => item.id === dataSourceIndex.id);
            if (indexItemCancel > 0) {
              newData[indexItemCancel].purchase_date = dataSourceIndex.purchase_date;
            }
          }
          newData[index] = { ...dataSource[index], ...resultObj[key] };
        });

        // Filter data change
        const dataChange = newData
          .filter((item) => listItemChange.includes(item.key.toString()))
          .map((item) => {
            delete item.purchase_invoice_id;
            delete item.status_cancel;
            delete item.status_update;
            delete item.created_by;
            delete item.updated_by;
            delete item.deleted_by;
            delete item.created_at;
            delete item.updated_at;
            delete item.deleted_at;
            delete item.index_column;
            return { ...item, purchase_date: moment(item.purchase_date)?.format('YYYY-MM-DD') };
          });
        const groupedByTravelCode = dataChange.reduce((acc, item) => {
          const { travel_id } = item;
          if (!acc[travel_id]) {
            acc[travel_id] = [];
          }
          acc[travel_id].push(item);
          return acc;
        }, {});

        const resultGroup = Object.values(groupedByTravelCode);
        const bodyUpdate = resultGroup.map((item) => {
          const travelCode = item?.[0]?.travel_id;
          const travelId = optionTravels.find((travel) => travel.label === travelCode)?.value;
          return {
            purchase_invoice: {
              business_partner_id: currentCompany?.id,
              travel_id: travelId,
            },
            purchase_invoice_item: item,
          };
        });

        const responseUpdate = await createMultiPurchaseInvoiceItemWithBusinessPartner({
          data: bodyUpdate,
        });
        if (
          responseUpdate.status === STATUS_CODE.SUCCESSFUL ||
          responseUpdate.status === STATUS_CODE.CREATED
        ) {
          openNotificationSuccess(MESSAGE_ALERT.CREATED_SUCCESS);
          // Reset
          setIsLoading(false);

          history.replace(
            `/accounting-management/purchase-management?limit=30&page=1&business_partner_id=${currentCompany?.id}`,
          );
        } else {
          openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
        }
      }
      // End update
    } catch (errInfo) {
      openNotificationFail(MESSAGE_ALERT.CREATED_FAILED);
      setIsLoading(false);
      console.log('Save failed:', errInfo);
    }
    setIsLoading(false);
  };

  const onCreateItem = () => {
    if (!currentCompany) {
      window.alert(MESSAGE_ALERT.CREATE_INVOICE_ITEM_NO_HAVE_TOUR_AND_SUPPLIER);
      return;
    }
    const lastIndex = dataSource.length + 1 + countItemDeleted;
    try {
      const newDataSource = [...dataSource];
      newDataSource.push({
        key: lastIndex,
        subject_id: undefined,
        summary_subject_name: undefined,
        product_name: undefined,
        product_name_en: undefined,
        unit_price: undefined,
        quantity: undefined,
        tax_inclusion_type: 1,
        tax_category_code: undefined,
        tax_rate: undefined,
        amount_excluding_tax: undefined,
        consumption_tax: undefined,
        accounting_linked: 0,
        r_rate: currentCompany?.r_rate,
        r_amount: undefined,
        qualified_business_type: currentCompany?.qualified_invoice_issuer_type,
        invoice_number: moment().format('YYMM') + formatInvoiceNumber(lastIndex),
        memo: undefined,
        status: 'create',
        travel_id: undefined,
      });
      form.setFieldValue(`${lastIndex}.r_rate`, currentCompany?.r_rate ?? 0);
      form.setFieldValue(`${lastIndex}.tax_inclusion_type`, 1);
      form.setFieldValue(
        `${lastIndex}.qualified_business_type`,
        currentCompany?.qualified_invoice_issuer_type,
      );
      setListItemChange([...listItemChange, lastIndex.toString()]);
      setDataSource([...newDataSource]);
    } catch (error) {
      console.log('onCreateItem error', error);
    }
  };

  const onCopy = () => {
    try {
      const newDataSource = [...dataSource];

      const listItemChangeNew = [...listItemChange];
      const listIndexItemChange = [...selectedRowKeys];
      listIndexItemChange.sort((a, b) => Number(a) - Number(b));
      listIndexItemChange.forEach((indexItemChange, index) => {
        const lastIndex = dataSource.length + 1 + countItemDeleted + index;

        newDataSource.push({
          memo: form.getFieldValue(`${indexItemChange}.memo`),
          travel_id: null,
          key: lastIndex,
          subject_id: form.getFieldValue(`${indexItemChange}.subject_id`),
          summary_subject_name: form.getFieldValue(`${indexItemChange}.summary_subject_name`),
          product_name: form.getFieldValue(`${indexItemChange}.product_name`),
          product_name_en: form.getFieldValue(`${indexItemChange}.product_name_en`),
          unit_price: null,
          quantity: null,
          tax_inclusion_type: form.getFieldValue(`${indexItemChange}.tax_inclusion_type`),
          tax_category_code: form.getFieldValue(`${indexItemChange}.tax_category_code`),
          tax_rate: form.getFieldValue(`${indexItemChange}.tax_rate`),
          amount_excluding_tax: null,
          amount_including_tax: null,
          consumption_tax: null,
          accounting_linked: 0,
          r_rate: form.getFieldValue(`${indexItemChange}.r_rate`),
          r_amount: form.getFieldValue(`${indexItemChange}.r_amount`),
          qualified_business_type: form.getFieldValue(`${indexItemChange}.qualified_business_type`),
          invoice_number: moment().format('YYMM') + formatInvoiceNumber(lastIndex),
          status: 'create',
        });
        Object.keys(newDataSource[newDataSource.length - 1]).forEach((key) => {
          const keyForm = `${newDataSource[newDataSource.length - 1].key}.${key}`;
          form.setFieldValue(keyForm, newDataSource[newDataSource.length - 1][key]);
        });
        listItemChangeNew.push(lastIndex.toString());
      });
      setListItemChange([...listItemChangeNew]);
      setSelectedRowKeys([]);
      setDataSource([...newDataSource]);
    } catch (error) {
      console.log('onHandleModify error', error);
    }
  };

  useEffect(() => {
    let amount_excluding_tax: number = 0;
    let consumption_tax: number = 0;
    let amount_including_tax: number = 0;
    let r_amount: number = 0;
    dataSource.forEach((item) => {
      amount_excluding_tax += roundNumber(
        Number(form.getFieldValue(`${item.key}.amount_excluding_tax`) || 0),
      );
      consumption_tax += roundNumber(
        Number(form.getFieldValue(`${item.key}.consumption_tax`) || 0),
      );
      amount_including_tax += roundNumber(
        Number(form.getFieldValue(`${item.key}.amount_including_tax`) || 0),
      );
      r_amount += roundNumber(Number(form.getFieldValue(`${item.key}.r_amount`) || 0));
    });

    setSumAmountExcludingTax(amount_excluding_tax);
    setSumAmountConsumptionTax(consumption_tax);
    setSumAmountIncludingTax(amount_including_tax);
    setSumRAmount(r_amount);
  }, [listItemChange]);

  const isNoChange = useMemo(() => {
    if (listItemChange.length === 0) return true;
    return false;
  }, [listItemChange]);

  const refreshDataItem = () => {
    form.resetFields();
    setDataSource([]);
    setListItemChange([]);
    setSelectedRowKeys([]);
  };

  return (
    <Spin spinning={isLoading}>
      <PageContainer>
        <HeaderInformation
          setCurrentCompany={setCurrentCompany}
          onOpenModalSelectCompany={() => {
            if (listItemChange.length === 0) {
              refModalSelectCompany.current.open();
            } else refModalConfirmChangeCompany.current.open();
          }}
          currentCompany={currentCompany}
          rAmount={sumAmountIncludingTax - sumRAmount}
        />
        <div className="mt-6 flex justify-end">
          <HeaderAction
            selectedRowKeys={selectedRowKeys}
            confirmDeleteRow={confirmDeleteRow}
            onCreateItem={onCreateItem}
            onCopy={onCopy}
          />
        </div>
        <div className="p-2 rounded-xl bg-white mt-6">
          <TableTax
            isEditPage={isEditPage}
            isLoading={isLoading}
            form={form}
            dataSource={dataSource}
            listItemChange={listItemChange}
            optionTravels={optionTravels}
            setListItemChange={setListItemChange}
            selectedRowKeys={selectedRowKeys}
            setSelectedRowKeys={setSelectedRowKeys}
            optionsTaxCategory={optionsTaxCategory}
            dataTaxCategory={dataTaxCategory}
            optionsAccountCodeList={optionsAccountCodeList}
            dataAccountCodeList={dataAccountCodeList}
            dataSummarySubject={dataSummarySubject}
            sumAmountExcludingTax={sumAmountExcludingTax}
            sumAmountConsumptionTax={sumAmountConsumptionTax}
            sumAmountIncludingTax={sumAmountIncludingTax}
            sumRAmount={sumRAmount}
            optionTaxInclusionType={optionTaxInclusionType}
            optionQualifiedBusinessCategory={optionQualifiedBusinessCategory}
          />
        </div>

        <div className="flex gap-6 justify-center mt-10">
          <BasicButton
            styleType="noneOutLine"
            disabled={isLoading}
            className="!w-[290px]  !bg-white flex justify-center items-center"
            onClick={() => {
              history.goBack();
            }}
          >
            <Image preview={false} src={IconBack} width={8} />
            <p className="ml-2">{TEXT_ACTION.RETURN}</p>
          </BasicButton>
          <BasicButton
            disabled={isLoading || isNoChange}
            styleType="accept"
            className={`!w-[290px] flex justify-center items-center ${
              isLoading || isNoChange ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
            }`}
            onClick={onSave}
          >
            <Image preview={false} src={IconSave} width={12} height={14} />
            <p>{TEXT_ACTION.SAVE}</p>
          </BasicButton>
        </div>

        <BasicFormModal
          ref={refModalSelectCompany}
          content={
            <FormSelectCompany
              target={2}
              isCompanyActive={true}
              onSelect={setCurrentCompany}
              onClose={() => refModalSelectCompany.current.close()}
            />
          }
          className="!w-[1200px] [&_.ant-modal-body]:!px-2"
          title="取引先名"
          isValidate={true}
          hideListButton={true}
        />

        <BasicModal
          ref={refModalConfirmChangeCompany}
          title={'警告'}
          content={<>この変更により、入力したすべてのデータが更新されます。続行しますか?</>}
          okText="同意する"
          onSubmit={() => {
            refreshDataItem();
            refModalConfirmChangeCompany.current?.close();
            refModalSelectCompany.current?.open();
          }}
        />

        <BasicModal
          ref={refModalConfirmSaveData}
          title={'警告'}
          content={
            <>
              選択したデータには新たにデータが追加されました。この操作を実行する前に、データの保存を実行してください。
            </>
          }
          okText="理解した"
          hideCloseButton={true}
          onSubmit={() => {
            refModalConfirmSaveData.current?.close();
          }}
        />
        <BasicModal
          ref={refModalDataChangedBeforeUpdate}
          title={'警告'}
          content={<>データが変更されました。続行する前にデータを再読み込みしてください。</>}
          okText="リロード"
          hideCloseButton={true}
          onSubmit={() => {
            history.go(0);
            refModalDataChangedBeforeUpdate.current?.close();
          }}
        />
      </PageContainer>
    </Spin>
  );
};

export default InvoicesPage;
