import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import BasicButton from '@/components/Commons/BasicButton';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import { CopyOutlined, PlusOutlined } from '@ant-design/icons';
import { Image, Popconfirm } from 'antd';

const HeaderAction = ({
  confirmDeleteRow,
  selectedRowKeys,
  onCreateItem,
  onCopy,
}: {
  onCreateItem: () => void;
  onCopy: () => void;
  confirmDeleteRow: () => void;
  selectedRowKeys: React.Key[];
}) => {
  return (
    <div className="flex gap-x-[12px]">
      <BasicButton
        disabled={selectedRowKeys.length === 0}
        icon={<CopyOutlined />}
        className=" flex justify-center items-center !border-white hover:!border-[#FDAF2E] !text-[#FDAF2E] !bg-white hover:!bg-white hover:shadow-md hover:opacity-70"
        onClick={onCopy}
      >
        {TEXT_ACTION.COPY}
      </BasicButton>
      <Popconfirm
        title={TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
        disabled={selectedRowKeys.length === 0}
        onConfirm={() => confirmDeleteRow()}
        okText={TEXT_ACTION.DELETE}
        cancelText={TEXT_ACTION.CANCEL}
      >
        <BasicButton
          styleType="danger"
          disabled={selectedRowKeys.length === 0}
          className=" w-[84px] hover:shadow-md hover:opacity-70 !border-[#ff4d4f] !text-[#ff4d4f] !bg-white"
        >
          <Image preview={false} src={IconDelete} width={15} height={14} />
          <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
        </BasicButton>
      </Popconfirm>

      <BasicButton
        icon={<PlusOutlined />}
        className="flex items-center justify-center"
        styleType="accept"
        onClick={onCreateItem}
      >
        {TEXT_ACTION.CREATE_NEW}
      </BasicButton>
    </div>
  );
};

export default HeaderAction;
