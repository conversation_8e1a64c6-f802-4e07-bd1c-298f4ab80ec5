import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import {
  getListAccountingTravel,
  type AccountingTravelListType,
} from '@/apis/accounting/traveList';
import { getListBusinessPartner } from '@/apis/businessPartner';
import SelectIcon from '@/assets/imgs/common-icons/select-icon.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import { formatCurrency } from '@/utils/utils';
import { useDebounce } from '@uidotdev/usehooks';
import { Image } from 'antd';
import moment from 'moment';
import { useEffect, useState } from 'react';

const HeaderInformation = ({
  currentCompany,
  currentTour,
  sumAmountIncludingTax,
  rAmount,
  setCurrentTour,
  setCurrentCompany,
  onOpenModalSelectTour,
  onOpenModalSelectCompany,
}: {
  currentTour: AccountingTravelListType;
  currentCompany: BusinessPartnerDetailType;
  sumAmountIncludingTax: number;
  rAmount: number;
  setCurrentTour: (val: AccountingTravelListType) => void;
  setCurrentCompany: (val: BusinessPartnerDetailType) => void;
  onOpenModalSelectTour: () => void;
  onOpenModalSelectCompany: () => void;
}) => {
  const [textSearchTour, setTextSearchTour] = useState<string>(currentTour?.travel_id ?? '');
  const [textSearchCompany, setTextSearchCompany] = useState<string>(
    currentCompany?.business_partner_code ?? '',
  );
  const codeTourToSearch = useDebounce(textSearchTour, 300);
  const codeCompanyToSearch = useDebounce(textSearchCompany, 300);

  useEffect(() => {
    if (currentCompany?.business_partner_code) {
      setTextSearchCompany(currentCompany.business_partner_code);
    }
  }, [currentCompany]);

  useEffect(() => {
    if (currentTour?.travel_id) {
      setTextSearchTour(currentTour.travel_id);
    }
  }, [currentTour]);

  const fetchDataTour = async () => {
    const resData = await getListAccountingTravel({
      page: 1,
      limit: 1,
      keyword: codeTourToSearch,
    });
    if (resData?.data?.data) {
      setCurrentTour(resData.data.data[0]);
    } else {
      setCurrentTour(undefined);
    }
  };

  useEffect(() => {
    if (codeTourToSearch.length > 6) {
      fetchDataTour();
    } else {
      setCurrentTour(undefined);
    }
  }, [codeTourToSearch]);

  const fetchDataCompany = async () => {
    const resData = await getListBusinessPartner({
      page: 1,
      limit: 1,
      business_partner_code: codeCompanyToSearch,
    });
    if (resData?.data?.data) {
      setCurrentCompany(resData.data.data[0]);
    } else {
      setCurrentCompany(undefined);
    }
  };

  useEffect(() => {
    if (codeCompanyToSearch.length > 2) {
      fetchDataCompany();
    }
  }, [codeCompanyToSearch]);

  return (
    <div className="flex justify-between flex-wrap">
      <div className="grid grid-cols-10 gap-6 max-w-[1000px]">
        <div className="col-span-2">
          <BasicInput
            title="旅行ID"
            className="!h-10"
            placeholder="旅行ID"
            value={textSearchTour}
            onChange={(e) => setTextSearchTour(e.target.value)}
          />
        </div>
        <div className="col-span-4">
          <p className="mb-2 text-[13px] leading-4 font-medium">
            旅行 <span className="text-[red]">*</span>
          </p>
          <BasicButton
            className={`
            !border-[#EBE9FA] flex justify-between !px-[11px] items-center !w-full
             ${
               currentTour?.tour_name ? '!text-[rgba(0,0,0,0.85)]' : '!text-[rgba(0,0,0,0.3)] '
             } !bg-white hover:opacity:40 font-normal`}
            onClick={onOpenModalSelectTour}
          >
            <span>{currentTour?.tour_name ?? 'すべて'}</span>
            <Image src={SelectIcon} alt="select icon" preview={false} className="w-5 h-5" />
          </BasicButton>
        </div>
        <div className="col-span-2">
          <BasicDatePicker
            title="出発日"
            disabled={true}
            className="!h-10 !bg-[#DCDEE3]"
            placeholder="すべて"
            value={moment(currentTour?.departure_date)}
          />
        </div>
        <div className="col-span-2">
          <BasicDatePicker
            title="帰着日"
            disabled={true}
            className="!h-10 !bg-[#DCDEE3]"
            placeholder="すべて"
            value={moment(currentTour?.return_date)}
          />
        </div>
        <div className="col-span-2">
          <>
            <BasicInput
              title="仕入先ID"
              className="!h-10"
              placeholder="仕入先ID"
              value={textSearchCompany}
              onChange={(e) => setTextSearchCompany(e.target.value)}
            />
          </>
        </div>
        <div className="col-span-4">
          <p className="mb-2 text-[13px] leading-4 font-medium">
            仕入先名 <span className="text-[red]">*</span>
          </p>
          <BasicButton
            className={`
            !border-[#EBE9FA] flex justify-between !px-[11px] items-center !w-full
             ${
               currentCompany?.business_partner_name
                 ? '!text-[rgba(0,0,0,0.85)]'
                 : '!text-[rgba(0,0,0,0.3)] '
             } !bg-white hover:opacity:40 font-normal`}
            onClick={onOpenModalSelectCompany}
          >
            <span className="line-clamp-1">
              {currentCompany?.business_partner_name ?? 'すべて'}
            </span>
            <Image src={SelectIcon} alt="select icon" preview={false} className="w-5 h-5" />
          </BasicButton>
        </div>

        <div className="col-span-2">
          <div className="mb-2 text-[13px] leading-4 font-medium">純仕入額</div>
          <div className="mb-2 text-[14px] leading-4 font-medium h-10 flex items-center justify-end">
            {formatCurrency(rAmount)}
          </div>
        </div>
        <div className="col-span-2">
          <div className="mb-2 text-[13px] leading-4 font-medium">支払総額</div>
          <div className="mb-2 text-[14px] leading-4 font-medium h-10 flex items-center justify-end">
            {formatCurrency(sumAmountIncludingTax)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeaderInformation;
