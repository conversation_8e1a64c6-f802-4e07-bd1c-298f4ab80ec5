import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import type {
  PurchaseInvoiceItemType,
  PurchaseInvoiceType,
} from '@/apis/accounting/purchaseManagement';
import {
  createPurchaseInvoiceItem,
  deletePurchaseInvoiceItems,
  getDetailPurchaseInvoice,
} from '@/apis/accounting/purchaseManagement';
import {
  getAccountingTravelDetail,
  type AccountingTravelListType,
} from '@/apis/accounting/traveList';
import type { AggregationItemDetailType } from '@/apis/master/AggregationItem';
import { getListAggregationItem } from '@/apis/master/AggregationItem';
import type { SubjectMasterDetailType } from '@/apis/master/subjectMaster';
import { getListSubjectMaster } from '@/apis/master/subjectMaster';
import type { DatumGetListTaxCategory } from '@/apis/master/taxCategory';
import { getListTaxCategory } from '@/apis/master/taxCategory';
import IconBack from '@/assets/imgs/common-icons/back-btn.svg';
import IconSave from '@/assets/imgs/common-icons/save-white-btn.svg';
import BasicButton from '@/components/Commons/BasicButton';
import type { BasicFormModalRef } from '@/components/Commons/BasicFormModal';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import BasicModal from '@/components/Commons/BasicModal';
import PageContainer from '@/components/Commons/Page/Container';
import FormSelectCompany from '@/components/Form/FormSelectCompany';
import FormSelectTour from '@/components/Form/FormSelectTour';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import { optionQualifiedBusinessCategory, optionTaxInclusionType } from '@/constants/data';
import STATUS_CODE from '@/constants/statusCode';
import { formatInvoiceNumber, roundNumber } from '@/utils';
import { Form, Image, Space, Spin, notification } from 'antd';
import type { Moment } from 'moment';
import moment from 'moment';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useLocation } from 'react-router';
import { history, useParams } from 'umi';
import HeaderAction from './components/HeaderAction';
import HeaderInformation from './components/HeaderInformation';
import TableTax from './components/TableTax';

interface DetailRowItemPurchaseInvoiceType {
  id?: number;
  key: number;
  subject_id: string | number;
  summary_subject_name: string;
  product_name: string;
  product_name_en: string;
  unit_price: number;
  quantity: number;
  tax_inclusion_type: number;
  tax_category_code: number;
  tax_rate: number;
  amount_excluding_tax?: number;
  amount_including_tax?: number;
  consumption_tax: number;
  invoice_number?: string;
  accounting_linked?: number;
  memo: string;
  status?: string;
  isDelete?: boolean;
  voucher_posting_date?: string | Date;
  purchase_invoice_id?: number;
  status_cancel?: number | null;
  status_update?: number | null;
  created_by?: number | null;
  updated_by?: number | null;
  deleted_by?: number | null;
  created_at?: string | Date | null;
  updated_at?: string | Date | null;
  deleted_at?: string | Date | null;
  index_column?: number | null;
  r_rate?: number | null;
  r_amount?: number | null;
  qualified_business_type?: number | null;
  isDisableForm?: boolean;
  line_change?: number;
  purchase_date?: Moment;
  reverse_purchase_date?: string;
  isRemoveToSubmitForm?: boolean;
  total_payment?: number | null;
}

const InvoicesPage = () => {
  const isEditPage = true;
  const [form] = Form.useForm();
  const refModalSelectTour = useRef<BasicFormModalRef>();
  // const refModalConfirmChangePage = useRef(null);
  const refModalConfirmSaveData = useRef(null);
  const refModalSelectCompany = useRef(null);
  const refModalConfirmChangeTour = useRef(null);
  const refModalDataChangedBeforeUpdate = useRef(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [currentTour, setCurrentTour] = useState<AccountingTravelListType>();
  const [currentCompany, setCurrentCompany] = useState<BusinessPartnerDetailType>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<DetailRowItemPurchaseInvoiceType[]>([]);
  const [listItemChange, setListItemChange] = useState<string[]>([]);
  const [listItemCreate, setListItemCreate] = useState<string[]>([]);
  const [listItemBlock, setListItemBlock] = useState<number[]>([]);
  const [listItemDelete, setListItemDelete] = useState<number[]>([]);
  const [countItemDeleted, setCountItemDeleted] = useState<number>(0);
  const [api, contextHolder] = notification.useNotification();
  const [detailDataPurchaseInvoice, setDetailDataPurchaseInvoice] = useState<PurchaseInvoiceType>();
  const [sumAmountExcludingTax, setSumAmountExcludingTax] = useState<number>(0);
  const [sumAmountConsumptionTax, setSumAmountConsumptionTax] = useState<number>(0);
  const [sumAmountIncludingTax, setSumAmountIncludingTax] = useState<number>(0);
  const [sumRAmount, setSumRAmount] = useState<number>(0);
  const [listItemAccountingLinked, setListItemAccountingLinked] = useState<number[]>([]);

  const paramUrl = useParams();
  const { query } = useLocation() as any;
  const purchaseInvoiceId = (paramUrl as { id: string })?.id;

  const [optionsTaxCategory, setOptionsTaxCategory] = useState<{ value: any; label: string }[]>([]);
  const [dataTaxCategory, setDataTaxCategory] = useState<DatumGetListTaxCategory[]>();
  const [optionsAccountCodeList, setOptionsAccountCodeList] = useState<
    { value: any; label: string }[]
  >([]);
  const [dataAccountCodeList, setDataAccountCodeList] = useState<SubjectMasterDetailType[]>();
  const [dataSummarySubject, setDataSummarySubject] = useState<AggregationItemDetailType[]>();
  const handleGetTaxCategoryMaster = async () => {
    const res = await getListTaxCategory({ limit: 'all', order: 'asc', sort: 'tax_category_code' });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setDataTaxCategory(res?.data?.data);
      const options = res?.data?.data?.map((item) => {
        return {
          value: item.id,
          label: item.tax_category_abbreviation,
          disabled: item.status === 0 || !item.target?.includes?.('2'),
        };
      });
      setOptionsTaxCategory(options);
    }
  };

  const handleGetListAccountMaster = async () => {
    const res = await getListSubjectMaster({ limit: 'all', order: 'asc', sort: 'id' });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setDataAccountCodeList(res?.data?.data);
      const options = res?.data?.data.map((item) => {
        return {
          value: item.id,
          label: item.subject_name,
          disabled: item.status === 0 || !item.target?.includes?.('2'),
        };
      });
      setOptionsAccountCodeList(options);
    }
  };
  const handleGetListSummarySubject = async () => {
    const res = await getListAggregationItem({ limit: 'all' });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setDataSummarySubject(res?.data?.data);
    }
  };

  //effect get list
  useEffect(() => {
    handleGetTaxCategoryMaster();
    handleGetListAccountMaster();
    handleGetListSummarySubject();
  }, []);

  //effect handle warning change page
  useEffect(() => {
    const unblock =
      listItemChange.length > 0
        ? history.block((a, b) => {
            if (
              !purchaseInvoiceId &&
              a.pathname.includes('accounting-management/purchase-management/invoices') &&
              b === 'REPLACE'
            ) {
            } else {
              return TEXT_WARNING.leave_page_and_lose_changes;
            }
          })
        : null;

    return () => {
      if (unblock) {
        unblock?.();
      }
    };
  }, [listItemChange, history]);

  const formatDataPurchaseInvoiceItems = async (invoice_items: PurchaseInvoiceItemType[]) => {
    if (invoice_items) {
      let amountExcludingTax: number = 0;
      let consumptionTax: number = 0;
      let amountIncludingTax: number = 0;
      let rAmount: number = 0;
      const listBlockNew = [];
      const listItemAccountingLinkedNew = [];
      const newDataSource = [];

      // Create maps for quick lookup
      const accountCodeMap = new Map(dataAccountCodeList.map((account) => [account.id, account]));
      const summarySubjectMap = new Map(dataSummarySubject.map((summary) => [summary.id, summary]));

      for (let index = 0; index < invoice_items.length; index++) {
        const item = invoice_items[index];
        const subjectMaster = accountCodeMap.get(Number(item?.subject_id));
        const summaryItem = summarySubjectMap.get(Number(subjectMaster?.summary_item_id));

        const roundedAmountExcludingTax = roundNumber(item?.amount_excluding_tax);
        const roundedConsumptionTax = roundNumber(item?.consumption_tax);
        const roundedAmountIncludingTax = roundNumber(item?.amount_including_tax);
        const roundedRAmount = roundNumber(item?.r_amount);

        amountExcludingTax += roundedAmountExcludingTax;
        consumptionTax += roundedConsumptionTax;
        amountIncludingTax += roundedAmountIncludingTax;
        rAmount += roundedRAmount;

        if (item.status_cancel !== 0 || item.status_update !== 0) {
          listBlockNew.push(index + 1);
        }
        if (item.accounting_linked === 1) {
          listItemAccountingLinkedNew.push(item.id);
        }

        newDataSource.push({
          key: index + 1,
          id: item.id,
          accounting_linked: item.accounting_linked,
          subject_id: item?.subject_id ? Number(item?.subject_id) : undefined,
          summary_subject_name: summaryItem?.summary_item_name,
          product_name: item?.product_name,
          product_name_en: item?.product_name_en,
          purchase_date: item?.purchase_date ? moment(item?.purchase_date) : moment(),
          unit_price: item?.unit_price,
          quantity: item?.quantity,
          tax_inclusion_type: Number(item?.tax_inclusion_type) ?? 1,
          qualified_business_type: item?.qualified_business_type
            ? Number(item?.qualified_business_type)
            : undefined,
          tax_category_code: item?.tax_category_code,
          tax_rate: item?.tax_rate,
          amount_excluding_tax: item?.amount_excluding_tax,
          consumption_tax: item?.consumption_tax,
          amount_including_tax: item?.amount_including_tax,
          r_rate: item?.r_rate,
          memo: item?.memo,
          r_amount: item?.r_amount,
          isDelete: item.status_cancel !== 0 || item.status_update !== 0,
          isDisableForm: item.accounting_linked === 1,
          total_payment: item?.total_payment,
        });
      }

      setListItemBlock(listBlockNew);
      setListItemAccountingLinked(listItemAccountingLinkedNew);

      setDataSource([...newDataSource]);
      setSumAmountExcludingTax(amountExcludingTax);
      setSumAmountConsumptionTax(consumptionTax);
      setSumAmountIncludingTax(amountIncludingTax);
      setSumRAmount(rAmount);

      newDataSource.forEach((item) => {
        Object.entries(item).forEach(([key, value]) => {
          const keyForm = `${item.key}.${key}`;
          form.setFieldValue(keyForm, value);
        });
      });
    }
  };

  const getPurchaseInvoice = async (id) => {
    setIsLoading(true);
    form.resetFields();
    // Reset
    setListItemChange([]);
    setListItemCreate([]);
    const resGetDetailPurchaseInvoice = await getDetailPurchaseInvoice(id);
    if (resGetDetailPurchaseInvoice.status === STATUS_CODE.SUCCESSFUL) {
      setCurrentTour(resGetDetailPurchaseInvoice.data.data?.travel);
      setCurrentCompany(resGetDetailPurchaseInvoice.data.data?.business_partner);
      formatDataPurchaseInvoiceItems(resGetDetailPurchaseInvoice.data.data?.purchase_invoice_item);
      if (resGetDetailPurchaseInvoice?.data?.data) {
        setDetailDataPurchaseInvoice(resGetDetailPurchaseInvoice?.data?.data);
      }
    } else {
      openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
    }
    setIsLoading(false);
  };

  const fetchAccountingTravelDetail = async (id) => {
    const resTravelDetail = await getAccountingTravelDetail(id);
    if (resTravelDetail.status === STATUS_CODE.SUCCESSFUL) {
      setCurrentTour(resTravelDetail?.data?.data);
    }
  };

  useEffect(() => {
    if (purchaseInvoiceId && dataAccountCodeList && dataTaxCategory && dataSummarySubject) {
      getPurchaseInvoice(purchaseInvoiceId);
    }
    if (!purchaseInvoiceId && query.tourId) {
      fetchAccountingTravelDetail(query.tourId);
    }
  }, [purchaseInvoiceId, dataAccountCodeList, dataTaxCategory, dataSummarySubject]);

  useEffect(() => {
    if (!purchaseInvoiceId && query.tourId) {
      fetchAccountingTravelDetail(query.tourId);
    }
  }, [query.tourId, purchaseInvoiceId]);

  const itemCreateNewInListItemSelected = () => {
    if (selectedRowKeys.length > 0 && listItemCreate.length > 0) {
      return selectedRowKeys.some((item) => listItemCreate.includes(item.toString()));
    }
    return false;
  };

  const removeRowFormData = (key) => {
    form.setFieldsValue({
      [`${key}.subject_id`]: undefined,
      [`${key}.summary_subject_name`]: undefined,
      [`${key}.product_name`]: undefined,
      [`${key}.product_name_en`]: undefined,
      [`${key}.unit_price`]: undefined,
      [`${key}.quantity`]: undefined,
      [`${key}.tax_inclusion_type`]: undefined,
      [`${key}.tax_category_code`]: undefined,
      [`${key}.tax_rate`]: undefined,
      [`${key}.amount_excluding_tax`]: undefined,
      [`${key}.consumption_tax`]: undefined,
      [`${key}.accounting_linked`]: undefined,
      [`${key}.r_rate`]: undefined,
      [`${key}.r_amount`]: undefined,
      [`${key}.qualified_business_type`]: undefined,
      [`${key}.invoice_number`]: undefined,
      [`${key}.memo`]: undefined,
      [`${key}.status`]: undefined,
    });
  };

  const checkWhenReverse = () => {
    const newListIdToReverse = [...dataSource]
      .filter((item) => selectedRowKeys.includes(item.key))
      .map((itemMap) => itemMap.id);
    if (newListIdToReverse.every((item) => !listItemAccountingLinked.includes(item))) {
      window.alert('会計未連携は削除のみで修正と取消の操作できません。\n再度ご確認ください。');
      return true;
    }
    return false;
  };

  const checkWhenUpdateOrDelete = () => {
    const newListIdToCheck = [...dataSource]
      .filter((item) => {
        return (
          selectedRowKeys.includes(item.key) &&
          !listItemCreate.includes(item.key.toString()) &&
          item.id
        );
      })
      .map((itemMap) => itemMap.id);
    if (newListIdToCheck.some((item) => listItemAccountingLinked.includes(item))) {
      return false;
    }
    return newListIdToCheck;
  };

  const confirmDeleteRow = () => {
    const listDelete = checkWhenUpdateOrDelete();
    if (listDelete === false) {
      window.alert('会計連携済の記録があるため、削除できません。\n再度ご確認ください。');
      return;
    }
    // Check item xoá có được tạo hoá đơn từ payment thì báo lỗi
    if (listDelete.length > 0) {
      const listSourceDataOrigin = detailDataPurchaseInvoice?.purchase_invoice_item;
      const hasItemInPayment = listSourceDataOrigin.some(
        (item) => listDelete.includes(item.id) && item?.total_payment > 0,
      );
      if (hasItemInPayment) {
        openNotificationFail('支払済みの金額を下回っています。支払伝票から修正してください');
        return;
      }
    }
    setIsLoading(true);
    setDataSource([...dataSource].filter((item) => !selectedRowKeys.includes(item.key)));
    selectedRowKeys.forEach((key) => {
      removeRowFormData(key);
    });
    setListItemBlock(listItemBlock.filter((item) => !selectedRowKeys.includes(item)));
    setListItemCreate(listItemCreate.filter((item) => !selectedRowKeys.includes(item)));
    setListItemChange(listItemChange.filter((item) => !selectedRowKeys.includes(item)));

    setListItemDelete([...listItemDelete, ...listDelete]);
    setCountItemDeleted(countItemDeleted + selectedRowKeys.length);
    setSelectedRowKeys([]);

    setIsLoading(false);
  };

  const onSave = async () => {
    setIsLoading(true);
    try {
      const valueBeforeValidate = form.getFieldsValue();
      const values = { ...valueBeforeValidate };
      await form.validateFields();
      if (dataSource.length === 0) {
        openNotificationFail('アイテムは作成されていません');
        setIsLoading(false);
        return;
      }
      // Start delete
      if (listItemDelete.length > 0) {
        const responseDelete = await deletePurchaseInvoiceItems({ id: [...listItemDelete] });
        if (responseDelete.status === STATUS_CODE.SUCCESSFUL) {
          setListItemDelete([]);
          setCountItemDeleted(0);
        } else if (responseDelete.status === STATUS_CODE.CONFLICT) {
          refModalDataChangedBeforeUpdate.current.open();
          return;
        } else {
          setIsLoading(false);
          openNotificationFail(
            responseDelete.error?.data?.errors?.message ||
              responseDelete.error?.data?.message ||
              MESSAGE_ALERT.DELETE_FAILED,
          );
          return;
        }
      }
      // End delete

      // Start Update
      if (listItemChange.length > 0) {
        const resultObj = Object.keys(values).reduce((acc, key) => {
          const [index, property] = key.split('.');
          const value = values[key];

          if (!acc[index]) {
            acc[index] = {};
          }

          acc[index][property] = value;

          return acc;
        }, {});

        const newData = [...dataSource];
        Object.keys(resultObj).forEach((key) => {
          const index = dataSource.findIndex((item) => item.key === Number(key));
          const dataSourceIndex = { ...dataSource[index] };
          if (dataSourceIndex.status === 'reverse') {
            const indexItemCancel = dataSource.findIndex(
              (item) => item.id === dataSourceIndex.id && item.isDelete,
            );

            const indexItemUpdate = dataSource.findIndex(
              (item) =>
                item.id === dataSourceIndex.id && item.status === 'update' && !item.isDelete,
            );

            if (indexItemUpdate >= 0) {
              newData[indexItemUpdate].reverse_purchase_date = form
                .getFieldValue(`${key}.purchase_date`)
                ?.format('YYYY-MM-DD');
            } else if (indexItemCancel >= 0) {
              newData[indexItemCancel].purchase_date = form
                .getFieldValue(`${key}.purchase_date`)
                ?.format('YYYY-MM-DD');
            }
          }
          newData[index] = { ...dataSource[index], ...resultObj[key] };
        });

        // Filter data change
        const dataChange = newData
          .filter((item) => listItemChange.includes(item.key.toString()))
          .filter((item) => item.status !== 'reverse' && item.isRemoveToSubmitForm !== true)
          .map((item) => {
            if (!item.status) {
              item.status = 'edit';
            }
            delete item.purchase_invoice_id;
            delete item.status_cancel;
            delete item.status_update;
            delete item.created_by;
            delete item.updated_by;
            delete item.deleted_by;
            delete item.created_at;
            delete item.updated_at;
            delete item.deleted_at;
            delete item.index_column;
            return { ...item, purchase_date: moment(item.purchase_date)?.format('YYYY-MM-DD') };
          });

        const responseUpdate = await createPurchaseInvoiceItem({
          data: {
            purchase_invoice: {
              travel_id: currentTour?.id,
              business_partner_id: currentCompany?.id,
              id: purchaseInvoiceId ? Number(purchaseInvoiceId) : undefined,
            },
            purchase_invoice_item: dataChange,
          },
        });
        if (
          responseUpdate.status === STATUS_CODE.SUCCESSFUL ||
          responseUpdate.status === STATUS_CODE.CREATED
        ) {
          openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
          // Reset
          setListItemChange([]);
          setListItemCreate([]);
          setIsLoading(false);

          getPurchaseInvoice(purchaseInvoiceId);
        } else if (responseUpdate.status === STATUS_CODE.CONFLICT) {
          refModalDataChangedBeforeUpdate.current.open();
        } else {
          openNotificationFail(
            responseUpdate.error?.data?.errors?.message || MESSAGE_ALERT.EDIT_FAILED,
          );
        }
      } else if (listItemDelete.length > 0) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        getPurchaseInvoice(purchaseInvoiceId);
      }
      // End update
    } catch (errInfo) {
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      setIsLoading(false);
      console.log('Save failed:', errInfo);
    }
    setIsLoading(false);
  };

  const handleCheckBeforeSave = async () => {
    try {
      await form.validateFields();
      if (
        detailDataPurchaseInvoice?.total_purchase &&
        detailDataPurchaseInvoice?.total_purchase > 0
      ) {
        const key = `open${Date.now()}`;
        const btn = (
          <Space>
            <BasicButton
              onClick={() => {
                notification.close(key);
              }}
              styleType={'back'}
              className={`w-[164px] flex items-center space-x-[8px] justify-center`}
            >
              {TEXT_ACTION.NO}
            </BasicButton>
            <BasicButton
              onClick={() => {
                notification.close(key);
                onSave();
              }}
              styleType="accept"
              className="w-[164px] flex items-center space-x-[8px] justify-center"
            >
              {TEXT_ACTION.YES}
            </BasicButton>
          </Space>
        );
        const listSourceWithoutReverse = dataSource.filter((item) => item.status !== 'reverse');
        const listSourceDataOrigin = detailDataPurchaseInvoice?.purchase_invoice_item;

        // Check các item lưu hiện tại có item nào bị giảm tiền nhỏ hơn item được liên kết payment thì báo lỗi
        const hasItemInPayment = listSourceWithoutReverse.some(
          (item) =>
            item?.total_payment > 0 &&
            Number(form.getFieldValue(`${item.key}.amount_including_tax`) ?? 0) -
              Number(form.getFieldValue(`${item.key}.r_amount`) ?? 0) <
              item?.total_payment,
        );
        if (hasItemInPayment) {
          openNotificationFail('支払済みの金額を下回っています。支払伝票から修正してください');
          return;
        }
        if (
          listSourceWithoutReverse.length === listSourceDataOrigin?.length &&
          sumAmountIncludingTax > detailDataPurchaseInvoice?.total_payment
        ) {
          notification.open({
            message: '仕入明細',
            description: '支払処理済みの明細ですが、本当に修正してもいいですか？',
            btn,
            key,
            duration: 0,
            placement: 'top',
          });
        } else if (listSourceWithoutReverse.length > listSourceDataOrigin?.length) {
          onSave();
        } else if (
          listSourceWithoutReverse.length === listSourceDataOrigin?.length &&
          sumAmountIncludingTax >= detailDataPurchaseInvoice?.total_purchase
        ) {
          notification.open({
            message: '仕入明細',
            description: '支払処理済みの明細ですが、本当に修正してもいいですか？',
            btn,
            key,
            duration: 0,
            placement: 'top',
          });
        } else {
          onSave();
        }
      } else {
        onSave();
      }
    } catch (error) {
      console.log('error', error);
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
    }
  };

  const onCreateItem = () => {
    if (!currentCompany || !currentTour) {
      window.alert(MESSAGE_ALERT.CREATE_INVOICE_ITEM_NO_HAVE_TOUR_AND_SUPPLIER);
      return;
    }
    const lastIndex = dataSource.length + 1 + countItemDeleted;
    try {
      const newDataSource = [...dataSource];
      newDataSource.push({
        key: lastIndex,
        subject_id: undefined,
        summary_subject_name: undefined,
        product_name: undefined,
        product_name_en: undefined,
        unit_price: undefined,
        quantity: undefined,
        tax_inclusion_type: 1,
        tax_category_code: undefined,
        tax_rate: undefined,
        amount_excluding_tax: undefined,
        consumption_tax: undefined,
        accounting_linked: 0,
        r_rate: currentCompany?.r_rate,
        r_amount: undefined,
        qualified_business_type: currentCompany?.qualified_invoice_issuer_type,
        invoice_number: moment().format('YYMM') + formatInvoiceNumber(lastIndex),
        memo: undefined,
        status: 'create',
      });
      form.setFieldValue(`${lastIndex}.r_rate`, currentCompany?.r_rate ?? 0);
      form.setFieldValue(`${lastIndex}.tax_inclusion_type`, 1);
      if (currentTour?.return_date) {
        form.setFieldValue(`${lastIndex}.purchase_date`, moment(currentTour?.return_date));
      }
      form.setFieldValue(
        `${lastIndex}.qualified_business_type`,
        currentCompany?.qualified_invoice_issuer_type,
      );
      setListItemCreate([...listItemCreate, lastIndex.toString()]);
      setListItemChange([...listItemChange, lastIndex.toString()]);
      setDataSource([...newDataSource]);
    } catch (error) {
      console.log('onCreateItem error', error);
    }
  };

  const onHandleRevert = () => {
    try {
      const isCheckingReverse = checkWhenReverse();
      if (isCheckingReverse) {
        return;
      }
      const isChecking = itemCreateNewInListItemSelected();
      if (isChecking) {
        refModalConfirmSaveData.current.open();
        return;
      }
      const newDataSource = [...dataSource];
      const listIndexItemChange = [...selectedRowKeys].map((item) =>
        newDataSource.findIndex((data) => data.key === item),
      );
      const listBlockNew = [...listItemBlock];
      const listItemChangeNew = [...listItemChange];
      listIndexItemChange.forEach((indexItemChange, index) => {
        newDataSource[indexItemChange] = {
          ...dataSource[indexItemChange],
          status: 'cancel',
          line_change: dataSource[indexItemChange].key,
          isDelete: true,
        };
        listItemChangeNew.push((indexItemChange + 1).toString());

        listBlockNew.push(indexItemChange + 1);

        Object.keys(newDataSource[indexItemChange]).forEach((key) => {
          const keyForm = `${newDataSource[indexItemChange].key}.${key}`;
          form.setFieldValue(keyForm, newDataSource[indexItemChange][key]);
        });
        //reverse item
        const amount_excluding_tax = Number(dataSource[indexItemChange]?.amount_excluding_tax) * -1;
        const consumption_tax = Number(dataSource[indexItemChange]?.consumption_tax) * -1;
        const amount_including_tax = Number(dataSource[indexItemChange]?.amount_including_tax) * -1;
        const r_amount = Number(dataSource[indexItemChange]?.r_amount) * -1;
        const lastIndex = dataSource.length + 1 + countItemDeleted;
        newDataSource.push({
          ...dataSource[indexItemChange],
          isDisableForm: true,
          isRemoveToSubmitForm: true,
          status: 'reverse',
          key: lastIndex + index * 1,
          quantity: dataSource[indexItemChange].quantity * -1,
          memo: dataSource[indexItemChange].key + '行目の取消',
          amount_excluding_tax,
          consumption_tax,
          amount_including_tax,
          accounting_linked: 0,
          r_amount,
        });
        Object.keys(newDataSource[newDataSource.length - 1]).forEach((key) => {
          const keyForm = `${newDataSource[newDataSource.length - 1].key}.${key}`;
          form.setFieldValue(keyForm, newDataSource[newDataSource.length - 1][key]);
        });
        listBlockNew.push(lastIndex + index * 1);
      });
      setListItemChange([...listItemChangeNew]);
      setListItemBlock([...listBlockNew]);
      setSelectedRowKeys([]);
      setDataSource([...newDataSource]);
    } catch (error) {
      console.log('onHandleRevert error', error);
    }
  };

  const onHandleModify = () => {
    try {
      const isCheckingReverse = checkWhenReverse();
      if (isCheckingReverse) {
        return;
      }
      const isChecking = itemCreateNewInListItemSelected();
      if (isChecking) {
        refModalConfirmSaveData.current.open();
        return;
      }
      const newDataSource = [...dataSource];
      const listIndexItemChange = [...selectedRowKeys].map((item) =>
        newDataSource.findIndex((data) => data.key === item),
      );
      const listBlockNew = [...listItemBlock];
      const listItemChangeNew = [...listItemChange];
      const listItemCreateNew = [...listItemCreate];

      listIndexItemChange.forEach((indexItemChange, index) => {
        newDataSource[indexItemChange] = {
          ...dataSource[indexItemChange],
          isRemoveToSubmitForm: true,
          isDelete: true,
        };

        listItemChangeNew.push((indexItemChange + 1).toString());
        listBlockNew.push(dataSource[indexItemChange].key);
        const lastIndex = dataSource.length + 1 + countItemDeleted;
        newDataSource.push({
          ...dataSource[indexItemChange],
          isDelete: true,
          key: lastIndex + index * 2,
          status: 'reverse',
          quantity: dataSource[indexItemChange].quantity * -1,
          memo: dataSource[indexItemChange].key + '行目の修正',
          amount_excluding_tax: dataSource[indexItemChange].amount_excluding_tax * -1,
          consumption_tax: dataSource[indexItemChange].consumption_tax * -1,
          amount_including_tax: dataSource[indexItemChange].amount_including_tax * -1,
          r_amount: dataSource[indexItemChange].r_amount * -1,
          accounting_linked: 0,
        });
        listBlockNew.push(lastIndex + index * 2);
        // new value reverse
        Object.keys(newDataSource[newDataSource.length - 1]).forEach((key) => {
          const keyForm = `${newDataSource[newDataSource.length - 1].key}.${key}`;
          form.setFieldValue(keyForm, newDataSource[newDataSource.length - 1][key]);
        });
        newDataSource.push({
          ...dataSource[indexItemChange],
          key: lastIndex + 1 + index * 2,
          memo: dataSource[indexItemChange].key + '行目の修正',
          line_change: dataSource[indexItemChange].key,
          isDisableForm: false,
          status: 'update',
          accounting_linked: 0,
        });
        listItemCreateNew.push((lastIndex + 1 + index * 2).toString());
        listItemChangeNew.push((lastIndex + 1 + index * 2).toString());
        // save new value
        Object.keys(newDataSource[newDataSource.length - 1]).forEach((key) => {
          const keyForm = `${newDataSource[newDataSource.length - 1].key}.${key}`;
          const keyFormNewValue = `${newDataSource[indexItemChange].key}.${key}`;
          const newValue = form.getFieldValue(keyFormNewValue);
          form.setFieldValue(keyForm, newValue);
        });
        form.setFieldValue(
          `${newDataSource[newDataSource.length - 1].key}.memo`,
          dataSource[indexItemChange].key + '行目の修正',
        );
        form.setFieldValue(`${newDataSource[newDataSource.length - 1].key}.status`, 'update');
        // new value back to old
        Object.keys(newDataSource[indexItemChange]).forEach((key) => {
          const keyForm = `${newDataSource[indexItemChange].key}.${key}`;
          form.setFieldValue(keyForm, newDataSource[indexItemChange][key]);
        });
      });
      setListItemCreate([...listItemCreateNew]);
      setListItemChange([...listItemChangeNew]);
      setListItemBlock([...listBlockNew]);
      setSelectedRowKeys([]);
      setDataSource([...newDataSource]);
    } catch (error) {
      console.log('onHandleModify error', error);
    }
  };

  useEffect(() => {
    let amount_excluding_tax: number = 0;
    let consumption_tax: number = 0;
    let amount_including_tax: number = 0;
    let r_amount: number = 0;
    dataSource.forEach((item) => {
      amount_excluding_tax += roundNumber(
        Number(form.getFieldValue(`${item.key}.amount_excluding_tax`) || 0),
      );
      consumption_tax += roundNumber(
        Number(form.getFieldValue(`${item.key}.consumption_tax`) || 0),
      );
      amount_including_tax += roundNumber(
        Number(form.getFieldValue(`${item.key}.amount_including_tax`) || 0),
      );
      r_amount += roundNumber(Number(form.getFieldValue(`${item.key}.r_amount`) || 0));
    });

    setSumAmountExcludingTax(amount_excluding_tax);
    setSumAmountConsumptionTax(consumption_tax);
    setSumAmountIncludingTax(amount_including_tax);
    setSumRAmount(r_amount);
  }, [listItemChange, listItemCreate, listItemDelete]);

  const isNoChange = useMemo(() => {
    if (listItemDelete.length === 0 && listItemChange.length === 0 && detailDataPurchaseInvoice?.id)
      return true;
    return false;
  }, [listItemDelete, listItemChange, detailDataPurchaseInvoice]);

  const refreshDataItem = () => {
    setDataSource([]);
    setListItemBlock([]);
    setListItemCreate([]);
    setListItemChange([]);
    setListItemDelete([]);
    setSelectedRowKeys([]);
  };

  return (
    <Spin spinning={isLoading}>
      <PageContainer>
        {contextHolder}
        <HeaderInformation
          setCurrentTour={setCurrentTour}
          setCurrentCompany={setCurrentCompany}
          currentTour={currentTour}
          currentCompany={currentCompany}
          purchaseSlipId={detailDataPurchaseInvoice?.purchase_invoice_id}
          sumAmountIncludingTax={detailDataPurchaseInvoice?.total_purchase ?? 0}
          rAmount={sumAmountIncludingTax - sumRAmount}
        />
        <div className="mt-6 flex justify-end">
          <HeaderAction
            selectedRowKeys={selectedRowKeys}
            onHandleModify={onHandleModify}
            onHandleRevert={onHandleRevert}
            confirmDeleteRow={confirmDeleteRow}
            onCreateItem={onCreateItem}
          />
        </div>
        <div className="p-2 rounded-xl bg-white mt-6">
          <TableTax
            isEditPage={isEditPage}
            isLoading={isLoading}
            form={form}
            dataSource={dataSource}
            listItemChange={listItemChange}
            setListItemChange={setListItemChange}
            selectedRowKeys={selectedRowKeys}
            setSelectedRowKeys={setSelectedRowKeys}
            listItemBlock={listItemBlock}
            optionsTaxCategory={optionsTaxCategory}
            dataTaxCategory={dataTaxCategory}
            optionsAccountCodeList={optionsAccountCodeList}
            dataAccountCodeList={dataAccountCodeList}
            dataSummarySubject={dataSummarySubject}
            sumAmountExcludingTax={sumAmountExcludingTax}
            sumAmountConsumptionTax={sumAmountConsumptionTax}
            sumAmountIncludingTax={sumAmountIncludingTax}
            sumRAmount={sumRAmount}
            optionTaxInclusionType={optionTaxInclusionType}
            optionQualifiedBusinessCategory={optionQualifiedBusinessCategory}
          />
        </div>

        <div className="flex gap-6 justify-center mt-10">
          <BasicButton
            styleType="noneOutLine"
            disabled={isLoading}
            className="!w-[290px]  !bg-white flex justify-center items-center"
            onClick={() => {
              history.goBack();
            }}
          >
            <Image preview={false} src={IconBack} width={8} />
            <p className="ml-2">{TEXT_ACTION.RETURN}</p>
          </BasicButton>
          <BasicButton
            disabled={isLoading || isNoChange}
            styleType="accept"
            className={`!w-[290px] flex justify-center items-center ${
              isLoading || isNoChange ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
            }`}
            onClick={handleCheckBeforeSave}
          >
            <Image preview={false} src={IconSave} width={12} height={14} />
            <p>{TEXT_ACTION.SAVE}</p>
          </BasicButton>
        </div>

        <BasicFormModal
          ref={refModalSelectTour}
          content={
            <FormSelectTour
              onSelect={setCurrentTour}
              onClose={() => refModalSelectTour.current.close()}
            />
          }
          className="!w-[1300px] [&_.ant-modal-body]:!px-2"
          title="旅行"
          isValidate={true}
          hideListButton={true}
        />
        <BasicFormModal
          ref={refModalSelectCompany}
          content={
            <FormSelectCompany
              target={2}
              isCompanyActive={true}
              onSelect={setCurrentCompany}
              onClose={() => refModalSelectCompany.current.close()}
            />
          }
          className="!w-[1200px] [&_.ant-modal-body]:!px-2"
          title="取引先名"
          isValidate={true}
          hideListButton={true}
        />
        <BasicModal
          ref={refModalConfirmChangeTour}
          title={'警告'}
          content={<>この変更により、入力したすべてのデータが更新されます。続行しますか?</>}
          okText="同意する"
          onSubmit={() => {
            refreshDataItem();
            refModalConfirmChangeTour.current?.close();
            refModalSelectTour.current?.open();
          }}
        />
        <BasicModal
          ref={refModalConfirmSaveData}
          title={'警告'}
          content={
            <>
              選択したデータには新たにデータが追加されました。この操作を実行する前に、データの保存を実行してください。
            </>
          }
          okText="理解した"
          hideCloseButton={true}
          onSubmit={() => {
            refModalConfirmSaveData.current?.close();
          }}
        />
        <BasicModal
          ref={refModalDataChangedBeforeUpdate}
          title={'警告'}
          content={<>データが変更されました。続行する前にデータを再読み込みしてください。</>}
          okText="リロード"
          hideCloseButton={true}
          onSubmit={() => {
            history.go(0);
            refModalDataChangedBeforeUpdate.current?.close();
          }}
        />
      </PageContainer>
    </Spin>
  );
};

export default InvoicesPage;
