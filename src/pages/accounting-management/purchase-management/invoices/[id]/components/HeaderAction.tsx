import IconCancel from '@/assets/imgs/common-icons/cancel.svg';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import IconEdit from '@/assets/imgs/common-icons/edit-blue.svg';
import BasicButton from '@/components/Commons/BasicButton';
import OverwriteSaveSVG from '@/components/SVG/OverwriteSaveSVG';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import { PlusOutlined } from '@ant-design/icons';
import { Image, Popconfirm } from 'antd';

const HeaderAction = ({
  confirmDeleteRow,
  selectedRowKeys,
  onCreateItem,
  onHandleRevert,
  onHandleModify,
}: {
  onCreateItem: () => void;
  onHandleRevert: () => void;
  confirmDeleteRow: () => void;
  onHandleModify: () => void;
  selectedRowKeys: React.Key[];
}) => {
  return (
    <div className="flex gap-x-[12px]">
      <BasicButton
        className="w-[84px] flex justify-center items-center !border-[#225DE0] !bg-white hover:shadow-md hover:opacity-70 "
        styleType="noneOutLine"
        disabled={selectedRowKeys.length === 0}
        onClick={() => onHandleModify()}
      >
        <Image preview={false} src={IconEdit} width={14} height={14} />
        <p className="text-xs !ml-1 !text-[#225DE0]">{TEXT_ACTION.MODIFY}</p>
      </BasicButton>
      <BasicButton
        className="w-[84px] flex justify-center items-center !border-[#FDAF2E] !text-[#FDAF2E] !bg-white hover:shadow-md hover:opacity-70"
        styleType="noneOutLine"
        disabled={selectedRowKeys.length === 0}
        onClick={() => onHandleRevert()}
      >
        <Image preview={false} src={IconCancel} width={16} height={16} />
        <p className="text-xs !ml-1 text-[#FDAF2E]"> {TEXT_ACTION.UNDO}</p>
      </BasicButton>
      <Popconfirm
        title={TEXT_WARNING.Are_you_sure_you_want_to_delete_this_item}
        disabled={selectedRowKeys.length === 0}
        onConfirm={() => confirmDeleteRow()}
        okText={TEXT_ACTION.DELETE}
        cancelText={TEXT_ACTION.CANCEL}
      >
        <BasicButton
          styleType="danger"
          disabled={selectedRowKeys.length === 0}
          className=" w-[84px] hover:shadow-md hover:opacity-70 !border-[#ff4d4f] !text-[#ff4d4f] !bg-white"
        >
          <Image preview={false} src={IconDelete} width={15} height={14} />
          <p className="text-xs !ml-1">{TEXT_ACTION.DELETE}</p>
        </BasicButton>
      </Popconfirm>

      <BasicButton
        icon={<PlusOutlined />}
        className="flex items-center justify-center"
        styleType="accept"
        onClick={onCreateItem}
      >
        {TEXT_ACTION.CREATE_NEW}
      </BasicButton>
    </div>
  );
};

export default HeaderAction;
