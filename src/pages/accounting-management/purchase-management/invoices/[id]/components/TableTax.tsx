import type { AggregationItemDetailType } from '@/apis/master/AggregationItem';
import type { SubjectMasterDetailType } from '@/apis/master/subjectMaster';
import type { DatumGetListTaxCategory } from '@/apis/master/taxCategory';
import BasicModal from '@/components/Commons/BasicModal';
import BasicTable from '@/components/Commons/BasicTable';
import EditableCell from '@/components/Commons/EditableCell';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { taxCodeNotApplicable } from '@/constants/data';
import { formatCurrency } from '@/utils/utils';
import type { FormInstance } from 'antd';
import { Form, Table } from 'antd';
import type { FieldData } from 'rc-field-form/lib/interface';
import React, { useRef } from 'react';

// eslint-disable-next-line max-lines-per-function
const HomePage = ({
  form,
  isEditPage,
  isLoading,
  dataSource,
  listItemChange,
  setListItemChange,
  selectedRowKeys,
  setSelectedRowKeys,
  listItemBlock,
  optionsTaxCategory,
  dataTaxCategory,
  optionsAccountCodeList,
  dataAccountCodeList,
  dataSummarySubject,
  sumAmountExcludingTax,
  sumAmountConsumptionTax,
  sumAmountIncludingTax,
  optionTaxInclusionType,
  optionQualifiedBusinessCategory,
  sumRAmount,
}: {
  isEditPage: boolean;
  isLoading: boolean;
  form: FormInstance<any>;
  dataSource: any[];
  listItemChange: string[];
  setListItemChange: (val: string[]) => void;
  selectedRowKeys: React.Key[];
  setSelectedRowKeys: (val: React.Key[]) => void;
  listItemBlock: number[];
  optionsTaxCategory: { value: any; label: string }[];
  dataTaxCategory: DatumGetListTaxCategory[];
  optionsAccountCodeList: { value: any; label: string }[];
  dataAccountCodeList: SubjectMasterDetailType[];
  dataSummarySubject: AggregationItemDetailType[];
  sumAmountExcludingTax: number;
  sumAmountConsumptionTax: number;
  sumAmountIncludingTax: number;
  sumRAmount: number;
  optionTaxInclusionType: { value: any; label: string }[];
  optionQualifiedBusinessCategory: { value: any; label: string }[];
}) => {
  const refModalConfirmChangePage = useRef(null);
  //special field allway call change when validate form
  const oldFirstSubjectId = Form.useWatch('1.purchase_date', form);

  const handleListItemChange = (changeField: FieldData[]) => {
    const nameFieldChange = changeField?.[0]?.name?.[0];
    const keyChange = nameFieldChange.split('.')[0];
    const valueChange = changeField?.[0]?.value;
    const currentTaxInclusionType = form.getFieldValue(`${keyChange}.tax_inclusion_type`);
    const changePrice = () => {
      let amount_excluding_tax = 0;
      let consumption_tax = 0;
      let amount_including_tax = 0;
      const tax_inclusion_type = Number(form.getFieldValue(`${keyChange}.tax_inclusion_type`) || 0);
      const r_rate = form.getFieldValue(`${keyChange}.r_rate`);
      if (tax_inclusion_type || tax_inclusion_type === 0) {
        if (tax_inclusion_type === 2) {
          amount_excluding_tax =
            Number(form.getFieldValue(`${keyChange}.unit_price`) || 0) *
            Number(form.getFieldValue(`${keyChange}.quantity`) || 0);
          consumption_tax =
            (amount_excluding_tax * Number(form.getFieldValue(`${keyChange}.tax_rate`) || 0)) / 100;
          amount_including_tax = amount_excluding_tax + consumption_tax;
        } else {
          amount_including_tax =
            Number(form.getFieldValue(`${keyChange}.unit_price`) || 0) *
            Number(form.getFieldValue(`${keyChange}.quantity`) || 0);
          amount_excluding_tax =
            amount_including_tax /
            (1 + Number(form.getFieldValue(`${keyChange}.tax_rate`) || 0) / 100);
          consumption_tax = amount_including_tax - amount_excluding_tax;
        }
        const objUpdate: { [key: string]: any } = {};
        objUpdate[`${keyChange}.amount_excluding_tax`] = amount_excluding_tax;
        objUpdate[`${keyChange}.consumption_tax`] = consumption_tax;
        objUpdate[`${keyChange}.amount_including_tax`] = amount_including_tax;
        objUpdate[`${keyChange}.r_amount`] = (amount_including_tax * r_rate) / 100;
        form.setFieldsValue({
          ...objUpdate,
        });
      }
    };
    if (nameFieldChange === '1.purchase_date') {
      if (valueChange?.toString() === oldFirstSubjectId?.toString()) {
        return;
      }
    }
    if (nameFieldChange.includes('subject_id')) {
      const subjectMaster = dataAccountCodeList.find((item) => item.id === valueChange);
      const summaryItem = dataSummarySubject.find(
        (item) => item.id === Number(subjectMaster?.summary_item_id),
      );
      form.setFieldValue(`${keyChange}.summary_subject_name`, summaryItem?.summary_item_name);
      form.setFieldValue(`${keyChange}.product_name`, subjectMaster?.subject_name);
      form.setFieldValue(`${keyChange}.product_name_en`, subjectMaster?.subject_name_en);
      const taxCategory = dataTaxCategory.find(
        (item) => item.id === Number(subjectMaster?.tax_category_id),
      );
      if (taxCodeNotApplicable.includes(taxCategory.tax_category_code)) {
        form.setFieldValue(`${keyChange}.tax_inclusion_type`, 0);
      } else if (currentTaxInclusionType === 0) {
        form.setFieldValue(`${keyChange}.tax_inclusion_type`, 1);
      }
      if (taxCategory) {
        form.setFieldValue(`${keyChange}.tax_category_code`, taxCategory?.id);
        form.setFieldValue(`${keyChange}.tax_rate`, taxCategory?.tax_rate);
        changePrice();
      }
    } else if (nameFieldChange.includes('tax_category_code')) {
      const taxCategory = dataTaxCategory.find((item) => item.id === valueChange);
      if (
        taxCodeNotApplicable.includes(taxCategory.tax_category_code) &&
        currentTaxInclusionType !== 0
      ) {
        form.setFieldValue(`${keyChange}.tax_inclusion_type`, 0);
      }
      if (
        !taxCodeNotApplicable.includes(taxCategory.tax_category_code) &&
        currentTaxInclusionType === 0
      ) {
        form.setFieldValue(`${keyChange}.tax_inclusion_type`, 1);
      }
      form.setFieldValue(`${keyChange}.tax_rate`, taxCategory.tax_rate);
      changePrice();
    } else if (nameFieldChange.includes('r_rate')) {
      if (isNaN(Number(valueChange))) {
        form.setFieldValue(`${keyChange}.r_rate`, 0);
        form.setFieldValue(`${keyChange}.r_amount`, 0);
        return;
      }
      const valueRate = Math.floor(Number(valueChange) * 10) / 10;
      const amount_including_tax = form.getFieldValue(`${keyChange}.amount_including_tax`);
      const r_amount = (Number(amount_including_tax) * Number(valueRate)) / 100;
      form.setFieldValue(`${keyChange}.r_amount`, r_amount);
    } else if (
      nameFieldChange.includes('unit_price') ||
      nameFieldChange.includes('quantity') ||
      nameFieldChange.includes('tax_rate') ||
      nameFieldChange.includes('tax_inclusion_type')
    ) {
      changePrice();
    }
    setListItemChange([...listItemChange, keyChange]);
  };

  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };

  const onSelectChange = (newSelectedRowKeys: React.Key[]) => {
    const listExpected = newSelectedRowKeys.filter(
      (item) => !listItemBlock.includes(item as number),
    );
    const isSameSelect =
      listExpected.length === selectedRowKeys.length &&
      newSelectedRowKeys.length === dataSource.length &&
      selectedRowKeys.every((item) => listExpected.includes(item));
    if (isSameSelect && selectedRowKeys.length > 0) {
      setSelectedRowKeys([]);
      return;
    }
    setSelectedRowKeys(listExpected);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: onSelectChange,
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'stt',
      key: 'stt',
      width: 60,
      render: (_, record, index) => {
        return <p className="text-center">{index + 1}</p>;
      },
    },
    {
      title: '科目コード',
      dataIndex: 'subject_id',
      key: 'subject_id',
      editable: true,
      width: 180,
      formType: 'select',
      options: optionsAccountCodeList,
      ruleFormItem: [{ required: true, message: 'この項目は必須です' }],
    },
    {
      title: '集計科目名',
      dataIndex: 'summary_subject_name',
      key: 'summary_subject_name',
      editable: true,
      formType: 'input',
      disable: true,
    },
    {
      title: '品名',
      dataIndex: 'product_name',
      key: 'product_name',
      editable: true,
      formType: 'input',
      ruleFormItem: [
        { required: true, message: 'この項目は必須です' },
        // {
        //   validator: (_, value) =>
        //     value && value.trim() !== ''
        //       ? Promise.resolve()
        //       : Promise.reject(new Error('※空白は入力できません。')),
        // },
      ],
    },
    {
      title: '品名（英字）',
      dataIndex: 'product_name_en',
      key: 'product_name_en',
      editable: true,
      formType: 'input',
      ruleFormItem: [
        {
          whitespace: true,
          message: '※空白は入力できません。',
        },
      ],
    },
    {
      title: '伝票計上日',
      dataIndex: 'purchase_date',
      key: 'purchase_date',
      editable: true,
      formType: 'datePicker',
      ruleFormItem: [{ required: true, message: 'この項目は必須です' }],
      width: 150,
    },

    {
      title: '単価',
      dataIndex: 'unit_price',
      key: 'unit_price',
      editable: true,
      formType: 'inputNumber',
      width: 180,
      ruleFormItem: [{ required: true, message: 'この項目は必須です' }],
      inputProps: { isRightAlign: true },
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      editable: true,
      formType: 'inputNumber',
      width: 120,
      ruleFormItem: [{ required: true, message: 'この項目は必須です' }],
      inputProps: { isRightAlign: true },
    },
    {
      title: '内外税',
      dataIndex: 'tax_inclusion_type',
      key: 'tax_inclusion_type',
      editable: true,
      width: 120,
      formType: 'select',
      options: optionTaxInclusionType,
      ruleFormItem: [{ required: true, message: 'この項目は必須です' }],
    },
    {
      title: '適格事業者区分',
      dataIndex: 'qualified_business_type',
      key: 'qualified_business_type',
      width: 140,
      editable: true,
      formType: 'select',
      options: optionQualifiedBusinessCategory,
      ruleFormItem: [{ required: true, message: 'この項目は必須です' }],
    },

    {
      title: '税区分',
      dataIndex: 'tax_category_code',
      key: 'tax_category_code',
      width: 160,
      editable: true,
      formType: 'select',
      options: optionsTaxCategory,
      ruleFormItem: [{ required: true, message: 'この項目は必須です' }],
    },
    {
      title: '税率',
      dataIndex: 'tax_rate',
      key: 'tax_rate',
      editable: true,
      formType: 'inputNumber',
      disable: true,
      width: 84,
      ruleFormItem: [{ required: true, message: 'この項目は必須です' }],
      inputProps: { isRightAlign: true, isPercent: true },
    },
    {
      title: '金額（税抜)',
      dataIndex: 'amount_excluding_tax',
      key: 'amount_excluding_tax',
      editable: true,
      formType: 'inputNumber',
      width: 160,
      inputProps: { isRightAlign: true, allowDecimal: true, isRoundNumber: true },
      ruleFormItem: [{ required: true, message: 'この項目は必須です' }],
    },
    {
      title: '消費税',
      dataIndex: 'consumption_tax',
      key: 'consumption_tax',
      editable: true,
      formType: 'inputNumber',
      width: 140,
      inputProps: { isRightAlign: true, allowDecimal: true, isRoundNumber: true },
      ruleFormItem: [{ required: true, message: 'この項目は必須です' }],
    },
    {
      title: '金額(税込)',
      dataIndex: 'amount_including_tax',
      key: 'amount_including_tax',
      editable: true,
      formType: 'inputNumber',
      width: 160,
      inputProps: { isRightAlign: true, allowDecimal: true, isRoundNumber: true },
      ruleFormItem: [{ required: true, message: 'この項目は必須です' }],
    },
    {
      title: 'R率',
      dataIndex: 'r_rate',
      key: 'r_rate',
      editable: true,
      formType: 'inputNumber',
      width: 84,
      inputProps: { isRightAlign: true, isPercent: true },
    },
    {
      title: 'R額',
      dataIndex: 'r_amount',
      key: 'r_amount',
      editable: true,
      formType: 'inputNumber',
      width: 120,
      inputProps: { isRightAlign: true, allowDecimal: true, isRoundNumber: true },
    },
    // {
    //   title: '請求書番号',
    //   dataIndex: 'invoice_number',
    //   key: 'invoice_number',
    //   width: 120,
    //   render: (_, record) => <></>,
    // },
    {
      title: '会計連携済',
      dataIndex: 'accounting_linked',
      key: 'accounting_linked',
      width: 120,
      render: (_, record) => (
        <p className="text-xs !ml-1">{record.accounting_linked === 1 ? '済' : '未'}</p>
      ),
    },
    {
      title: '備考',
      dataIndex: 'memo',
      key: 'memo',
      editable: true,
      formType: 'input',
    },
  ];

  const columns = defaultColumns.map((col) => {
    if (!col.editable && !isEditPage) {
      return { ...col, width: col.width || 250 };
    }
    return {
      ...col,
      width: col.width || 250,
      onCell: (record: { [key: string]: string }) => {
        let optionsRecord = col.options;
        if (col.key === 'tax_inclusion_type') {
          const taxId = form.getFieldValue(`${record.key}.tax_category_code`);
          const taxCode = dataTaxCategory.find((item) => item.id === taxId)?.tax_category_code;
          if (!taxCodeNotApplicable.includes(taxCode)) {
            optionsRecord = col.options.map((item) => {
              if (item.value === 0) {
                return { ...item, disabled: true };
              }
              return { ...item };
            });
          }
        }
        const disable =
          col.key === 'purchase_date' && record.status === 'reverse'
            ? false
            : record?.isDelete || col.disable || record?.isDisableForm
            ? true
            : false;
        return {
          record,
          editable: col.editable,
          isEditPage,
          disable,
          dataIndex: col.dataIndex,
          title: col.title,
          formType: col.formType,
          inputProps: col.inputProps,
          options: optionsRecord,
          ruleFormItem: col.ruleFormItem,
          form,
        };
      },
    };
  });

  return (
    <>
      <Form
        form={form}
        component={false}
        onFieldsChange={(changeField: FieldData[]) => handleListItemChange(changeField)}
      >
        <BasicTable
          className="!mt-0"
          tableProps={{
            scroll: { x: 1900, y: 800 },
            loading: isLoading,
            rowSelection: rowSelection,
            components: {
              body: {
                cell: EditableCell,
              },
            },
            columns,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            rowKey: 'key',
            rowClassName: getRowClassName,
            summary: (pageData) => (
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={11} />
                <Table.Summary.Cell index={13} className="text-right">
                  合計
                </Table.Summary.Cell>
                <Table.Summary.Cell index={14} colSpan={1} />
                <Table.Summary.Cell index={15} className="text-right">
                  {formatCurrency(sumAmountExcludingTax)}
                </Table.Summary.Cell>
                <Table.Summary.Cell index={16} className="text-right">
                  {formatCurrency(sumAmountConsumptionTax)}
                </Table.Summary.Cell>
                <Table.Summary.Cell index={17} className="text-right">
                  {formatCurrency(sumAmountIncludingTax)}
                </Table.Summary.Cell>
                <Table.Summary.Cell index={18} colSpan={1} />
                <Table.Summary.Cell index={19} className="text-right">
                  {formatCurrency(sumRAmount)}
                </Table.Summary.Cell>
              </Table.Summary.Row>
            ),
          }}
          hasPagination={false}
        />
        <BasicModal
          ref={refModalConfirmChangePage}
          title={'警告'}
          content={<>{MESSAGE_ALERT.CONFIRM_CHANGE_PAGE}</>}
          okText="ページを変更する"
          onSubmit={() => {
            setListItemChange([]);
            refModalConfirmChangePage.current?.close();
          }}
        />
      </Form>
    </>
  );
};

export default HomePage;
