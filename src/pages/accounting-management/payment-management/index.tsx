import type { BaseParams } from '@/@types/request';
import type { PaymentSlipListItem } from '@/apis/accounting/paymentManagement';
import { getListPaymentSlip } from '@/apis/accounting/paymentManagement';
import PageContainer from '@/components/Commons/Page/Container';
import STATUS_CODE from '@/constants/statusCode';
import { useEffect, useState } from 'react';
import HeaderAction from './components/HeaderAction';
import TableListPayment from './components/TableList';
import type { CurrencyMasterDetailType } from '@/apis/master/currencyType';
import { getListCurrencyTypeMaster } from '@/apis/master/currencyType';
import { ITEM_PER_PAGE } from '@/utils/constants';
import { useUrlSearchParams } from 'use-url-search-params';

const initSearchParams = {
  limit: ITEM_PER_PAGE,
  page: '1',
};

const PaymentManagement = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [dataSource, setDataSource] = useState<PaymentSlipListItem[]>([]);
  const [total, setTotal] = useState(0);

  const [optionsCurrencyType, setOptionsCurrencyType] = useState<CurrencyMasterDetailType[]>([]);
  const [parameter, setParameter] = useUrlSearchParams(initSearchParams, undefined, true);

  const handleGetTaxCategoryMaster = async () => {
    const res = await getListCurrencyTypeMaster({
      limit: 'all',
      order: 'asc',
      sort: 'currency_type_code',
    });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      setOptionsCurrencyType(res?.data?.data);
    }
  };

  useEffect(() => {
    handleGetTaxCategoryMaster();
  }, []);

  const onFetchData = async () => {
    setIsLoading(true);
    try {
      const resGetListPurchaseInvoice = await getListPaymentSlip({
        ...parameter,
      });
      if (resGetListPurchaseInvoice.status === STATUS_CODE.SUCCESSFUL) {
        const resData = resGetListPurchaseInvoice.data;
        const dataRender = resData.data.map((item, index) => {
          return {
            ...item,
            key: (Number(parameter.page) - 1) * Number(parameter.limit) + index + 1,
            business_partner_code: item?.business_partner?.business_partner_code,
            business_partner_name: item?.business_partner?.business_partner_name,
            total_amount: (item?.fee ?? 0) + item.payment_amount,
          };
        });
        setDataSource(dataRender);
        setTotal(resData.total);
      }
    } catch (error) {
      console.log('error', error);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    onFetchData();
  }, [
    parameter?.keyword,
    parameter?.limit,
    parameter?.page,
    parameter?.tourId,
    parameter?.currency_type_master_id,
    parameter?.business_partner_id,
    parameter?.payment_start_date,
    parameter?.payment_end_date,
    parameter?.business_partner_code,
  ]);

  return (
    <PageContainer>
      <HeaderAction
        paramSearch={parameter}
        onFetchData={onFetchData}
        setParamSearch={setParameter}
        optionsCurrencyType={optionsCurrencyType}
      />
      <div className="p-2 rounded-xl bg-white mt-6">
        <TableListPayment
          total={total}
          onFetchData={onFetchData}
          isLoading={isLoading}
          dataSource={dataSource}
          paramSearch={parameter}
          setParamSearch={setParameter}
          optionsCurrencyType={optionsCurrencyType}
        />
      </div>
    </PageContainer>
  );
};

export default PaymentManagement;
