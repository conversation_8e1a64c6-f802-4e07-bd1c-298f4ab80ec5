import type { BaseParams } from '@/@types/request';
import { cancelPaymentSlip, type PaymentSlipListItem } from '@/apis/accounting/paymentManagement';
import { CurrencyMasterDetailType } from '@/apis/master/currencyType';
import IconCancel from '@/assets/imgs/common-icons/cancel.svg';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import IconEdit from '@/assets/imgs/common-icons/edit-blue.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicTable from '@/components/Commons/BasicTable';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { formatMoney } from '@/utils';
import { Image, Popconfirm, Popover } from 'antd';
import moment from 'moment';
import { history } from 'umi';

const TableList = ({
  isLoading,
  dataSource,
  paramSearch,
  setParamSearch,
  total,
  optionsCurrencyType,
  onFetchData,
}: {
  isLoading: boolean;
  dataSource: PaymentSlipListItem[];
  paramSearch?: BaseParams;
  setParamSearch?: (val: BaseParams) => void;
  total: number;
  optionsCurrencyType: CurrencyMasterDetailType[];
  onFetchData?: () => void;
}) => {
  const confirmDeleteRow = async (id: number, isLinked: boolean) => {
    try {
      if (isLinked) {
        openNotificationFail(MESSAGE_ALERT.DELETE_NEED_ACCOUNTING_NOT_LINKED);
        return;
      }
      const resDelete = await cancelPaymentSlip(Number(id));
      if (resDelete.status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        onFetchData?.();
      } else if (
        resDelete?.error?.data?.errors?.payment_id === 'Payment Slip is linked with accounting'
      ) {
        openNotificationFail(MESSAGE_ALERT.CANCEL_NEED_ACCOUNTING_LINKED);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      console.log('onHandleDelete error', error);
    }
  };

  const onHandleRevert = async (id: number) => {
    try {
      history.push(`/accounting-management/payment-management/edit/${id}?isRevert=1`);
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      console.log('onHandleRevert error', error);
    }
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'key',
      key: 'key',
      width: 70,
    },
    {
      title: '支払伝票ID',
      dataIndex: 'payment_slip_id',
      key: 'payment_slip_id',
      render: (_, record) => <span>{record?.payment_slip_id}</span>,
      width: 120,
    },
    {
      title: '支払日',
      dataIndex: 'payment_date',
      key: 'payment_date',
      render: (_, record) => (
        <span>{record?.payment_date ? moment(record?.payment_date).format('YYYY/MM/DD') : ''}</span>
      ),
      width: 120,
    },
    {
      title: '仕入先ID',
      dataIndex: 'business_partner_code',
      key: 'business_partner_code',
      width: 120,
    },
    {
      title: '仕入先名',
      dataIndex: 'business_partner_name',
      key: 'business_partner_name',
      width: 280,
    },
    {
      title: '支払種別',
      dataIndex: 'currency_type_master_id',
      key: 'currency_type_master_id',
      render: (_, record) => (
        <div>
          {optionsCurrencyType && record.currency_type_master_id
            ? optionsCurrencyType.find((item) => item.id === record.currency_type_master_id)
                ?.currency_type_name
            : ''}
        </div>
      ),
      width: 180,
    },
    {
      title: '支払額',
      dataIndex: 'payment_amount',
      key: 'payment_amount',
      render: (_, record) => (
        <div className="text-right">
          {record.payment_amount !== null ? formatMoney(record?.payment_amount) : 0}
        </div>
      ),
      width: 140,
    },
    {
      title: '手数料',
      dataIndex: 'fee',
      key: 'fee',
      render: (_, record) => (
        <div className="text-right">{record.fee !== null ? formatMoney(record?.fee) : 0}</div>
      ),
      width: 120,
    },
    {
      title: '合計金額',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (_, record) => (
        <div className="text-right">
          {record.total_amount !== null ? formatMoney(record?.total_amount) : 0}
        </div>
      ),
      width: 140,
    },
    {
      title: '会計連携済',
      dataIndex: 'accounting_linked',
      key: 'accounting_linked',
      render: (_, record) => (
        <p className="text-xs !ml-1">{record.accounting_linked === 1 ? '済' : '未'}</p>
      ),
      width: 120,
    },

    {
      title: '',
      dataIndex: 'groupAction',
      key: 'groupAction',
      width: 280,
      render: (_, record) => (
        <div className="flex gap-4">
          <BasicButton
            onClick={() =>
              history.push(
                `/accounting-management/payment-management/edit/${record?.id}?exclude_zero_value=${record.is_cancelled}`,
              )
            }
            styleType="danger"
            className="!h-[24px] w-[76px] border-[transparent] hover:!border-[#225DE0] !text-[#225DE0]"
          >
            <Image preview={false} src={IconEdit} width={12} height={13} />
            <p className="text-xs !ml-1">{TEXT_ACTION.EDIT}</p>
          </BasicButton>
          {record.accounting_linked === 1 &&
          !(record.is_inversed || record.is_cancelled || record.payment_slip_inverse_id) ? (
            <Popconfirm
              title={
                <div className="whitespace-pre-line">{MESSAGE_ALERT.CONFIRM_CANCEL_DOCUMENT}</div>
              }
              onConfirm={() => onHandleRevert(record.id)}
              okText={TEXT_ACTION.CANCEL_DOCUMENT}
              cancelText={TEXT_ACTION.CANCEL}
            >
              <BasicButton
                styleType="danger"
                className="!h-[24px] w-[98px] border-[transparent] hover:!border-[#FDAF2E] !text-[#FDAF2E]"
              >
                <Image preview={false} src={IconCancel} width={13} height={13} />
                <p className="text-xs !ml-1">{TEXT_ACTION.CANCEL_DOCUMENT}</p>
              </BasicButton>
            </Popconfirm>
          ) : (
            <Popover
              content={
                <>
                  {record.is_inversed === 1 || record.payment_slip_inverse_id
                    ? MESSAGE_ALERT.ALREADY_CANCEL
                    : record.is_cancelled === 1
                    ? MESSAGE_ALERT.ALREADY_DELETE
                    : MESSAGE_ALERT.CANCEL_NEED_ACCOUNTING_LINKED}
                </>
              }
              trigger="hover"
            >
              <BasicButton
                styleType="danger"
                className="!h-[24px] w-[98px] !cursor-not-allowed !border-[transparent] !text-[#FDAF2E] !bg-[#f5f5f5]"
              >
                <Image preview={false} src={IconCancel} width={13} height={13} />
                <p className="text-xs !ml-1">{TEXT_ACTION.CANCEL_DOCUMENT}</p>
              </BasicButton>
            </Popover>
          )}
          {record.accounting_linked !== 1 &&
          !(record.is_inversed || record.is_cancelled || record.payment_slip_inverse_id) ? (
            <Popconfirm
              title={
                <div className="whitespace-pre-line">{MESSAGE_ALERT.CONFIRM_DELETE_DOCUMENT}</div>
              }
              onConfirm={() => confirmDeleteRow(record.id, record.accounting_linked === 1)}
              okText={TEXT_ACTION.DELETE_DOCUMENT}
              cancelText={TEXT_ACTION.CANCEL}
            >
              <BasicButton
                styleType="danger"
                className="!h-[24px] w-[98px] border-[transparent] hover:!border-[#ff4d4f] !text-[#ff4d4f]"
              >
                <Image preview={false} src={IconDelete} width={15} height={14} />
                <p className="text-xs !ml-1">{TEXT_ACTION.DELETE_DOCUMENT}</p>
              </BasicButton>
            </Popconfirm>
          ) : (
            <Popover
              content={
                <>
                  {record.is_inversed === 1 || record.payment_slip_inverse_id
                    ? MESSAGE_ALERT.ALREADY_CANCEL
                    : record.is_cancelled === 1
                    ? MESSAGE_ALERT.ALREADY_DELETE
                    : MESSAGE_ALERT.DELETE_NEED_ACCOUNTING_NOT_LINKED}
                </>
              }
              trigger="hover"
            >
              <BasicButton
                styleType="danger"
                className="!h-[24px] w-[98px] !border-[transparent] !cursor-not-allowed !text-[#ff4d4f] !bg-[#f5f5f5]"
              >
                <Image preview={false} src={IconDelete} width={15} height={14} />
                <p className="text-xs !ml-1">{TEXT_ACTION.DELETE_DOCUMENT}</p>
              </BasicButton>
            </Popover>
          )}
        </div>
      ),
    },
  ];

  return (
    <>
      <BasicTable
        className="!mt-0"
        tableProps={{
          scroll: { x: 1600 },
          loading: isLoading,
          columns: defaultColumns,
          dataSource: dataSource,
          bordered: false,
          pagination: false,
          rowKey: 'id',
        }}
        page={Number(paramSearch.page) as number}
        pageSize={Number(paramSearch.limit) as number}
        onChangePage={(p: number) => {
          setParamSearch({ ...paramSearch, page: p });
        }}
        total={total}
        onSelectPageSize={(v) => setParamSearch({ ...paramSearch, limit: v })}
      />
    </>
  );
};

export default TableList;
