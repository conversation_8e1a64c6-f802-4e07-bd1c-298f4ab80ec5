import type { BusinessPartnerDetailType } from '@/@types/businessPartner';

import type {
  DataPaymentSlipDetailType,
  PaymentSlipItemType,
} from '@/apis/accounting/paymentManagement';
import {
  createPaymentSlip,
  updatePaymentSlip,
  getDetailPaymentSlip,
  getListPaymentSlipItem,
  cancelPaymentSlip,
  inversePaymentSlip,
} from '@/apis/accounting/paymentManagement';
import type { ParamSearchListSaleInvoice } from '@/apis/accounting/saleManagement';
import type { AccountingTravelListType } from '@/apis/accounting/traveList';
import IconBack from '@/assets/imgs/common-icons/back-btn.svg';
import IconCoin from '@/assets/imgs/common-icons/coins-01.svg';
import IconSave from '@/assets/imgs/common-icons/save-white-btn.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicFormModal from '@/components/Commons/BasicFormModal';
import BasicModal from '@/components/Commons/BasicModal';
import PageContainer from '@/components/Commons/Page/Container';
import FormSelectCompany from '@/components/Form/FormSelectCompany';
import { openNotificationFail, openNotificationSuccess } from '@/components/Notification';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_WARNING } from '@/constants/commonText';
import STATUS_CODE from '@/constants/statusCode';
import { Form, Image } from 'antd';
import moment from 'moment';
import { useLocation } from 'react-router';
import { useEffect, useMemo, useRef, useState } from 'react';
import { history, useParams } from 'umi';
import HeaderAction from './components/HeaderAction';
import HeaderInformation from './components/HeaderInformation';
import TableTax from './components/TableTax';
import { BaseParams } from '@/@types/request';

interface RowDataSources {
  key: number;
  tour_name: string;
  departure_date?: string | Date;
  purchase_invoice_item_id: number;
  voucher_posting_date: string | Date;
  total_amount: number;
  business_partner_id: number;
  sales_amount?: null;
  accounting_linked?: number;
  return_date?: string | Date;
  purchase_destination?: string | Date;
  payment_slip_id?: number;
  payment_amount?: number;
  current_payment_amount?: number;
  total_payment_amount?: number;
  unpaid_amount?: number;
  travel?: AccountingTravelListType;
  isDelete?: boolean;
  id?: number;
  status_cancel?: number;
  purchase_invoice_code: string;
  travel_code: string;
  previous_payment_amount?: string | number;
  payment_slip_item_id?: number;
  is_reversed?: number;
  reverse_item_id?: number;
}

const ITEM_PER_PAGE = 30;

const InvoicesPage = () => {
  const paramUrl = useParams();
  const paymentInvoiceId = (paramUrl as { id: string })?.id;
  const { query } = useLocation() as any;
  const excludeZeroValue = (query as { exclude_zero_value: string })?.exclude_zero_value;
  const isRevertQuery = (query as { isRevert: string })?.isRevert;
  const [form] = Form.useForm();
  const [formPayment] = Form.useForm();
  const paymentAmount = Form.useWatch('payment_amount', formPayment);
  const paymentFee = Form.useWatch('fee', formPayment);
  const refModalConfirmChangePage = useRef(null);
  const refModalConfirmSaveData = useRef(null);
  const refModalSelectCompany = useRef(null);
  const refModalConfirmChangeCompany = useRef(null);
  const refModalDataChangedBeforeUpdate = useRef(null);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [currentCompany, setCurrentCompany] = useState<BusinessPartnerDetailType>();
  const [dataSource, setDataSource] = useState<RowDataSources[]>([]);
  const [listItemChange, setListItemChange] = useState<string[]>([]);
  const [sumTotalAmountReceived, setSumTotalAmountReceived] = useState<number>(0);
  const [sumPreviousPaymentAmount, setSumPreviousPaymentAmount] = useState<number>(0);
  const [sumCurrentPaymentAmount, setSumCurrentPaymentAmount] = useState<number>(0);
  const [sumCurrentPaymentAmountCurrentPage, setSumCurrentPaymentAmountCurrentPage] =
    useState<number>(0);
  const [sumUnpaidAmount, setSumUnpaidAmount] = useState<number>(0);
  const [paramSearch, setParamSearch] = useState<ParamSearchListSaleInvoice>({
    limit: ITEM_PER_PAGE,
    page: 1,
    exclude_zero_value: excludeZeroValue === '1' ? undefined : true,
    is_unpaid_amount: paymentInvoiceId ? undefined : true,
  });
  const [params, setParams] = useState<BaseParams>({
    page: 1,
    limit: ITEM_PER_PAGE,
  });

  const [isRevert, setIsRevert] = useState<boolean>(isRevertQuery === '1' ? true : false);

  const [isFirstTimeLoadData, setIsFirstTimeLoadData] = useState<boolean>(true);
  const [dataWithNoParams, setDataWithNoParams] = useState<RowDataSources[]>([]);
  const [allFormData, setAllFormData] = useState<Record<string, any>>({});
  const [dataDetailPayment, setDataDetailPayment] = useState<DataPaymentSlipDetailType>();

  useEffect(() => {
    formPayment.setFieldValue('payment_date', moment());
  }, []);

  const isEditPaymentPage = useMemo(() => {
    if (paymentInvoiceId) return true;
    return false;
  }, [paymentInvoiceId]);

  const isEditPage = useMemo(() => {
    if (dataDetailPayment?.is_cancelled === 1 || dataDetailPayment?.is_inversed === 1) return false;
    return true;
  }, [dataDetailPayment]);

  useEffect(() => {
    const unblock =
      listItemChange.length > 0
        ? history.block((a, b) => {
            if (
              !isEditPaymentPage &&
              a.pathname.includes('accounting-management/payment-management/edit') &&
              b === 'REPLACE'
            ) {
            } else {
              return TEXT_WARNING.leave_page_and_lose_changes;
            }
          })
        : null;

    return () => {
      if (unblock) {
        unblock?.();
      }
    };
  }, [listItemChange, history]);

  const handleChangeCompany = (company: BusinessPartnerDetailType) => {
    if (company?.id === currentCompany?.id) return;
    setIsFirstTimeLoadData(true);
    setCurrentCompany(company);
  };

  const refreshDataItem = () => {
    form.resetFields();
    setDataSource([]);
    setListItemChange([]);
  };

  const formatDataPayment = async (invoice_items: RowDataSources[]) => {
    if (invoice_items) {
      const listBlockNew = [];
      const listCanDeleteAndReverseNew = [];
      const newFormData = {};
      invoice_items.forEach((item, index) => {
        if (item.is_reversed === 1 || item.reverse_item_id) {
          listBlockNew.push(index + 1);
        }
        if (item.accounting_linked === 1 && item?.payment_slip_item_id) {
          listCanDeleteAndReverseNew.push(item?.payment_slip_item_id);
        }
        Object.keys(item).forEach((key) => {
          const keyForm = `${item.key}.${key}`;
          form.setFieldValue(keyForm, item[key]);
          newFormData[keyForm] = item[key];
        });
      });
      setAllFormData(newFormData);
    }
  };

  const formatDataSource = ({
    dataListPaymentItem,
    isLinked,
    isFirstTimeLoad,
    newSourceAll,
    isHandleRevert = false,
  }: {
    dataListPaymentItem: PaymentSlipItemType[];
    isLinked?: number | null;
    isFirstTimeLoad?: boolean;
    newSourceAll?: RowDataSources[];
    isHandleRevert?: boolean;
  }) => {
    if (isFirstTimeLoad) {
      const newSource = dataListPaymentItem.map((item, index) => {
        const total_amount = item?.total_amount ?? 0;
        const payment_amount = item?.payment_amount ?? 0;
        const current_payment_amount =
          (item?.current_payment_amount && item?.current_payment_amount !== 0) || paymentInvoiceId
            ? item?.current_payment_amount * (isHandleRevert ? -1 : 1)
            : undefined;
        const previous_payment_amount =
          payment_amount + (isHandleRevert ? (current_payment_amount ?? 0) * -1 : 0);

        const total_payment_amount = previous_payment_amount + payment_amount;
        const unpaid_amount =
          total_amount - (current_payment_amount ?? 0) - previous_payment_amount;

        return {
          ...item,
          isDelete: item.is_reversed === 1 || item.reverse_item_id ? true : false,
          isDisableForm: isLinked,
          key: index + 1,
          tour_name: item?.tour_name,
          total_amount: total_amount,
          previous_payment_amount: previous_payment_amount,
          current_payment_amount: current_payment_amount,
          total_payment_amount: total_payment_amount,
          unpaid_amount: unpaid_amount,
          accounting_linked: isLinked ?? 0,
          purchase_accounting_linked: item?.accounting_linked,
        };
      });
      return newSource;
    } else {
      const dataClone = newSourceAll?.length > 0 ? [...newSourceAll] : [...dataWithNoParams];
      const newSource = dataClone.filter((dataAllItem) => {
        const itemIncludeDataSource = dataListPaymentItem.find(
          (paymentItem) =>
            paymentItem.purchase_invoice_item_id === dataAllItem.purchase_invoice_item_id,
        );
        if (itemIncludeDataSource) return true;
        return false;
      });
      return newSource;
    }
  };

  const onFetchListPayment = async (companyId: number) => {
    setIsLoading(true);
    if (isFirstTimeLoadData) {
      refreshDataItem();
    }
    try {
      let newSourceAll = [];
      //fetch all data - call 1 times
      if (isFirstTimeLoadData) {
        const resGetAllListSaleInvoice = await getListPaymentSlipItem({
          business_partner_id: companyId,
        });
        if (resGetAllListSaleInvoice.status === STATUS_CODE.SUCCESSFUL) {
          const resData = resGetAllListSaleInvoice.data?.data;
          [...newSourceAll] = formatDataSource({
            dataListPaymentItem: resData,
            isLinked: 0,
            isFirstTimeLoad: true,
          });
          setDataWithNoParams([...newSourceAll]);
          formatDataPayment([...newSourceAll]);
          setIsFirstTimeLoadData(false);
        } else {
          openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
        }
      }
      //fetch by params
      const resGetListSaleInvoice = await getListPaymentSlipItem({
        ...paramSearch,
        business_partner_id: companyId,
      });
      if (resGetListSaleInvoice.status === STATUS_CODE.SUCCESSFUL) {
        const resData = resGetListSaleInvoice.data?.data;
        const newSource = formatDataSource({
          dataListPaymentItem: resData,
          isFirstTimeLoad: false,
          newSourceAll,
        });
        setDataSource(newSource);
      } else {
        openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
      }
    } catch (error) {
      console.log('error', error);
      openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
    }
    setIsLoading(false);
  };

  const onFetchDetailPaymentInvoice = async (id: string) => {
    setIsLoading(true);

    try {
      let newSourceAll = [];
      //fetch all data - call 1 times

      if (isFirstTimeLoadData) {
        refreshDataItem();
        const detailResponseAll = await getDetailPaymentSlip({
          id: Number(id),
        });
        if (detailResponseAll?.data?.data?.id) {
          const dataPaymentResponseAll = detailResponseAll?.data?.data;
          handleChangeCompany(dataPaymentResponseAll?.business_partner);
          formPayment.setFieldsValue({
            memo: isRevert ? '取消済み伝票' : dataPaymentResponseAll?.memo,
            fee: (dataPaymentResponseAll?.fee ?? 0) * (isRevert ? -1 : 1),
            payment_amount: (dataPaymentResponseAll?.payment_amount ?? 0) * (isRevert ? -1 : 1),
            currency_type_master_id: dataPaymentResponseAll?.currency_type_master_id,
            accounting_linked: dataPaymentResponseAll?.accounting_linked,
            payment_date: dataPaymentResponseAll?.payment_date
              ? moment(dataPaymentResponseAll?.payment_date)
              : moment(),
          });
          setDataDetailPayment(dataPaymentResponseAll);
          newSourceAll = formatDataSource({
            dataListPaymentItem: dataPaymentResponseAll.items,
            isLinked: isRevert ? 0 : dataPaymentResponseAll.accounting_linked,
            isFirstTimeLoad: true,
            isHandleRevert: isRevert,
          });

          setDataWithNoParams([...newSourceAll]);
          formatDataPayment([...newSourceAll]);
          setIsFirstTimeLoadData(false);
        } else {
          openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
          return;
        }
      }
      //fetch with params
      const detailResponse = await getDetailPaymentSlip({
        ...paramSearch,
        id: Number(id),
      });
      if (detailResponse?.data?.data?.id) {
        const dataPaymentResponse = detailResponse?.data?.data;

        const newSource = formatDataSource({
          dataListPaymentItem: dataPaymentResponse.items,
          isFirstTimeLoad: false,
          newSourceAll,
        });
        setDataSource([...newSource]);
      } else {
        openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
      }
    } catch (error) {
      console.log('error', error);
      openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
    }
    setIsLoading(false);
  };

  useEffect(() => {
    if (currentCompany?.id && !paymentInvoiceId) {
      setParams({ ...params, page: 1 });
      onFetchListPayment(currentCompany?.id);
    }
  }, [
    currentCompany?.id,
    paymentInvoiceId,
    paramSearch?.keyword,
    paramSearch?.departure_date,
    paramSearch?.return_date,
    paramSearch?.is_unpaid_amount,
  ]);

  useEffect(() => {
    if (paymentInvoiceId) {
      setParams({ ...params, page: 1 });
      onFetchDetailPaymentInvoice(paymentInvoiceId);
    }
  }, [
    paymentInvoiceId,
    paramSearch?.keyword,
    paramSearch?.departure_date,
    paramSearch?.return_date,
    paramSearch?.is_unpaid_amount,
  ]);

  const confirmDeleteRow = async () => {
    try {
      const resDelete = await cancelPaymentSlip(Number(paymentInvoiceId));
      if (resDelete.status === STATUS_CODE.SUCCESSFUL) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        history.replace(
          `/accounting-management/payment-management/edit/${paymentInvoiceId}?exclude_zero_value=1`,
        );
        window.location.reload();
      } else if (
        resDelete?.error?.data?.errors?.payment_id === 'Payment Slip is linked with accounting'
      ) {
        openNotificationFail(MESSAGE_ALERT.CANCEL_NEED_ACCOUNTING_LINKED);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      console.log('onHandleDelete error', error);
    }
  };
  const onHandleRevert = async () => {
    try {
      const resRevert = await inversePaymentSlip(Number(paymentInvoiceId));
      if (resRevert.status === STATUS_CODE.SUCCESSFUL) {
        setIsFirstTimeLoadData(true);
        setIsRevert(false);
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        setTimeout(() => {
          history.replace(
            `/accounting-management/payment-management/edit/${resRevert.data?.data?.payment_id}`,
          );
        }, 500);
      } else if (
        resRevert?.error?.data?.errors?.payment_id === 'Payment Slip is not linked with accounting'
      ) {
        openNotificationFail(MESSAGE_ALERT.CANCEL_NEED_ACCOUNTING_LINKED);
      } else if (
        resRevert?.error?.data?.errors?.payment_id === 'Payment Slip is already inversed'
      ) {
        openNotificationFail(MESSAGE_ALERT.CANCEL_NEED_ACCOUNTING_LINKED);
      } else {
        openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
      console.log('onHandleRevert error', error);
    }
  };

  const onSave = async () => {
    setIsLoading(true);
    try {
      if (isRevert) {
        onHandleRevert();
        return;
      }
      const valuePayment = await formPayment.validateFields();

      // Start Update
      if (Number(paymentAmount ?? 0) + Number(paymentFee ?? 0) !== sumCurrentPaymentAmount) {
        openNotificationFail(MESSAGE_ALERT.PAYMENT_SUM_AMOUNT_AND_FEE_NOT_EQUAL_SUM_CURRENT_AMOUNT);
        setIsLoading(false);
        return;
      }

      await form.validateFields();
      const resultObj = Object.keys(allFormData).reduce((acc, key) => {
        const [index, property] = key.split('.');
        const value = allFormData[key];

        if (!acc[index]) {
          acc[index] = {};
        }

        acc[index][property] = value;

        return acc;
      }, {});

      const newData = [...dataWithNoParams];

      Object.keys(resultObj).forEach((key) => {
        const index = dataWithNoParams.findIndex((item) => item.key === Number(key));
        newData[index] = { ...dataWithNoParams[index], ...resultObj[key] };
      });

      // Filter data change
      const dataChange = newData.map((item) => {
        return {
          payment_slip_item_id: item?.payment_slip_item_id,
          purchase_invoice_item_id: item?.purchase_invoice_item_id,
          current_payment_amount:
            item?.current_payment_amount != undefined ? item?.current_payment_amount : 0,
        };
      });
      let saveItems;
      if (paymentInvoiceId) {
        saveItems = {
          updates: [...dataChange],
        };
      } else {
        saveItems = [...dataChange];
      }
      const dataSubmit = {
        ...valuePayment,
        business_partner_id: currentCompany?.id,
        payment_date: moment(valuePayment.payment_date).format('YYYY-MM-DD'),
        items: saveItems,
        fee: Number(paymentFee ?? 0),
      };
      if (paymentInvoiceId) {
        dataSubmit.id = Number(paymentInvoiceId);
      }
      const handleSaveApi = paymentInvoiceId ? updatePaymentSlip : createPaymentSlip;
      const responseSave = await handleSaveApi(dataSubmit);

      if (
        responseSave.status === STATUS_CODE.SUCCESSFUL ||
        responseSave.status === STATUS_CODE.CREATED
      ) {
        openNotificationSuccess(MESSAGE_ALERT.EDIT_SUCCESS);
        // Reset
        setListItemChange([]);
        setIsLoading(false);

        if (paymentInvoiceId) {
          onFetchDetailPaymentInvoice(paymentInvoiceId);
        } else {
          history.replace(
            `/accounting-management/payment-management/edit/${responseSave.data?.data?.id}`,
          );
        }
      } else if (responseSave.status === STATUS_CODE.CONFLICT) {
        refModalDataChangedBeforeUpdate.current.open();
      } else {
        openNotificationFail(
          responseSave?.error?.data?.errors?.message ?? MESSAGE_ALERT.EDIT_FAILED,
        );
      }
      // End update
    } catch (errInfo) {
      openNotificationFail(MESSAGE_ALERT.EDIT_FAILED);
      setIsLoading(false);
      console.log('Save failed:', errInfo);
    }
    setIsLoading(false);
  };
  const dataSourceWithPageLimit = useMemo(() => {
    const dataSplice = [...dataSource].slice(
      (Number(params.page) - 1) * Number(params.limit),
      Number(params.page) * Number(params.limit),
    );
    return dataSplice;
  }, [dataSource, params.page, params.limit]);

  useEffect(() => {
    let current_payment_amount: number = 0;
    dataWithNoParams.forEach((item) => {
      const keyIndex = item.key;
      current_payment_amount += Number(allFormData[`${keyIndex}.current_payment_amount`] ?? 0);
    });
    setSumCurrentPaymentAmount(current_payment_amount);
  }, [listItemChange, dataWithNoParams, allFormData, isRevert]);

  useEffect(() => {
    let total_amount: number = 0;
    let previous_payment_amount: number = 0;
    let current_payment_amount_current_page: number = 0;
    let unpaid_amount: number = 0;
    dataSourceWithPageLimit.forEach((item) => {
      const keyIndex = item.key;
      total_amount += Number(form.getFieldValue(`${keyIndex}.total_amount`) ?? 0);
      previous_payment_amount += Number(
        form.getFieldValue(`${keyIndex}.previous_payment_amount`) ?? 0,
      );
      current_payment_amount_current_page += Number(
        allFormData[`${keyIndex}.current_payment_amount`] ?? 0,
      );
      unpaid_amount += Number(form.getFieldValue(`${keyIndex}.unpaid_amount`) ?? 0);
    });
    setSumTotalAmountReceived(total_amount);
    setSumPreviousPaymentAmount(previous_payment_amount);
    setSumCurrentPaymentAmountCurrentPage(current_payment_amount_current_page);
    setSumUnpaidAmount(unpaid_amount);
  }, [dataSourceWithPageLimit, allFormData]);

  const sumItemNotInDataSource = () => {
    const otherListChangeNotInDataSource = dataWithNoParams.filter((item) => {
      const isSameKey = dataSourceWithPageLimit.findIndex((itemSource) => {
        return Number(itemSource.key) === Number(item.key);
      });
      return isSameKey === -1;
    });
    let result = 0;
    otherListChangeNotInDataSource.forEach((item) => {
      result += Number(allFormData[`${item.key}.current_payment_amount`] ?? 0);
    });
    return result;
  };

  const handleAutoAllocate = () => {
    const sumOtherItem = sumItemNotInDataSource();
    let totalPaymentAmount = Number(paymentAmount ?? 0) + Number(paymentFee ?? 0) - sumOtherItem;
    const newListItemChange = [...listItemChange];
    let newAllForm = { ...allFormData };
    dataSourceWithPageLimit.forEach((item) => {
      if (totalPaymentAmount >= 0) {
        newListItemChange.push(item.key.toString());
      }
      if (item.isDelete) {
        return true;
      }
      const totalAmountReceived = Number(item.total_amount ?? 0);
      const previousPaymentAmount = Number(item.previous_payment_amount ?? 0);
      const unpaidAmount = totalAmountReceived - previousPaymentAmount;

      if (unpaidAmount >= 0) {
        const currentPaymentAmount = Math.min(totalPaymentAmount, unpaidAmount);
        form.setFieldValue(
          `${item.key}.current_payment_amount`,
          currentPaymentAmount === 0 ? undefined : currentPaymentAmount,
        );
        form.setFieldValue(
          `${item.key}.total_payment_amount`,
          previousPaymentAmount + currentPaymentAmount,
        );
        form.setFieldValue(
          `${item.key}.unpaid_amount`,
          totalAmountReceived - (previousPaymentAmount + currentPaymentAmount),
        );
        totalPaymentAmount -= currentPaymentAmount;
        newAllForm = {
          ...newAllForm,
          [`${item.key}.current_payment_amount`]:
            currentPaymentAmount === 0 ? undefined : currentPaymentAmount,
        };
      } else {
        const currentPaymentAmount = totalPaymentAmount;
        form.setFieldValue(
          `${item.key}.current_payment_amount`,
          currentPaymentAmount === 0 ? undefined : currentPaymentAmount,
        );
        form.setFieldValue(
          `${item.key}.total_payment_amount`,
          previousPaymentAmount + currentPaymentAmount,
        );
        form.setFieldValue(
          `${item.key}.unpaid_amount`,
          totalAmountReceived - (previousPaymentAmount + currentPaymentAmount),
        );
        newAllForm = {
          ...newAllForm,
          [`${item.key}.current_payment_amount`]:
            currentPaymentAmount === 0 ? undefined : currentPaymentAmount,
        };
        totalPaymentAmount -= currentPaymentAmount;
      }
      return true;
    });
    setAllFormData({
      ...newAllForm,
    });
    setListItemChange([...newListItemChange]);
  };

  const onDaftRevert = () => {
    setIsRevert(true);
    const newSourceAll = formatDataSource({
      dataListPaymentItem: dataDetailPayment.items,
      isLinked: dataDetailPayment.accounting_linked,
      isFirstTimeLoad: true,
      isHandleRevert: true,
    });
    formPayment.setFieldsValue({
      fee: dataDetailPayment?.fee * -1,
      payment_amount: dataDetailPayment?.payment_amount * -1,
      accounting_linked: 0,
      memo: '取消済み伝票',
    });

    formatDataPayment([...newSourceAll]);
  };

  return (
    <PageContainer>
      <HeaderInformation
        form={formPayment}
        isEditPaymentPage={isEditPaymentPage}
        isDisabled={!isEditPage}
        isRevert={isRevert}
        onOpenModalSelectCompany={() => {
          if (listItemChange.length === 0) {
            refModalSelectCompany.current.open();
          } else refModalConfirmChangeCompany.current.open();
        }}
        currentCompany={currentCompany}
        setCurrentCompany={handleChangeCompany}
        sumCurrentPaymentAmount={sumCurrentPaymentAmount}
        paymentSlipID={dataDetailPayment?.payment_slip_id}
      />
      <HeaderAction
        listItemChange={listItemChange}
        isLinked={isEditPaymentPage && dataDetailPayment?.accounting_linked === 1}
        isCanceled={
          dataDetailPayment?.is_inversed === 1 || dataDetailPayment?.payment_slip_inverse_id
        }
        isDeleted={dataDetailPayment?.is_cancelled === 1}
        paramSearch={paramSearch}
        setParamSearch={setParamSearch}
        onDaftRevert={onDaftRevert}
        isRevert={isRevert}
        confirmDeleteRow={confirmDeleteRow}
        paymentInvoiceId={paymentInvoiceId}
      />
      <div className="p-2 rounded-xl bg-white mt-6">
        <TableTax
          isEditPage={isEditPage}
          isLoading={isLoading}
          form={form}
          dataSource={dataSourceWithPageLimit}
          total={dataSource.length}
          listItemChange={listItemChange}
          setListItemChange={setListItemChange}
          sumTotalAmountReceived={sumTotalAmountReceived}
          sumPreviousPaymentAmount={sumPreviousPaymentAmount}
          sumCurrentPaymentAmountCurrentPage={sumCurrentPaymentAmountCurrentPage}
          sumUnpaidAmount={sumUnpaidAmount}
          allFormData={allFormData}
          setAllFormData={setAllFormData}
          params={params}
          setParams={setParams}
        />
      </div>

      <div className="flex gap-6 justify-center mt-10">
        <BasicButton
          styleType="noneOutLine"
          disabled={isLoading}
          className="!w-[190px]  !bg-white flex justify-center items-center"
          onClick={() => {
            history.goBack();
          }}
        >
          <Image preview={false} src={IconBack} width={8} />
          <p className="ml-2">{TEXT_ACTION.RETURN}</p>
        </BasicButton>
        <BasicButton
          disabled={isLoading || !isEditPage || isRevert}
          styleType="accept"
          className={`!w-[190px] flex justify-center items-center !bg-[#225DE0] hover:!bg-[#225DE0] !text-white ${
            isLoading || !isEditPage || isRevert ? '!bg-[gray] hover:!bg-[gray] ' : ''
          }`}
          onClick={handleAutoAllocate}
        >
          <Image preview={false} src={IconCoin} width={16} height={16} />
          <p>{TEXT_ACTION.AUTO_ALLOCATE}</p>
        </BasicButton>
        <BasicButton
          disabled={isLoading}
          styleType="accept"
          className={`!w-[190px] flex justify-center items-center ${
            isLoading ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
          }`}
          onClick={onSave}
        >
          <Image preview={false} src={IconSave} width={12} height={14} />
          <p>{TEXT_ACTION.SAVE}</p>
        </BasicButton>
      </div>

      <BasicFormModal
        ref={refModalSelectCompany}
        content={
          <FormSelectCompany
            defaultParams={{ is_use_purchase: 1 }}
            onSelect={handleChangeCompany}
            onClose={() => refModalSelectCompany.current.close()}
            target={4}
          />
        }
        className="!w-[1200px] [&_.ant-modal-body]:!px-2"
        title="取引先名"
        isValidate={true}
        hideListButton={true}
      />
      <BasicModal
        ref={refModalConfirmChangePage}
        title={'警告'}
        content={<>{MESSAGE_ALERT.CONFIRM_CHANGE_PAGE}</>}
        okText="ページを変更する"
        onSubmit={() => {
          setListItemChange([]);
          refModalConfirmChangePage.current?.close();
          history.replace('/accounting-management/payment-management');
        }}
      />

      <BasicModal
        ref={refModalConfirmChangeCompany}
        title={'警告'}
        content={<>この変更により、入力したすべてのデータが更新されます。続行しますか?</>}
        okText="同意する"
        onSubmit={() => {
          refreshDataItem();
          refModalConfirmChangeCompany.current?.close();
          refModalSelectCompany.current?.open();
        }}
      />
      <BasicModal
        ref={refModalConfirmSaveData}
        title={'警告'}
        content={
          <>
            選択したデータには新たにデータが追加されました。この操作を実行する前に、データの保存を実行してください。
          </>
        }
        okText="理解した"
        hideCloseButton={true}
        onSubmit={() => {
          refModalConfirmSaveData.current?.close();
        }}
      />
      <BasicModal
        ref={refModalDataChangedBeforeUpdate}
        title={'警告'}
        content={<>データが変更されました。続行する前にデータを再読み込みしてください。</>}
        okText="リロード"
        hideCloseButton={true}
        onSubmit={() => {
          history.go(0);
          refModalDataChangedBeforeUpdate.current?.close();
        }}
      />
    </PageContainer>
  );
};

export default InvoicesPage;
