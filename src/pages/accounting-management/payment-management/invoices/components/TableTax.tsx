import BasicModal from '@/components/Commons/BasicModal';
import BasicTable from '@/components/Commons/BasicTable';
import EditableCell from '@/components/Commons/EditableCell';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { formatCurrency } from '@/utils/utils';
import type { FormInstance } from 'antd';
import { Form, Table } from 'antd';
import moment from 'moment';
import type { FieldData } from 'rc-field-form/lib/interface';
import { useRef } from 'react';

// eslint-disable-next-line max-lines-per-function
const HomePage = ({
  form,
  isEditPage,
  isLoading,
  dataSource,
  listItemChange,
  setListItemChange,
  sumTotalAmountReceived,
  sumPreviousPaymentAmount,
  sumCurrentPaymentAmountCurrentPage,
  sumUnpaidAmount,
  allFormData,
  setAllFormData,
  params,
  setParams,
  total,
}: {
  isEditPage: boolean;
  isLoading: boolean;
  form: FormInstance<any>;
  dataSource: any[];
  listItemChange: string[];
  setListItemChange: (val: string[]) => void;
  sumTotalAmountReceived: number;
  sumPreviousPaymentAmount: number;
  sumCurrentPaymentAmountCurrentPage: number;
  sumUnpaidAmount: number;
  allFormData: Record<string, any>;
  setAllFormData: (val: Record<string, any>) => void;
  params: Record<string, any>;
  setParams: (val: Record<string, any>) => void;
  total: number;
}) => {
  const refModalConfirmChangePage = useRef(null);
  const oldFirstCurrentPaymentAmount = Form.useWatch('1.current_deposit_amount', form);

  const handleListItemChange = (changeField: FieldData[]) => {
    const nameFieldChange = changeField?.[0]?.name?.[0];
    const keyChange = nameFieldChange.split('.')[0];
    const valueChange = changeField?.[0]?.value;
    if (nameFieldChange === '1.current_payment_amount') {
      if (valueChange?.toString() === oldFirstCurrentPaymentAmount?.toString()) {
        return;
      }
    }
    if (nameFieldChange.includes('current_payment_amount')) {
      const total_amount = Number(form.getFieldValue(`${keyChange}.total_amount`));
      const previous_payment_amount = Number(
        form.getFieldValue(`${keyChange}.previous_payment_amount`),
      );
      const keyFormTotalPaymentAmount = `${keyChange}.total_payment_amount`;
      const total_payment_amount = Number(valueChange) + previous_payment_amount;
      form.setFieldValue(keyFormTotalPaymentAmount, total_payment_amount);
      const keyFormUnpaidAmount = `${keyChange}.unpaid_amount`;

      const unpaidAmount = total_amount - Number(valueChange) - previous_payment_amount;
      form.setFieldValue(keyFormUnpaidAmount, unpaidAmount);
    }

    setListItemChange([...listItemChange, keyChange]);
    setAllFormData({ ...allFormData, [nameFieldChange]: valueChange });
  };

  const getRowClassName = (record) => {
    return record?.isDelete ? 'deleted-row' : '';
  };

  const defaultColumns = [
    {
      title: '#',
      dataIndex: 'stt',
      key: 'stt',
      width: 70,
      render: (_, __, index) => {
        return <div>{(params.page - 1) * params.limit + index + 1}</div>;
      },
    },
    {
      title: '旅行ID',
      dataIndex: 'travel_code',
      key: 'travel_code',
      width: 82,
    },
    {
      title: 'ツアー名',
      dataIndex: 'tour_name',
      key: 'tour_name',
      width: 200,
    },
    // {
    //   title: '出発日',
    //   dataIndex: 'departure_date',
    //   key: 'departure_date',
    //   width: 140,
    //   render: (_, { departure_date }) => (
    //     <>{departure_date ? moment(departure_date).format('YYYY/MM/DD') : ''}</>
    //   ),
    // },
    {
      title: '仕入伝票ID',
      dataIndex: 'purchase_invoice_code',
      key: 'purchase_invoice_code',

      width: 120,
    },
    {
      title: '伝票計上日',
      dataIndex: 'purchase_date',
      key: 'purchase_date',

      width: 100,
      render: (_, { purchase_date }) => <>{moment(purchase_date).format('YYYY/MM/DD')}</>,
    },

    {
      title: '今回支払額',
      dataIndex: 'current_payment_amount',
      key: 'current_payment_amount',
      width: 120,
      editable: true,
      formType: 'inputNumber',
      inputProps: { isRightAlign: true },
    },
    {
      title: '未支払額',
      dataIndex: 'unpaid_amount',
      key: 'unpaid_amount',
      width: 140,
      editable: true,
      formType: 'textOnly',
      // ruleFormItem: [
      //   {
      //     validator: (_, value) =>
      //       Number(value) >= 0
      //         ? Promise.resolve()
      //         : Promise.reject(new Error('数値は0以上でなければなりません')),
      //   },
      // ],
      inputProps: { isRightAlign: true, isNumber: true },
    },

    {
      title: '前回までの支払額',
      dataIndex: 'previous_payment_amount',
      key: 'previous_payment_amount',
      width: 140,
      editable: true,
      formType: 'textOnly',
      inputProps: { isRightAlign: true, isNumber: true },
    },
    {
      title: '総仕入額',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 100,
      editable: true,
      formType: 'textOnly',
      inputProps: { isRightAlign: true, isNumber: true },
    },
    // {
    //   title: '総支払額',
    //   dataIndex: 'total_payment_amount',
    //   key: 'total_payment_amount',
    //   width: 140,
    //   editable: true,
    //   formType: 'textOnly',
    //   inputProps: { isRightAlign: true, isNumber: true },
    // },

    {
      title: '仕入会計連携済',
      dataIndex: 'purchase_accounting_linked',
      key: 'purchase_accounting_linked',
      width: 120,
      render: (_, record) => (
        <p className="text-xs !ml-1">
          <p className="text-xs !ml-1 text-center">
            {record.purchase_accounting_linked === 1 ? '済' : '未'}
          </p>
        </p>
      ),
    },
    // {
    //   title: '会計連携済',
    //   dataIndex: 'accounting_linked',
    //   key: 'accounting_linked',
    //   width: 120,
    //   render: (_, record) => (
    //     <p className="text-xs !ml-1">{record.accounting_linked === 1 ? '済' : '未'}</p>
    //   ),
    // },
  ];

  const columns = defaultColumns.map((col) => {
    if (!col.editable && !isEditPage) {
      return { ...col, width: col.width || 250 };
    }
    return {
      ...col,
      width: col.width || 250,
      onCell: (record: { [key: string]: string }) => ({
        record,
        editable: col.editable,
        isEditPage,
        disable: (record?.isDelete || record?.isDisableForm) ?? false,
        dataIndex: col.dataIndex,
        title: col.title,
        formType: col.formType,
        inputProps: col.inputProps,
        form,
      }),
    };
  });

  return (
    <>
      <Form
        form={form}
        component={false}
        onFieldsChange={(changeField: FieldData[]) => handleListItemChange(changeField)}
      >
        <BasicTable
          className="!mt-0"
          tableProps={{
            scroll: { x: 1600, y: 800 },
            loading: isLoading,
            components: {
              body: {
                cell: EditableCell,
              },
            },
            columns,
            dataSource: dataSource,
            bordered: false,
            pagination: false,
            rowKey: 'key',
            rowClassName: getRowClassName,
            summary: (pageData) => (
              <Table.Summary.Row>
                <Table.Summary.Cell index={0} colSpan={4} />
                <Table.Summary.Cell index={5}>合計</Table.Summary.Cell>

                <Table.Summary.Cell index={6} className="text-right">
                  {formatCurrency(sumCurrentPaymentAmountCurrentPage)}
                </Table.Summary.Cell>
                <Table.Summary.Cell index={7} className="text-right">
                  {formatCurrency(sumUnpaidAmount)}
                </Table.Summary.Cell>
                <Table.Summary.Cell index={8} className="text-right">
                  {formatCurrency(sumPreviousPaymentAmount)}
                </Table.Summary.Cell>
                <Table.Summary.Cell index={9} className="text-right">
                  {formatCurrency(sumTotalAmountReceived)}
                </Table.Summary.Cell>
              </Table.Summary.Row>
            ),
          }}
          page={Number(params.page) as number}
          pageSize={Number(params.limit) as number}
          onChangePage={(p: number) => {
            setParams({ ...params, page: p });
          }}
          total={total}
          onSelectPageSize={(v) => {
            setParams({ ...params, limit: v, page: 1 });
          }}
        />
        <BasicModal
          ref={refModalConfirmChangePage}
          title={'警告'}
          content={<>{MESSAGE_ALERT.CONFIRM_CHANGE_PAGE}</>}
          okText="ページを変更する"
          onSubmit={() => {
            setListItemChange([]);
            refModalConfirmChangePage.current?.close();
          }}
        />
      </Form>
    </>
  );
};

export default HomePage;
