import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import { getListBusinessPartner } from '@/apis/businessPartner';
import { getListCurrencyTypeMaster } from '@/apis/master/currencyType';
import SelectIcon from '@/assets/imgs/common-icons/select-icon.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import BasicNumbericInput from '@/components/Commons/BasicNumbericInput';
import BasicSelect from '@/components/Commons/BasicSelect';
import BasicTextArea from '@/components/Commons/BasicTextArea';
import { TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import { defaultPaymentType } from '@/constants/data';
import STATUS_CODE from '@/constants/statusCode';
import { useDebounce } from '@uidotdev/usehooks';
import type { FormInstance } from 'antd';
import { Form, Image } from 'antd';
import { useEffect, useState } from 'react';

const HeaderInformation = ({
  onOpenModalSelectCompany,
  currentCompany,
  form,
  isDisabled,
  isEditPaymentPage,
  isRevert,
  paymentSlipID,
  setCurrentCompany,
  sumCurrentPaymentAmount,
}: {
  onOpenModalSelectCompany: () => void;
  currentCompany: BusinessPartnerDetailType;
  form: FormInstance<any>;
  isDisabled?: boolean;
  isEditPaymentPage?: boolean;
  isRevert?: boolean;
  paymentSlipID?: number;
  setCurrentCompany: (val: BusinessPartnerDetailType) => void;
  sumCurrentPaymentAmount: number;
}) => {
  const CodeCurrencyNotCheckAmount = '6';
  const [optionsCurrencyType, setOptionsCurrencyType] = useState<
    { value: any; label: string; currency_type_code: string; disabled: boolean }[]
  >([]);
  const [textSearchCompany, setTextSearchCompany] = useState<string>(
    currentCompany?.business_partner_code ?? '',
  );
  const codeCompanyToSearch = useDebounce(textSearchCompany, 300);
  const currencyValue = Form.useWatch('currency_type_master_id', form);
  const paymentAmount = Form.useWatch('payment_amount', form);
  const fee = Form.useWatch('fee', form);

  const handleGetTaxCategoryMaster = async () => {
    const res = await getListCurrencyTypeMaster({
      limit: 'all',
      order: 'asc',
      sort: 'currency_type_code',
    });
    if (res.status === STATUS_CODE.SUCCESSFUL) {
      // setOptionsCurrencyType(res?.data?.data);
      const options = res?.data?.data?.map((item) => {
        return {
          value: item.id,
          label: item.currency_type_name,
          currency_type_code: item.currency_type_code,
          disabled: item.status === 0 || !item.target?.includes?.('4'),
        };
      });
      if (!isEditPaymentPage && options.find((item) => item.value === defaultPaymentType)) {
        form.setFieldValue('currency_type_master_id', defaultPaymentType);
      }
      setOptionsCurrencyType(options);
    }
  };

  useEffect(() => {
    handleGetTaxCategoryMaster();
  }, []);

  useEffect(() => {
    if (currentCompany?.business_partner_code) {
      setTextSearchCompany(currentCompany.business_partner_code);
    }
  }, [currentCompany]);

  useEffect(() => {
    const payment_amount = form.getFieldValue('payment_amount');
    if (payment_amount !== undefined && payment_amount !== null) {
      form.validateFields(['payment_amount']);
    }
  }, [currencyValue]);

  const fetchDataCompany = async () => {
    const resData = await getListBusinessPartner({
      page: 1,
      limit: 1,
      business_partner_code: codeCompanyToSearch,
    });
    if (resData?.data?.data && resData?.data?.total === 1) {
      setCurrentCompany(resData.data.data[0]);
    } else {
      setCurrentCompany(undefined);
    }
  };

  useEffect(() => {
    if (codeCompanyToSearch.length > 2) {
      fetchDataCompany();
    }
  }, [codeCompanyToSearch]);

  useEffect(() => {
    if (paymentAmount !== undefined) {
      const unapplied_amount = Number(paymentAmount) + Number(fee ?? 0) - sumCurrentPaymentAmount;
      form.setFieldsValue({ unapplied_amount });
    }
  }, [paymentAmount, fee, sumCurrentPaymentAmount]);

  return (
    <Form form={form} component={false}>
      <div className="grid grid-flow-col-7 lg:grid-cols-11 gap-6 lg:min-w-[900px]">
        <div className="col-span-1">
          {isDisabled || isEditPaymentPage ? (
            <>
              <p className="mb-2 text-[13px] leading-4 font-medium">仕入先ID</p>
              <div className="h-10 flex items-center">
                <span>{currentCompany?.business_partner_code}</span>
              </div>
            </>
          ) : (
            <BasicInput
              title="仕入先ID"
              className="!h-10"
              placeholder="仕入先ID"
              value={textSearchCompany}
              onChange={(e) => setTextSearchCompany(e.target.value)}
            />
          )}
        </div>
        <div className="col-span-2">
          <p className="mb-2 text-[13px] leading-4 font-medium">
            {TEXT_TITLE.Supplier} <span className="text-[red]">*</span>
          </p>

          {isEditPaymentPage ? (
            <div className="h-10 flex items-center">
              <span>{currentCompany?.business_partner_name}</span>
            </div>
          ) : (
            <BasicButton
              className="!border-[#EBE9FA] flex justify-between !px-[11px] items-center !w-full !bg-white hover:opacity:40"
              onClick={onOpenModalSelectCompany}
            >
              <span
                className={`line-clamp-1 mr-2 ${
                  currentCompany ? '!text-[rgba(0,0,0,0.85)]' : '!text-[rgba(0,0,0,0.3)]'
                }`}
              >
                {currentCompany?.business_partner_name ?? 'すべて'}
              </span>
              <Image src={SelectIcon} preview={false} alt="select icon" className="w-5 h-5" />
            </BasicButton>
          )}
        </div>
        <div className="col-span-2">
          <Form.Item
            className="!mb-0"
            name="currency_type_master_id"
            rules={[{ required: true, message: 'を入力してください' }]}
          >
            <BasicSelect
              className={`${
                isRevert
                  ? '[&_.ant-select-selector]:!bg-[#DCDEE3] [&_.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector]:!text-[#9499A5]'
                  : ''
              }`}
              disabled={isRevert}
              title={TEXT_TITLE.Payment_Type}
              placeholder={TEXT_PLACEHOLDER.Please_select}
              options={optionsCurrencyType}
              required={true}
            />
          </Form.Item>
        </div>

        <div className="col-span-2">
          <Form.Item
            className="!mb-0"
            name="payment_date"
            rules={[{ required: true, message: 'を入力してください' }]}
          >
            <BasicDatePicker
              disabled={isRevert}
              title={TEXT_TITLE.Payment_date}
              className={`!h-10 ${
                isRevert ? '!bg-[#DCDEE3] !text-[#9499A5]' : '!bg-white hover:opacity:40'
              }`}
              required={true}
            />
          </Form.Item>
        </div>

        <div className="col-span-4 row-span-2">
          <Form.Item className="!mb-0" name="memo">
            <BasicTextArea
              disabled={isRevert}
              className={`!h-[128px] ${
                isRevert ? '!bg-[#DCDEE3] !text-[#9499A5]' : '!bg-white hover:opacity:40'
              }`}
              title={TEXT_TITLE.remarks}
              placeholder={TEXT_PLACEHOLDER.remarks}
            />
          </Form.Item>
        </div>
        {paymentSlipID && (
          <div className="col-span-1">
            <div className="flex-1">
              <p className="mb-2 text-[13px] leading-4 font-medium">{TEXT_TITLE.Payment_Id}</p>
              <div className="h-10 flex items-center">
                <p className="">{paymentSlipID}</p>
              </div>
            </div>
          </div>
        )}
        <div className={`flex ${paymentSlipID ? 'col-span-2' : 'col-span-1'} `}>
          <div className="flex-1">
            <p className="mb-2 text-[13px] leading-4 font-medium">{TEXT_TITLE.Accounting_Linked}</p>
            <div className="h-10 flex items-center">
              <p className="">{form.getFieldValue('accounting_linked') === 1 ? '済' : '未'}</p>
            </div>
          </div>
        </div>
        <div className="col-span-2">
          <Form.Item
            className="!mb-0"
            name="payment_amount"
            rules={[
              { required: true, message: 'を入力してください' },
              {
                validator: (_, value) => {
                  const currency_type_master_id = form.getFieldValue('currency_type_master_id');
                  const currency_type_master_code = optionsCurrencyType.find(
                    (item) => item.value === currency_type_master_id,
                  )?.currency_type_code;
                  return Number(value) !== 0 ||
                    isDisabled ||
                    currency_type_master_code === CodeCurrencyNotCheckAmount
                    ? Promise.resolve()
                    : Promise.reject('値は0であってはなりません');
                },
              },
            ]}
          >
            <BasicNumbericInput
              className={`${
                isDisabled || isRevert
                  ? '!bg-[#DCDEE3] !text-[#9499A5]'
                  : '!bg-white hover:opacity:40'
              }`}
              disabled={isDisabled || isRevert}
              isRightAlign
              title={TEXT_TITLE.Payment_amount}
              placeholder="0"
              required={true}
            />
          </Form.Item>
        </div>
        <div className={`${paymentSlipID ? 'col-span-1' : 'col-span-2'}`}>
          <Form.Item className="!mb-0" name="fee">
            <BasicNumbericInput
              className={`${
                isDisabled || isRevert
                  ? '!bg-[#DCDEE3] !text-[#9499A5]'
                  : '!bg-white hover:opacity:40'
              }`}
              disabled={isDisabled || isRevert}
              isRightAlign
              title={TEXT_TITLE.fee}
              placeholder="0"
            />
          </Form.Item>
        </div>
        <div className={`${paymentSlipID ? 'col-span-1' : 'col-span-2'}`}>
          <Form.Item className="!mb-0" name="unapplied_amount">
            <BasicNumbericInput
              className="!bg-white !text-[#9499A5]"
              disabled
              isRightAlign
              title={TEXT_TITLE.Unapplied_Amount}
              placeholder="0"
            />
          </Form.Item>
        </div>
      </div>
    </Form>
  );
};

export default HeaderInformation;
