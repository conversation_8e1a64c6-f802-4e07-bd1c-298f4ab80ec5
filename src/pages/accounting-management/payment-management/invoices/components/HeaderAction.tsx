import IconCancel from '@/assets/imgs/common-icons/cancel.svg';
import IconDelete from '@/assets/imgs/common-icons/delete.svg';
import ArrowRight from '@/assets/imgs/svg/arrow-right-02.svg';
import BasicButton from '@/components/Commons/BasicButton';
import BasicCheckboxGroup from '@/components/Commons/BasicCheckboxGroup';
import BasicDatePicker from '@/components/Commons/BasicDatePicker';
import BasicInput from '@/components/Commons/BasicInput';
import SearchSVG from '@/components/SVG/SearchSVG';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { TEXT_ACTION, TEXT_PLACEHOLDER, TEXT_TITLE } from '@/constants/commonText';
import { Image, Popconfirm, Popover } from 'antd';
import type { Moment } from 'moment';
import moment from 'moment';
import { useEffect, useState } from 'react';

const HeaderAction = ({
  paramSearch,
  setParamSearch,
  onDaftRevert,
  confirmDeleteRow,
  listItemChange,
  isLinked,
  isCanceled,
  isDeleted,
  isRevert,
  paymentInvoiceId,
}) => {
  const [startDate, setStartDate] = useState<Moment>(null);
  const [endDate, setEndDate] = useState<Moment>(null);
  const [isOtherThanZero, setIsOtherThanZero] = useState<boolean>(paymentInvoiceId ? false : true);
  const [searchValue, setSearchValue] = useState<string>('');
  const handleSearchByValue = () => {
    if (listItemChange.length === 0) {
    }
    const paramToSearch = { ...paramSearch, page: 1 };
    setParamSearch({
      ...paramToSearch,
      departure_date: startDate ? startDate.format('YYYY-MM-DD') : undefined,
      return_date: endDate ? endDate.format('YYYY-MM-DD') : undefined,
      is_unpaid_amount: isOtherThanZero === true ? isOtherThanZero : undefined,
      keyword: searchValue,
      exclude_zero_value: undefined,
    });
  };

  useEffect(() => {
    if (endDate && !startDate) {
      setStartDate(endDate);
    } else if (moment(endDate).isBefore(startDate)) {
      setStartDate(endDate);
    }
  }, [endDate]);

  useEffect(() => {
    if (endDate && moment(startDate).isAfter(endDate)) {
      setEndDate(startDate);
    }
  }, [startDate]);

  return (
    <>
      <div className="flex justify-between items-end mt-6 flex-wrap gap-6">
        <div className="flex items-center gap-6 flex-wrap">
          <BasicInput
            style={{
              width: '200px',
              height: '40px',
            }}
            value={searchValue}
            title={`${TEXT_TITLE.Travel_Id}`}
            placeholder={TEXT_PLACEHOLDER.Search_by_keyword}
            onChange={(val) => {
              setSearchValue(val.target.value);
            }}
          />
          <div className="flex items-center gap-4">
            <BasicDatePicker
              value={startDate}
              onChange={setStartDate}
              title={TEXT_TITLE.Purchase_date_start}
            />
            <div className="">
              <div className="h-6" />
              <div className="flex items-center gap-20">
                <Image preview={false} src={ArrowRight} alt="arrow right" />
              </div>
            </div>
            <BasicDatePicker
              value={endDate}
              onChange={setEndDate}
              title={TEXT_TITLE.Purchase_date_end}
            />
          </div>
          <div className="">
            <div className="h-6" />
            <div className="flex items-center gap-20">
              <BasicCheckboxGroup
                value={isOtherThanZero ? [true] : []}
                onChange={(val) => {
                  if (val.length > 0) {
                    setIsOtherThanZero(true);
                  } else setIsOtherThanZero(false);
                }}
                options={[{ label: '未支払額が0以外', value: true }]}
              />
              <BasicButton
                disabled={isRevert}
                icon={<SearchSVG colorSvg="white" />}
                className={`flex items-center w-[120px] ${
                  isRevert ? '!bg-[gray] hover:!bg-[gray] !text-white' : ''
                }`}
                styleType="accept"
                onClick={handleSearchByValue}
              >
                <p className="ml-2">{TEXT_ACTION.SEARCH}</p>
              </BasicButton>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-6">
          {isLinked && !(isCanceled || isDeleted) ? (
            <Popconfirm
              title={
                <div className="whitespace-pre-line">{MESSAGE_ALERT.CONFIRM_CANCEL_DOCUMENT}</div>
              }
              disabled={isRevert}
              onConfirm={() => onDaftRevert()}
              okText={TEXT_ACTION.CANCEL_DOCUMENT}
              cancelText={TEXT_ACTION.CANCEL}
            >
              <BasicButton
                className={`w-[98px] flex justify-center items-center !border-[#FDAF2E] !text-[#FDAF2E] !bg-white hover:shadow-md hover:opacity-70 ${
                  isRevert ? 'cursor-not-allowed' : ''
                }`}
                styleType="noneOutLine"
              >
                <Image preview={false} src={IconCancel} width={16} height={16} />
                <p className="text-xs !ml-1 text-[#FDAF2E]"> {TEXT_ACTION.CANCEL_DOCUMENT}</p>
              </BasicButton>
            </Popconfirm>
          ) : (
            <Popover
              content={
                <>
                  {isDeleted
                    ? MESSAGE_ALERT.ALREADY_DELETE
                    : isCanceled
                    ? MESSAGE_ALERT.ALREADY_CANCEL
                    : MESSAGE_ALERT.CANCEL_NEED_ACCOUNTING_LINKED}
                </>
              }
              trigger="hover"
            >
              <BasicButton
                className={`w-[98px] cursor-not-allowed flex justify-center items-center !text-[#FDAF2E] !bg-[#f5f5f5] hover:shadow-md hover:opacity-70 ${
                  isRevert ? 'cursor-not-allowed' : ''
                }`}
                styleType="noneOutLine"
              >
                <Image preview={false} src={IconCancel} width={16} height={16} />
                <p className="text-xs !ml-1 text-[#FDAF2E]"> {TEXT_ACTION.CANCEL_DOCUMENT}</p>
              </BasicButton>
            </Popover>
          )}
          {!isLinked && !(isCanceled || isDeleted) ? (
            <Popconfirm
              title={
                <div className="whitespace-pre-line">{MESSAGE_ALERT.CONFIRM_DELETE_DOCUMENT}</div>
              }
              disabled={isRevert}
              onConfirm={() => confirmDeleteRow()}
              okText={TEXT_ACTION.DELETE_DOCUMENT}
              cancelText={TEXT_ACTION.CANCEL}
            >
              <BasicButton
                styleType="danger"
                className=" w-[98px] hover:shadow-md hover:opacity-70 !border-[#ff4d4f] !text-[#ff4d4f] !bg-white"
              >
                <Image preview={false} src={IconDelete} width={15} height={14} />
                <p className="text-xs !ml-1">{TEXT_ACTION.DELETE_DOCUMENT}</p>
              </BasicButton>
            </Popconfirm>
          ) : (
            <Popover
              content={
                <>
                  {isDeleted
                    ? MESSAGE_ALERT.ALREADY_DELETE
                    : isCanceled
                    ? MESSAGE_ALERT.ALREADY_CANCEL
                    : MESSAGE_ALERT.DELETE_NEED_ACCOUNTING_NOT_LINKED}
                </>
              }
              trigger="hover"
            >
              <BasicButton
                styleType="danger"
                className=" w-[98px] cursor-not-allowed hover:shadow-md hover:opacity-70 !border-[#dcdee3] !text-[#ff4d4f] !bg-[#f5f5f5]"
              >
                <Image preview={false} src={IconDelete} width={15} height={14} />
                <p className="text-xs !ml-1">{TEXT_ACTION.DELETE_DOCUMENT}</p>
              </BasicButton>
            </Popover>
          )}
        </div>
      </div>
    </>
  );
};

export default HeaderAction;
