/**
 * Ant Design Pro v4 use `@ant-design/pro-layout` to handle Layout.
 *
 * @see You can view component api by: https://github.com/ant-design/ant-design-pro-layout
 */
import { getPageTitle, getMenuData } from '@ant-design/pro-layout';
import LoadingGlobal from '@/components/Commons/LoadingGlobal';
import Authorized from '@/utils/Authorized';
import { getToken } from '@/utils/authority';
import { Layout } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { matchRoutes } from 'react-router-config';
import { Link, connect, useIntl } from 'umi';
import { Helmet, HelmetProvider } from 'react-helmet-async';
import styles from './BasicLayout.less';
import { getCompany } from '@/apis/master/companyMaster';
import { openNotificationBlock, openNotificationFail } from '@/components/Notification';
import { DeviceProvider } from '@/providers/DeviceProvider';
import HeaderLayout from './components/header';
import SideBar from './components/SideBar';
import ContentLayout from './components/ContentLayout';

const { Header, Sider, Content } = Layout;

const AppName = '生産性向上システム';

/** Use Authorized check all menu item */
const menuDataRender = (menuList) =>
  menuList.map((item) => {
    const localItem = {
      ...item,
      children: item.children ? menuDataRender(item.children) : undefined,
    };
    return Authorized.check(item.authority, localItem, null);
  });

// eslint-disable-next-line max-lines-per-function
const BasicLayout = (props) => {
  const {
    route = {
      routes: [],
    },
  } = props;
  const { routes = [] } = route;
  const {
    dispatch,
    children,
    currentUser,
    location = {
      pathname: '/',
    },
  } = props;

  const { formatMessage } = useIntl();
  const { breadcrumb } = getMenuData(routes);
  const [companyDetailData, setCompanyDetailData] = useState();
  const title = getPageTitle({
    pathname: location.pathname,
    formatMessage,
    breadcrumb,
    ...props,
  });

  const [showSidebar, setShowSidebar] = useState(true);
  const [isLoading, setIsLoading] = useState(true);

  const fetchDataCompany = async () => {
    try {
      setIsLoading(true);
      const resData = await getCompany();
      const detailCompany = resData?.data?.data;
      if (detailCompany) {
        setCompanyDetailData(detailCompany);
      } else {
        openNotificationFail(MESSAGE_ALERT.FAILED_TO_GET_DATA);
      }
      setIsLoading(false);
    } catch (error) {
      setIsLoading(false);
    }
  };
  useEffect(() => {
    let accessToken = getToken();
    if (dispatch) {
      if (!accessToken) {
        dispatch({
          type: 'user/logout',
        });
      } else {
        dispatch({
          type: 'user/fetchCurrent',
        });
        dispatch({
          type: 'global/fetchMasterData',
        });
      }
    }

    const script = document?.createElement('script');
    script.src = process?.env?.MUL_PAY_SCRIPT_URL;
    script.async = true;
    document.body.appendChild(script);

    return () => {
      document.body.removeChild(script);
      // connection?.close();
    };
  }, []);

  // const fullwidthToHalfwidth = (str) => {
  //   try {
  //     return str
  //       .replace(/[\uFF01-\uFF5E]/g, (ch) => {
  //         // Chỉ chuyển đổi nếu ký tự là fullwidth
  //         if (ch.charCodeAt(0) >= 0xff01 && ch.charCodeAt(0) <= 0xff5e) {
  //           return String.fromCharCode(ch.charCodeAt(0) - 0xfee0);
  //         }
  //         return ch; // Giữ nguyên ký tự nếu không phải fullwidth
  //       })
  //       .replace(/\u3000/g, ' '); // Chuyển đổi khoảng trắng fullwidth sang halfwidth
  //   } catch (error) {
  //     return str;
  //   }
  // };

  // useEffect(() => {
  //   const handleInput = (event) => {
  //     const target = event?.target;

  //     if ((target.type === 'text' || target.type === 'textarea') && target?.value) {
  //       const newValue = fullwidthToHalfwidth(target.value);
  //       target.value = newValue;
  //     }
  //   };

  //   document.addEventListener('input', handleInput);

  //   return () => {
  //     document.removeEventListener('input', handleInput);
  //   };
  // }, []);

  const authorized = useMemo(
    () =>
      matchRoutes(props.route.children, location.pathname || '/').pop().route || {
        authority: undefined,
      },
    [location.pathname, props.route.children, currentUser?.user?.id],
  );
  useEffect(() => {
    if (currentUser?.user?.id) {
      fetchDataCompany();
    }
  }, [currentUser?.user?.id]);

  useEffect(() => {
    const handleOffline = () => {
      openNotificationFail(
        <div>
          ネットワーク接続が失われまた。ネットワーク接続を確認して、もう一度お試しください。
        </div>,
      );
    };

    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  return (
    <HelmetProvider>
      <DeviceProvider>
        <Helmet>
          <title>
            {/* {process.env.ENVIRONMENT === 'staging' ? 'STAGING - ' : ''} */}
            {title ?? AppName}
          </title>
          <meta name="description" content={title} />
        </Helmet>
        <div className={styles.container}>
          <LoadingGlobal />
          <Layout>
            <SideBar
              isLoading={isLoading}
              showSidebar={showSidebar}
              setShowSidebar={setShowSidebar}
              logo={companyDetailData?.logo}
            />
            <Layout>
              <HeaderLayout currentUser={currentUser} showSidebar={showSidebar} />
              <ContentLayout authority={authorized.authority} showSidebar={showSidebar}>
                {children}
              </ContentLayout>
            </Layout>
          </Layout>
        </div>
      </DeviceProvider>
    </HelmetProvider>
  );
};

export default connect(({ user }) => ({
  currentUser: user.currentUser,
  user,
}))(BasicLayout);
