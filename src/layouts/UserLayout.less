@import '~antd/es/style/themes/default.less';

.container {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  overflow: auto;
  // background: @layout-body-background;
  background-image: url('../assets/imgs/bg_login.png');
  background-position: unset !important;
  background-size: cover !important;
}

.lang {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 80px;
  padding-left: 46px;
  color: rgba(68, 70, 9, 1);
  font-weight: 700;
  line-height: 44px;
  background: @layout-sider-background;
  :global(.ant-dropdown-trigger) {
    margin-right: 24px;
  }
}

.content {
  display: flex;
  align-items: center;
  justify-content: center;
}

@media (min-width: @screen-md-min) {
  .container {
    background-repeat: no-repeat;
    background-position: center 110px;
    background-size: 100%;
  }

  .content {
    padding: 32px 0 24px;
  }
}

.top {
  text-align: center;
}

.header {
  height: 44px;
  line-height: 44px;
  a {
    text-decoration: none;
  }
}

.logo {
  height: 44px;
  margin-right: 16px;
  vertical-align: top;
}

.title {
  position: relative;
  top: 2px;
  color: @heading-color;
  font-weight: 600;
  font-size: 33px;
  font-family: Avenir, 'Helvetica Neue', Arial, Helvetica, sans-serif;
}

.desc {
  margin-top: 12px;
  margin-bottom: 40px;
  color: @text-color-secondary;
  font-size: @font-size-base;
}

.copyright {
  position: absolute;
  bottom: 30px;
  text-align: center;
}
