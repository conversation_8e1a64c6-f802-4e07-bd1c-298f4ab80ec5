import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { useDeviceType } from '@/providers/DeviceProvider';
import Authorized from '@/utils/Authorized';
import { Button, Layout, Result } from 'antd';
import React, { ReactNode } from 'react';
import { Link } from 'umi';

const { Content } = Layout;

const noMatch = (
  <Result
    status={403}
    title="403"
    subTitle="申し訳ありませんが、このページへのアクセスは許可されていません。"
    extra={
      <Button type="primary">
        <Link to="/" className="no-underline">
          {'戻る'}
        </Link>
      </Button>
    }
  />
);
interface Props {
  showSidebar: boolean;
  authority: any;
  children: ReactNode;
}
const ContentLayout = (props: Props) => {
  const { showSidebar, authority, children } = props;
  const deviceType = useDeviceType();

  return (
    <Content
      style={{
        paddingLeft:
          deviceType === DeviceTypeEnum?.DESKTOP ? (showSidebar ? '260px' : '80px') : undefined,
      }}
    >
      <Authorized authority={authority} noMatch={noMatch}>
        {/* <Provider store={store}>
  <PersistGate loading={null} persistor={persistor}> */}
        {children}
        {/* </PersistGate>
</Provider> */}
      </Authorized>
    </Content>
  );
};

export default ContentLayout;
