import IconNoneUser from '@/assets/imgs/common-icons/icon-none-user.svg';
import IconLogOut from '@/assets/imgs/common-icons/logout_button.svg';
import BasicModal from '@/components/Commons/BasicModal';
import { openNotificationFail } from '@/components/Notification';
import { ROUTE_NAME } from '@/constants';
import { MESSAGE_ALERT } from '@/constants/commonMessage';
import { removeAuthority } from '@/utils/authority';
import type { MenuProps } from 'antd';
import { Drawer, Dropdown, Image } from 'antd';
import { useEffect, useMemo, useRef, useState } from 'react';
import { history } from 'umi';
import type { MenuItemType } from '../menuBar/common';
import { menuItems } from '../menuBar/common';
import styles from './HeaderMain.less';
import RouteLabel from '@/constants/routeLabel';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';
import { MenuOutlined } from '@ant-design/icons';
import MenuBar from '../menuBar/MenuBar';

const HeaderMain = ({ currentUser }) => {
  const [openMenu, setOpenMenu] = useState(false);

  const menuPath = history?.location?.pathname;
  // const dispatch = useDispatch();
  const refDisableModal = useRef(null);
  const deviceType = useDeviceType();

  const specialLabelMenu = {
    'account-management': 'アカウント一覧',
    'business-partner-master': '取引先マスター',
    'business-partner-master-create': ' 取引先作成',
    'business-partner-master-detail': '取引先詳細',
    'business-partner-master-edit': '取引先編集',
    'aggregation-item-master': '集計科目マスター',
    'item-master': '科目マスター',
    'tax-category-master': '税区分マスター',
    'consumption-tax-master': '消費税マスター',
    'accounting-subject-master': '勘定科目マスター',
    'currency-type-master': '金種マスター',
    'travel-type-master': '旅行種別マスター',

    'deposit-management': '入金管理',
    'payment-management': '支払管理',
  };

  const renderSpecialMenu = () => {
    const path = menuPath?.split('/');

    const keys = Object.keys(specialLabelMenu);
    let foundKey: string | undefined;
    let maxMatchLength = 0;

    keys.forEach((key) => {
      if (path.includes(key) && key.length > maxMatchLength) {
        foundKey = key;
        maxMatchLength = key.length;
      }
    });

    return foundKey ? specialLabelMenu[foundKey] : undefined;
  };

  const currentItem = useMemo(() => {
    let currentMenu: MenuItemType;
    menuItems?.forEach((item) => {
      if (menuPath === item?.path) {
        currentMenu = item;
      }
      if (item?.children) {
        item?.children?.forEach((child) => {
          if (menuPath === child?.path) {
            currentMenu = child;
          }
          child.children?.forEach((subChild) => {
            if (menuPath === subChild?.path) {
              currentMenu = subChild;
            }
          });
        });
      }
    });

    if (!currentMenu) {
      RouteLabel.forEach((item) => {
        if (menuPath === item.path) {
          currentMenu = item;
        }
        // const pathExpectId = item.path.split(':')?.[0];
        const pathExpectId = item.path.split('/:id').filter((i) => i !== '');
        if (pathExpectId.length > 1) {
          if (
            menuPath.includes(pathExpectId?.[0]) &&
            menuPath.includes(pathExpectId?.[pathExpectId.length - 1])
          ) {
            currentMenu = item;
          }
        } else {
          if (menuPath.includes(pathExpectId?.[0])) {
            currentMenu = item;
          }
        }
      });
    } else {
      const label = renderSpecialMenu();
      if (label) {
        currentMenu.labelRender = label;
      }
    }
    return currentMenu;
  }, [menuPath, specialLabelMenu]);

  const openModalConfirmLogout = () => {
    if (refDisableModal) {
      refDisableModal.current.open();
    }
  };

  const handleLogout = async () => {
    try {
      // const { status } = await logout();
      // if (status === STATUS_CODE.SUCCESSFUL) {
      // dispatch(logout);
      removeAuthority();
      history.push('/user/login');
      // }
    } catch (error) {
      openNotificationFail(MESSAGE_ALERT.SERVER_ERROR);
    }
  };

  const items: MenuProps['items'] = [
    {
      label: <div onClick={() => history.push(ROUTE_NAME.CHANGE_PASSWORD)}>PW変更</div>,
      key: '1',
    },
    {
      type: 'divider',
    },
    deviceType === DeviceTypeEnum.MOBILE
      ? {
          label: currentUser?.user?.name,
          key: '2',
        }
      : null,
    // {
    //   label: <div onClick={openModalConfirmLogout}>ログアウト</div>,
    //   key: '3',
    // },
  ];

  const contentModal = <p className="text-center">ログアウトしますか？</p>;

  const [headerText, setHeaderText] = useState(document.title);
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      const newTitle = document.title;

      setHeaderText(newTitle);
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [menuPath]);

  return (
    <div className={styles.container}>
      <div className={`${styles.headerLeft} gap-x-2`}>
        {deviceType !== DeviceTypeEnum.DESKTOP ? (
          <div className="flex items-center">
            <MenuOutlined className="text-[20px]" onClick={() => setOpenMenu(true)} />
          </div>
        ) : null}
        {currentItem?.icon ? (
          <div className={styles.headerIcon}>
            <Image
              src={currentItem?.icon}
              width={14}
              height={14}
              preview={false}
              style={{
                filter: 'brightness(0) invert(1)',
                display: !currentItem?.icon ? 'none' : 'block',
              }}
            />
          </div>
        ) : null}

        <div
          className={styles.headerMenuLabel}
          style={{
            fontSize:
              deviceType === DeviceTypeEnum.DESKTOP
                ? '24px'
                : deviceType === DeviceTypeEnum.TABLET
                ? '20px'
                : '18px',
          }}
        >
          {/* {currentItem?.labelRender ?? currentItem?.label} */}
          {headerText}
        </div>
      </div>
      <div className="flex items-center gap-4">
        <Dropdown menu={{ items }} trigger={['click']}>
          <div className={`${styles.headerRight} cursor-pointer`}>
            <div className={styles.userInformation}>
              <div
                className={styles.userName}
                style={{
                  fontSize: deviceType === DeviceTypeEnum.DESKTOP ? '12px' : '10px',
                }}
              >
                {deviceType !== DeviceTypeEnum.MOBILE ? currentUser?.user?.name : null}
              </div>
              <Image
                src={IconNoneUser}
                width={34}
                height={34}
                preview={false}
                style={{
                  display: 'block',
                }}
              />
            </div>
          </div>
        </Dropdown>

        <Image
          src={IconLogOut}
          width={34}
          height={34}
          preview={false}
          className="cursor-pointer"
          style={{
            display: 'block',
          }}
          onClick={openModalConfirmLogout}
        />
      </div>

      {/* Modal confirm logout */}
      <BasicModal
        ref={refDisableModal}
        title="ログアウト"
        buttonCloseTitle="いいえ"
        buttonSubmitTitle="はい"
        content={contentModal}
        onSubmit={handleLogout}
      />

      <Drawer
        title={undefined}
        placement="left"
        size={deviceType === DeviceTypeEnum.MOBILE ? 'default' : 'large'}
        onClose={() => setOpenMenu(false)}
        open={openMenu}
      >
        <MenuBar showMenu={true} />
      </Drawer>
    </div>
  );
};

export default HeaderMain;
