import React from 'react';
import HeaderMain from './HeaderMain';
import { Layout } from 'antd';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

const { Header } = Layout;

interface Props {
  showSidebar: boolean;
  currentUser: any;
}
const HeaderLayout = (props: Props) => {
  const { currentUser, showSidebar } = props;
  const deviceType = useDeviceType();

  return (
    <Header
      style={{
        paddingLeft:
          deviceType === DeviceTypeEnum?.DESKTOP ? (showSidebar ? '260px' : '80px') : undefined,
        borderBottom: '1px solid #F2F3F8',
      }}
    >
      <HeaderMain currentUser={currentUser} />
    </Header>
  );
};

export default HeaderLayout;
