import { Image } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import { history } from 'umi';
import styles from './MenuBar.less';
// import { SettingIcon } from '@/assets/imgs/menu-icons';
import type { MenuItemType } from './common';
import { menuItems } from './common';
import MenuItem from './MenuItem';
import { ROUTE_NAME } from '@/constants';

interface Props {
  showMenu?: boolean;
}
const MenuBar = ({ showMenu }: Props) => {
  const [menuActive, setMenuActive] = useState<string>('');
  const menuPath = history?.location?.pathname?.split('/').join('');
  const commonPath = [ROUTE_NAME.CHANGE_PASSWORD, ROUTE_NAME.PROFILE];

  const permissionStorage = localStorage.getItem('permissions');
  const permissions =
    permissionStorage && permissionStorage != 'undefined' ? JSON.parse(permissionStorage) : [];
  const modelStorage = localStorage.getItem('models');
  const models = modelStorage && modelStorage != 'undefined' ? JSON.parse(modelStorage) : [];

  const currentMenu = useMemo(() => {
    const arr = menuItems?.map((item) => {
      if (
        (!item?.model || item?.model?.some((itemModel) => models?.includes(itemModel))) &&
        !item?.children
      ) {
        return item;
      }
      if (
        (!item?.model || item?.model?.some((itemModel) => models?.includes(itemModel))) &&
        item?.children
      ) {
        const newChildren = item?.children?.filter(
          (i) => !i?.model || i?.permission?.some((itemModel) => permissions?.includes(itemModel)),
        );

        return { ...item, children: newChildren };
      }
    });

    return arr;
  }, [models, permissions]);

  useEffect(() => {
    const currentItemMenu = currentMenu?.find((item) => {
      if (!item?.children?.length && menuPath.includes(item?.key)) {
        return item;
      } else if (item?.children?.length) {
        const menu = item?.children?.find((i) => menuPath.includes(i?.key));
        return menu;
      }
      return false;
    });

    if (currentItemMenu?.children?.length) {
      const newMenuActive = currentItemMenu?.children?.find((child) =>
        menuPath.includes(child?.key),
      );
      setMenuActive(newMenuActive?.key);
    } else {
      setMenuActive(currentItemMenu?.key);
    }
  }, [menuPath, currentMenu]);

  useEffect(() => {
    let hasMenuItem: MenuItemType;
    const menuItem = currentMenu?.find((item) => {
      if (!item?.children?.length && menuPath.includes(item?.key)) {
        return item;
      } else if (item?.children?.length) {
        const menu = item?.children?.find((i) => menuPath?.includes(i?.key));

        return menu;
      }
    });

    if (menuItem?.children?.length) {
      const newMenuActive = menuItem?.children?.find((child) => menuPath?.includes(child?.key));
      hasMenuItem = newMenuActive;
    } else {
      hasMenuItem = menuItem;
    }

    // common path auto pass
    if (commonPath.includes(`/${menuPath}`)) {
      history.replace(history.location.pathname);
      return;
    }
  }, [menuPath, currentMenu, permissions]);

  return (
    <div className={styles.container}>
      <div>
        {currentMenu?.map((item) => (
          <MenuItem key={item?.key} menuActive={menuActive} item={item} showMenu={showMenu} />
        ))}
      </div>
      <div
        className={styles.menuItem}
        onClick={() => {}}
        style={{
          borderTop: '1px solid #F2F3F8',
        }}
      >
        {/* <Image
          src={SettingIcon}
          width={20}
          height={20}
          preview={false}
          className={styles.menuIcon}
        /> */}
        {/* {showMenu ? <div className={styles.menuLabel}> 設定</div> : null} */}
      </div>
    </div>
  );
};

export default MenuBar;
