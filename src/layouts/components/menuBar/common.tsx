import {
  SSGIcon,
  ItineraryManagementIcon,
  PurchaseAndMaterialManagementIcon,
  AccountManagementIcon,
  AccountingManagementIcon,
  SGGManagementIcon,
  MasterManagementIcon,
  TouristDestinationsIcon,
  AccommodationIcon,
  RestaurantsIcon,
  BusesIcon,
  HireCarsIcon,
  RailwaysIcon,
  AirlinesIcon,
  ShipsIcon,
  DeliveryIcon,
  GuidesIcon,
  OtherIcon,
  TravelManagementIcon,
  SalesManagementIcon,
  PurchaseManagementIcon,
  PaymentManagementIcon,
  DepositManagementIcon,
  InvoiceIssuanceIcon,
  ReportOutputIcon,
  TaxCategoryIcon,
  ConsumptionTaxIcon,
  SubjectIcon,
  AggregationItemIcon,
  AccountSubjectIcon,
  DenominationIcon,
  TravelTypeFromIcon,
  BusinessPartnerIcon,
  CompanyMasterIcon,
} from '@/assets/imgs/menu-icons';
import { history } from 'umi';

export type MenuItemType = {
  type?: 0 | 1;
  key?: string;
  icon?: string;
  path?: string;
  label: string;
  labelRender?: string;
  onClick?: () => void;
  role?: string[];
  children?: MenuItemType[];
  model?: string[];
  permission?: string[];
};

export const menuItems: MenuItemType[] = [
  {
    type: 0,
    key: 'global-sgg',
    icon: SSGIcon,
    path: 'global-sgg',
    onClick: () => {
      history.push('/global-sgg');
    },
    label: 'SGG',
  },
  {
    type: 0,
    key: 'itinerary-management',
    icon: ItineraryManagementIcon,
    path: 'itinerary-management',
    onClick: () => {
      history.push('/itinerary-management');
    },
    label: '旅程管理',
  },
  {
    type: 0,
    key: 'purchase-and-material-management',
    icon: PurchaseAndMaterialManagementIcon,
    label: '仕入・素材管理',
    children: [
      {
        type: 1,
        key: 'tourist-destinations',
        icon: TouristDestinationsIcon,
        label: '観光地',
        path: '/purchase-and-material-management/tourist-destinations',
        onClick: () => {
          history.push('/purchase-and-material-management/tourist-destinations');
        },
      },
      {
        type: 1,
        key: 'hotel',
        icon: AccommodationIcon,
        label: '宿泊施設',
        path: '/purchase-and-material-management/hotel',
        onClick: () => {
          history.push('/purchase-and-material-management/hotel');
        },
      },
      {
        type: 1,
        key: 'restaurants',
        icon: RestaurantsIcon,
        label: '飲食店',
        path: '/purchase-and-material-management/restaurants',
        onClick: () => {
          history.push('/purchase-and-material-management/restaurants');
        },
      },
      {
        type: 1,
        key: 'buses',
        icon: BusesIcon,
        label: 'バス',
        path: '/purchase-and-material-management/buses',
        onClick: () => {
          history.push('/purchase-and-material-management/buses');
        },
      },
      {
        type: 1,
        key: 'hire-cars',
        icon: HireCarsIcon,
        label: 'ハイヤー',
        path: '/purchase-and-material-management/hire-cars',
        onClick: () => {
          history.push('/purchase-and-material-management/hire-cars');
        },
      },
      {
        type: 1,
        key: 'railways',
        icon: RailwaysIcon,
        label: '鉄道',
        path: '/purchase-and-material-management/railways',
        onClick: () => {
          history.push('/purchase-and-material-management/railways');
        },
      },
      {
        type: 1,
        key: 'airlines',
        icon: AirlinesIcon,
        label: '航空券',
        path: '/purchase-and-material-management/airlines',
        onClick: () => {
          history.push('/purchase-and-material-management/airlines');
        },
      },
      {
        type: 1,
        key: 'ships',
        icon: ShipsIcon,
        label: '船',
        path: '/purchase-and-material-management/ships',
        onClick: () => {
          history.push('/purchase-and-material-management/ships');
        },
      },
      {
        type: 1,
        key: 'delivery',
        icon: DeliveryIcon,
        label: '宅配',
        path: '/purchase-and-material-management/delivery',
        onClick: () => {
          history.push('/purchase-and-material-management/delivery');
        },
      },
      {
        type: 1,
        key: 'guides',
        icon: GuidesIcon,
        label: 'ガイド',
        path: '/purchase-and-material-management/guides',
        onClick: () => {
          history.push('/purchase-and-material-management/guides');
        },
      },
      {
        type: 1,
        key: 'other',
        icon: OtherIcon,
        label: 'その他',
        path: '/purchase-and-material-management/other',
        onClick: () => {
          history.push('/purchase-and-material-management/other');
        },
      },
    ],
  },

  {
    type: 0,
    key: 'accounting-management',
    icon: AccountingManagementIcon,
    model: [
      'travel_management',
      'sales_management',
      'purchase_management',
      'deposit_management',
      'payment_management',
      'invoice_management',
    ],
    label: '会計関連',
    children: [
      {
        type: 1,
        key: 'travel-management',
        icon: TravelManagementIcon,
        label: '旅行管理',
        path: '/accounting-management/travel-management',
        permission: ['view_travel'],
        onClick: () => {
          history.push('/accounting-management/travel-management');
        },
      },
      {
        type: 1,
        key: 'sales-management',
        icon: SalesManagementIcon,
        label: '売上管理',
        path: '/accounting-management/sales-management',
        permission: ['view_sale_slip'],
        onClick: () => {
          history.push('/accounting-management/sales-management');
        },
      },
      {
        type: 1,
        key: 'purchase-management',
        icon: PurchaseManagementIcon,
        label: '仕入管理',
        path: '/accounting-management/purchase-management',
        permission: ['view_purchase_slip'],
        onClick: () => {
          history.push('/accounting-management/purchase-management');
        },
      },
      {
        type: 1,
        key: 'deposit-management',
        path: '/accounting-management/deposit-management',
        permission: ['view_deposit_slip'],
        onClick: () => {
          history.push('/accounting-management/deposit-management');
        },
        icon: PaymentManagementIcon,
        label: '入金管理',
      },
      {
        type: 1,
        icon: DepositManagementIcon,
        label: '支払管理',
        key: 'payment-management',
        path: '/accounting-management/payment-management',
        permission: ['view_payment_slip'],
        onClick: () => {
          history.push('/accounting-management/payment-management');
        },
      },
      {
        type: 1,
        key: 'invoice-issuance',
        icon: InvoiceIssuanceIcon,
        label: '請求書発行',
        path: '/accounting-management/invoice-issuance',
        permission: ['view_invoice'],
        onClick: () => {
          history.push('/accounting-management/invoice-issuance');
        },
      },
      {
        type: 1,
        key: 'report-output',
        icon: ReportOutputIcon,
        label: '帳票出力',
        permission: ['view_invoice'],
        children: [
          {
            type: 1,
            key: 'sales-balance-list',
            label: '売上収支一覧表',
            path: '/accounting-management/report-output/sales-balance-list',
            onClick: () => {
              history.push('/accounting-management/report-output/sales-balance-list');
            },
          },
          {
            type: 1,
            key: 'monthly-sales-balance-list',
            label: '月次売上収支一覧表',
            path: '/accounting-management/report-output/monthly-sales-balance-list',
            onClick: () => {
              history.push('/accounting-management/report-output/monthly-sales-balance-list');
            },
          },
          {
            type: 1,
            key: 'annual-sales-report',
            label: '年次売上帳票',
            path: '/accounting-management/report-output/annual-sales-report',
            onClick: () => {
              history.push('/accounting-management/report-output/annual-sales-report');
            },
          },
          {
            type: 1,
            key: 'purchase-list',
            label: '仕入一覧表',
            path: '/accounting-management/report-output/purchase-list',
            onClick: () => {
              history.push('/accounting-management/report-output/purchase-list');
            },
          },

          {
            type: 1,
            key: 'deposit-list',
            label: '入金帳票',
            path: '/accounting-management/report-output/deposit-list',
            onClick: () => {
              history.push('/accounting-management/report-output/deposit-list');
            },
          },

          {
            type: 1,
            key: 'payment-list',
            label: '支払帳票',
            path: '/accounting-management/report-output/payment-list',
            onClick: () => {
              history.push('/accounting-management/report-output/payment-list');
            },
          },
        ],
      },
    ],
  },
  {
    type: 0,
    key: 'master-management',
    icon: MasterManagementIcon,
    label: 'マスター管理',
    model: ['master_management'],
    children: [
      {
        type: 1,
        key: 'tax-category-master',
        icon: TaxCategoryIcon,
        label: '税区分マスター',
        path: '/master-management/tax-category-master',
        permission: ['view_master_data'],
        onClick: () => {
          history.push('/master-management/tax-category-master');
        },
      },
      {
        type: 1,
        key: 'consumption-tax-master',
        icon: ConsumptionTaxIcon,
        label: '消費税マスター',
        path: '/master-management/consumption-tax-master',
        permission: ['view_master_data'],
        onClick: () => {
          history.push('/master-management/consumption-tax-master');
        },
      },
      {
        type: 1,
        key: 'aggregation-item-master',
        icon: AggregationItemIcon,
        label: '集計科目マスター',
        path: '/master-management/aggregation-item-master',
        permission: ['view_master_data'],
        onClick: () => {
          history.push('/master-management/aggregation-item-master');
        },
      },
      {
        type: 1,
        key: 'item-master',
        icon: SubjectIcon,
        label: '科目マスター',
        path: '/master-management/item-master',
        permission: ['view_master_data'],
        onClick: () => {
          history.push('/master-management/item-master');
        },
      },
      {
        type: 1,
        key: 'accounting-subject-master',
        icon: AccountSubjectIcon,
        label: '勘定科目マスター',
        path: '/master-management/accounting-subject-master',
        permission: ['view_master_data'],
        onClick: () => {
          history.push('/master-management/accounting-subject-master');
        },
      },
      {
        type: 1,
        key: 'currency-type-master',
        icon: DenominationIcon,
        label: '金種マスター',
        path: '/master-management/currency-type-master',
        permission: ['view_master_data'],
        onClick: () => {
          history.push('/master-management/currency-type-master');
        },
      },
      {
        type: 1,
        key: 'travel-type-master',
        icon: TravelTypeFromIcon,
        label: '旅行種別マスター',
        path: '/master-management/travel-type-master',
        permission: ['view_master_data'],
        onClick: () => {
          history.push('/master-management/travel-type-master');
        },
      },
      {
        type: 1,
        key: 'business-partner-master',
        icon: BusinessPartnerIcon,
        label: '取引先マスター',
        path: '/master-management/business-partner-master',
        onClick: () => {
          history.push('/master-management/business-partner-master');
        },
      },
      {
        type: 1,
        key: 'company-master',
        icon: CompanyMasterIcon,
        label: '自社マスター',
        path: '/master-management/company-master',
        permission: ['view_master_data'],
        onClick: () => {
          history.push('/master-management/company-master');
        },
      },
    ],
  },
  {
    type: 0,
    key: 'sgg-management',
    icon: SGGManagementIcon,
    label: 'SGG管理',
    path: '/sgg-management/list',
    model: ['account_list'],
    onClick: () => {
      history.push('/sgg-management/list');
    },
  },

  {
    type: 0,
    key: 'account-management',
    icon: AccountManagementIcon,
    label: 'アカウント管理',
    path: '/account-management/list',
    model: ['account_list'],
    onClick: () => {
      history.push('/account-management/list');
    },
  },
];
