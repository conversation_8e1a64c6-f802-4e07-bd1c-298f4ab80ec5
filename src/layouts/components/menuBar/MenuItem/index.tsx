import React, { useEffect, useState } from 'react';
import type { MenuItemType } from '../common';
import styles from '../MenuBar.less';
import { Image, Popover } from 'antd';
import MenuItemChildren from './MenuItemChildren';
import ArrowDown from '@/components/SVG/ArrowDown';

interface Props {
  item: MenuItemType;
  menuActive: string;
  showMenu: boolean;
}

type ItemShowType = {
  nameKey: string;
  isShow: boolean;
};

const MenuItem = ({ item, menuActive, showMenu }: Props) => {
  const [showChildren, setShowChildren] = useState<boolean>(false);
  const [listItemShowChild, setListItemShowChild] = useState<ItemShowType[]>([]);

  const getLisMenuShow = () => {
    const storedObject = localStorage.getItem('showMenuChild');
    const listParse = JSON.parse(storedObject) ?? [];

    const isShowItem = listParse?.find((i: ItemShowType) => i?.nameKey === item?.key)?.isShow;

    setShowChildren(isShowItem);
    setListItemShowChild(listParse);
  };

  useEffect(() => {
    getLisMenuShow();
  }, []);

  const handleClick = () => {
    if (item?.children) {
      setShowChildren(!showChildren);
      const itemShow: ItemShowType = {
        nameKey: item.key,
        isShow: !showChildren,
      };

      let newListItemShowChild = [...listItemShowChild];
      const hasItemShowChild = newListItemShowChild?.find((i) => i?.nameKey === itemShow?.nameKey);

      if (hasItemShowChild) {
        newListItemShowChild = newListItemShowChild?.map((i) =>
          i?.nameKey === itemShow?.nameKey ? itemShow : i,
        );
      } else {
        newListItemShowChild.push(itemShow);
      }

      localStorage.setItem('showMenuChild', JSON.stringify(newListItemShowChild));
      setListItemShowChild(newListItemShowChild);
    } else {
      item?.onClick?.();
    }
  };

  return (
    <>
      <div
        className={styles.menuItem}
        onClick={handleClick}
        style={{
          display: !item?.icon ? 'none' : '',
          backgroundColor: menuActive === item?.key ? '#3997C8' : '',
          color: menuActive === item?.key ? '#fff' : '#414244',
          borderTopRightRadius: menuActive === item?.key ? '20px' : '',
          borderBottomRightRadius: menuActive === item?.key ? '20px' : '',
          paddingLeft: showMenu && item?.type === 1 ? '53px' : '18px',
          marginRight: showMenu ? '18px' : '11px',
        }}
      >
        <Image
          style={{
            ...(menuActive === item?.key && {
              filter: 'brightness(0) invert(1)',
            }),
          }}
          src={item?.icon}
          width={20}
          height={20}
          preview={false}
          className={styles.menuIcon}
        />
        {showMenu ? <div className={styles.menuLabel}> {item?.label}</div> : null}
        {item?.children?.length > 0 && (
          <div className={`${showChildren ? 'rotate-180' : ''}`}>
            <ArrowDown />
          </div>
        )}
      </div>
      {item?.children?.length > 0 && showChildren ? (
        <>
          {item?.children?.map?.((menu, index) => (
            <React.Fragment key={`menu_children_${index}_${menu?.key}`}>
              {menu?.children ? (
                <Popover
                  placement="right"
                  trigger="click"
                  className=""
                  overlayClassName="[&_.ant-popover-inner-content]:!p-2 [&_.ant-popover-inner]:rounded-md"
                  content={menu?.children?.length > 0 ? <MenuItemChildren Menu={menu} /> : null}
                >
                  <div
                    className={styles.menuItem}
                    style={{
                      display: !menu?.icon ? 'none' : '',
                      backgroundColor: menuActive === menu?.key ? '#3997C8' : '',
                      color: menuActive === menu?.key ? '#fff' : '#414244',
                      borderTopRightRadius: menuActive === menu?.key ? '20px' : '',
                      borderBottomRightRadius: menuActive === menu?.key ? '20px' : '',
                      paddingLeft: showMenu && menu?.type === 1 ? '53px' : '18px',
                      marginRight: showMenu ? '18px' : '11px',
                    }}
                  >
                    <Image
                      style={{
                        ...(menuActive === menu?.key && {
                          filter: 'brightness(0) invert(1)',
                        }),
                      }}
                      src={menu?.icon}
                      width={20}
                      height={20}
                      preview={false}
                      className={styles.menuIcon}
                    />
                    {showMenu ? <div className={styles.menuLabel}>{menu?.label}</div> : null}
                    <div className="rotate-[-90deg]">
                      <ArrowDown />
                    </div>
                  </div>
                </Popover>
              ) : (
                <div
                  className={styles.menuItem}
                  onClick={() => {
                    menu?.onClick();
                  }}
                  style={{
                    display: !menu?.icon ? 'none' : '',
                    backgroundColor: menuActive === menu?.key ? '#3997C8' : '',
                    color: menuActive === menu?.key ? '#fff' : '#414244',
                    borderTopRightRadius: menuActive === menu?.key ? '20px' : '',
                    borderBottomRightRadius: menuActive === menu?.key ? '20px' : '',
                    paddingLeft: showMenu && menu?.type === 1 ? '53px' : '18px',
                    marginRight: showMenu ? '18px' : '11px',
                  }}
                >
                  <Image
                    style={{
                      ...(menuActive === menu?.key && {
                        filter: 'brightness(0) invert(1)',
                      }),
                    }}
                    src={menu?.icon}
                    width={20}
                    height={20}
                    preview={false}
                    className={styles.menuIcon}
                  />
                  {showMenu ? <div className={styles.menuLabel}> {menu?.label}</div> : null}
                </div>
              )}
            </React.Fragment>
          ))}
        </>
      ) : null}
    </>
  );
};

export default MenuItem;
