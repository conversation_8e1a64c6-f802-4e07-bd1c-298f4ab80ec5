import React from 'react';
import type { MenuItemType } from '../common';

interface Props {
  Menu: MenuItemType;
}

const MenuItemChildren = ({ Menu }: Props) => {
  return (
    <div className="flex flex-col gap-y-[6px]">
      {Menu?.children?.map((item) => (
        <div
          key={item?.key}
          className="min-w-[100px] cursor-pointer hover:bg-[#3997C8a9] px-1 py-[2px] rounded"
          onClick={item?.onClick}
        >
          {item?.label}
        </div>
      ))}
    </div>
  );
};

export default MenuItemChildren;
