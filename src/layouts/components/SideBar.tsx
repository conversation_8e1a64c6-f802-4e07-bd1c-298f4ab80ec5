import { Image, Layout, Skeleton } from 'antd';
import React from 'react';
import MenuBar from './menuBar/MenuBar';
import styles from '../BasicLayout.less';
import { useDeviceType } from '@/providers/DeviceProvider';
import { DeviceTypeEnum } from '@/constants/responsiveSize';

const { Sider } = Layout;

interface Props {
  showSidebar: boolean;
  setShowSidebar: React.Dispatch<React.SetStateAction<boolean>>;
  logo?: string;
  isLoading: boolean;
}
const SideBar = (props: Props) => {
  const { showSidebar, setShowSidebar, isLoading, logo } = props;
  const deviceType = useDeviceType();

  if (deviceType === DeviceTypeEnum.DESKTOP) {
    return (
      <Sider
        width={`${showSidebar ? '260px' : '74px'}`}
        style={{
          backgroundColor: '#fff',
          borderRight: '1px solid #F2F3F8',
          overflow: 'auto',
          height: '100vh',
          position: 'fixed',
          left: 0,
          top: 0,
          bottom: 0,
          zIndex: 99,
        }}
        collapsed={!showSidebar}
        collapsible
        trigger={null}
      >
        <div
          style={{
            margin: '8px 10px',
            display: 'flex',
            maxHeight: '150px',
            alignItems: 'center',
            justifyItems: 'center',
            paddingLeft: showSidebar ? '15px' : '0',
          }}
        >
          <div onClick={() => setShowSidebar(!showSidebar)} className={styles.logo}>
            {isLoading ? (
              <Skeleton.Image active className="[&_.ant-skeleton-image]:!h-[60px]" />
            ) : (
              <Image
                src={logo ?? '/imgs/showa-logo.jpg'}
                preview={false}
                style={{
                  maxHeight: '100px',
                  transform: 'scale(1.1)',
                  transformOrigin: 'center',
                  objectFit: 'contain',
                }}
              />
            )}
          </div>
        </div>
        <MenuBar showMenu={showSidebar} />
      </Sider>
    );
  } else {
    return (
      <Sider width={0}>
        <div />
      </Sider>
    );
  }
};

export default SideBar;
