export default {
  'navbar.lang': 'English',
  'menu.home': 'Home',
  'menu.test': 'test menu',
  'menu.banner.setting': 'Banners',
  'menu.banner.detail': 'Advertising banner detail',
  'menu.ceremony': 'Ceremony',
  // 'menu.dashboard.analysis': 'Analysis',
  // 'menu.dashboard.monitor': 'Monitor',
  // 'menu.dashboard.workplace': 'Workplace',
  'menu.ngword': 'NG word',
  'menu.ngword.worklist': 'NG Word List',
  'menu.ngword.messageNG.list': 'NG Message list',

  'menu.ceremonyDetail': 'Ceremony Detail',
  // 'menu.form.stepform': 'Step Form',
  // 'menu.form.stepform.info': 'Step Form(write transfer information)',
  // 'menu.form.stepform.confirm': 'Step Form(confirm transfer information)',
  // 'menu.form.stepform.result': 'Step Form(finished)',
  // 'menu.form.advancedform': 'Advanced Form',
  'menu.user.management': 'Users',
  'menu.user.management.identify': 'Identification',
  // 'menu.user.management.identify.list': 'Identification List',
  // 'menu.user.management.identify.detail': 'Detail user identification',
  'menu.user.management.confirm-self-introduce': 'Confirm (Self-Introduce)',
  'menu.user.management.profile.photo-confirm': 'Confirm (Profile image)',
  'menu.user.management.list': 'User list',
  'menu.user.management.notification': 'Notification',
  // 'menu.user.management.detail': 'User Detail',
  // 'menu.user.listall': 'All User',
  'menu.report.graph': 'Report Graph',
  'menu.report.violation': 'Violation Report',
  'menu.changepassword': 'Change Password',
  'menu.medicalTreatmentSchedule': 'Medical Treatment Schedule',
  'menu.reservationList': 'Reservation List',
  'menu.patientList': 'Patient List',
  'menu.paymentManagement': 'Payment management',
  'menu.shippingManagement': 'Shipping Management',
  'menu.shift': 'Shift',
  'menu.contentManagement': 'Content Management',
  'menu.setting': 'Setting',
  'menu.portalAdministrator': 'Portal administrator',
  // 'menu.report.violation.detail': 'Violation Report Detail',
  // riêng trang violation
  password: 'Password',
  logIn: 'Log in',
  detail: 'Detail',
  reportReason: 'Violation Type',
  resultEmpty: 'The search result is 0.',
  update: 'Update',
};
