import { getMe } from '@/apis/user';
import { reloadAuthorized } from '@/utils/Authorized';
import { history } from 'umi';
import { getPageQuery } from '@/utils/utils';
import { stringify } from 'qs';

export default {
  namespace: 'user',

  state: {
    list: [],
    currentUser: null,
    accessToken: null,
    permission: null,
  },

  effects: {
    *fetchCurrent(_, { call, put }) {
      const { response } = yield call(getMe);
      if (response.status === 200) {
        yield put({
          type: 'saveCurrentUser',
          payload: response.data.data,
        });
      } else if (response.status === 401) {
        yield put({
          type: 'removeUserInfo',
        });
        window.location.href = '/user/login';
      }
    },
  },

  reducers: {
    saveMasterData(state, action) {
      try {
        const data = action.payload;
        let mapData = data;
        global.mapData = mapData;

        return {
          ...state,
          masterData: mapData,
        };
      } catch (e) {
        console.log('eee', e);
      }
    },
    saveAccessToken(state, action) {
      try {
        return {
          ...state,
          accessToken: action.payload,
        };
      } catch (e) {
        console.log('eee', e);
      }
    },
    saveCurrentUser(state, action) {
      localStorage.setItem('permissions', JSON.stringify(action.payload.permissions)); // auto reload
      localStorage.setItem('models', JSON.stringify(action.payload.models)); // auto reload
      reloadAuthorized();
      return {
        ...state,
        permission: action?.payload?.permissions,
        currentUser: action.payload,
      };
    },
    removeUserInfo(state, action) {
      return {
        ...state,
        currentUser: null,
        permission: null,
        accessToken: null,
      };
    },
    logout() {
      localStorage.removeItem('accessToken');
      const { redirect } = getPageQuery(); // Note: There may be security issues, please note
      if (window.location.pathname !== '/user/login' && !redirect) {
        history.replace({
          pathname: '/user/login',
          search: stringify({
            redirect: window.location.href,
          }),
        });
      }
    },
  },
};
