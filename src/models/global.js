import { getCountry } from '@/apis/common';
import { getAggregationItemCode } from '@/apis/master/AggregationItem';
import { getListTaxCategoryCode } from '@/apis/master/taxCategory';

export default {
  namespace: 'global',

  state: {
    collapsed: false,
    masterData: null,
    managerData: [],
    notices: [],
    country: [],
    listTaxMasterCode: [],
    listAggregationMasterCode: [],
  },

  effects: {
    *fetchMasterData(_, { call, put }) {
      // const { response, data } = yield call(getMasterData);
      // console.log('fetch0', response);
      // if (response.status === 200) {
      //   yield put({ type: 'saveMasterData', payload: response.data.data });
      // }
    },
    *fetchCountry(_, { call, put }) {
      const { response, data } = yield call(getCountry);
      if (response.status === 200) {
        yield put({ type: 'fetchCountrySuccess', payload: response.data.data });
      }
    },
  },

  reducers: {
    saveMasterData(state, action) {
      return {
        ...state,
        masterData: action.payload,
      };
    },
    fetchCountrySuccess(state, { payload }) {
      return {
        ...state,
        country: payload,
      };
    },
  },

  subscriptions: {
    setup({ history }) {
      // Subscribe history(url) change, trigger `load` action if pathname is `/`
      return history.listen(({ pathname, search }) => {
        if (typeof window.ga !== 'undefined') {
          window.ga('send', 'pageview', pathname + search);
        }
      });
    },
  },
};
