import { useState } from 'react';
import { openNotificationSuccess, openNotificationWarning } from '@/components/Notification';

/* eslint-disable max-lines-per-function */
export default function useCommonModel() {
  const [globalLoading, setGlobalLoading] = useState(false);

  const handleUploadImage = async (formData, configs) => {
    try {
      const { response } = {};
      if (response.message && response.message == 'Network Error') {
        setSubmitting(false);
        openNotificationWarning(
          formatMessage({ id: 'FailedConnectServer' }),
          formatMessage({ id: 'pleaseCheckAgain' }),
          '#f81d22',
        );
        return;
      }
      if (response?.status == 201 || response?.status == 200) {
        openNotificationSuccess('お知らせを追加しました。', '', '#389e0d');
      } else {
        setAlertMsg('Upload image unsuccessfully');
      }
    } catch (error) {
      console.log({ error });
    }
  };

  return {
    globalLoading,
    setGlobalLoading,
    handleUploadImage,
  };
}
