import type { BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type ReportSaleParamsType = {
  start?: string;
  finish?: string;
  travel_id?: string;
  limit?: number | string;
  page?: number;
  is_export_excel?: boolean;
  type_date?: 'voucher_posting_date' | 'departure_date' | 'return_date' | string;
  report_type?: 'report_by_travel' | 'report_by_business_partner' | 'report_by_subject' | string;
};

export type ResponseReportSaleTypeByTravel = {
  id: number;
  travel_code: string;
  departure_date: string;
  return_date: string;
  tour_name: string;
  sale: null;
  travel_type: string;
  month: string;
  business_partner_items: {
    business_partner_id: number;
    business_partner_name: string;
    business_partner_name_kana: string;
    business_partner_code: string;
    voucher_posting_date: string;
    total_sale_amount: number;
    total_purchase_amount: number;
    gross_profit_amount: number;
    commission: number;
    total_gross_profit: number;
    gross_profit_rate: number;
    sale_date: string;
  }[];
  admin?: {
    id: number;
    name: string;
  };
  total_commission: number;
  total_gross_profit: number;
  total_gross_profit_amount: number;
  total_gross_profit_rate: number;
  total_purchase_amount: number;
  total_sale_amount: number;
};
export type ResponseReportSaleTypeByBP = {
  id: number;
  business_partner_code: string;
  business_partner_name: string;
  total_purchase_amount: number;
  total_commission: number;
  total_gross_profit: number;
  total_gross_profit_rate: number;
  total_gross_profit_amount: number;
  total_sale_amount: number;
  travels: {
    travel_id: number;
    travel_code: string;
    voucher_posting_date: string;
    tour_name: string;
    departure_date: string;
    return_date: string;
    total_sales_amount: number;
    total_purchase_amount: number;
    gross_profit: number;
    commission: number;
    total_gross_profit: number;
    gross_profit_rate: number;
    admin?: {
      id: number;
      name: string;
    };
    sales_by_months: {
      [key: string]: {
        id: number;
        sale_invoice_id: number;
        sale_amount: number;
        total_sale_amount: number;
        total_purchase_amount: number;
        commission: number;
        gross_profit_amount: number;
        total_gross_profit: number;
        gross_profit_rate: number;
        sale_date: string;
      };
    };
  }[];
};
export type ResponseReportSaleTypeBySubject = {
  id: number;
  travel_code: string;
  departure_date: string;
  return_date: string;
  tour_name: string;
  sale: null;
  travel_type: null;
  month: string;
  total_sale_amount: number;
  total_purchase_amount: number;
  total_gross_profit_amount: number;
  total_commission: number;
  total_gross_profit: number;
  total_gross_profit_rate: number;
  subject_items: {
    subject_id: number;
    summary_subject_name: string;
    total_sale_amount: number;
    total_purchase_amount: number;
    gross_profit_amount: number;
    commission: number;
    total_gross_profit: number;
    gross_profit_rate: number;
  }[];
  sale_invoice: {
    id: number;
    business_partner_id: number;
    travel_id: number;
    total_amount_received: number;
    voucher_posting_date: string;
    sale_invoice_item: {
      id: number;
      sale_invoice_id: number;
      amount_excluding_tax: number;
      amount_including_tax: number;
      subject_id: number;
      summary_subject_name: string;
    }[];
  }[];
  purchase_invoice: {
    id: number;
    business_partner_id: number;
    travel_id: number;
    total_payment: number;
    voucher_posting_date: string;
    purchase_invoice_item: {
      id: number;
      purchase_invoice_id: number;
      amount_excluding_tax: number;
      amount_including_tax: number;
      r_amount: number;
      subject_id: number;
      summary_subject_name: string;
    }[];
  }[];
};
export async function getReportSale(params: ReportSaleParamsType) {
  return request<
    BaseResponse<
      ResponseList<
        | ResponseReportSaleTypeByTravel[]
        | ResponseReportSaleTypeByBP[]
        | ResponseReportSaleTypeBySubject[]
      >
    >
  >(`/report_outputs/report-sale`, {
    method: 'GET',
    params,
  });
}
