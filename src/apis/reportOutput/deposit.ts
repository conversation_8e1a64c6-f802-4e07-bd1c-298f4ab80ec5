import type { BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type ReportDepositParamsType = {
  start?: string;
  finish?: string;
  travel_id?: string;
  limit?: number | string;
  page?: number;
  business_partner_id?: string;
  report_type?:
    | 'report_deposit_list'
    | 'report_deposit_list_unpaid_exp'
    | 'report_deposit_list_unpaid_collection_plan'
    | string;
};

export type DepositItemType = {
  deposit_item_id: number;
  current_deposit_amount: number;
  travel_id: number;
  tour_name: string;
  departure_date: string;
  return_date: string;
  sale_invoice_id: number;
  voucher_posting_date: string;
};

export type ResponseReportDepositType = {
  deposit_id: number;
  currency_type_master_id: number;
  currency_type_master: {
    id: number;
    currency_type_code: string;
    currency_type_name: string;
  };
  id: 5;
  fee: number;
  deposit_date: string;
  business_partner_id: number;
  business_partner_code: string;
  business_partner_name: string;
  total_deposit_amount: number;
  note: string | null;
  deposit_items: DepositItemType[];
};
export type TravelType = {
  deposit_slip_id: number;
  sale_invoice_id: number;
  deposit_date: string;
  travel_id: string;
  tour_name: string;
  total_amount: number;
  departure_date: string;
  return_date: string;
  voucher_posting_date: string;
  sale_date: string;
  issued_date: string;
  previous_paid: number;
  current_paid: number;
  remaining_amount: number;
  expired_days: number;
  payment_deadline: number;
  consolidated_invoice_id: string;
  note: string;
};

export type ResponseReportUnpaidDepositType = {
  business_partner_id: number;
  business_partner_code: string;
  business_partner_name: string;
  travels: TravelType[];
};

export type ResponseReportDepositTypeOther = Record<
  string,
  {
    id: number;
    deposit_id: number;
    currency_type_master_id: number;
    currency_type_master: {
      id: number;
      currency_type_code: string;
      currency_type_name: string;
    };
    fee: number;
    deposit_date: string;
    business_partner_id: number;
    business_partner_code: string;
    business_partner_name: string;
    total_deposit_amount: number;
    note: string | null;
    deposit_items: {
      deposit_item_id: number;
      current_deposit_amount: number;
      travel_id: string;
      tour_name: string;
      departure_date: string;
      return_date: string;
      sale_invoice_id: string;
      voucher_posting_date: string;
    }[];
  }[]
>;

export async function getReportDeposit(params: ReportDepositParamsType) {
  return request<
    BaseResponse<
      ResponseList<
        | ResponseReportDepositType[]
        | ResponseReportUnpaidDepositType[]
        | ResponseReportDepositTypeOther
      >
    >
  >(`/report_outputs/report-deposit`, {
    method: 'GET',
    params,
  });
}
