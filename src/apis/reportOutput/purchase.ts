import type { BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type ReportPurchaseParamsType = {
  start?: string;
  finish?: string;
  travel_id?: string;
  limit?: number | string;
  page?: number;
  type_date?: 'voucher_posting_date' | 'departure_date' | 'return_date' | string;
  report_type?: 'report_by_travel' | 'report_by_business_partner' | 'report_by_subject' | string;
  is_export_excel?: boolean;
};

export type ResponseReportPurchaseTypeByTravel = {
  id: number;
  travel_code: string;
  departure_date: string;
  return_date: string;
  tour_name: string;
  sale: null;
  travel_type: null;
  month: string;
  total_sale_amount: number;
  total_purchase_amount: number;
  gross_profit_amount: number;
  commission: number;
  total_gross_profit: number;
  gross_profit_rate: number;
  sale_invoice: {
    id: number;
    business_partner_id: number;
    travel_id: number;
    total_amount_received: number;
    voucher_posting_date: string;
    sale_invoice_item: {
      id: number;
      sale_invoice_id: number;
      amount_excluding_tax: number;
      amount_including_tax: number;
      subject_id: number;
      summary_subject_name: string;
    }[];
  }[];
  purchase_invoice: {
    id: number;
    business_partner_id: number;
    travel_id: number;
    total_payment: number;
    voucher_posting_date: string;
    purchase_invoice_item: {
      id: number;
      purchase_invoice_id: number;
      amount_excluding_tax: number;
      amount_including_tax: number;
      r_amount: number;
      subject_id: number;
      summary_subject_name: string;
    }[];
  }[];
};
export type ResponseReportPurchaseTypeByBP = {
  items_ranking: ResponseItemsRankingReportPurchaseTypeByBP[];
  other_item: ResponseOtherItemReportPurchaseTypeByBP;
  total_items: ResponseTotalItemReportPurchaseTypeByBP;
};

export type ResponseItemsRankingReportPurchaseTypeByBP = {
  id: number;
  business_partner_code: string;
  business_partner_name: string;
  total_item_quantity: number;
  total_amount_excluding_tax: number;
  total_amount_including_tax: number;
  total_r_amount: number;
  total_consumption_tax: number;
  total_payment_amount: number;
  occupancy_rate: number;
};

export type ResponseOtherItemReportPurchaseTypeByBP = {
  total_item_quantity: number;
  total_amount_excluding_tax: number;
  total_amount_including_tax: number;
  total_r_amount: number;
  total_consumption_tax: number;
  total_payment_amount: number;
  total_occupancy_rate: number;
};
export type ResponseTotalItemReportPurchaseTypeByBP = {
  total_amount_excluding_tax: number;
  total_amount_including_tax: number;
  total_r_amount: number;
  total_payment_amount: number;
  total_consumption_tax: number;
};

export type ResponseReportPurchaseTypeBySubject = {
  id: number;
  travel_code: string;
  departure_date: string;
  return_date: string;
  tour_name: string;
  sale: null;
  travel_type: null;
  month: string;
  total_sale_amount: number;
  total_purchase_amount: number;
  gross_profit_amount: number;
  commission: number;
  total_gross_profit: number;
  gross_profit_rate: number;
  subject_items: Record<
    string,
    {
      subject_id: number;
      summary_subject_name: string;
      total_sale_amount: number;
      total_purchase_amount: number;
      commission: number;
      total_gross_profit: number;
      gross_profit_rate: number;
    }
  >;
  sale_invoice: {
    id: number;
    business_partner_id: number;
    travel_id: number;
    total_amount_received: number;
    voucher_posting_date: string;
    sale_invoice_item: {
      id: number;
      sale_invoice_id: number;
      amount_excluding_tax: number;
      amount_including_tax: number;
      subject_id: number;
      summary_subject_name: string;
    }[];
  }[];
  purchase_invoice: {
    id: number;
    business_partner_id: number;
    travel_id: number;
    total_payment: number;
    voucher_posting_date: string;
    purchase_invoice_item: {
      id: number;
      purchase_invoice_id: number;
      amount_excluding_tax: number;
      amount_including_tax: number;
      r_amount: number;
      subject_id: number;
      summary_subject_name: string;
    }[];
  }[];
};
export type ResponseReportPurchaseTypeForTravel = {
  id: number;
  total_amount_excluding_tax: number;
  total_consumption_tax: number;
  total_amount_including_tax: number;
  total_r_amount: number;
  total_payment: number;
  payment_offset: number;
  travel_id: string;
  travel_code: string;
  tour_name: string;
  departure_date: string;
  return_date: string;
  travel_type: string;
  business_partner_items: {
    purchase_invoice_id: number[];
    business_partner_id: number;
    business_partner_name: string;
    business_partner_code: string;
    tax_category_code: number;
    tax_rate: number;
    amount_excluding_tax: number;
    consumption_tax: number;
    amount_including_tax: number;
    r_amount: number;
    total_payment: number;
    payment_offset: number;
    tax_category_name: string;
    purchase_invoice_code: string;
    item_type: string;
    purchase_date: string;
  }[][];
};

export type ResponseReportPurchaseTypeForBP = {
  id: number;
  total_amount_excluding_tax: number;
  total_consumption_tax: number;
  total_amount_including_tax: number;
  total_r_amount: number;
  total_payment: number;
  payment_offset: number;
  business_partner_code: string;
  business_partner_name: string;
  travel_items: {
    purchase_invoice_id: number[];
    travel_id: number;
    travel_code: string;
    tour_name: string;
    tax_category_code: number;
    tax_rate: number;
    amount_excluding_tax: number;
    consumption_tax: number;
    amount_including_tax: number;
    r_amount: number;
    total_payment: number;
    payment_offset: number;
    purchase_date: string;
    item_type: string;
    purchase_invoice_code: string;
    tax_category_name: string;
    product_name: string;
  }[][];
};

export async function getReportPurchase(params: ReportPurchaseParamsType) {
  return request<
    BaseResponse<
      ResponseList<
        | ResponseReportPurchaseTypeForTravel[]
        | ResponseReportPurchaseTypeForBP[]
        | ResponseReportPurchaseTypeByBP[]
      >
    >
  >(`/report_outputs/report-purchase`, {
    method: 'GET',
    params,
  });
}
