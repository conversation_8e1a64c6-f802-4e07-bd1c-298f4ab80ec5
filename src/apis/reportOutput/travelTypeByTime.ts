import type { BaseResponse } from '@/@types/request';
import request from '@/utils/request';

export type ReportSaleParamsType = {
  start?: string;
  finish?: string;
  travel_id?: string;
  limit?: number | string;
  page?: number;
  type_date?: 'voucher_posting_date' | 'departure_date' | 'return_date' | string;
  report_type?: 'report_by_year' | 'report_by_month' | string;
  is_export_excel?: boolean;
};

export type ResponseReportTravelTypeByMonth = {
  data: ResponseDataReportTravelTypeByMonth[];
  file_url?: string;
};

export type ResponseDataReportTravelTypeByMonth = {
  id: number;
  category_id: number;
  travel_code: string;
  category_name: string;
  total_sale_amount: number;
  total_purchase_amount: number;
  commission: number;
  gross_profit_amount: number;
  total_gross_profit: number;
  gross_profit_rate: number;
};

export type ResponseReportTravelTypeByMonthWithLastYear = {
  data: ResponseReportDataTravelTypeByMonthWithLastYear[];
  summary: {
    month: string;
    year: string;
    total_sale_amount: number;
    total_purchase_amount: number;
    commission: number;
    gross_profit_amount: number;
    total_gross_profit: number;
    gross_profit_rate: number;
    total_sale_amount_last_year: number;
    total_purchase_amount_last_year: number;
    commission_last_year: number;
    gross_profit_amount_last_year: number;
    total_gross_profit_last_year: number;
    gross_profit_rate_last_year: number;
    total_sale_amount_diff: number;
    total_purchase_amount_diff: number;
    commission_diff: number;
    gross_profit_amount_diff: number;
    total_gross_profit_diff: number;
    gross_profit_rate_diff: number;
  };
  file_url?: string;
};

export type ResponseReportDataTravelTypeByMonthWithLastYear = {
  id: number;
  category_id: number;
  travel_code: string;
  category_name: string;
  total_sale_amount: number;
  total_purchase_amount: number;
  commission: number;
  gross_profit_amount: number;
  total_gross_profit: number;
  gross_profit_rate: number;
  total_sale_amount_last_year: number;
  total_purchase_amount_last_year: number;
  commission_last_year: number;
  gross_profit_amount_last_year: number;
  total_gross_profit_last_year: number;
  gross_profit_rate_last_year: number;
  total_sale_amount_diff: number;
  total_purchase_amount_diff: number;
  commission_diff: number;
  gross_profit_amount_diff: number;
  total_gross_profit_diff: number;
  gross_profit_rate_diff: number;
};

export type ResponseReportTravelTypeByYear = ResponseDataReportTravelTypeByYear[];

export type ResponseDataReportTravelTypeByYear = {
  id: number;
  travel_name: string;
  travel_code: string;
  monthly_data: Record<
    string,
    {
      total_sales: number;
      total_purchases: number;
      total_commission: number;
      gross_profit: number;
      total_gross_profit: number;
      total_gross_profit_rate: number;
    }
  >;
  summary?: Record<
    string,
    {
      total_sales: number;
      total_purchases: number;
      total_commission: number;
      gross_profit: number;
      total_gross_profit: number;
      total_gross_profit_rate: number;
    }
  >;
};

export type ResponseReportTravelTypeForLastYear = ResponseDataReportTravelTypeForLastYear[];

export type ResponseDataReportTravelTypeForLastYear = {
  id: number;
  travel_name: string;
  travel_code: string;
  monthly_data: Record<
    string,
    {
      total_sales: number;
      total_purchases: number;
      total_commission: number;
      gross_profit: number;
      total_gross_profit: number;
      total_gross_profit_rate: number;
      past_year_total_sales: number;
      past_year_total_gross_profit: number;
      compare_total_sales: number;
      compare_total_gross_profit: number;
    }
  >;
  summary?: Record<
    string,
    {
      total_sales: number;
      total_purchases: number;
      total_commission: number;
      gross_profit: number;
      total_gross_profit: number;
      total_gross_profit_rate: number;
      past_year_total_sales: number;
      past_year_total_gross_profit: number;
      compare_total_sales: number;
      compare_total_gross_profit: number;
    }
  >;
};

export async function getReportTravelByMonth(params: ReportSaleParamsType) {
  return request<
    BaseResponse<ResponseReportTravelTypeByMonth | ResponseReportTravelTypeByMonthWithLastYear>
  >(`/report_outputs/report-travel-master-by-month`, {
    method: 'GET',
    params,
  });
}

export async function getReportTravelByYear(params: ReportSaleParamsType) {
  return request<BaseResponse<{ data: ResponseReportTravelTypeByYear; file_url?: string }>>(
    `/report_outputs/report-travel-master-by-year`,
    {
      method: 'GET',
      params,
    },
  );
}
