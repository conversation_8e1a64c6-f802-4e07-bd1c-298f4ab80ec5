import type { BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type ReportPaymentParamsType = {
  start?: string;
  finish?: string;
  travel_id?: string;
  limit?: number | string;
  page?: number;
  type_date?: 'voucher_posting_date' | 'departure_date' | 'return_date' | string;
  report_type?: 'report_payment_list' | 'report_payment_list_unpaid' | string;
};

export type ResponseReportPaymentTypeByTravel = {
  id: number;
  travel_code: string;
  departure_date: string;
  return_date: string;
  tour_name: string;
  sale: null;
  travel_type: null;
  month: string;
  total_sale_amount: number;
  total_purchase_amount: number;
  gross_profit_amount: number;
  commission: number;
  total_gross_profit: number;
  gross_profit_rate: number;
  sale_invoice: {
    id: number;
    business_partner_id: number;
    travel_id: number;
    total_amount_received: number;
    voucher_posting_date: string;
    sale_invoice_item: {
      id: number;
      sale_invoice_id: number;
      amount_excluding_tax: number;
      amount_including_tax: number;
      subject_id: number;
      summary_subject_name: string;
    }[];
  }[];
  purchase_invoice: {
    id: number;
    business_partner_id: number;
    travel_id: number;
    total_payment: number;
    voucher_posting_date: string;
    purchase_invoice_item: {
      id: number;
      purchase_invoice_id: number;
      amount_excluding_tax: number;
      amount_including_tax: number;
      r_amount: number;
      subject_id: number;
      summary_subject_name: string;
    }[];
  }[];
};
export type ResponseReportPaymentTypeByBP = {
  id: number;
  total_amount_excluding_tax: number;
  total_consumption_tax: number;
  total_amount_including_tax: number;
  total_r_amount: number;
  total_payment: number;
  business_partner_code: string;
  business_partner_name: string;
};
export type ResponseReportPaymentTypeBySubject = {
  id: number;
  travel_code: string;
  departure_date: string;
  return_date: string;
  tour_name: string;
  sale: null;
  travel_type: null;
  month: string;
  total_sale_amount: number;
  total_purchase_amount: number;
  gross_profit_amount: number;
  commission: number;
  total_gross_profit: number;
  gross_profit_rate: number;
  subject_items: Record<
    string,
    {
      subject_id: number;
      summary_subject_name: string;
      total_sale_amount: number;
      total_purchase_amount: number;
      commission: number;
      total_gross_profit: number;
      gross_profit_rate: number;
    }
  >;
  sale_invoice: {
    id: number;
    business_partner_id: number;
    travel_id: number;
    total_amount_received: number;
    voucher_posting_date: string;
    sale_invoice_item: {
      id: number;
      sale_invoice_id: number;
      amount_excluding_tax: number;
      amount_including_tax: number;
      subject_id: number;
      summary_subject_name: string;
    }[];
  }[];
  purchase_invoice: {
    id: number;
    business_partner_id: number;
    travel_id: number;
    total_payment: number;
    voucher_posting_date: string;
    purchase_invoice_item: {
      id: number;
      purchase_invoice_id: number;
      amount_excluding_tax: number;
      amount_including_tax: number;
      r_amount: number;
      subject_id: number;
      summary_subject_name: string;
    }[];
  }[];
};
export type ResponseReportPaymentType = {
  id: number;
  payment_slip_id: number;
  payment_amount: number;
  payment_date: string;
  fee: number;
  memo: null;
  accounting_linked: number;
  is_cancelled: number;
  is_inversed: number;
  payment_slip_inverse_id: null;
  total_amount_including_tax: number;
  total_r_amount: number;
  total_current_payment_amount: number;
  total_payment_balance: number;
  total_payment_amount_after_r_amount: number;
  business_partner: {
    id: number;
    business_partner_code: string;
    business_partner_name: string;
  };
  currency_type_master: {
    id: number;
    currency_type_code: string;
    currency_type_name: string;
  };
  items: {
    id: number;
    payment_slip_id: number;
    purchase_invoice_item_id: number;
    purchase_invoice_item_code: string;
    purchase_invoice_code: string;
    product_name: string;
    summary_subject_name: string;
    current_payment_amount: number;
    purchase_invoice_id: number;
    amount_excluding_tax: number;
    consumption_tax: number;
    amount_including_tax: number;
    r_amount: number;
    payment_balance: number;
    payment_amount_after_r_amount: number;
    voucher_posting_date: string;
    purchase_date?: string;
    travel_id: number;
    travel_code: string;
    tour_name: string;
  }[];
};

export type ResponseReportPaymentUnpaidType = {
  business_partner: {
    id: number;
    business_partner_code: number;
    business_partner_name: string;
  };
  items: {
    id: number;
    amount_excluding_tax: number;
    consumption_tax: number;
    amount_including_tax: number;
    r_amount: number;
    r_amount_excluding_tax: number;
    summary_subject_name: string;
    product_name: string;
    purchase_date: string;
    business_partner_id: number;
    business_partner_code: number;
    business_partner_name: string;
    business_partner_name_kana: string;
    payment_slip_id: number;
    payment_slip_code: number;
    payment_amount: number;
    currency_type_master_id: number;
    payment_date: string;
    fee: number;
    memo: null | string;
    accounting_linked: number;
    is_cancelled: number;
    is_inversed: number;
    payment_slip_inverse_id: null | number;
    travel_id: number;
    travel_code: string;
    departure_date: string;
    return_date: string;
    tour_name: string;
    purchase_invoice_code: string;
    current_payment_amount: number;
  }[];
  total_amount_tax: number;
  total_r_amount: number;
  total_current_payment_amount: number;
  total_payment_balance: number;
  total_payment_amount_after_r_amount: number;
};

export async function getReportPayment(params: ReportPaymentParamsType) {
  return request<ResponseReportPaymentType[][] | ResponseReportPaymentUnpaidType[]>(
    `/report_outputs/report-payment`,
    {
      method: 'GET',
      params,
    },
  );
}
