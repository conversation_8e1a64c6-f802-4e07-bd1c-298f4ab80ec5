import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import type { TravelTypeMasterDetailType, TravelTypeMasterType } from '@/@types/travelTypeMaster';
import request from '@/utils/request';

export async function getListTravelTypeMaster(params: BaseParams) {
  return request<ResponseList<TravelTypeMasterDetailType[]>>(`/travel_master`, {
    method: 'GET',
    params: params,
  });
}

export async function getTravelTypeMasterDetail(id: number) {
  return request<BaseResponse<any>>(`/travel_master/${id}`, {
    method: 'GET',
  });
}

export async function createTravelTypeMaster(payload: { data: TravelTypeMasterType[] }) {
  return request<BaseResponse<any>>(`/travel_master`, {
    method: 'PUT',
    data: payload,
  });
}

export async function createOneTravelTypeMaster(payload: { data: TravelTypeMasterType }) {
  return request<BaseResponse<any>>(`/travel_master`, {
    method: 'POST',
    data: payload.data,
  });
}

export async function updateTravelTypeMaster(id: number, payload: TravelTypeMasterType) {
  return request<BaseResponse<any>>(`/travel_master/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteTravelTypeMaster(id: number) {
  return request<BaseResponse<any>>(`/travel_master/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvTravelTypeMaster(formData: FormData) {
  return request(`/travel_master/import/csv`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportTravelTypeMaster(params: BaseParams) {
  return request<any>('/travel_master/export/csv', {
    method: 'GET',
    params: params,
  });
}
