import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type UserItem = {
  id: number;
  name: string;
  email: string;
  is_active: boolean;
  last_login_at: string;
  roles: Role[];
};

export type DataGetMe = {
  user: UserItem;
  models: string[];
  role: {
    id: number;
    name: string;
  };
  permissions: string[];
};

export type Role = {
  id: number;
  name: string;
  pivot: {
    model_id: number;
    role_id: number;
    model_type: string;
  };
  permissions: Permission[];
};

export type Permission = {
  id: number;
  name: string;
  model: string;
  pivot: {
    role_id: number;
    permission_id: number;
  };
};

export type BodyUpdateUserType = {
  id: number;
  name?: string;
  email?: string;
  is_active?: number;
  role?: number;
};

export type BodyCreateUserType = {
  name: string;
  email: string;
  role: number;
};

export async function getListUsers(params: BaseParams) {
  return request<ResponseList<UserItem[]>>(`/users`, {
    method: 'GET',
    params,
  });
}

export async function getDetailUser(id: number | string) {
  return request<BaseResponse<UserItem>>(`/users/${id}`, {
    method: 'GET',
  });
}

export async function updateUser(data: BodyUpdateUserType) {
  return request<BaseResponse<UserItem>>(`/users/${data.id}`, {
    method: 'PUT',
    data,
  });
}
export async function createUser(data: BodyCreateUserType) {
  return request<BaseResponse<UserItem>>(`/users`, {
    method: 'POST',
    data,
  });
}
