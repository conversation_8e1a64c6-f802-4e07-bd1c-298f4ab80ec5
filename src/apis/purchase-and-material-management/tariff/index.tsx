import type { BaseParams, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type TariffType = Record<string, any>;

export type EntityType =
  | 'travel-spot'
  | 'hotel'
  | 'restaurant'
  | 'bus'
  | 'hire-car'
  | 'bullet-train'
  | 'airplane'
  | 'ship'
  | 'guide'
  | 'delivery'
  | 'service-other';

export enum EntityEnum {
  'travel-spot' = 'travel-spot',
  'hotel' = 'hotel',
  'restaurant' = 'restaurant',
  'bus' = 'bus',
  'hire-car' = 'hire-car',
  'bullet-train' = 'bullet-train',
  'airplane' = 'airplane',
  'ship' = 'ship',
  'guide' = 'guide',
  'delivery' = 'delivery',
  'service-other' = 'service-other',
}

export enum UploadFileEntityEnum {
  'travel_spot' = 'travel_spot',
  'hotel' = 'hotel',
  'restaurant' = 'restaurant',
  'bus' = 'bus',
  'hire_car' = 'hire_car',
  'bullet_train' = 'bullet_train',
  'airplane' = 'airplane',
  'ship' = 'ship',
  'tour_guides' = 'tour_guides',
  'delivery' = 'delivery',
  'service_other' = 'service_other',
}

export enum EntityCancelFeeEnum {
  'travel-spot-cancel-fee' = 'travel-spot-cancel-fee',
  'hotel-cancel-fee' = 'hotel-cancel-fee',
  'restaurant-cancel-fee' = 'restaurant-cancel-fee',
  'bus-cancel-fee' = 'bus-cancel-fee',
  'hire-car-cancel-fee' = 'hire-car-cancel-fee',
  'bullet-train-cancel-fee' = 'bullet-train-cancel-fee',
  'airplane-cancel-fee' = 'airplane-cancel-fee',
  'ship-cancel-fee' = 'ship-cancel-fee',
  'guide-cancel-fee' = 'guide-cancel-fee',
  'delivery-cancel-fee' = 'delivery-cancel-fee',
  'service-other-cancel-fee' = 'service-other-cancel-fee',
}

export type TariffParam = BaseParams & {
  type: 'price' | 'cancel_fee';
  entity_id: string | number;
};

export type PayloadTarrifType = {
  tariffable_id: number;
  data: TariffType[];
};

export async function getListTariffByEntity(TariffParam: TariffParam, entity: EntityEnum) {
  return request<ResponseList<Record<string, any>[]>>(`/tariffs/${entity}`, {
    method: 'GET',
    params: TariffParam,
  });
}

export async function createTariff(entity: EntityEnum, payload: PayloadTarrifType) {
  return request<ResponseList<Record<string, any>[]>>(`/tariffs/${entity}`, {
    method: 'POST',
    data: payload,
  });
}

export async function updateTariff(
  payload: PayloadTarrifType & {
    type: 'price' | 'cancel_fee';
  },
  entity: EntityEnum,
) {
  return request<ResponseList<Record<string, any>[]>>(`/tariffs/${entity}`, {
    method: 'POST',
    data: payload,
  });
}

export async function deleteTariff(tariff_id: number) {
  return request<ResponseList<any>>(`/tariffs/${tariff_id}`, {
    method: 'DELETE',
  });
}

export async function updateFileLinkTariff(
  tariffId: number,
  payload: {
    file_link: string;
  },
) {
  return request<ResponseList<Record<string, any>[]>>(`/tariffs/${tariffId}/update-file_link`, {
    method: 'PATCH',
    data: payload,
  });
}

// upload file material
export type ParamFileEntity = {
  type: UploadFileEntityEnum;
  id: string | number;
  keyword?: string;
};

export async function getFileMasterDataByEntity(params: ParamFileEntity & BaseParams) {
  return request<ResponseList<Record<string, any>[]>>(`/file/master-data`, {
    method: 'GET',
    params,
  });
}

export async function uploadFileMasterDataByEntity(payload: FormData, config: any) {
  return request<ResponseList<Record<string, any>[]>>(`/file/master-data/upload`, {
    method: 'POST',
    data: payload,
    ...config,
  });
}

export async function deleteFileMasterDataByEntity(path: string) {
  return request<ResponseList<any>>(`/file/master-data`, {
    method: 'DELETE',
    params: { path },
  });
}
