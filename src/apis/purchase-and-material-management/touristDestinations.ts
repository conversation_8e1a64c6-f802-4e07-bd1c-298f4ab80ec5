import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type TouristSpotType = {
  control_number: string | number;
  name_jp: string;
  name_en: string;
  prefecture_code: string;
  city_code: string;
  prefecture: any;
  city?: any;
  municipalities: any;
  address?: string;
  home_page?: string;
  reservation_method: string[];
  payment_method: string[];
  memo: string;
  parking_lot: 1 | 0;
  parking_fee: number;
  phone_number?: string;
  fax?: string;
  email?: string;
  manager?: string;
  business_partner_id: number;
};

export type TouristSpotDetailType = TouristSpotType & {
  id: number;
  business_partner: any;
  orderNumber?: number;
};

export type TouristDestinationPriceListType = {
  id?: number | string;
  key?: string | number;
  title: string;
  adult: number;
  child: number;
};

export type CommonCancellationFeeType = {
  id: number | string;
  key?: number | string;
  title: string;
  content?: string;
  cancellation_fee?: string;
};

export async function getListTouristSpot(params: BaseParams) {
  return request<ResponseList<TouristSpotDetailType[]>>('/travel_spots', {
    method: 'GET',
    params: params,
  });
}

export async function getTouristSpotDetail(id: number | string) {
  return request<BaseResponse<TouristSpotDetailType>>(`/travel_spots/${id}`, {
    method: 'GET',
  });
}

export async function deleteTouristSpot(id: number) {
  return request(`/travel_spots/${id}`, {
    method: 'DELETE',
  });
}

export async function createTouristSpot(body: TouristSpotType) {
  return request(`/travel_spots`, {
    method: 'POST',
    data: body,
  });
}

export async function updateTouristSpot(id: number, body: TouristSpotType) {
  return request(`/travel_spots/${id}`, {
    method: 'PUT',
    data: body,
  });
}

export async function importCsvPriceTouristSpot(formData: FormData, travel_spot_id: string) {
  return request(`/travel_spots/${travel_spot_id}/import/csv-price`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvPriceTouristSpot(params: BaseParams, travel_spot_id: number) {
  return request<any>(`/travel_spots/${travel_spot_id}/export/csv-price`, {
    method: 'GET',
    params: params,
  });
}
export async function importCsvCancelFeeTouristSpot(formData: FormData, travel_spot_id: string) {
  return request(`/travel_spots/${travel_spot_id}/import/csv-cancellation-fee`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvCancelFeeTouristSpot(params: BaseParams, travel_spot_id: number) {
  return request<any>(`/travel_spots/${travel_spot_id}/export/csv-cancellation-fee`, {
    method: 'GET',
    params: params,
  });
}
