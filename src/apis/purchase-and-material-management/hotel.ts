import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type HotelType = {
  control_number?: string;
  name_en: string;
  name_jp: string;
  address: string;
  prefecture_code: string;
  city_code: string;
  rank: number | string;
  representative_number: number;
  memo: string;
  phone_number: string;
  home_page: string;
  fax: string;
  email: string;
  manager: string;
  reservation_method: string[];
  payment_method: string[];
  parking_lot: 1 | 0;
  parking_fee: number;
  business_partner_id: number;
  prefecture?: any; //
  city?: any;
  municipalities?: any; //
};

export type HotelDetailtype = HotelType & {
  id?: number;
  business_partner?: any;
  orderNumber?: number;
};

export type HotelListPriceType = {
  id: number;
  key?: number | string;
  roomType: string;
  meal: string[];
  fee: string | number;
};

export type HotelCancellationFeeType = {
  id: number | string;
  key?: number | string;
  title: string;
  content: string;
};

export async function getListHotel(params: BaseParams) {
  return request<ResponseList<HotelDetailtype[]>>(`/hotels`, {
    method: 'GET',
    params,
  });
}

export async function getHotelDetail(id: number) {
  return request<BaseResponse<HotelDetailtype>>(`/hotels/${id}`, {
    method: 'GET',
  });
}

export async function createHotel(payload: HotelType) {
  return request<BaseResponse<any>>(`/hotels`, {
    method: 'POST',
    data: payload,
  });
}

export async function updateHotel(id: number, payload: HotelType) {
  return request<BaseResponse<any>>(`/hotels/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteHotel(id: number) {
  return request<BaseResponse<any>>(`/hotels/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvPriceHotel(formData: FormData, hotel_id: string) {
  return request(`/hotels/${hotel_id}/import/csv-price`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvPriceHotel(params: BaseParams, hotel_id: number) {
  return request<any>(`/hotels/${hotel_id}/export/csv-price`, {
    method: 'GET',
    params: params,
  });
}
export async function importCsvCancelFeeHotel(formData: FormData, hotel_id: string) {
  return request(`/hotels/${hotel_id}/import/csv-cancellation-fee`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvCancelFeeHotel(params: BaseParams, hotel_id: number) {
  return request<any>(`/hotels/${hotel_id}/export/csv-cancellation-fee`, {
    method: 'GET',
    params: params,
  });
}
