import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type DeliveryType = {
  control_number: string;
  name_jp: string;
  name_en: string;
  reservation_method?: string[];
  payment_method?: string[];
  address?: string;
  home_page?: string;
  memo?: string;
  phone_number?: string;
  fax?: string;
  email?: string;
  manager?: string;
  business_partner_id: number | string;
};

export type DeliveryDetailType = DeliveryType & {
  id: string | number;
  key: string | number;
  business_partner: any;
  orderNumber?: number;
};

export type DeliveryPriceType = {
  id: string | number;
  key: string | number;
  title: string;
  fee_per_item: number;
  truck_2t: number;
  truck_4t: number;
  other: number;
};

export async function getListDelivery(params: BaseParams) {
  return request<ResponseList<DeliveryDetailType[]>>(`/deliveries`, {
    method: 'GET',
    params,
  });
}

export async function getDeliveryDetail(id: number) {
  return request<BaseResponse<DeliveryDetailType>>(`/deliveries/${id}`, {
    method: 'GET',
  });
}

export async function createDelivery(payload: DeliveryType) {
  return request<BaseResponse<any>>(`/deliveries`, {
    method: 'POST',
    data: payload,
  });
}

export async function updateDelivery(id: number, payload: DeliveryType) {
  return request<BaseResponse<any>>(`/deliveries/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteDelivery(id: number) {
  return request<BaseResponse<any>>(`/deliveries/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvPriceDelivery(formData: FormData, delivery_id: string) {
  return request(`/deliveries/${delivery_id}/import/csv-price`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvPriceDelivery(params: BaseParams, delivery_id: number) {
  return request<any>(`/deliveries/${delivery_id}/export/csv-price`, {
    method: 'GET',
    params: params,
  });
}
export async function importCsvCancelFeeDelivery(formData: FormData, delivery_id: string) {
  return request(`/deliveries/${delivery_id}/import/csv-cancellation-fee`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvCancelFeeDelivery(params: BaseParams, delivery_id: number) {
  return request<any>(`/deliveries/${delivery_id}/export/csv-cancellation-fee`, {
    method: 'GET',
    params: params,
  });
}
