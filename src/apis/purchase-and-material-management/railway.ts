import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type RailwayType = {
  control_number: string;
  name_jp: string;
  name_en: string;
  home_page: string;
  reservation_method: string[];
  payment_method: string[];
  memo: string;
  phone_number?: string;
  fax?: string;
  email?: string;
  manager?: string;
  business_partner_id: number | string;
};

export type RailwayDetailType = RailwayType & {
  id: number;
  key: number;
  business_partner?: any;
  orderNumber?: number;
};

export type RailwayPriceListType = {
  id: number;
  key: number;
  departure: string;
  destination: string;
  adult: number;
  child: number;
};

export async function getListRailway(params: BaseParams) {
  return request<ResponseList<RailwayDetailType[]>>(`/bullet_trains`, {
    method: 'GET',
    params,
  });
}

export async function getRailwayDetail(id: number) {
  return request<BaseResponse<RailwayDetailType>>(`/bullet_trains/${id}`, {
    method: 'GET',
  });
}
export async function createRailway(payload: RailwayType) {
  return request<BaseResponse<any>>(`/bullet_trains`, {
    method: 'POST',
    data: payload,
  });
}

export async function updateRailway(id: number, payload: RailwayType) {
  return request<BaseResponse<any>>(`/bullet_trains/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteRailway(id: number) {
  return request<BaseResponse<any>>(`/bullet_trains/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvPriceRailway(formData: FormData, bullet_train_id: string) {
  return request(`/bullet_trains/${bullet_train_id}/import/csv-price`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvPriceRailway(params: BaseParams, bullet_train_id: number) {
  return request<any>(`/bullet_trains/${bullet_train_id}/export/csv-price`, {
    method: 'GET',
    params: params,
  });
}
export async function importCsvCancelFeeRailway(formData: FormData, bullet_train_id: string) {
  return request(`/bullet_trains/${bullet_train_id}/import/csv-cancellation-fee`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvCancelFeeRailway(params: BaseParams, bullet_train_id: number) {
  return request<any>(`/bullet_trains/${bullet_train_id}/export/csv-cancellation-fee`, {
    method: 'GET',
    params: params,
  });
}
