import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type ParamsListBusTariff = {
  keyword?: string;
};

export type BusType = {
  control_number?: string;
  home_page?: string;
  memo?: string;
  name_en?: string;
  name_jp?: string;
  address?: string;
  prefecture?: any;
  city?: any;
  municipalities?: string;
  area?: string;
  vehicle_type: string[];
  reservation_method: string[];
  payment_method: string[];
  phone_number?: string;
  main_phone_number?: string;
  fax?: string;
  email?: string;
  manager?: string;
  bus_type?: string;
};

export type BusDetailType = BusType & {
  id?: number;
  key?: string | number;
  business_partner: any;
  orderNumber?: number;
};

export type BusPriceType = {
  id: string | number;
  key: string | number;
  title: string;
  large: number;
  medium: number;
  small: number;
  commuter: number;
  others: number;
};

export async function getListBus(params: BaseParams) {
  return request<ResponseList<BusDetailType[]>>('/bus', {
    method: 'GET',
    params: params,
  });
}

export async function getBusDetail(busId: number) {
  return request<BaseResponse<BusDetailType>>(`/bus/${busId}`, {
    method: 'GET',
  });
}

export async function createBus(payload: BusType) {
  return request<BaseResponse<any>>('/bus', {
    method: 'POST',
    data: payload,
  });
}

export async function deleteBus(busId: number) {
  return request<BaseResponse<any>>(`/bus/${busId}`, {
    method: 'DELETE',
  });
}

export async function updateBus(busId: number | string, payload: BusType) {
  return request<BaseResponse<any>>(`/bus/${busId}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function importCsvPriceBus(formData: FormData, bus_id: string) {
  return request(`/bus/${bus_id}/import/csv-price`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvPriceBus(params: BaseParams, bus_id: number) {
  return request<any>(`/bus/${bus_id}/export/csv-price`, {
    method: 'GET',
    params: params,
  });
}
export async function importCsvCancelFeeBus(formData: FormData, bus_id: string) {
  return request(`/bus/${bus_id}/import/csv-cancellation-fee`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvCancelFeeBus(params: BaseParams, bus_id: number) {
  return request<any>(`/bus/${bus_id}/export/csv-cancellation-fee`, {
    method: 'GET',
    params: params,
  });
}
