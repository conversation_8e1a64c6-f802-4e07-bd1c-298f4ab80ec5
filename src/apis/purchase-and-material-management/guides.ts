import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type GuideType = {
  control_number: string;
  name_jp: string;
  name_en: string;
  prefecture: any;
  city: any;
  municipalities: string;
  address?: string;
  home_page?: string;
  memo?: string;
  reservation_method?: string[];
  phone_number?: string;
  fax?: string;
  email?: string;
  manager?: string;
  business_partner_id: number | string;
};

export type GuideDetailType = GuideType & {
  id: string | number;
  key: string | number;
  business_partner: any;
  orderNumber?: number;
};

export type GuidePriceType = {
  id: string | number;
  key: string | number;
  title: string;
  gender: string;
  language: string;
  age: number;
  yearOfExperience: number;
  priceOneDay: number;
  chip: number;
};

export async function getListGuide(params: BaseParams) {
  return request<ResponseList<GuideDetailType[]>>(`/tour_guides`, {
    method: 'GET',
    params,
  });
}

export async function getGuideDetail(id: number) {
  return request<BaseResponse<GuideDetailType>>(`/tour_guides/${id}`, {
    method: 'GET',
  });
}

export async function createGuide(payload: GuideType) {
  return request<BaseResponse<any>>(`/tour_guides`, {
    method: 'POST',
    data: payload,
  });
}

export async function updateGuide(id: number, payload: GuideType) {
  return request<BaseResponse<any>>(`/tour_guides/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteGuide(id: number) {
  return request<BaseResponse<any>>(`/tour_guides/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvPriceGuide(formData: FormData, tour_guide_id: string) {
  return request(`/tour_guides/${tour_guide_id}/import/csv-price`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvPriceGuide(params: BaseParams, tour_guide_id: number) {
  return request<any>(`/tour_guides/${tour_guide_id}/export/csv-price`, {
    method: 'GET',
    params: params,
  });
}
export async function importCsvCancelFeeGuide(formData: FormData, tour_guide_id: string) {
  return request(`/tour_guides/${tour_guide_id}/import/csv-cancellation-fee`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvCancelFeeGuide(params: BaseParams, tour_guide_id: number) {
  return request<any>(`/tour_guides/${tour_guide_id}/export/csv-cancellation-fee`, {
    method: 'GET',
    params: params,
  });
}
