import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type OtherType = {
  control_number: string;
  name_jp: string;
  name_en: string;
  reservation_method?: string[];
  payment_method?: string[];
  address?: string;
  home_page?: string;
  memo?: string;
  phone_number?: string;
  fax?: string;
  email?: string;
  manager?: string;
  business_partner_id: number | string;
};

export type OtherDetailType = OtherType & {
  id: string | number;
  key: string | number;
  business_partner?: any;
  orderNumber?: number;
};

export type OtherPriceType = {
  id: string | number;
  key: string | number;
  title: string;
  fee: number;
};

export async function getListOther(params: BaseParams) {
  return request<ResponseList<OtherDetailType[]>>(`/service_others`, {
    method: 'GET',
    params,
  });
}

export async function getOtherDetail(id: number) {
  return request<BaseResponse<OtherDetailType>>(`/service_others/${id}`, {
    method: 'GET',
  });
}

export async function createOther(payload: OtherType) {
  return request<BaseResponse<any>>(`/service_others`, {
    method: 'POST',
    data: payload,
  });
}

export async function updateOther(id: number, payload: OtherType) {
  return request<BaseResponse<any>>(`/service_others/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteOther(id: number) {
  return request<BaseResponse<any>>(`/service_others/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvPriceOther(formData: FormData, service_other_id: string) {
  return request(`/service_others/${service_other_id}/import/csv-price`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvPriceOther(params: BaseParams, service_other_id: number) {
  return request<any>(`/service_others/${service_other_id}/export/csv-price`, {
    method: 'GET',
    params: params,
  });
}
export async function importCsvCancelFeeOther(formData: FormData, service_other_id: string) {
  return request(`/service_others/${service_other_id}/import/csv-cancellation-fee`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvCancelFeeOther(params: BaseParams, service_other_id: number) {
  return request<any>(`/service_others/${service_other_id}/export/csv-cancellation-fee`, {
    method: 'GET',
    params: params,
  });
}
