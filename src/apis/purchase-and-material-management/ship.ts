import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type ShipsType = {
  control_number: string;
  name_jp: string;
  name_en: string;
  prefecture: any;
  city: any;
  municipalities: string;
  address: string;
  home_page: string;
  reservation_method: string[];
  payment_method: string[];
  memo: string;
  phone_number?: string;
  fax?: string;
  email?: string;
  manager?: string;
  business_partner_id: number | string;
};

export type ShipDetailtype = ShipsType & {
  id: string | number;
  key: string | number;
  business_partner?: any;
  orderNumber?: number;
};

export type ShipPriceListType = {
  id: string | number;
  key: string | number;
  title: string;
  departure: string;
  destination: string;
  adult: number;
  child: number;
  others: number;
};

export async function getListShip(params: BaseParams) {
  return request<ResponseList<ShipDetailtype[]>>(`/ships`, {
    method: 'GET',
    params,
  });
}

export async function getShipDetail(id: number) {
  return request<BaseResponse<ShipDetailtype>>(`/ships/${id}`, {
    method: 'GET',
  });
}

export async function createShip(payload: ShipsType) {
  return request<BaseResponse<any>>(`/ships`, {
    method: 'POST',
    data: payload,
  });
}

export async function updateShip(id: number, payload: ShipsType) {
  return request<BaseResponse<any>>(`/ships/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteShip(id: number) {
  return request<BaseResponse<any>>(`/ships/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvPriceShip(formData: FormData, ship_id: string) {
  return request(`/ships/${ship_id}/import/csv-price`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvPriceShip(params: BaseParams, ship_id: number) {
  return request<any>(`/ships/${ship_id}/export/csv-price`, {
    method: 'GET',
    params: params,
  });
}
export async function importCsvCancelFeeShip(formData: FormData, ship_id: string) {
  return request(`/ships/${ship_id}/import/csv-cancellation-fee`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvCancelFeeShip(params: BaseParams, ship_id: number) {
  return request<any>(`/ships/${ship_id}/export/csv-cancellation-fee`, {
    method: 'GET',
    params: params,
  });
}
