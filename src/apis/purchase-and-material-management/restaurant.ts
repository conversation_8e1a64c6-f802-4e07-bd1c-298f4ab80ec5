import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type RestaurantType = {
  name_jp: string;
  name_en: string;
  control_number: string;
  prefecture?: any; //
  city?: any;
  municipalities?: any; //
  address: string;
  home_page: string;
  reservation_method: string[];
  payment_method: string[];
  parking_lot: 1 | 0;
  parking_fee: number;
  reservatiom_method: string;
  memo: string;
  phone_number: string;
  fax: string;
  email: string;
  manager: string;
  business_partner_id: string;
};

export type RestaurantDetaiType = RestaurantType & {
  id: number;
  control_number: string;
  key?: string | number;
  business_partner: any;
  orderNumber?: number;
};

export type RestaurantPriceListType = {
  id: number;
  key?: number;
  menu: string;
  adult: number;
  child: number;
  guide: number;
};

export type RestaurantCancellationFeeType = {
  id: number | string;
  key?: number | string;
  title: string;
  content: string;
};

export async function getListRestaurant(params: BaseParams) {
  return request<ResponseList<RestaurantDetaiType[]>>(`/restaurants`, {
    method: 'GET',
    params,
  });
}

export async function getRestaurantDetail(id: number) {
  return request<BaseResponse<RestaurantDetaiType>>(`/restaurants/${id}`, {
    method: 'GET',
  });
}

export async function createRestaurant(payload: RestaurantType) {
  return request<BaseResponse<any>>(`/restaurants`, {
    method: 'POST',
    data: payload,
  });
}

export async function updateRestaurant(id: number, payload: RestaurantType) {
  return request<BaseResponse<any>>(`/restaurants/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteRestaurant(id: number) {
  return request<BaseResponse<any>>(`/restaurants/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvPriceRestaurant(formData: FormData, restaurant_id: string) {
  return request(`/restaurants/${restaurant_id}/import/csv-price`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvPriceRestaurant(params: BaseParams, restaurants_id: number) {
  return request<any>(`/restaurants/${restaurants_id}/export/csv-price`, {
    method: 'GET',
    params: params,
  });
}
export async function importCsvCancelFeeRestaurant(formData: FormData, restaurants_id: string) {
  return request(`/restaurants/${restaurants_id}/import/csv-cancellation-fee`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvCancelFeeRestaurant(params: BaseParams, restaurants_id: number) {
  return request<any>(`/restaurants/${restaurants_id}/export/csv-cancellation-fee`, {
    method: 'GET',
    params: params,
  });
}
