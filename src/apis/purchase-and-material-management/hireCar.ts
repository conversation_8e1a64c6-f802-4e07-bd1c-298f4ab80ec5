import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type HireCarType = {
  control_number: string;
  name_jp: string;
  name_en: string;
  prefecture?: any; //
  city?: any;
  municipalities?: any; //
  address: string;
  home_page: string;
  area: string;
  reservation_method: string[];
  vehicle_type?: string[];
  payment_method: string[];
  memo: string;
  phone_number: string;
  fax: string;
  email: string;
  manager: string;
  tax: number;
  business_partner_id: string;
};

export type HireCarDetailType = HireCarType & {
  id: string | number;
  key: string | number;
  business_partner: any;
  orderNumber?: number;
};

export type TariffType = {
  id: number;
  type: string;
  fee: number;
};

export type HireCarPriceType = {
  id: string | number;
  key: string | number;
  title: string;
  luxury_sedan?: string | number;
  alphard?: string | number;
  hiace?: string | number;
  others?: string | number;
};

export async function getListHireCar(params: BaseParams) {
  return request<ResponseList<HireCarDetailType[]>>(`/hire_cars`, {
    method: 'GET',
    params: params,
  });
}

export async function getHireCarDetail(id: number | string) {
  return request<BaseResponse<HireCarDetailType>>(`/hire_cars/${id}`, {
    method: 'GET',
  });
}

export async function deleteHireCar(id: number) {
  return request<BaseResponse<any>>(`/hire_cars/${id}`, {
    method: 'DELETE',
  });
}

export async function createHireCar(body: HireCarType) {
  return request<BaseResponse<any>>(`/hire_cars`, {
    method: 'POST',
    data: body,
  });
}

export async function updateHireCar(id: number, body: HireCarType) {
  return request<BaseResponse<any>>(`/hire_cars/${id}`, {
    method: 'PUT',
    data: body,
  });
}

export async function importCsvPriceHireCar(formData: FormData, hire_car_id: string) {
  return request(`/hire_cars/${hire_car_id}/import/csv-price`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvPriceHireCar(params: BaseParams, hire_car_id: number) {
  return request<any>(`/hire_cars/${hire_car_id}/export/csv-price`, {
    method: 'GET',
    params: params,
  });
}
export async function importCsvCancelFeeHireCar(formData: FormData, hire_car_id: string) {
  return request(`/hire_cars/${hire_car_id}/import/csv-cancellation-fee`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvCancelFeeHireCar(params: BaseParams, hire_car_id: number) {
  return request<any>(`/hire_cars/${hire_car_id}/export/csv-cancellation-fee`, {
    method: 'GET',
    params: params,
  });
}
