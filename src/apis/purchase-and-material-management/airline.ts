import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type AirlineType = {
  control_number: string;
  name_jp: string;
  name_en: string;
  home_page?: string;
  reservation_method?: string[];
  payment_method?: string[];
  memo?: string;
  phone_number?: string;
  fax?: string;
  email?: string;
  manager?: string;
  business_partner_id?: string;
};

export type AirlineDetailType = AirlineType & {
  id: number;
  key: number;
  business_partner?: any;
  orderNumber?: number;
};

export type AirlinePriceType = {
  id: number | string;
  key: number | string;
  departure: string;
  destination: string;
  first_class: number;
  business_class: number;
  premium_economy_class: number;
  economy_class: number;
};

export async function getListAirline(params: BaseParams) {
  return request<ResponseList<AirlineDetailType[]>>(`/airplanes`, {
    method: 'GET',
    params,
  });
}

export async function getAirlineDetail(id: number) {
  return request<BaseResponse<AirlineDetailType>>(`/airplanes/${id}`, {
    method: 'GET',
  });
}

export async function createAirline(payload: AirlineType) {
  return request<BaseResponse<any>>(`/airplanes`, {
    method: 'POST',
    data: payload,
  });
}

export async function updateAirline(id: number, payload: AirlineType) {
  return request<BaseResponse<any>>(`/airplanes/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteAirline(id: number) {
  return request<BaseResponse<any>>(`/airplanes/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvPriceAirline(formData: FormData, airplane_id: string) {
  return request(`/airplanes/${airplane_id}/import/csv-price`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvPriceAirline(params: BaseParams, airplane_id: number) {
  return request<any>(`/airplanes/${airplane_id}/export/csv-price`, {
    method: 'GET',
    params: params,
  });
}
export async function importCsvCancelFeeAirline(formData: FormData, airplane_id: string) {
  return request(`/airplanes/${airplane_id}/import/csv-cancellation-fee`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCsvCancelFeeAirline(params: BaseParams, airplane_id: number) {
  return request<any>(`/airplanes/${airplane_id}/export/csv-cancellation-fee`, {
    method: 'GET',
    params: params,
  });
}
