import { BaseResponse } from '@/@types/request';
import request from '@/utils/request';

export type TariffType = {
  id: number;
  type?: string;
  fee: number;
  tariffable_id?: number;
  hotel_tariff_type?: string;
  hotel_area?: string;
  hotel_meal?: string;
  hotel_payment_unit?: string;
};

export type TariffDetailType = {
  id: number;
  type: string;
  fee: number;
};

export type PayloadTariffHireCar = {
  type: string;
  fee: number;
};

export async function getListEntityTypeByEntity(entity: string) {
  return request<BaseResponse<any>>(`/entities/${entity}/type`, {
    method: 'GET',
  });
}

export async function getEntityTariffs(entity: string, entityId: number | string) {
  return request<BaseResponse<any>>(`/tariffs/${entity}/${entityId}`, {
    method: 'GET',
  });
}

export async function getDetailTariff(tariffId: number | string) {
  return request<BaseResponse<any>>(`/tariffs/${tariffId}`, {
    method: 'GET',
  });
}

export async function createEntityTariff(
  entity: string,
  entityId: number | string,
  payload: PayloadTariffHireCar,
) {
  return request<BaseResponse<any>>(`/tariffs/${entity}/${entityId}`, {
    method: 'POST',
    data: payload,
  });
}

export async function updateEntityTariff(tariffId: number | string, payload: PayloadTariffHireCar) {
  return request<BaseResponse<any>>(`/tariffs/${tariffId}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteTariff(id: number | string) {
  return request<BaseResponse<any>>(`/tariffs/${id}`, {
    method: 'DELETE',
  });
}
