import { extend } from 'umi-request';
import type { BaseParams, ResponseList } from '@/@types/request';
import request from '@/utils/request';
import type { AccountingTravelListType } from './traveList';
import { BusinessPartnerDetailType } from '@/@types/businessPartner';
import { ReportInvoiceParams } from './saleManagement';

export type DepositSlipType = {
  deposit_slip_id: string;
  travel_id: string;
  tour_name: string;
  departure_date: string;
  sales_day: string;
  sales_amount: number;
  previous_deposit_amount: number;
  current_deposit_amount: number;
  total_deposit_amount: number;
  outstanding_amount: number;
  travel: AccountingTravelListType;
  business_partner_id: {
    business_partner_code: string;
    business_partner_name: string;
    id: number;
  };
};

export type DepositSlipDetailType = {
  data: DataDepositSlipDetailType;
};

export type DataDepositSlipDetailType = {
  id: number;
  business_partner: BusinessPartnerDetailType;
  deposit_slip_id: number;
  deposit_date: Date | string;
  business_partner_id: number;
  deposit_amount: null | number;
  currency_type_master_id: null;
  deposit_slip_type: null;
  accounting_linked: null;
  fee: null | number;
  memo: null | string;
  created_by: null | Date | string;
  updated_by: null | Date | string;
  deleted_by: null | Date | string;
  created_at: null | Date | string;
  updated_at: null | Date | string;
  deleted_at: null | Date | string;
  items: DepositSlipItemType[];
  is_cancelled: number;
  is_inversed: number;
  deposit_slip_inverse_id?: number;
};

export type DepositSlipItemType = {
  sale_id: number;
  travel_id: number;
  sale_invoice_id: number;
  sale_invoice_item_id: number;
  sale_invoice_code: string;
  travel_code: string;
  tour_name: string;
  business_partner_id: number;
  sales_amount?: null;
  invoice_issued?: number;
  departure_date?: null;
  return_date?: string | Date;
  sale_destination?: string | Date;
  voucher_posting_date: string | Date;
  total_amount?: number;
  deposit_slip_id?: number;
  deposit_amount?: number;
  current_deposit_amount?: number;
  total_deposit_amount?: number;
  unpaid_amount?: number;
  accounting_linked: number;
  travel?: AccountingTravelListType;
  is_reversed: 0 | 1;
  reverse_item_id: number;
  deposit_slip_item_id?: number;
};

export type ListDepositSlipItemParams = {
  business_partner_id: number;
  departure_date?: Date | string;
  return_date?: Date | string;
};

export type DepositSlipItemDetailDataType = {
  deposit_slip_id: number;
  sale_invoice_id: number;
  current_deposit_amount: number;
};

export type CreateOrUpdateDepositSlipDataType = {
  deposit_date: Date | string;
  business_partner_id: number;
  deposit_amount: number;
  current_type_master_id: number;
  deposit_slip_type: number;
  fee: number;
  memo?: string;
  id?: number;
};

export type DepositSlipListItem = {
  deposit_slip_id: number;
  business_partner: BusinessPartnerDetailType;
  deposit_date: string;
  deposit_amount: number;
  accounting_linked: null | number;
  fee?: number;
};

export type UpdateDepositSlipDataType = {
  id: number;
  deposit_date: string;
  deposit_amount: number;
  currency_type_master_id: number;
  fee: number;
  memo: string;
  items: {
    updates: {
      deposit_slip_item_id: number;
      current_deposit_amount: number;
    }[];
    reverses: number[];
    deletes: number[];
  };
};

export interface GetDetailDepositParamsType extends BaseParams {
  id: number;
}

export async function getListDepositSlip(params: BaseParams) {
  return request<ResponseList<DepositSlipListItem[]>>(`/deposit_slips`, {
    method: 'GET',
    params: params,
  });
}
export async function getListDepositSlipItem(params: ListDepositSlipItemParams) {
  return request<ResponseList<DepositSlipItemType[]>>(`/deposit_slips/items`, {
    method: 'GET',
    params: params,
  });
}
export async function getDetailDepositSlip(params: GetDetailDepositParamsType) {
  return request<DepositSlipDetailType>(`/deposit_slips/${params.id}`, {
    method: 'GET',
    params,
  });
}

export async function createDepositSlip(body: CreateOrUpdateDepositSlipDataType) {
  return request<any>(`/deposit_slips`, {
    method: 'POST',
    data: body,
  });
}

export async function updateDepositSlip(body: UpdateDepositSlipDataType) {
  return request<any>(`/deposit_slips/${body.id}`, {
    method: 'PUT',
    data: body,
  });
}

export async function listReportDepositInvoice(params: ReportInvoiceParams) {
  return request<any>('report/deposit', {
    method: 'GET',
    params: params,
  });
}

export async function markReportedDepositInvoiceItem(body: ReportInvoiceParams) {
  return request<any>('mask_reported/deposit', {
    method: 'POST',
    data: body,
  });
}

export async function cancelDepositSlip(id: number) {
  return request<any>(`/deposit_slips/${id}/cancel`, {
    method: 'PUT',
  });
}

export async function inverseDepositSlip(id: number) {
  return request<any>(`/deposit_slips/${id}/inverse`, {
    method: 'POST',
  });
}

export async function createOrUpdateDepositSlip(body: CreateOrUpdateDepositSlipDataType) {
  return request<any>(`/deposit_slip_items`, {
    method: 'POST',
    data: body,
  });
}

export async function importCsvDepositInvoice(formData: FormData) {
  return request(`/deposit_slips/import/csv`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
