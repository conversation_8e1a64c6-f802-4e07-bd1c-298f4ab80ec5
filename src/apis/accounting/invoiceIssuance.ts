import { BusinessPartnerDetailType } from '@/@types/businessPartner';
import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';
import type { SaleInvoiceItemType } from './saleManagement';
import type { AccountingTravelListType } from './traveList';

export type DepositSlipType = {
  deposit_slip_id: string;
  travel_id: string;
  tour_name: string;
  departure_date: string;
  sales_day: string;
  sales_amount: number;
  previous_deposit_amount: number;
  current_deposit_amount: number;
  total_deposit_amount: number;
  outstanding_amount: number;
  travel: AccountingTravelListType;
  business_partner_id: {
    business_partner_code: string;
    business_partner_name: string;
    id: number;
  };
};

export type DepositSlipDetailType = {
  data: DataDepositSlipDetailType;
};

export type DataDepositSlipDetailType = {
  id: number;
  business_partner: BusinessPartnerDetailType;
  deposit_slip_id: number;
  deposit_date: Date | string;
  business_partner_id: number;
  deposit_amount: null | number;
  currency_type_master_id: null;
  deposit_slip_type: null;
  accounting_linked: null;
  fee: null | number;
  memo: null | string;
  created_by: null | Date | string;
  updated_by: null | Date | string;
  deleted_by: null | Date | string;
  created_at: null | Date | string;
  updated_at: null | Date | string;
  deleted_at: null | Date | string;
  items: DepositSlipItemType[];
  is_cancelled: number;
  is_inversed: number;
  deposit_slip_inverse_id?: number;
};

export type DepositSlipItemType = {
  sale_id: number;
  travel_id: number;
  sale_invoice_id: number;
  sale_invoice_item_id: number;
  sale_invoice_code: string;
  travel_code: string;
  tour_name: string;
  business_partner_id: number;
  sales_amount?: null;
  invoice_issued?: number;
  departure_date?: null;
  return_date?: string | Date;
  sale_destination?: string | Date;
  voucher_posting_date: string | Date;
  total_amount?: number;
  deposit_slip_id?: number;
  deposit_amount?: number;
  current_deposit_amount?: number;
  total_deposit_amount?: number;
  unpaid_amount?: number;
  accounting_linked: number;
  travel?: AccountingTravelListType;
  is_reversed: 0 | 1;
  reverse_item_id: number;
  deposit_slip_item_id?: number;
};

export type ListSaleItemParams = {
  business_partner_id: number;
  travel_id: number;
  invoice_linked?: number;
};

export type GetDepositByTourAndBPParams = {
  business_partner_id: number;
  travel_id: number;
};

export type DepositSlipItemDetailDataType = {
  deposit_slip_id: number;
  sale_invoice_id: number;
  current_deposit_amount: number;
};

export type CreateOrUpdateDepositSlipDataType = {
  deposit_date: Date | string;
  business_partner_id: number;
  deposit_amount: number;
  current_type_master_id: number;
  deposit_slip_type: number;
  fee: number;
  memo?: string;
  id?: number;
};

export type DepositSlipListItem = {
  deposit_slip_id: number;
  business_partner: BusinessPartnerDetailType;
  deposit_date: string;
  deposit_amount: number;
  accounting_linked: null | number;
  fee?: number;
};

export type UpdateDepositSlipDataType = {
  id: number;
  deposit_date: string;
  deposit_amount: number;
  currency_type_master_id: number;
  fee: number;
  memo: string;
  items: {
    updates: {
      deposit_slip_item_id: number;
      current_deposit_amount: number;
    }[];
    reverses: number[];
    deletes: number[];
  };
};

export interface BodyCreateInvoiceIssuance {
  items: SaleInvoiceItemType[];
  billing_category: number;
  travel_id: number;
  business_partner_id: number;
  issued_date: string;
  title: number;
  payment_deadline: string;
  seal: string;
  logo: string;
  memo: string;
}

export interface BodyUpdateInvoiceIssuance {
  items: SaleInvoiceItemType[];
  billing_category: number;
  travel_id: number;
  business_partner_id: number;
  issued_date: string;
  title: number;
  payment_deadline: string;
  seal: string;
  logo: string;
  memo: string;
  id?: string;
}
export interface GetDetailDepositParamsType extends BaseParams {
  id: number;
}

export interface GetListInvoiceIssuance {
  id: number;
  consolidated_invoice_id: string;
  billing_category: string;
  travel_id: number;
  business_partner_id: number;
  issued_date: string;
  title: string;
  payment_deadline: string;
  seal: string;
  logo: string;
  memo: string | null;
  created_by: {
    id: number;
    name: string;
    email: string;
    phone_number: string;
    skype: string | null;
    occupation: string;
    is_active: boolean;
    created_by: number | null;
    updated_by: number;
    deleted_by: number | null;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    last_login_at: string;
  } | null;
  updated_by: string | null;
  deleted_by: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  business_partner: {
    id: number;
    business_partner_code: string;
    business_partner_name: string;
  };
  manager: {
    name: string;
    idA: number;
    email: string;
  };
  travel: {
    id: number;
    travel_id: string;
    tour_name: string;
  };
  pictures?: {
    id: number;
    path: string;
    picture_type: string;
    pictureable_id: number;
  }[];
}
export type DetailInvoiceIssuance = {
  id: number;
  consolidated_invoice_id: string;
  billing_category: string;
  travel_id: number;
  business_partner_id: number;
  issued_date: string;
  title: string;
  payment_deadline: string;
  seal: string;
  logo: string;
  memo: string | null;
  is_disable: number | null;
  created_by: {
    id: number;
    name: string;
    email: string;
    phone_number: string;
    skype: string | null;
    occupation: string;
    is_active: boolean;
    created_by: number | null;
    updated_by: number;
    deleted_by: number | null;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    last_login_at: string;
  };
  manager: {
    id: number;
    name: string;
    email: string;
    phone_number: string;
    skype: string | null;
    occupation: string;
    is_active: boolean;
    created_by: number | null;
    updated_by: number;
    deleted_by: number | null;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
    last_login_at: string;
  };
  updated_by: string | null;
  deleted_by: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  business_partner: BusinessPartnerDetailType;
  travel: AccountingTravelListType;
  items: DetailInvoiceIssuanceItem[];
  option_invoice_print: number;
  option_language: number;
  deposit_amount: number;
  deposit_entered: number;
  pictures: {
    id: number;
    path: string;
    pictureable_id: number;
    pictureable_type: string;
  }[];
};

export interface DetailInvoiceIssuanceItem {
  consolidated_invoice_id: number;
  created_at: string | null;
  created_by: number;
  deleted_at: string | null;
  deleted_by: string | null;
  id: number;
  memo: string | null;
  sale_invoice_item_id: number;
  sale_invoice_item: SaleInvoiceItemType;
}

export interface InvoiceIssuanceResponse {
  billing_category: number;
  travel_id: number;
  business_partner_id: number;
  issued_date: string;
  title: number;
  payment_deadline: string;
  seal: string;
  logo: string;
  consolidated_invoice_id: string;
  updated_at: string;
  created_at: string;
  id: number;
}

export interface BodyUploadFilePDFInvoice {
  file: File;
}

export interface ResponseGenerateInvoiceID {
  data: string;
}

export interface ResponseDepositAmountByTourAndBP {
  data: number;
}

export interface ResponseGetBase64Image {
  base64: string;
}

export async function getDepositAmountByTourAndBP(params: GetDepositByTourAndBPParams) {
  return request<ResponseDepositAmountByTourAndBP>(`/consolidated_invoices/total-deposit`, {
    method: 'GET',
    params: params,
  });
}

export async function getListSaleItemByTourAndBP(params: ListSaleItemParams) {
  return request<ResponseList<SaleInvoiceItemType[]>>(`/consolidated_invoices/items`, {
    method: 'GET',
    params: params,
  });
}

export async function createInvoiceIssuance(body: BodyCreateInvoiceIssuance) {
  return request<BaseResponse<InvoiceIssuanceResponse>>(`/consolidated_invoices`, {
    method: 'POST',
    data: body,
  });
}

export async function updateInvoiceIssuance(body: BodyUpdateInvoiceIssuance) {
  return request<BaseResponse<InvoiceIssuanceResponse>>(`/consolidated_invoices/${body.id}`, {
    method: 'PUT',
    data: body,
  });
}

export async function getListInvoiceIssuance(params: BaseParams) {
  return request<ResponseList<GetListInvoiceIssuance[]>>(`/consolidated_invoices`, {
    method: 'GET',
    params: params,
  });
}
export async function getDetailInvoiceIssuance(id: string | number) {
  return request<BaseResponse<DetailInvoiceIssuance>>(`/consolidated_invoices/${id}`, {
    method: 'GET',
  });
}

export async function uploadFilePDFInvoice(body: FormData) {
  return request<any>(`/file/upload`, {
    method: 'POST',
    data: body,
  });
}
export async function generateInvoiceID() {
  return request<BaseResponse<string>>(`/consolidated_invoices/generate-invoice-id`, {
    method: 'GET',
  });
}
export async function getBase64Image({ file_link }: { file_link: string }) {
  return request<ResponseGetBase64Image>(`/file/convert-base64`, {
    method: 'GET',
    params: { file_link },
  });
}
