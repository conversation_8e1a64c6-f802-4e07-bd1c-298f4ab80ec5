import type { BaseParams, ResponseList } from '@/@types/request';
import request from '@/utils/request';
import type { AccountingTravelListType } from './traveList';
import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import type { ReportInvoiceParams } from './saleManagement';

export type PaymentSlipType = {
  payment_slip_id: string;
  travel_id: string;
  tour_name: string;
  departure_date: string;
  purchase_day: string;
  purchase_amount: number;
  previous_payment_amount: number;
  current_payment_amount: number;
  total_payment_amount: number;
  outstanding_amount: number;
  travel: AccountingTravelListType;
  business_partner_id: {
    business_partner_code: string;
    business_partner_name: string;
    id: number;
  };
};

export type PaymentSlipDetailType = {
  data: DataPaymentSlipDetailType;
};

export type DataPaymentSlipDetailType = {
  id: number;
  business_partner: BusinessPartnerDetailType;
  payment_slip_id: number;
  payment_date: Date | string;
  business_partner_id: number;
  payment_amount: null | number;
  currency_type_master_id: null;
  payment_slip_type: null;
  accounting_linked: null;
  fee: null | number;
  memo: null | string;
  created_by: null | Date | string;
  updated_by: null | Date | string;
  deleted_by: null | Date | string;
  created_at: null | Date | string;
  updated_at: null | Date | string;
  deleted_at: null | Date | string;
  items: PaymentSlipItemType[];
  is_cancelled: number;
  is_inversed: number;
  payment_slip_inverse_id?: number;
};

export type PaymentSlipItemType = {
  purchase_id: number;
  travel_id: number;
  purchase_invoice_id: number;
  purchase_invoice_item_id: number;
  purchase_invoice_code: string;
  travel_code: string;
  tour_name: string;
  business_partner_id: number;
  purchase_amount?: null;
  invoice_issued?: number;
  departure_date?: null;
  return_date?: string | Date;
  purchase_destination?: string | Date;
  voucher_posting_date: string | Date;
  total_amount?: number;
  payment_slip_id?: number;
  payment_amount?: number;
  current_payment_amount?: number;
  total_payment_amount?: number;
  unpaid_amount?: number;
  accounting_linked: number;
  travel?: AccountingTravelListType;
  is_reversed: 0 | 1;
  reverse_item_id: number;
};

export type ListPaymentSlipItemParams = {
  business_partner_id: number;
  departure_date?: Date | string;
  return_date?: Date | string;
};

export type PaymentSlipItemDetailDataType = {
  payment_slip_id: number;
  purchase_invoice_id: number;
  current_payment_amount: number;
};

export type CreateOrUpdatePaymentSlipDataType = {
  payment_date: Date | string;
  business_partner_id: number;
  payment_amount: number;
  current_type_master_id: number;
  payment_slip_type: number;
  fee: number;
  memo?: string;
  id?: number;
};

export type PaymentSlipListItem = {
  payment_slip_id: number;
  business_partner: {
    id: number;
    business_partner_code: string;
    business_partner_name: string;
  };
  payment_date: string;
  payment_amount: number;
  accounting_linked: null | number;
  fee?: number;
};

export type UpdatePaymentSlipDataType = {
  id: number;
  payment_date: string;
  payment_amount: number;
  currency_type_master_id: number;
  fee: number;
  memo: string;
  items: {
    updates: {
      payment_slip_item_id: number;
      current_payment_amount: number;
    }[];
    reverses: number[];
    deletes: number[];
  };
};

export interface GetDetailPaymentParamsType extends BaseParams {
  id: number;
}

export async function getListPaymentSlip(params: BaseParams) {
  return request<ResponseList<PaymentSlipListItem[]>>(`/payment_slips`, {
    method: 'GET',
    params: params,
  });
}
export async function getListPaymentSlipItem(params: ListPaymentSlipItemParams) {
  return request<ResponseList<PaymentSlipItemType[]>>(`/payment_slips/items`, {
    method: 'GET',
    params: params,
  });
}
export async function getDetailPaymentSlip(params: GetDetailPaymentParamsType) {
  return request<PaymentSlipDetailType>(`/payment_slips/${params.id}`, {
    method: 'GET',
    params,
  });
}

export async function createPaymentSlip(body: CreateOrUpdatePaymentSlipDataType) {
  return request<any>(`/payment_slips`, {
    method: 'POST',
    data: body,
  });
}

export async function updatePaymentSlip(body: UpdatePaymentSlipDataType) {
  return request<any>(`/payment_slips/${body.id}`, {
    method: 'PUT',
    data: body,
  });
}

export async function listReportPaymentInvoice(params: ReportInvoiceParams) {
  return request<any>('report/payment', {
    method: 'GET',
    params: params,
  });
}

export async function markReportedPaymentInvoiceItem(body: ReportInvoiceParams) {
  return request<any>('mask_reported/payment', {
    method: 'POST',
    data: body,
  });
}

export async function cancelPaymentSlip(id: number) {
  return request<any>(`/payment_slips/${id}/cancel`, {
    method: 'PUT',
  });
}

export async function inversePaymentSlip(id: number) {
  return request<any>(`/payment_slips/${id}/inverse`, {
    method: 'POST',
  });
}

export async function importCsvPaymentInvoice(formData: FormData) {
  return request(`/payment_slips/import/csv`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
