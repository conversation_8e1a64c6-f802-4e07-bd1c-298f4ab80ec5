import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';
import type { AccountingTravelListType } from './traveList';

export interface SaleInvoiceType {
  id: number;
  sale_invoice_item: SaleInvoiceItemType[];
  travel_id: string;
  sale_invoice_id: number | null;
  tour_name: string | null;
  business_partner: BusinessPartnerDetailType;
  travel: AccountingTravelListType;
  business_partner_id: number;
  sales_amount: number | null;
  invoice_issued: boolean | null;
  departure_date: string | Date | null;
  return_date: string | Date | null;
  sale_destination: string | null;
  voucher_posting_date: string | Date | null;
  total_amount_received: number | null;
  created_by: string | null;
  updated_by: string | null;
  deleted_by: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  total_sale?: number;
}

export interface ReportInvoiceParams extends BaseParams {
  start?: string;
  finish?: string;
}

export interface SaleInvoiceItemType {
  id: number;
  index_column: number;
  subject_id: string;
  sale_invoice_id: number;
  summary_subject_name: string;
  product_name: string;
  product_name_en: string;
  quantity: number;
  unit_price: number;
  tax_inclusion_type: string;
  tax_category_code: number;
  tax_rate: number;
  amount_excluding_tax: number;
  consumption_tax: number;
  amount_including_tax: number;
  invoice_number: string;
  accounting_linked: number;
  memo: string | null;
  status_cancel: number;
  status_update: number;
  created_by: number;
  updated_by: number | null;
  deleted_by: number | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  sale_date: string | null;
  use_at: string | null;
  sale_invoice_item_id?: string;
  consolidated_invoice_item_id?: number;
  consolidate_invoice_item_id?: string;
  is_exists?: 0 | 1 | null;
  consolidated_invoice_id?: string;
  consolidated_invoices_is_disable?: 0 | 1 | null;
}

export interface ParamSearchListSaleInvoice extends BaseParams {
  travel_id?: number;
  business_partner_id?: number;
}

export async function getListInvoiceItemsById(id: string | number) {
  return request<any>(`/sale_invoice_items/${id}`, {
    method: 'GET',
  });
}
export async function getListSaleInvoice(params: ParamSearchListSaleInvoice) {
  return request<ResponseList<SaleInvoiceType[]>>(`/sale_invoice`, {
    method: 'GET',
    params,
  });
}

export async function getDetailSaleInvoice(id: string | number) {
  return request<BaseResponse<SaleInvoiceType>>(`/sale_invoice/${id}`, {
    method: 'GET',
  });
}

export async function createSaleInvoiceItem(body: any) {
  return request<ResponseList<any>>(`/sale_invoice_items`, {
    method: 'POST',
    data: body,
  });
}

export async function updateSaleInvoice(body: any) {
  return request<ResponseList<any>>(`/sale_invoice/${body.id}`, {
    method: 'PUT',
    data: body,
  });
}

export async function createSaleInvoice(body: any) {
  return request<ResponseList<any>>(`/sale_invoice`, {
    method: 'POST',
    data: body,
  });
}

export async function exportSaleInvoice(params: BaseParams) {
  return request<any>('/sale_invoice/export/csv', {
    method: 'GET',
    params: params,
  });
}

export async function listReportSaleInvoice(params: ReportInvoiceParams) {
  return request<any>('report/sale', {
    method: 'GET',
    params: params,
  });
}

export async function markReportedSaleInvoiceItem(body: ReportInvoiceParams) {
  return request<any>('mask_reported/sale', {
    method: 'POST',
    data: body,
  });
}

export async function deleteSaleInvoiceItems(body: { id: number[] }) {
  return request<ResponseList<any>>(`/sale_invoice_items`, {
    method: 'DELETE',
    data: body,
  });
}

export async function importCsvSaleInvoice(formData: FormData) {
  return request(`/sale_invoice/import/csv`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
