import { StatusCountType } from '@/@types/booking';
import { BusinessPartnerDetailType } from '@/@types/businessPartner';
import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type AccountingTravelListType = {
  id: number;
  travel_id: string;
  travel_type: string | number;
  tour_name: string;
  departure_date: string | Date;
  return_date: string | Date;
  deposit: number;
  payment: number;
  purchase: number;
  sale: number;
  total_billing_amount: number;
  consolidated_invoice: number;
  files?: string[];
  children_count: number;
  is_use_sale?: 1 | 0;
  is_use_purchase?: 1 | 0;
  admin: any;
  admin_id: number;
  businessPartner: BusinessPartnerDetailType;
};

export type AccountingTravelListDetailType = {
  adult_count: number;
  countStatus?: StatusCountType;
} & AccountingTravelListType;

export type TravelListSearchParams = {
  departure_date?: string;
  return_date?: string;
} & BaseParams;

export async function getListAccountingTravel(params: TravelListSearchParams) {
  return request<ResponseList<AccountingTravelListType[]>>(`/travels`, {
    method: 'GET',
    params,
  });
}

export async function getAccountingTravelDetail(id: number) {
  return request<BaseResponse<AccountingTravelListDetailType>>(`/travels/${id}`, {
    method: 'GET',
  });
}

export async function updateAccountingTravelDetail(
  id: number,
  payload: AccountingTravelListDetailType,
) {
  return request<BaseResponse<AccountingTravelListDetailType>>(`/travels/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function importTravelList(formData: FormData) {
  return request<BaseResponse<any>>(`/travels/import/csv`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportTravelList(params: BaseParams) {
  return request<any>('/travels/export/csv', {
    method: 'GET',
    params: params,
  });
}
