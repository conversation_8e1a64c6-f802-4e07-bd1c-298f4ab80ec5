import type { BusinessPartnerDetailType } from '@/@types/businessPartner';
import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';
import type { AccountingTravelListType } from './traveList';
import { ReportInvoiceParams } from './saleManagement';

export interface PurchaseInvoiceType {
  id: number;
  travel_id: number;
  purchase_invoice_id: number | null;
  tour_name: string | null;
  purchase_invoice_item: PurchaseInvoiceItemType[];
  business_partner: BusinessPartnerDetailType;
  travel: AccountingTravelListType;
  business_partner_id: number;
  purchases_amount: number | null;
  invoice_issued: boolean | null;
  departure_date: Date | null;
  return_date: Date | null;
  purchase_destination: string | null;
  voucher_posting_date: Date | null;
  total_amount_received: number | null;
  total_payment: number;
  created_by: string | null;
  updated_by: string | null;
  deleted_by: string | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  total_purchase?: number;
}

export interface PurchaseInvoiceItemType {
  id: number;
  subject_id: string;
  index_column: number;
  purchase_invoice_id: number;
  summary_subject_name: string;
  product_name: string;
  product_name_en: string;
  quantity: number;
  unit_price: number;
  tax_inclusion_type: number;
  qualified_business_type: string;
  tax_category_code: number | null;
  tax_rate: number;
  amount_excluding_tax: number;
  consumption_tax: number;
  amount_including_tax: number;
  r_rate: number;
  r_amount: number;
  invoice_number: string;
  accounting_linked: number;
  memo: string | null;
  status_cancel: number;
  status_update: number;
  created_by: number | null;
  updated_by: number | null;
  deleted_by: number | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  purchase_date: string | null;
  total_payment: number | null;
}

export interface ParamSearchListPurchaseInvoice extends BaseParams {
  travel_id?: number;
  business_partner_id?: number;
}

export async function getListInvoiceItemsById(id: string | number) {
  return request<any>(`/purchase_invoice_items/${id}`, {
    method: 'GET',
  });
}
export async function getListPurchaseInvoice(params: ParamSearchListPurchaseInvoice) {
  return request<ResponseList<PurchaseInvoiceType[]>>(`/purchase_invoice`, {
    method: 'GET',
    params,
  });
}

export async function getDetailPurchaseInvoice(id: string | number) {
  return request<BaseResponse<PurchaseInvoiceType>>(`/purchase_invoice/${id}`, {
    method: 'GET',
  });
}

export async function createPurchaseInvoiceItem(body: any) {
  return request<ResponseList<any>>(`/purchase_invoice_items`, {
    method: 'POST',
    data: body,
  });
}

export async function createMultiPurchaseInvoiceItemWithBusinessPartner(body: any) {
  return request<ResponseList<any>>(`/purchase_invoice_items/bulk-create`, {
    method: 'POST',
    data: body,
  });
}

export async function updatePurchaseInvoice(body: any) {
  return request<ResponseList<any>>(`/purchase_invoice/${body.id}`, {
    method: 'PUT',
    data: body,
  });
}

export async function createPurchaseInvoice(body: any) {
  return request<ResponseList<any>>(`/purchase_invoice`, {
    method: 'POST',
    data: body,
  });
}

export async function exportPurchaseInvoice(params: BaseParams) {
  return request<any>('/purchase_invoice/export/csv', {
    method: 'GET',
    params: params,
  });
}

export async function listReportPurchaseInvoice(params: ReportInvoiceParams) {
  return request<any>('report/purchase', {
    method: 'GET',
    params: params,
  });
}
export async function markReportedPurchaseInvoiceItem(body: ReportInvoiceParams) {
  return request<any>('mask_reported/purchase', {
    method: 'POST',
    data: body,
  });
}

export async function deletePurchaseInvoiceItems(body: { id: number[] }) {
  return request<ResponseList<any>>(`/purchase_invoice_items`, {
    method: 'DELETE',
    data: body,
  });
}

export async function importCsvPurchaseInvoice(formData: FormData) {
  return request(`/purchase_invoice/import/csv`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}
