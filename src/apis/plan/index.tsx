import request from '@/utils/request';

export type Plan = {
  id: number | string;
  reference_id: number | string;
  reference_type: string;
  reference_type_key:
    | 'hotel'
    | 'travel-spot'
    | 'bus'
    | 'bullet-train'
    | 'public-transport'
    | 'guide'
    | 'special'
    | 'hire-car'
    | 'delivery';
  date: string;
  name_jp?: string;
  name_en?: string;
  address?: string;
  home_page?: string;
  phone_number?: string;
  introduce_text?: string;
  pictures?: {
    id?: number | string;
    path?: string;
  };
};

export type PlanInfo = {
  plan_version?: number;
  id: number;
  booking_id: number;
  title: string;
  plan_items?: any[];
  plan_status: number;
  plan_description: string;
  start_date: string;
  end_date: string;
  created_by: number;
  updated_by: number;
  deleted_by: null;
  created_at: string;
  updated_at: string;
  deleted_at: string;
  is_model_course: boolean;
  agency_picture: string;
};

export type UpdatePlan = {
  item_array: {
    reference_id: number;
    reference_type: string;
    date: string;
    day?: number;
  }[];
  is_model_course: boolean;
};

// todo: update
export type PlanInfoPayload = {
  title: string;
  plan_status: number;
  plan_description: string;
  start_date: string;
  end_date: string;
};

export type PlansResponse = {
  message: string;
  data: {
    plan_items: Plan[];
    plan: PlanInfo;
  };
};

export type PlanPayloadType = {
  booking_id: number;
  title: string;
  plan_status: number;
  plan_description: string;
  start_date: string;
  end_date: string;
};

export async function getListPlan(planId: number | string) {
  return request<PlansResponse>(`/plans/${planId}/items`, {
    method: 'GET',
    params: { sort_order: 'asc' },
  });
}

export async function createPlan(booking_id: number, payload: PlanPayloadType) {
  return request<PlansResponse>(`/bookings/${booking_id}/plans`, {
    method: 'POST',
    data: payload,
  });
}

export async function updatePlan(planId: number, data: UpdatePlan) {
  return request<PlansResponse>(`/plans/${planId}/items`, {
    method: 'POST',
    data,
  });
}

export async function updateDuplicatePlan(planId: number, data: UpdatePlan) {
  return request<PlansResponse>(`/plans/${planId}/new-version`, {
    method: 'POST',
    data,
  });
}

export async function updatePlanInfo(
  booking_id: string | number,
  data: PlanInfoPayload,
  planId: string | number,
) {
  return request<PlansResponse>(`/itineraries/${booking_id}/plans/${planId}`, {
    method: 'PUT',
    data,
  });
}
