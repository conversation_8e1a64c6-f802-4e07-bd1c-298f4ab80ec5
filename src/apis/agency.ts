import request from '@/utils/request';

export type ParamsListAgency = {
  limit?: number;
  page?: number;
  keyword?: string;
  country?: number;
};

export type BaseResponse<T> = {
  status: string;
  message: string;
  data: T;
};

export type AgencyDetailType = {
  id: number;
  agency_name?: string;
  picture?: any;
  representative?: number;
  home_page?: string;
  memo?: string;
  phone_number?: string;
  fax?: string;
  email?: string;
  country?: {
    label: string;
    value: number;
  };
};

export type payloadBodyAgency = {
  agency_name?: string;
  country?: number;
  picture?: string;
  representative?: number;
  city_code?: number;
  memo?: string;
  phone_number?: string;
  fax?: string;
  email?: string;
};

export async function getListAgency(params: ParamsListAgency) {
  return request<{ total: number; data: AgencyDetailType[] }>('/agency', {
    method: 'GET',
    params: params,
  });
}

export async function getAgencyDetail(id: number) {
  return request<BaseResponse<any>>(`/agency/${id}`, {
    method: 'GET',
  });
}

export async function deleteAgency(id: number) {
  return request<BaseResponse<any>>(`/agency/${id}`, {
    method: 'DELETE',
  });
}

export async function createAgency(body: payloadBodyAgency) {
  return request<BaseResponse<any>>(`/agency`, {
    method: 'POST',
    data: body,
  });
}

export async function updateAgency(id: number, body: payloadBodyAgency) {
  return request<BaseResponse<any>>(`/agency/${id}`, {
    method: 'PUT',
    data: body,
  });
}
