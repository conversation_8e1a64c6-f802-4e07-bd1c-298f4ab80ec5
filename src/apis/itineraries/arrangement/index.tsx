import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import type { EntityEnum } from '@/apis/purchase-and-material-management/tariff';
import request from '@/utils/request';

export type arrangementType = {
  id: number;
  name: string;
  no?: number;
  key?: number;
  reference_id: number;
  reference_type: EntityEnum;
  scheduled_date: string;
  cancel_date: string;
  status: number;
  fee?: number;
  quantity?: number;
};

export type arrangementDetailType = arrangementType & {
  charge_person?: string;
  email?: string;
  fax?: string;
  history_change_status?: StatusChangeType[];
  list_comment?: CommentType[];
  phone_number?: string;
  place_sort?: string;
};

export type CommentType = {
  id: number;
  memo: string;
  plan_item_id?: number;
  created_at?: string;
  creator: {
    id: number;
    name?: string;
  };
};

export type StatusChangeType = {
  id?: number;
  status: number;
  charge_person: string;
  memo: string;
  unit_person?: number;
  fee?: number;
  creator?: {
    id: number;
    name: string;
  };
  current_status?: number;
  old_status?: number;
  created_at?: string;
};

export type UpdateCancelDateType = {
  id: number;
  cancel_date: string;
}[];

export async function getListArrangement(planId: number, params: BaseParams) {
  return request<ResponseList<arrangementType[]>>(`/plans/${planId}/items-sort`, {
    method: 'GET',
    params: params,
  });
}

export async function getArrangementDetail(planId: number, itemId: number) {
  return request<BaseResponse<arrangementDetailType>>(`/plans/${planId}/items-sort/${itemId}`, {
    method: 'GET',
  });
}

export async function addCommentToPlanItem(planId: number, payload: { memo: string }) {
  return request<BaseResponse<any>>(`/plan-items/${planId}/post-memo`, {
    method: 'POST',
    data: payload,
  });
}

export async function updateStatus(planId: number, payload: StatusChangeType) {
  return request<BaseResponse<any>>(`/plan-items/${planId}/change-status`, {
    method: 'PUT',
    data: payload,
  });
}

export async function UpdateCancelDate(planId: number, payload: UpdateCancelDateType) {
  return request<BaseResponse<any>>(`/plans/${planId}/items-sort`, {
    method: 'PUT',
    data: payload,
  });
}

export async function ExportExcelArrangement(planId: number) {
  return request<BaseResponse<any>>(`/plans/${planId}/items-sort/export-excel`, {
    method: 'GET',
  });
}
