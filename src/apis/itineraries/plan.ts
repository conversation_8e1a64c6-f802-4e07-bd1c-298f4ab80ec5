import type { BookingPastItem } from '@/@types/booking';
import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type BodyCreatePlanItinerary = {
  title: string;
  start_date: string;
  end_date: string;
  plan_description: string;
  idItinerary: string | number;
};

export type ItemPlanData = {
  id: number;
  itinerary_id: number;
  title: string;
  plan_version: number;
  start_date: string;
  end_date: string;
  estimated_amount: number;
  is_final: boolean;
  is_model_course: boolean;
  created_at: string;
  total_days?: number;
  creator: {
    id: number;
    name: string;
  };
};

export type ResponseListPreviewPlan = {
  overview: {
    bus_note: null | string;
    end_date: string;
    facility_note: null | string;
    flight_info: null | string;
    hotel_note: null | string;
    meal_note: null | string;
    other_note: null | string;
    pax_unit: null | string;
    start_date: string;
    title: string;
    tl_tg: null | string;
  };
  schedule: ResponseListSchedulePreviewPlan[];
};

export type ResponseListSchedulePreviewPlan = {
  id: number;
  plan_id: number;
  reference_id: number | null;
  reference_type?:
    | 'travel-spot'
    | 'hotel'
    | 'restaurant'
    | 'bus'
    | 'hire-car'
    | 'bullet-train'
    | 'airplane'
    | 'ship'
    | 'guide'
    | 'delivery'
    | 'service-other'
    | null;
  is_checked_hotel: boolean;
  is_checked_meal: boolean;
  is_checked_facility: boolean;
  is_checked_bus: boolean;
  start_time: string;
  end_time: string;
  name_en: string;
  name_jp: string;
  breakfast: 0 | 1 | 2;
  lunch: 0 | 1 | 2;
  dinner: 0 | 1 | 2;
  sort_order: number;
  created_by: number;
  updated_by: number;
  deleted_by: number | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  date: string | null;
  hotel_area: string | null;
  meal_area: string | null;
  facility_area: string | null;
  hotel_payment: 0 | 1 | 2 | 3 | null;
  meal_payment: 0 | 1 | 2 | 3 | null;
  facility_payment: 0 | 1 | 2 | 3 | null;
  hotel_room: string | null;
  hotel_breakfast: string | null;
  meal_menu: string | null;
  facility_remark: string | null;
  bus_period_of_use: string | null;
  bus_type: string | null;
  bus_driver_info: string | null;
  address: string | null;
  phone_number: string | null;
};

export type BodyUpdatePreviewPlan = {
  overview: {
    flight_info: string;
    pax_unit: string;
    tl_tg: string; // TG/TG
    bus_note: null | string;
    facility_note: null | string;
    hotel_note: null | string;
    meal_note: null | string;
    other_note: null | string;
  };
  schedule: {
    date: string;
    // reference_type: 'travel-spot' | 'hotel' | 'restaurant' | 'bus' | 'hire-car' | 'bullet-train' | 'airplane' | 'ship' | 'guide' | 'delivery' | 'service-other';
    // reference_id: number;
    is_checked_hotel: boolean; // checked push to item_categories
    is_checked_meal: boolean;
    is_checked_facility: boolean;
    is_checked_bus: boolean;
    start_time: string;
    end_time: string;
    name_en: string;
    name_jp: string;
    breakfast: 0 | 1 | 2; // 0: hotel, 1:  〇, 2: ×
    lunch: 0 | 1 | 2; // 0: hotel, 1:  〇, 2: ×
    dinner: 0 | 1 | 2; // 0: hotel, 1:  〇, 2: ×
    sort_order: number;
  }[];
  schedule_delete: number[];
};

export async function getListPlanItinerary({ idItinerary }: { idItinerary: number | string }) {
  return request<ResponseList<ItemPlanData[]>>(`/itineraries/${idItinerary}/plans`, {
    method: 'GET',
  });
}
export async function createPlanItinerary(body: BodyCreatePlanItinerary) {
  return request<BaseResponse<ItemPlanData>>(`/itineraries/${body.idItinerary}/plans`, {
    method: 'POST',
    data: body,
  });
}
export async function changeFinalPlanItinerary(id: number | string) {
  return request<ResponseList<ItemPlanData[]>>(`/plans/${id}/set-final-plan`, {
    method: 'PATCH',
  });
}

export async function getListPreviewPlan({
  id,
  params,
}: {
  id: number | string;
  params: BaseParams;
}) {
  return request<ResponseListPreviewPlan>(`/plans/${id}/previews`, {
    method: 'GET',
    params: params,
  });
}

export async function updatePreviewPlan({
  body,
  id,
}: {
  body: BodyUpdatePreviewPlan;
  id: number | string;
}) {
  return request<ResponseList<BookingPastItem[]>>(`/plans/${id}/previews`, {
    method: 'PUT',
    data: body,
  });
}

export async function exportPreviewPlan({
  id,
  params,
}: {
  id: number | string;
  params: { type: string };
}) {
  return request<BaseResponse<{ file_url?: string }>>(`/plans/${id}/previews/export-excel`, {
    method: 'GET',
    params,
  });
}
