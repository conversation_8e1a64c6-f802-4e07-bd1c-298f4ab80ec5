import type { BaseParams, BaseResponse } from '@/@types/request';
import type { EntityEnum } from '@/apis/purchase-and-material-management/tariff';
import request from '@/utils/request';

export type EstimateDataType = {
  id?: number | string;
  title?: string;
  tariff_id?: string;
  tariff_type?: string;
  sale_amount?: number;
  purchase_amount?: number;
  quantity?: number;
  [key: string]: any;
};

export type EstimatePriceItem = {
  id: number;
  sort_order?: number;
  plan_id: number;
  reference_id: number;
  reference_type: EntityEnum;
  date?: string;
  title?: string;
  sales_amount: number;
  purchase_amount: number;
  profit_percent: number | string;
  additional_data: EstimateDataType[];
  available_data: EstimateDataType[];
};

export type EstimatePriceResponse = {
  plan_items: {
    total: number;
    data: EstimatePriceItem[];
  };
  total_profit_percent: number;
  total_purchase_amount: number;
  total_sales_amount: number;
};

export type PayloadUpdateEstimatePrice = {
  plan_item_id: number | string;
  sort_order?: string | number;
  options: {
    additional_data: EstimateDataType[];
    available_data: EstimateDataType[];
  };
};

export enum ExportExeclEstimateTypeEnum {
  Individual = 'individual',
  Inclusive = 'inclusive',
  Domestic = 'domestic',
}

export async function getListEstimatePrice(planId: number, params?: BaseParams) {
  return request<BaseResponse<EstimatePriceResponse>>(`/plans/${planId}/items/estimate-prices`, {
    method: 'GET',
    params: { ...params },
  });
}

export async function saveEstimatePrice(planId: number, payload: PayloadUpdateEstimatePrice[]) {
  return request<BaseResponse<EstimatePriceResponse>>(`/plans/${planId}/items/estimate-prices`, {
    method: 'PUT',
    data: payload,
  });
}

export async function filterFeeTariffByColumn(tariff_id: string | number, column: string) {
  return request<BaseResponse<any>>(`tariffs/${tariff_id}/filter-fee?column=${column}`, {
    method: 'GET',
  });
}

export async function ExportExcelEstimate(plan_id: number, type: ExportExeclEstimateTypeEnum) {
  return request<BaseResponse<any>>(`/plans/${plan_id}/estimates/export-excel`, {
    method: 'GET',
    params: { type: type },
  });
}
