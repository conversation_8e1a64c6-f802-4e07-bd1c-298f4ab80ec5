import type { BookingPastItem } from '@/@types/booking';
import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';
import type { PlanInfoPayload, PlansResponse } from '../plan';

export type ItineraryResponseListType = {
  bookings: ResponseList<ItineraryItemType[]>;
  countStatus: {
    ESTIMATED: number;
    PROCESSING: number;
    BOOKED: number;
    ACTIVE: number;
    CANCELLED: number;
    COMPLETED: number;
  };
};

export type ItineraryItemType = {
  id: number;
  travel_id: string;
  tour_name: string;
  status: number;
  payment_status: number;
  memo: string;
  created_at: string;
  departure_date: string;
  admin_name: string;
  business_partner_name: string;
  start_date: null;
  end_date: null;
  total_days: number;
  estimatedAmount: number;
};

export type DetailItineraryDataResponseType = {
  id: number;
  travel_id: string;
  tour_name: string;
  businessPartner: {
    id: number;
    business_partner_code: string;
    business_partner_name: string;
    business_partner_name_kana: string;
    business_partner_name_en: string;
  };
  representative_name: string;
  departure_date: string;
  inquiry_date: string;
  customer_count: number;
  travelMaster: {
    id: number;
    travel_code: string;
    travel_name: string;
  };
  admin: {
    id: number;
    name: string;
  };
  memo: string;
  children_count: number;
  adult_count: number;
  infants_count: number;
  status: number;
  payment_status: number;
  plans: any[]; // Replace 'any' with the appropriate type
  documents: any[]; // Replace 'any' with the appropriate type
  created_at: string;
  updated_at: string;
};

export type ItemFileItineraryType = {
  id: number;
  itinerary_id: number;
  file_name: string;
  file_link: string;
  sort_order: number;
};

export async function createItinerary(body: any) {
  return request<any>('/itineraries', {
    method: 'POST',
    data: body,
  });
}
export async function getListItinerary(params: BaseParams) {
  return request<ItineraryResponseListType>('/itineraries', {
    method: 'GET',
    params,
  });
}

export async function getDetailItinerary(id: number | string) {
  return request<BaseResponse<DetailItineraryDataResponseType>>(`/itineraries/${id}`, {
    method: 'GET',
  });
}
export async function updateItinerary(body: any) {
  return request<Response>(`/itineraries/${body?.id}`, {
    method: 'PUT',
    data: body,
  });
}

export async function updatePlanInfo(
  booking_id: string | number,
  data: PlanInfoPayload,
  planId: string | number,
) {
  return request<PlansResponse>(`/itineraries/${booking_id}/plans/${planId}`, {
    method: 'PUT',
    data,
  });
}

export async function updateStatusItinerary({ id, status }: { id: number; status: number }) {
  return request<Response>(`/itineraries/${id}/change-status`, {
    method: 'PATCH',
    data: { status },
  });
}
export async function deleteItinerary(id: number | string) {
  return request<Response>(`/itineraries/${id}`, {
    method: 'DELETE',
  });
}

export async function getListAttachedFileItinerary(id: number | string) {
  return request<ResponseList<ItemFileItineraryType[]>>(`/itineraries/${id}/files`, {
    method: 'GET',
  });
}
export async function createAttachedFileItinerary({
  file_link,
  id,
}: {
  file_link: string;
  id: number | string;
}) {
  return request<ResponseList<ItemFileItineraryType>>(`/itineraries/${id}/files`, {
    method: 'POST',
    data: { file_link },
  });
}

export async function deleteAttachedFileItinerary(id: number | string) {
  return request<Response>(`/itineraries/files/${id}`, {
    method: 'DELETE',
  });
}

export async function sortAttachedFileItinerary({
  idFile,
  idItinerary,
  newIndex,
}: {
  idFile: number | string;
  idItinerary: number | string;
  newIndex: number;
}) {
  return request<Response>(`/itineraries/${idItinerary}/files/${idFile}`, {
    method: 'PATCH',
    data: { sort_order: newIndex },
  });
}
//booking past

export async function getListBookingPast(params: BaseParams) {
  return request<ResponseList<BookingPastItem[]>>(`/itineraries-past`, {
    method: 'GET',
    params,
  });
}
