import type { GetListCitiesResponse } from '@/@types/location';
import request from '@/utils/request';

export async function uploadImage(formData, config) {
  return request<any>(`/file/upload`, {
    method: 'POST',
    data: formData,
    ...config,
  });
}

export async function deleteImage(link: string) {
  return request(`/file/delete`, {
    method: 'DELETE',
    params: { file_link: link },
  });
}

export async function getCity(prefectureId: string | number) {
  return request<GetListCitiesResponse>(`/get-city?province_id=${prefectureId}`, {
    method: 'GET',
  });
}

export async function getCountry() {
  return request(`/get-country`, {
    method: 'GET',
  });
}
