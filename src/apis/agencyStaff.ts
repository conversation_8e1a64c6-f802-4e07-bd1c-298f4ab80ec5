import request from '@/utils/request';

export type ParamsListAgencyStaff = {
  limit?: number;
  page?: number;
  keyword?: string;
};

export type AgencyStaffType = {
  id: number;
  name: string;
  email: string;
  phone_number: string;
  skype: string;
};

export type payloadBodyAgencyStaff = {
  name?: string;
  email?: string;
  phone_number?: string;
  skype?: string;
};

export async function getList(staffId: number, params: ParamsListAgencyStaff) {
  return request<any>(`/agency-staffs/list/${staffId}`, {
    method: 'GET',
    params: params,
  });
}
export async function getDetailAgencyStaff(id: number) {
  return request(`/agency-staffs/${id}`, {
    method: 'GET',
  });
}
export async function createAgencyStaff(body: payloadBodyAgencyStaff) {
  return request('/agency-staffs', {
    method: 'POST',
    data: body,
  });
}

export async function updateAgencyStaff(id: number, body: payloadBodyAgencyStaff) {
  return request(`/agency-staffs/${id}`, {
    method: 'PUT',
    data: body,
  });
}

export async function deleteAgencyStaff(id: number) {
  return request(`/agency-staffs/${id}`, {
    method: 'DELETE',
  });
}
