import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type SubjectMasterType = {
  subject_code: string;
  subject_name: string;
  subject_name_en: string;
  tax_category_id: string | number;
  summary_item_id: string | number;
  unit_price: string;
  target: string;
  status: boolean | 0 | 1;
};

export type SubjectMasterDetailType = {
  id?: number;
  key?: number;
  created_at?: Date;
  updated_at?: Date;
} & SubjectMasterType;

export async function getListSubjectMaster(params: BaseParams) {
  return request<ResponseList<SubjectMasterDetailType[]>>(`/subject_master`, {
    method: 'GET',
    params,
  });
}
export async function getListSubjectCodeAutoMaster() {
  return request<ResponseList<SubjectMasterDetailType[]>>(
    `/subject_master/get-subject-code-auto?subject_code=12`,
    {
      method: 'GET',
    },
  );
}

export async function getSubjectMasterDetail(id: number) {
  return request<BaseResponse<SubjectMasterDetailType>>(`/subject_master/${id}`, {
    method: 'GET',
  });
}

// create or udate many
export async function createOrUpdateSubjectMaster(body: SubjectMasterDetailType[]) {
  return request<BaseResponse<any>>(`/subject_master`, {
    method: 'PUT',
    data: { data: body },
  });
}

export async function createSubjectMaster(body: SubjectMasterDetailType) {
  return request<BaseResponse<any>>(`/subject_master`, {
    method: 'POST',
    data: body,
  });
}

// update one
export async function updateSubjectMaster(id: number, payload: SubjectMasterType) {
  return request<BaseResponse<any>>(`/subject_master/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteSubjectMaster(id: number) {
  return request<BaseResponse<any>>(`/subject_master/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvSubjectMaster(formData: FormData) {
  return request(`/subject_master/import/csv`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportSubjectMaster(params: BaseParams) {
  return request<any>('/subject_master/export/csv', {
    method: 'GET',
    params: params,
  });
}
