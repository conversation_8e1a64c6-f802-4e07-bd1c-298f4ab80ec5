import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type AggregationItemType = {
  summary_item_code: string;
  summary_item_category_name: string;
  summary_item_name: string;
  summary_item_name_en: string;
  status: boolean | 1 | 0;
};

export type AggregationItemDetailType = {
  id?: number;
  key?: number;
  created_at?: Date;
  updated_at?: Date;
} & AggregationItemType;

export async function getListAggregationItem(params: BaseParams) {
  return request<ResponseList<AggregationItemDetailType[]>>(`/summary_item_master`, {
    method: 'GET',
    params,
  });
}

export async function getAggregationItemDetail(id: number) {
  return request<BaseResponse<AggregationItemDetailType>>(`/summary_item_master/${id}`, {
    method: 'GET',
  });
}

// create or udate many
export async function createOrUpdateAggregationItem(body: AggregationItemDetailType[]) {
  return request<BaseResponse<any>>(`/summary_item_master`, {
    method: 'PUT',
    data: { data: body },
  });
}

export async function createAggregationItem(body: AggregationItemDetailType) {
  return request<BaseResponse<any>>(`/summary_item_master`, {
    method: 'POST',
    data: body,
  });
}

// update one
export async function updateAggregationItem(id: number, payload: AggregationItemType) {
  return request<BaseResponse<any>>(`/summary_item_master/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteAggregationItem(id: number) {
  return request<BaseResponse<any>>(`/summary_item_master/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvAggregationItem(formData: FormData) {
  return request(`/summary_item_master/import/csv`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportAggregationItem(params: BaseParams) {
  return request<any>('/summary_item_master/export/csv', {
    method: 'GET',
    params: params,
  });
}

export async function getAggregationItemCode() {
  return request<ResponseList<any>>(`/summary_item_master/get_summary_item_code`, {
    method: 'GET',
  });
}
