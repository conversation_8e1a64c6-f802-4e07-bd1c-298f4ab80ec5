import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type ConsumptionTaxType = {
  consumption_tax_category_code: string;
  tax_category_code: string;
  consumption_tax_name: string;
  consumption_tax_abbreviation: string;
  consumption_tax_rate: number | string;
  target: number[] | string;
  status: boolean | 1 | 0;
};

export type ConsumptionTaxDetailType = {
  id?: number;
  key?: number;
  created_at?: Date;
  updated_at?: Date;
} & ConsumptionTaxType;

export async function getListConsumptionTax(params: BaseParams) {
  return request<ResponseList<ConsumptionTaxDetailType[]>>(`/consumption_tax_master`, {
    method: 'GET',
    params,
  });
}

export async function getConsumptionTaxDetail(id: number) {
  return request<BaseResponse<ConsumptionTaxDetailType>>(`/consumption_tax_master/${id}`, {
    method: 'GET',
  });
}

// create or udate many
export async function createOrUpdateConsumptionTax(body: ConsumptionTaxDetailType[]) {
  return request<BaseResponse<any>>(`/consumption_tax_master`, {
    method: 'PUT',
    data: { data: body },
  });
}

export async function createConsumptionTax(body: ConsumptionTaxDetailType) {
  return request<BaseResponse<any>>(`/consumption_tax_master`, {
    method: 'POST',
    data: body,
  });
}

// update one
export async function updateConsumptionTax(id: number, payload: ConsumptionTaxType) {
  return request<BaseResponse<any>>(`/consumption_tax_master/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteConsumptionTax(id: number) {
  return request<BaseResponse<any>>(`/consumption_tax_master/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvConsumptionTax(formData: FormData) {
  return request(`/consumption_tax_master/import/csv`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportConsumptionTax(params: BaseParams) {
  return request<any>('/consumption_tax_master/export/csv', {
    method: 'GET',
    params: params,
  });
}
