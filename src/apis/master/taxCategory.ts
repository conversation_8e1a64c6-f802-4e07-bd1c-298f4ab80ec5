import type { BaseParams, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export interface DataDeleteOneTaxCategorySuccessResponse {
  message: string;
  data: boolean;
}

export interface DeleteOneTaxCategoryFailedResponse {
  response: Response;
  error: ErrorDeleteOneTaxCategoryFailedResponse;
  status: number;
}

export interface ErrorDeleteOneTaxCategoryFailedResponse {
  data: DataDeleteOneTaxCategoryFailedResponse;
  status: number;
  statusText: string;
  headers: any;
  config: any;
  request: Request;
}

export interface DataDeleteOneTaxCategoryFailedResponse {
  errors: ErrorsDeleteOneTaxCategoryFailedResponse;
}

export interface ErrorsDeleteOneTaxCategoryFailedResponse {
  message: string;
}

export interface TaxCategoryParamsSearch {
  limit: number;
  page: number;
  sort?: string; // column_name;
  order?: 'desc' | 'asc';
  keyword?: string;
  status?: 0 | 1;
}
export interface GetListTaxCategoryResponse {
  current_page: number;
  data: DatumGetListTaxCategory[];
  first_page_url: string;
  from: number;
  last_page: number;
  last_page_url: string;
  links: LinkGetListTaxCategory[];
  next_page_url: null;
  path: string;
  per_page: string;
  prev_page_url: null;
  to: number;
  total: number;
}

export interface DatumGetListTaxCategory {
  id: number;
  key?: number;
  tax_category_code: string;
  tax_category_name: string;
  tax_category_abbreviation: string;
  tax_rate: number | string;
  target: string;
  status: number;
  created_at: Date;
  updated_at: Date;
}

export interface BodyCreateOrUpdateTaxCategory {
  id?: number;
  tax_category_code: string;
  tax_category_name: string;
  tax_category_abbreviation: string;
  tax_rate: number | string;
  target: string;
  status: number;
}

export interface LinkGetListTaxCategory {
  url: null | string;
  label: string;
  active: boolean;
}

export interface ResponseExportMasterData {
  response: Response;
  data: DataExportMasterData;
  status: number;
}

export interface DataExportMasterData {
  file_link: string;
}

export async function importCsvTaxCategory(formData: FormData) {
  return request(`/tax_category_master/import/csv`, { method: 'POST', data: formData });
}
export async function exportCsvTaxCategory(params: BaseParams) {
  return request<DataExportMasterData>(`/tax_category_master/export/csv`, {
    method: 'GET',
    params,
  });
}

export async function createOrUpdateTaxCategory(body: BodyCreateOrUpdateTaxCategory[]) {
  return request(`/tax_category_master`, {
    method: 'PUT',
    data: { data: body },
  });
}

export async function createTaxCategory(body: BodyCreateOrUpdateTaxCategory) {
  return request(`/tax_category_master`, {
    method: 'POST',
    data: body,
  });
}

export async function getListTaxCategory(params: BaseParams) {
  return request<ResponseList<DatumGetListTaxCategory[]>>('/tax_category_master', {
    method: 'GET',
    params,
  });
}

export async function getDetailTaxCategory(id: number | string) {
  return request(`/tax_category_master/${id}`, { method: 'GET' });
}

export async function updateListTaxCategory(body) {
  return request(`/tax_category_master`, { method: 'PUT', data: body });
}

export async function deleteOneTaxCategory(id: number | string) {
  return request(`/tax_category_master/${id}`, { method: 'DELETE' });
}

export async function getListTaxCategoryCode() {
  return request('/tax_category_master/get_tax_code', { method: 'GET' });
}

export async function getListTaxCodeCategory() {
  return request('/tax_category_master/get_tax_code_target', { method: 'GET' });
}
