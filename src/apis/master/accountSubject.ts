import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type AccountSubjectMasterType = {
  account_code: string;
  account_name: string;
  account_abbreviation: string;
  business_partner_id: string;
  status: boolean | 0 | 1;
};

export type AccountSubjectMasterDetailType = {
  id?: number;
  key?: number;
  created_at?: Date;
  updated_at?: Date;
} & AccountSubjectMasterType;

export async function getListAccountSubjectMaster(params: BaseParams) {
  return request<ResponseList<AccountSubjectMasterDetailType[]>>(`/account_master`, {
    method: 'GET',
    params,
  });
}

export async function getAccountSubjectMasterDetail(id: number) {
  return request<BaseResponse<AccountSubjectMasterDetailType>>(`/account_master/${id}`, {
    method: 'GET',
  });
}

// create or udate many
export async function createOrUpdateAccountSubjectMaster(body: AccountSubjectMasterType[]) {
  return request<BaseResponse<any>>(`/account_master`, {
    method: 'PUT',
    data: { data: body },
  });
}

export async function createAccountSubjectMaster(body: AccountSubjectMasterType) {
  return request<BaseResponse<any>>(`/account_master`, {
    method: 'POST',
    data: body,
  });
}

// update one
export async function updateAccountSubjectMaster(id: number, payload: AccountSubjectMasterType) {
  return request<BaseResponse<any>>(`/account_master/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteAccountSubjectMaster(id: number) {
  return request<BaseResponse<any>>(`/account_master/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvAccountSubjectMaster(formData: FormData) {
  return request(`/account_master/import/csv`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportAccountSubjectMaster(params: BaseParams) {
  return request<any>('/account_master/export/csv', {
    method: 'GET',
    params: params,
  });
}
