import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type CurrencyMasterType = {
  currency_type_code: string;
  currency_type_name: string;
  business_partner_type: number;
  account_code: string;
  target: string[] | string;
  status: boolean | 0 | 1;
};

export type CurrencyMasterDetailType = {
  id?: number;
  key?: number;
  created_at?: Date;
  updated_at?: Date;
} & CurrencyMasterType;

export async function getListCurrencyTypeMaster(params: BaseParams) {
  return request<ResponseList<CurrencyMasterDetailType[]>>(`/currency_master`, {
    method: 'GET',
    params,
  });
}

export async function getCurrencyTypeMasterDetail(id: number) {
  return request<BaseResponse<CurrencyMasterDetailType>>(`/currency_master/${id}`, {
    method: 'GET',
  });
}
// create or udate many
export async function createOrUpdateCurrencyTypeMaster(body: CurrencyMasterDetailType[]) {
  return request<BaseResponse<any>>(`/currency_master`, {
    method: 'PUT',
    data: { data: body },
  });
}

export async function createCurrencyTypeMaster(body: CurrencyMasterDetailType) {
  return request<BaseResponse<any>>(`/currency_master`, {
    method: 'POST',
    data: body,
  });
}

// update one
export async function updateCurrencyTypeMaster(id: number, payload: CurrencyMasterType) {
  return request<BaseResponse<any>>(`/currency_master/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteCurrencyTypeMaster(id: number) {
  return request<BaseResponse<any>>(`/currency_master/${id}`, {
    method: 'DELETE',
  });
}

export async function importCsvCurrencyTypeMaster(formData: FormData) {
  return request(`/currency_master/import/csv`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportCurrencyTypeMaster(params: BaseParams) {
  return request<any>('/currency_master/export/csv', {
    method: 'GET',
    params: params,
  });
}
