import type { BaseResponse } from '@/@types/request';
import request from '@/utils/request';

export type CompanyType = {
  company_name: string;
  company_name_en: string;
  postal_code: string;
  address: string;
  address_en: string;
  corporate_number: string;
  qualified_invoice_issuer_number: string;
  phone_number: string;
  fax: string;
  email: string;
  travel_agency_license_number: string;
  invoice_print_account_number: string;
  invoice_print_account_name: string;
  invoice_print_account_name_2: string;
  invoice_print_account_number_2: string;
  company_seal_estimate_invoice: string[];
  logo: string;
};

export async function getCompany() {
  return request<BaseResponse<CompanyType>>('/company_master', {
    method: 'GET',
  });
}

export async function createCompany(payload: CompanyType) {
  return request<any>('/company_master', {
    method: 'POST',
    data: payload,
  });
}

export async function editCompany(payload: CompanyType) {
  return request<any>('/company_master', {
    method: 'PUT',
    data: payload,
  });
}
