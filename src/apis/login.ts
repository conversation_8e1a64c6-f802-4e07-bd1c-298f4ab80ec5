import request from '@/utils/request';

export async function login(data) {
  return request('/auth/login', {
    method: 'POST',
    data: data,
  });
}

export async function logout() {
  return request('/auth/logout', { method: 'POST' });
}

export async function sendEmailForgotPassword(data) {
  return request('/auth/forgot-password', { method: 'POST', data: data });
}

export async function resetPassword(data) {
  return request('/auth/reset-password', { method: 'POST', data: data });
}
