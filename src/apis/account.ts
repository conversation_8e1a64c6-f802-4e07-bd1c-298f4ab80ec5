import type { BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type ParamsListAccount = {
  limit?: number;
  page?: number;
  keyword?: string;
  id?: number | string;
};

export type PayloadType = {
  name: string;
  email: string;
  occupation: number;
};

export type AccountManagementType = {
  id: number;
  name: string;
  email: string;
  occupation: number;
  last_login_at: string;
  is_active: boolean;
};

export type AccountDetailType = AccountManagementType & {
  created_at: string;
  updated_at: string;
};

export interface DetailType {
  id: number;
  name: string;
  email: string;
  occupation: number;
}

export async function getList(params: ParamsListAccount) {
  return request<ResponseList<AccountDetailType[]>>(`/accounts`, {
    method: 'GET',
    params: params,
  });
}

export async function getDetailAccount(id: number) {
  return request<BaseResponse<any>>(`/accounts/${id}`, {
    method: 'GET',
  });
}

export async function createAccount(payLoad: PayloadType) {
  return request<BaseResponse<any>>(`/accounts`, {
    method: 'POST',
    data: payLoad,
  });
}

export async function updateAccount(id: number, payLoad: PayloadType) {
  return request<BaseResponse<any>>(`/accounts/${id}`, {
    method: 'PUT',
    data: payLoad,
  });
}

export async function deleteAccount(id: number) {
  return request<BaseResponse<any>>(`/accounts/${id}`, {
    method: 'DELETE',
  });
}

export async function sendNewPassword(id: number) {
  return request<BaseResponse<any>>(`/accounts/${id}/reset-password`, {
    method: 'PUT',
  });
}

export async function disableAccount(id: number) {
  return request<BaseResponse<any>>(`/accounts/${id}/change-status`, {
    method: 'PUT',
  });
}
