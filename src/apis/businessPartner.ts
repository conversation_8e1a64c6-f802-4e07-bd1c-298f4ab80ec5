import type { BusinessPartnerDetailType, BusinessPartnerType } from '@/@types/businessPartner';
import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type ParamsSearchBusinessPartner = BaseParams & {
  target?: string | number;
  is_use_sale?: 1 | 0;
  is_use_purchase?: 1 | 0;
};

export async function getListBusinessPartner(params: ParamsSearchBusinessPartner) {
  return request<ResponseList<BusinessPartnerDetailType[]>>(`/business_partners`, {
    method: 'GET',
    params: params,
  });
}

export async function createBusinessPartner(payload: BusinessPartnerType) {
  return request<BaseResponse<any>>(`/business_partners`, {
    method: 'POST',
    data: payload,
  });
}

export async function updateBusinessPartner(id: number, payload: BusinessPartnerType) {
  return request<BaseResponse<any>>(`/business_partners/${id}`, {
    method: 'PUT',
    data: payload,
  });
}

export async function deleteBusinessPartner(id: number) {
  return request<BaseResponse<any>>(`/business_partners/${id}`, {
    method: 'DELETE',
  });
}

export async function getDetailBusinessPartner(id: number) {
  return request<BaseResponse<BusinessPartnerDetailType>>(`/business_partners/${id}`, {
    method: 'GET',
  });
}

export async function importCsvBusinessPartner(formData: FormData) {
  return request(`/business_partners/import/csv`, {
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
}

export async function exportBusinessPartner(params: BaseParams) {
  return request<any>('/business_partners/export/csv', {
    method: 'GET',
    params: params,
  });
}

export async function getTemplateCsvBusinessPartner() {
  return request<any>('/master/business_partner.csv', {
    method: 'GET',
  });
}

export async function patchChangeStatusBusinessPartner(id: number, status: 1 | 0) {
  return request<any>(`/business_partners/change-status/${id}`, {
    method: 'PATCH',
    data: {
      status: status,
    },
  });
}
