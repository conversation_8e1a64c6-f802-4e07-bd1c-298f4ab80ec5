import type { BaseParams, BaseResponse, ResponseList } from '@/@types/request';
import request from '@/utils/request';

export type BodyCreateSgg = {
  title: string;
  type: number;
  display_date: string;
  expiration_date: string;
  content: string;
};
export interface BodyUpdateSgg {
  id: number;
  title?: string;
  type?: number;
  display_date?: string;
  expiration_date?: string;
  content?: string;
}

export type ItemSgg = {
  id: number;
  title: string;
  type: number;
  display_date: string;
  expiration_date: string;
  content: string;
  created_at: string;
  updated_at: string;
};

export type ItemGlobalSgg = {
  id: number;
  title: string;
  display_date: string;
  expiration_date: string;
  created_at: string;
};

export type ItemImageSgg = {
  sgg_company_logo: null | string;
  sgg_photo: null | string;
};

export async function getListImageSgg() {
  return request<BaseResponse<ItemImageSgg>>(`/sggs/image-setting`, {
    method: 'GET',
  });
}

export async function updateListImageSgg(body, config) {
  return request<BaseResponse<ItemImageSgg>>(`/sggs/image-setting`, {
    method: 'POST',
    data: body,
    ...config,
  });
}

export async function getListSgg(params: BaseParams) {
  return request<ResponseList<ItemSgg[]>>(`/sggs`, {
    method: 'GET',
    params,
  });
}
export async function getListGlobalSgg(params: { type: 1 | 2 | 3 }) {
  return request<ItemGlobalSgg[]>(`/sggs/global`, {
    method: 'GET',
    params,
  });
}

export async function getDetailSgg(id: number) {
  return request<BaseResponse<ItemSgg>>(`/sggs/${id}`, {
    method: 'GET',
  });
}

export async function createSgg(body: BodyCreateSgg) {
  return request<BaseResponse<ItemSgg>>(`/sggs`, {
    method: 'POST',
    data: body,
  });
}

export async function updateSgg(body: BodyUpdateSgg) {
  return request<BaseResponse<ItemSgg>>(`/sggs/${body.id}`, {
    method: 'PUT',
    data: body,
  });
}

export async function deleteSgg(id: number) {
  return request(`/sggs/${id}`, {
    method: 'DELETE',
  });
}
