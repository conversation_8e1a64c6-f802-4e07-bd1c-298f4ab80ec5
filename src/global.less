@import '~antd/es/style/themes/default.less';

/* stylelint-disable selector-pseudo-class-parentheses-space-inside */

@font-face {
  font-weight: 100;
  font-family: 'Noto Sans JP';
  font-style: normal;
  src: url('/noto-sans-jp-v36-latin_japanese-100.eot');

  /* IE9 Compat Modes */
  src: local(''), url('/noto-sans-jp-v36-latin_japanese-100.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('/noto-sans-jp-v36-latin_japanese-100.woff2') format('woff2'),
    /* Super Modern Browsers */ url('/noto-sans-jp-v36-latin_japanese-100.woff') format('woff'),
    /* Modern Browsers */ url('/noto-sans-jp-v36-latin_japanese-100.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('/noto-sans-jp-v36-latin_japanese-100.svg#NotoSansJP')
      format('svg');

  /* Legacy iOS */
}

/* noto-sans-jp-300 - latin_japanese */
@font-face {
  font-weight: 300;
  font-family: 'Noto Sans JP';
  font-style: normal;
  src: url('/noto-sans-jp-v36-latin_japanese-300.eot');

  /* IE9 Compat Modes */
  src: local(''), url('/noto-sans-jp-v36-latin_japanese-300.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('/noto-sans-jp-v36-latin_japanese-300.woff2') format('woff2'),
    /* Super Modern Browsers */ url('/noto-sans-jp-v36-latin_japanese-300.woff') format('woff'),
    /* Modern Browsers */ url('/noto-sans-jp-v36-latin_japanese-300.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('/noto-sans-jp-v36-latin_japanese-300.svg#NotoSansJP')
      format('svg');

  /* Legacy iOS */
}

/* noto-sans-jp-regular - latin_japanese */
@font-face {
  font-weight: 400;
  font-family: 'Noto Sans JP';
  font-style: normal;
  src: url('/noto-sans-jp-v36-latin_japanese-regular.eot');

  /* IE9 Compat Modes */
  src: local(''),
    url('/noto-sans-jp-v36-latin_japanese-regular.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('/noto-sans-jp-v36-latin_japanese-regular.woff2') format('woff2'),
    /* Super Modern Browsers */ url('/noto-sans-jp-v36-latin_japanese-regular.woff') format('woff'),
    /* Modern Browsers */ url('/noto-sans-jp-v36-latin_japanese-regular.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('/noto-sans-jp-v36-latin_japanese-regular.svg#NotoSansJP')
      format('svg');

  /* Legacy iOS */
}

/* noto-sans-jp-500 - latin_japanese */
@font-face {
  font-weight: 500;
  font-family: 'Noto Sans JP';
  font-style: normal;
  src: url('/noto-sans-jp-v36-latin_japanese-500.eot');

  /* IE9 Compat Modes */
  src: local(''), url('/noto-sans-jp-v36-latin_japanese-500.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('/noto-sans-jp-v36-latin_japanese-500.woff2') format('woff2'),
    /* Super Modern Browsers */ url('/noto-sans-jp-v36-latin_japanese-500.woff') format('woff'),
    /* Modern Browsers */ url('/noto-sans-jp-v36-latin_japanese-500.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('/noto-sans-jp-v36-latin_japanese-500.svg#NotoSansJP')
      format('svg');

  /* Legacy iOS */
}

/* noto-sans-jp-700 - latin_japanese */
@font-face {
  font-weight: 700;
  font-family: 'Noto Sans JP';
  font-style: normal;
  src: url('/noto-sans-jp-v36-latin_japanese-700.eot');

  /* IE9 Compat Modes */
  src: local(''), url('/noto-sans-jp-v36-latin_japanese-700.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('/noto-sans-jp-v36-latin_japanese-700.woff2') format('woff2'),
    /* Super Modern Browsers */ url('/noto-sans-jp-v36-latin_japanese-700.woff') format('woff'),
    /* Modern Browsers */ url('/noto-sans-jp-v36-latin_japanese-700.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('/noto-sans-jp-v36-latin_japanese-700.svg#NotoSansJP')
      format('svg');

  /* Legacy iOS */
}

/* noto-sans-jp-900 - latin_japanese */
@font-face {
  font-weight: 900;
  font-family: 'Noto Sans JP';
  font-style: normal;
  src: url('/noto-sans-jp-v36-latin_japanese-900.eot');

  /* IE9 Compat Modes */
  src: local(''), url('/noto-sans-jp-v36-latin_japanese-900.eot?#iefix') format('embedded-opentype'),
    /* IE6-IE8 */ url('/noto-sans-jp-v36-latin_japanese-900.woff2') format('woff2'),
    /* Super Modern Browsers */ url('/noto-sans-jp-v36-latin_japanese-900.woff') format('woff'),
    /* Modern Browsers */ url('/noto-sans-jp-v36-latin_japanese-900.ttf') format('truetype'),
    /* Safari, Android, iOS */ url('/noto-sans-jp-v36-latin_japanese-900.svg#NotoSansJP')
      format('svg');

  /* Legacy iOS */
}

html,
body,
#root {
  height: 100%;
  font-family: 'Noto Sans JP', sans-serif;
}

::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-thumb {
  background: #d0d5dd;
  border-radius: 4px;
}

::-webkit-scrollbar-track {
  background: #f5f6f8;
}
.ant-layout-header {
  height: 70px !important;
}
.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ul,
ol {
  padding: 0;
  list-style: none;
}

.flexRow {
  display: flex;
  flex-direction: row;
}

.flexColumn {
  display: flex;
  flex-direction: column;
}

.jcc {
  justify-content: center;
}

.jcsb {
  justify-content: space-between;
}

.dFlex {
  display: flex;
}

.aic {
  align-items: center;
}

.w100 {
  width: 100%;
}

.ishover:hover {
  cursor: pointer;
}

@media (max-width: @screen-xl) {
  .ant-table {
    width: 100%;
    overflow-x: auto;

    &-thead > tr,
    &-tbody > tr {
      > th,
      > td {
        white-space: pre;

        > span {
          display: block;
        }
      }
    }
  }
}

// 兼容IE11
@media screen and(-ms-high-contrast: active), (-ms-high-contrast: none) {
  body .ant-design-pro > .ant-layout {
    min-height: 100vh;
  }
}

/* noto-sans-jp-100 - latin_japanese */

// .ant-upload-list-text .ant-upload-text-icon .anticon {
//   display: none;
// }
.ant-picker-panel .ant-picker-date-panel {
  th:nth-of-type(6),
  td.ant-picker-cell-in-view:nth-of-type(6) {
    // color: #0089c4; //remove custom
  }

  th:last-of-type,
  td.ant-picker-cell-in-view:last-of-type {
    // color: #ee4d4d; //remove custom
  }
  .ant-picker-cell-in-view.ant-picker-cell-selected .ant-picker-cell-inner,
  .ant-picker-cell-in-view.ant-picker-cell-range-start .ant-picker-cell-inner,
  .ant-picker-cell-in-view.ant-picker-cell-range-end .ant-picker-cell-inner {
    border-radius: 100%;
  }
  .ant-picker-cell-in-view.ant-picker-cell-range-start:not(.ant-picker-cell-range-start-single):not(
      .ant-picker-cell-range-end
    )
    .ant-picker-cell-inner {
    border-radius: 100%;
  }
  .ant-picker-cell-in-view.ant-picker-cell-range-end:not(.ant-picker-cell-range-end-single):not(
      .ant-picker-cell-range-start
    )
    .ant-picker-cell-inner {
    border-radius: 100%;
  }

  .ant-picker-content thead tr th {
    color: @primary-color;
    font-size: 10px;
  }
}

.ant-picker-time-panel-column
  > li.ant-picker-time-panel-cell-selected
  .ant-picker-time-panel-cell-inner {
  color: #fff !important;
  background: #333 !important;
}

.ant-menu-dark.ant-menu-dark:not(.ant-menu-horizontal) .ant-menu-item-selected {
  background-color: @layout-header-background !important;
}

.ant-page-header-heading-title {
  width: 100%;
  color: #333;
}

.ant-pro-top-nav-header-logo h1 {
  color: @primary-color !important;
  font-weight: 700;
  font-size: 18px !important;
}

.ant-pro-top-nav-header {
  box-shadow: none !important;
}

.ant-pro-page-container-warp {
  // margin-top: 2px !important;
}

.ant-page-header-heading-left {
  width: 100%;
}

.fix_row_display {
  .ant-form-item-control-input-content {
    display: flex;
  }
}

.text-overflow-1 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.overlink {
  max-width: 20vw;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.overlink:focus::after,
.overlink:hover::after {
  position: absolute;
  top: auto;
  left: auto;
  z-index: 1;
  display: block;
  max-width: 100vh;
  margin-top: -1.25rem;
  padding: 0 0.5rem;
  overflow: visible;
  color: black;
  white-space: normal;
  text-overflow: inherit;
  word-wrap: break-word;
  background: #fff;
  border: 1px solid #eaebec;
  box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.28);
  content: attr(data-text);
}

.video {
  position: sticky;
  top: 0;
  z-index: 99;
  background: #c4c4c4;
  border-radius: 0.25rem;
}

.localStream_in_page_call {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 50%;
  height: 20%;
  background-color: #787878;
  border-radius: 4px;
}
.page_image_in_call {
  .ant-image-mask-info {
    display: none;
  }
}
.ant-pro-card-body {
  padding: 0;
}
.fix_label_search {
  .ant-card-body {
    padding-bottom: 0;
  }
  .ant-form-item-label > label {
    width: 100%;
    text-align: start;
  }
  .ant-form-item-label > label::after {
    content: '';
  }
  .ant-form-item-label {
    white-space: unset;
  }
}

@media all {
  .page-break {
    display: none;
  }
}

@media print {
  html,
  body {
    height: initial !important;
    overflow: initial !important;
    -webkit-print-color-adjust: exact;
  }
}

@media print {
  .page-break {
    display: block;
    margin-top: 1rem;
    page-break-before: auto;
  }
}

@page {
  margin: 10mm;
  size: auto;
}
.ant-pro-table .ant-pro-table-search {
  display: none;
}

.loading {
  position: absolute;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  min-height: 100vh;
  background-color: rgb(250, 250, 250);
  opacity: 0.9;
}

.dropShadow {
  margin: 15px auto;
  border-radius: 8px;
  &:hover {
    box-shadow: 0 0 19px #4cd5b2, 0 4px 13px rgba(0, 0, 0, 0.14), inset 0 0 1px rgba(0, 0, 0, 0.43);
    + .ant-card-bordered {
      border: none !important;
    }
  }
}

//css hotel detail drawer
.rowTabHotelDetail {
  margin-bottom: 16px;
}
.headingBlock {
  color: @primary-color;
  font-weight: 700;
  font-size: 17px;
  font-style: normal;
  line-height: 25px;
  letter-spacing: 0.3px;
}

.titleBlock {
  min-height: 25px;
  color: #343434;
  font-weight: 700;
  font-size: 17px;
  font-style: normal;
  line-height: 25px;
  letter-spacing: 0.3px;
}

.descriptionBlock {
  color: #000;
  font-weight: 300;
  font-size: 14px;
  font-style: normal;
  line-height: 165%;
}

.inputNumberPhone {
  position: relative;
  display: inline-block;
  box-sizing: border-box;
  width: 100%;
  min-width: 0;
  margin: 0;
  padding: 0;
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-variant: tabular-nums;
  line-height: 1.5715;
  list-style: none;
  background-color: #fff;
  background-image: none;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  transition: all 0.3s;
}

.inputNumberPhone:placeholder-shown {
  text-overflow: ellipsis;
  opacity: 0.5;
}

.inputNumberPhone:hover {
  border-color: #4cd5b2;
  border-right-width: 1px !important;
}

.inputNumberPhone:focus {
  border-color: #4cd5b2;
  border-right-width: 1px !important;
  outline: 0;
  box-shadow: 0 0 0 2px rgb(24 144 255 / 20%);
}

.form-label-item {
  color: #343434;
  font-weight: 700;
  font-size: 15px;
  line-height: 130%;
}

.hidden {
  display: none !important;
}

.collapseIcon {
  display: none;
}

//css count input
.changeIcon {
  color: @primary-color;
  cursor: pointer;
}
.changeIconDisable {
  color: #e4e8eb;
  cursor: not-allowed;
}

.countValue {
  margin-right: 8px;
  margin-left: 8px;
}

@media screen and (max-width: 768px) {
  .collapseIcon {
    display: block;
  }
}

.ant-table-tbody {
  tr {
    &:hover {
      cursor: pointer;
    }
  }
}

.video-react {
  min-width: 400px !important;
}
.truncate {
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
}
.text-ellipsis {
  text-overflow: ellipsis !important;
}

.dropdown-tree-select-custom {
  .ant-select-tree-list-holder-inner {
    padding-right: 7px;
  }

  .ant-select-tree-treenode-switcher-open,
  .ant-select-tree-treenode-switcher-close {
    padding: 5px 10px;
    font-weight: bold;
    // background-color: #eceff1;
    border-radius: 4px;
  }

  .ant-select-tree-node-content-wrapper-open,
  .ant-select-tree-node-content-wrapper-close {
    pointer-events: none;
  }

  .ant-select-tree-treenode {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    padding: 0 !important;
    .ant-select-tree-indent,
    .ant-select-tree-switcher-noop {
      display: none;
    }

    .ant-select-tree-node-content-wrapper-normal {
      width: 100%;
      height: 100%;
      padding-left: 20px;
      font-weight: normal;
      background-color: white;
      &:hover {
        color: #0ab489 !important;
        background-color: #e8fbf8 !important;
      }
    }

    .ant-select-tree-node-selected {
      color: #0ab489 !important;
      background-color: #e8fbf8 !important;
      border-radius: 2px;
    }
  }

  .ant-select-tree-list-scrollbar {
    width: 2px !important;
    .ant-select-tree-list-scrollbar-thumb {
      background-color: #c4c4c4 !important;
    }
  }
}

// select multiple checkbox
.select-multiple-checkbox {
  .ant-select-selection-item {
    .ant-select-selection-item-content {
      .checked {
        display: none;
      }
    }
  }
}

.dropdown-select-multiple-checkbox {
  .ant-select-item-option {
    .ant-select-item-option-content {
      display: flex;
      gap: 8px;
      align-items: center;

      .checked,
      .unchecked {
        height: 16px;
      }
    }
  }
  .ant-select-item-option-state {
    display: none;
  }
}
// end select multiple checkbox
.ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #3997c8 !important;
  border-color: #d9d9d9 !important;
  opacity: 0.4;
}

.ant-checkbox-disabled.ant-checkbox-checked .ant-checkbox-inner::after {
  border-color: #fff !important;
}

.ant-table-row.deleted-row {
  background-color: #d9d9d9 !important;
}
.ant-table-row .ant-form-item {
  margin-bottom: 0 !important;
}

.editable-cell-value-wrap {
  padding-inline-end: 0 !important;
}

.ant-select-item-option-content,
.ant-select-selection-item {
  line-height: 18px !important;
  white-space: pre-line !important;
}

.ant-popover-inner-content {
  max-width: 320px !important;
  white-space: pre-line !important;
}
.grid-cols-18 {
  display: grid;
  grid-template-columns: repeat(18, 1fr);
}
.row-dragging {
  background: #fafafa;
  border: 1px solid #ccc;
}

.row-dragging td {
  padding: 16px;
}

.row-dragging .drag-visible {
  visibility: visible;
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='10px']::before {
  font-size: 10px !important;
  content: '10px';
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='12px']::before {
  font-size: 12px !important;
  content: '12px';
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='14px']::before {
  font-size: 14px !important;
  content: '14px';
}

.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='16px']::before {
  font-size: 16px !important;
  content: '16px';
}

.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='18px']::before {
  font-size: 18px !important;
  content: '18px';
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='20px']::before {
  font-size: 20px !important;
  content: '20px';
}

.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='22px']::before {
  font-size: 22px !important;
  content: '22px';
}

.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='24px']::before {
  font-size: 24px !important;
  content: '24px';
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='26px']::before {
  font-size: 26px !important;
  content: '26px';
}

.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='28px']::before {
  font-size: 28px !important;
  content: '28px';
}

.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='32px']::before {
  font-size: 32px !important;
  content: '32px';
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='36px']::before {
  font-size: 36px !important;
  content: '36px';
}
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value='40px']::before {
  font-size: 40px !important;
  content: '40px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='10px']::before {
  content: '10px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='12px']::before {
  content: '12px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='14px']::before {
  content: '14px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='16px']::before {
  content: '16px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='18px']::before {
  content: '18px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='20px']::before {
  content: '20px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='22px']::before {
  content: '22px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='24px']::before {
  content: '24px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='26px']::before {
  content: '26px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='28px']::before {
  content: '28px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='32px']::before {
  content: '32px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='36px']::before {
  content: '36px';
}
.ql-snow .ql-picker.ql-size .ql-picker-label[data-value='40px']::before {
  content: '40px';
}

.ant-btn.accept:focus,
.ant-btn.accept:active {
  color: #fff !important;
  background-color: #3997c8 !important;
}

.ant-btn.delete:focus,
.ant-btn.delete:active {
  color: #fff !important;
  background-color: #e04c58 !important;
}

.ant-btn.orange:focus,
.ant-btn.orange:active {
  color: #fff !important;
  background-color: #ff7648 !important;
}

.ant-btn.back:focus,
.ant-btn.back:active {
  color: #414244 !important;
  background-color: #cdced4cc !important;
}

.form-header-information
  .ant-select-disabled.ant-select:not(.ant-select-customize-input)
  .ant-select-selector {
  background: #dcdee3;
}
