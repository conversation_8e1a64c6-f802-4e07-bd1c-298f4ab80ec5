import type { City } from '@/@types/location';
import { getCity } from '@/apis/common';
import { useEffect, useState } from 'react';

function useFetchCity(prefectureId: string | number) {
  const [cities, setCities] = useState<City[]>([]);

  useEffect(() => {
    const getInitCity = async () => {
      try {
        const { data, status } = await getCity(prefectureId);
        if (status === 200) {
          setCities(data?.data);
        }
      } catch (error) {
        console.error('Failed to fetch city', error);
        setCities([]);
      }
    };
    if (prefectureId) {
      getInitCity();
    }
  }, [prefectureId]);

  return [cities];
}

export default useFetchCity;
