/**
 * This injects <PERSON><PERSON><PERSON>'s base styles and any base styles registered by
 * plugins.
 */

*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::-ms-backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

/*! tailwindcss v3.4.12 | MIT License | https://tailwindcss.com
 */

/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  /* 1 */
  border-width: 0;
  /* 2 */
  border-style: solid;
  /* 2 */
  border-color: currentColor;
  /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5;
  /* 1 */
  -webkit-text-size-adjust: 100%;
  /* 2 */
  -moz-tab-size: 4;
  /* 3 */
  -o-tab-size: 4;
     tab-size: 4;
  /* 3 */
  font-family: Graphik, sans-serif;
  /* 4 */
  -webkit-font-feature-settings: normal;
          font-feature-settings: normal;
  /* 5 */
  font-variation-settings: normal;
  /* 6 */
  -webkit-tap-highlight-color: transparent;
  /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0;
  /* 1 */
  line-height: inherit;
  /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0;
  /* 1 */
  color: inherit;
  /* 2 */
  border-top-width: 1px;
  /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  /* 1 */
  -webkit-font-feature-settings: normal;
          font-feature-settings: normal;
  /* 2 */
  font-variation-settings: normal;
  /* 3 */
  font-size: 1em;
  /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0;
  /* 1 */
  border-color: inherit;
  /* 2 */
  border-collapse: collapse;
  /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit;
  /* 1 */
  -webkit-font-feature-settings: inherit;
          font-feature-settings: inherit;
  /* 1 */
  font-variation-settings: inherit;
  /* 1 */
  font-size: 100%;
  /* 1 */
  font-weight: inherit;
  /* 1 */
  line-height: inherit;
  /* 1 */
  letter-spacing: inherit;
  /* 1 */
  color: inherit;
  /* 1 */
  margin: 0;
  /* 2 */
  padding: 0;
  /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button;
  /* 1 */
  background-color: transparent;
  /* 2 */
  background-image: none;
  /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield;
  /* 1 */
  outline-offset: -2px;
  /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button;
  /* 1 */
  font: inherit;
  /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/

dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-webkit-input-placeholder, textarea::-webkit-input-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input:-ms-input-placeholder, textarea:-ms-input-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::-ms-input-placeholder, textarea::-ms-input-placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1;
  /* 1 */
  color: #9ca3af;
  /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/

:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  /* 1 */
  vertical-align: middle;
  /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */

[hidden] {
  display: none;
}

a {
  text-decoration-line: underline;
}

/**
   * This injects Tailwind's component classes and any component classes
   * registered by plugins.
   */

.container {
  width: 100%;
}

@media (min-width: 640px) {
  .container {
    max-width: 640px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/**
   * This injects Tailwind's utility classes and any utility classes registered
   * by plugins.
   */

.static {
  position: static;
}

.fixed {
  position: fixed;
}

.absolute {
  position: absolute;
}

.relative {
  position: relative;
}

.\!top-\[40px\] {
  top: 40px !important;
}

.bottom-4 {
  bottom: 1rem;
}

.bottom-\[-6px\] {
  bottom: -6px;
}

.left-0 {
  left: 0px;
}

.right-0 {
  right: 0px;
}

.right-3 {
  right: 0.75rem;
}

.right-\[-6px\] {
  right: -6px;
}

.top-\[-6px\] {
  top: -6px;
}

.top-\[10px\] {
  top: 10px;
}

.top-\[14px\] {
  top: 14px;
}

.z-10 {
  z-index: 10;
}

.z-30 {
  z-index: 30;
}

.z-\[100\] {
  z-index: 100;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-span-5 {
  grid-column: span 5 / span 5;
}

.col-span-6 {
  grid-column: span 6 / span 6;
}

.col-span-8 {
  grid-column: span 8 / span 8;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

.m-9 {
  margin: 2.25rem;
}

.m-auto {
  margin: auto;
}

.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}

.mx-2 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.mx-8 {
  margin-left: 2rem;
  margin-right: 2rem;
}

.mx-\[30px\] {
  margin-left: 30px;
  margin-right: 30px;
}

.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}

.my-\[24px\] {
  margin-top: 24px;
  margin-bottom: 24px;
}

.\!mb-0 {
  margin-bottom: 0px !important;
}

.\!mb-1 {
  margin-bottom: 0.25rem !important;
}

.\!ml-1 {
  margin-left: 0.25rem !important;
}

.\!mt-0 {
  margin-top: 0px !important;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 0.75rem;
}

.mb-4 {
  margin-bottom: 1rem;
}

.mb-5 {
  margin-bottom: 1.25rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.mb-\[40px\] {
  margin-bottom: 40px;
}

.mb-\[60px\] {
  margin-bottom: 60px;
}

.ml-1 {
  margin-left: 0.25rem;
}

.ml-2 {
  margin-left: 0.5rem;
}

.ml-3 {
  margin-left: 0.75rem;
}

.ml-4 {
  margin-left: 1rem;
}

.ml-\[2px\] {
  margin-left: 2px;
}

.mr-10 {
  margin-right: 2.5rem;
}

.mr-2 {
  margin-right: 0.5rem;
}

.mr-8 {
  margin-right: 2rem;
}

.mr-\[2px\] {
  margin-right: 2px;
}

.mr-\[6px\] {
  margin-right: 6px;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-10 {
  margin-top: 2.5rem;
}

.mt-11 {
  margin-top: 2.75rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 0.75rem;
}

.mt-4 {
  margin-top: 1rem;
}

.mt-6 {
  margin-top: 1.5rem;
}

.mt-7 {
  margin-top: 1.75rem;
}

.mt-\[1\.5px\] {
  margin-top: 1.5px;
}

.mt-\[10px\] {
  margin-top: 10px;
}

.mt-\[14px\] {
  margin-top: 14px;
}

.\!mt-\[24px\] {
  margin-top: 24px !important;
}

.mt-\[32px\] {
  margin-top: 32px;
}

.mt-\[56px\] {
  margin-top: 56px;
}

.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

.block {
  display: block;
}

.inline-block {
  display: inline-block;
}

.\!flex {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
}

.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.table {
  display: table;
}

.grid {
  display: grid;
}

.\!hidden {
  display: none !important;
}

.hidden {
  display: none;
}

.\!h-10 {
  height: 2.5rem !important;
}

.\!h-5 {
  height: 1.25rem !important;
}

.\!h-9 {
  height: 2.25rem !important;
}

.\!h-\[112px\] {
  height: 112px !important;
}

.\!h-\[128px\] {
  height: 128px !important;
}

.\!h-\[24px\] {
  height: 24px !important;
}

.\!h-\[40px\] {
  height: 40px !important;
}

.\!h-fit {
  height: -webkit-fit-content !important;
  height: -moz-fit-content !important;
  height: fit-content !important;
}

.\!h-full {
  height: 100% !important;
}

.h-0 {
  height: 0px;
}

.h-1 {
  height: 0.25rem;
}

.h-10 {
  height: 2.5rem;
}

.h-12 {
  height: 3rem;
}

.h-2 {
  height: 0.5rem;
}

.h-5 {
  height: 1.25rem;
}

.h-6 {
  height: 1.5rem;
}

.h-7 {
  height: 1.75rem;
}

.h-9 {
  height: 2.25rem;
}

.h-\[120px\] {
  height: 120px;
}

.h-\[12px\] {
  height: 12px;
}

.h-\[176px\] {
  height: 176px;
}

.h-\[1px\] {
  height: 1px;
}

.h-\[20px\] {
  height: 20px;
}

.h-\[32px\] {
  height: 32px;
}

.h-\[36px\] {
  height: 36px;
}

.h-\[400px\] {
  height: 400px;
}

.h-\[40px\] {
  height: 40px;
}

.h-\[46px\] {
  height: 46px;
}

.h-\[60\%\] {
  height: 60%;
}

.h-\[60px\] {
  height: 60px;
}

.h-fit {
  height: -webkit-fit-content;
  height: -moz-fit-content;
  height: fit-content;
}

.h-full {
  height: 100%;
}

.max-h-\[200px\] {
  max-height: 200px;
}

.max-h-\[336px\] {
  max-height: 336px;
}

.max-h-\[500px\] {
  max-height: 500px;
}

.min-h-\[200px\] {
  min-height: 200px;
}

.min-h-\[46px\] {
  min-height: 46px;
}

.min-h-\[500px\] {
  min-height: 500px;
}

.\!w-5 {
  width: 1.25rem !important;
}

.\!w-\[100px\] {
  width: 100px !important;
}

.\!w-\[1160px\] {
  width: 1160px !important;
}

.\!w-\[1200px\] {
  width: 1200px !important;
}

.\!w-\[1300px\] {
  width: 1300px !important;
}

.\!w-\[1400px\] {
  width: 1400px !important;
}

.\!w-\[140px\] {
  width: 140px !important;
}

.\!w-\[186px\] {
  width: 186px !important;
}

.\!w-\[190px\] {
  width: 190px !important;
}

.\!w-\[216px\] {
  width: 216px !important;
}

.\!w-\[230px\] {
  width: 230px !important;
}

.\!w-\[240px\] {
  width: 240px !important;
}

.\!w-\[290px\] {
  width: 290px !important;
}

.\!w-\[300px\] {
  width: 300px !important;
}

.\!w-\[400px\] {
  width: 400px !important;
}

.\!w-\[600px\] {
  width: 600px !important;
}

.\!w-\[84px\] {
  width: 84px !important;
}

.\!w-full {
  width: 100% !important;
}

.w-0 {
  width: 0px;
}

.w-5 {
  width: 1.25rem;
}

.w-6 {
  width: 1.5rem;
}

.w-\[108px\] {
  width: 108px;
}

.w-\[112px\] {
  width: 112px;
}

.w-\[120px\] {
  width: 120px;
}

.w-\[124px\] {
  width: 124px;
}

.w-\[12px\] {
  width: 12px;
}

.w-\[140px\] {
  width: 140px;
}

.w-\[160px\] {
  width: 160px;
}

.w-\[162px\] {
  width: 162px;
}

.w-\[164px\] {
  width: 164px;
}

.w-\[186px\] {
  width: 186px;
}

.w-\[20\%\] {
  width: 20%;
}

.w-\[200px\] {
  width: 200px;
}

.w-\[20px\] {
  width: 20px;
}

.w-\[220px\] {
  width: 220px;
}

.w-\[240px\] {
  width: 240px;
}

.w-\[248px\] {
  width: 248px;
}

.w-\[260px\] {
  width: 260px;
}

.w-\[280px\] {
  width: 280px;
}

.w-\[290px\] {
  width: 290px;
}

.w-\[30\%\] {
  width: 30%;
}

.w-\[320px\] {
  width: 320px;
}

.w-\[360px\] {
  width: 360px;
}

.w-\[40px\] {
  width: 40px;
}

.w-\[68px\] {
  width: 68px;
}

.w-\[70px\] {
  width: 70px;
}

.w-\[74px\] {
  width: 74px;
}

.w-\[76px\] {
  width: 76px;
}

.w-\[80px\] {
  width: 80px;
}

.w-\[84px\] {
  width: 84px;
}

.w-\[88\%\] {
  width: 88%;
}

.w-\[88px\] {
  width: 88px;
}

.w-\[94px\] {
  width: 94px;
}

.w-\[98px\] {
  width: 98px;
}

.w-fit {
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
}

.w-full {
  width: 100%;
}

.\!min-w-\[140px\] {
  min-width: 140px !important;
}

.\!min-w-\[240px\] {
  min-width: 240px !important;
}

.min-w-\[100px\] {
  min-width: 100px;
}

.min-w-\[120px\] {
  min-width: 120px;
}

.min-w-\[140px\] {
  min-width: 140px;
}

.min-w-\[160px\] {
  min-width: 160px;
}

.min-w-\[190px\] {
  min-width: 190px;
}

.min-w-\[200px\] {
  min-width: 200px;
}

.min-w-\[360px\] {
  min-width: 360px;
}

.min-w-\[50px\] {
  min-width: 50px;
}

.min-w-\[75px\] {
  min-width: 75px;
}

.min-w-\[80px\] {
  min-width: 80px;
}

.min-w-\[50\%\] {
  min-width: 50%;
}

.max-w-\[1000px\] {
  max-width: 1000px;
}

.max-w-\[200px\] {
  max-width: 200px;
}

.max-w-\[300px\] {
  max-width: 300px;
}

.max-w-\[350px\] {
  max-width: 350px;
}

.max-w-\[400px\] {
  max-width: 400px;
}

.max-w-\[424px\] {
  max-width: 424px;
}

.max-w-\[600px\] {
  max-width: 600px;
}

.max-w-\[70\%\] {
  max-width: 70%;
}

.flex-1 {
  -webkit-box-flex: 1;
      -ms-flex: 1 1 0%;
          flex: 1 1 0%;
}

.flex-grow {
  -webkit-box-flex: 1;
      -ms-flex-positive: 1;
          flex-grow: 1;
}

.-translate-x-full {
  --tw-translate-x: -100%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.translate-x-full {
  --tw-translate-x: 100%;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-180 {
  --tw-rotate: 180deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.rotate-\[-90deg\] {
  --tw-rotate: -90deg;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.transform {
  -webkit-transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
          transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\!cursor-not-allowed {
  cursor: not-allowed !important;
}

.cursor-not-allowed {
  cursor: not-allowed;
}

.cursor-pointer {
  cursor: pointer;
}

.touch-pan-x {
  --tw-pan-x: pan-x;
  -ms-touch-action: var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom);
      touch-action: var(--tw-pan-x) var(--tw-pan-y) var(--tw-pinch-zoom);
}

.resize {
  resize: both;
}

.grid-cols-10 {
  grid-template-columns: repeat(10, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}

.\!flex-col {
  -webkit-box-orient: vertical !important;
  -webkit-box-direction: normal !important;
      -ms-flex-direction: column !important;
          flex-direction: column !important;
}

.flex-col {
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
      -ms-flex-direction: column;
          flex-direction: column;
}

.flex-wrap {
  -ms-flex-wrap: wrap;
      flex-wrap: wrap;
}

.items-end {
  -webkit-box-align: end;
      -ms-flex-align: end;
          align-items: flex-end;
}

.items-center {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.justify-start {
  -webkit-box-pack: start;
      -ms-flex-pack: start;
          justify-content: flex-start;
}

.justify-end {
  -webkit-box-pack: end;
      -ms-flex-pack: end;
          justify-content: flex-end;
}

.justify-center {
  -webkit-box-pack: center;
      -ms-flex-pack: center;
          justify-content: center;
}

.justify-between {
  -webkit-box-pack: justify;
      -ms-flex-pack: justify;
          justify-content: space-between;
}

.gap-1 {
  gap: 0.25rem;
}

.gap-10 {
  gap: 2.5rem;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-20 {
  gap: 5rem;
}

.gap-4 {
  gap: 1rem;
}

.gap-5 {
  gap: 1.25rem;
}

.gap-6 {
  gap: 1.5rem;
}

.gap-7 {
  gap: 1.75rem;
}

.gap-9 {
  gap: 2.25rem;
}

.gap-\[10px\] {
  gap: 10px;
}

.gap-\[120px\] {
  gap: 120px;
}

.gap-\[36px\] {
  gap: 36px;
}

.gap-\[4px\] {
  gap: 4px;
}

.gap-\[6px\] {
  gap: 6px;
}

.gap-x-1 {
  -webkit-column-gap: 0.25rem;
     -moz-column-gap: 0.25rem;
          column-gap: 0.25rem;
}

.gap-x-2 {
  -webkit-column-gap: 0.5rem;
     -moz-column-gap: 0.5rem;
          column-gap: 0.5rem;
}

.gap-x-3 {
  -webkit-column-gap: 0.75rem;
     -moz-column-gap: 0.75rem;
          column-gap: 0.75rem;
}

.gap-x-6 {
  -webkit-column-gap: 1.5rem;
     -moz-column-gap: 1.5rem;
          column-gap: 1.5rem;
}

.gap-x-\[12px\] {
  -webkit-column-gap: 12px;
     -moz-column-gap: 12px;
          column-gap: 12px;
}

.gap-x-\[16px\] {
  -webkit-column-gap: 16px;
     -moz-column-gap: 16px;
          column-gap: 16px;
}

.gap-x-\[20px\] {
  -webkit-column-gap: 20px;
     -moz-column-gap: 20px;
          column-gap: 20px;
}

.gap-x-\[22px\] {
  -webkit-column-gap: 22px;
     -moz-column-gap: 22px;
          column-gap: 22px;
}

.gap-x-\[24px\] {
  -webkit-column-gap: 24px;
     -moz-column-gap: 24px;
          column-gap: 24px;
}

.gap-y-2 {
  row-gap: 0.5rem;
}

.gap-y-4 {
  row-gap: 1rem;
}

.gap-y-6 {
  row-gap: 1.5rem;
}

.gap-y-\[16px\] {
  row-gap: 16px;
}

.gap-y-\[6px\] {
  row-gap: 6px;
}

.gap-y-\[8px\] {
  row-gap: 8px;
}

.space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-\[10px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(10px * var(--tw-space-x-reverse));
  margin-left: calc(10px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-\[15px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(15px * var(--tw-space-x-reverse));
  margin-left: calc(15px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-\[16px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(16px * var(--tw-space-x-reverse));
  margin-left: calc(16px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-\[18px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(18px * var(--tw-space-x-reverse));
  margin-left: calc(18px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-\[24px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(24px * var(--tw-space-x-reverse));
  margin-left: calc(24px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-\[4px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(4px * var(--tw-space-x-reverse));
  margin-left: calc(4px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-\[6px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(6px * var(--tw-space-x-reverse));
  margin-left: calc(6px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-\[7px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(7px * var(--tw-space-x-reverse));
  margin-left: calc(7px * calc(1 - var(--tw-space-x-reverse)));
}

.space-x-\[8px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(8px * var(--tw-space-x-reverse));
  margin-left: calc(8px * calc(1 - var(--tw-space-x-reverse)));
}

.space-y-\[16px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(16px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(16px * var(--tw-space-y-reverse));
}

.space-y-\[17px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(17px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(17px * var(--tw-space-y-reverse));
}

.space-y-\[20px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(20px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(20px * var(--tw-space-y-reverse));
}

.space-y-\[24px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(24px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(24px * var(--tw-space-y-reverse));
}

.overflow-auto {
  overflow: auto;
}

.overflow-x-auto {
  overflow-x: auto;
}

.overflow-y-auto {
  overflow-y: auto;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.scroll-smooth {
  scroll-behavior: smooth;
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.whitespace-pre {
  white-space: pre;
}

.whitespace-pre-line {
  white-space: pre-line;
}

.break-words {
  overflow-wrap: break-word;
}

.\!rounded-\[0\.5rem\] {
  border-radius: 0.5rem !important;
}

.\!rounded-\[10px\] {
  border-radius: 10px !important;
}

.\!rounded-\[8px\] {
  border-radius: 8px !important;
}

.\!rounded-lg {
  border-radius: 0.5rem !important;
}

.rounded {
  border-radius: 0.25rem;
}

.rounded-\[12px\] {
  border-radius: 12px;
}

.rounded-\[2px\] {
  border-radius: 2px;
}

.rounded-\[4px\] {
  border-radius: 4px;
}

.rounded-\[8px\] {
  border-radius: 8px;
}

.rounded-full {
  border-radius: 9999px;
}

.rounded-lg {
  border-radius: 0.5rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.rounded-xl {
  border-radius: 0.75rem;
}

.rounded-t-xl {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}

.\!border {
  border-width: 1px !important;
}

.border {
  border-width: 1px;
}

.border-\[1px\] {
  border-width: 1px;
}

.border-b {
  border-bottom-width: 1px;
}

.border-r {
  border-right-width: 1px;
}

.border-t {
  border-top-width: 1px;
}

.border-dashed {
  border-style: dashed;
}

.\!border-none {
  border-style: none !important;
}

.border-none {
  border-style: none;
}

.\!border-\[\#225DE0\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(34 93 224 / var(--tw-border-opacity)) !important;
}

.\!border-\[\#3997C8\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(57 151 200 / var(--tw-border-opacity)) !important;
}

.\!border-\[\#CDCED4CC\] {
  border-color: #CDCED4CC !important;
}

.\!border-\[\#DCDEE3\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(220 222 227 / var(--tw-border-opacity)) !important;
}

.\!border-\[\#EBE9FA\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(235 233 250 / var(--tw-border-opacity)) !important;
}

.\!border-\[\#FDAF2E\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(253 175 46 / var(--tw-border-opacity)) !important;
}

.\!border-\[\#dcdee3\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(220 222 227 / var(--tw-border-opacity)) !important;
}

.\!border-\[\#ff4d4f\] {
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 77 79 / var(--tw-border-opacity)) !important;
}

.\!border-\[transparent\] {
  border-color: transparent !important;
}

.\!border-white {
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 255 255 / var(--tw-border-opacity)) !important;
}

.border-\[\#3F3F661F\] {
  border-color: #3F3F661F;
}

.border-\[\#CDCED4CC\] {
  border-color: #CDCED4CC;
}

.border-\[\#DCDEE3\] {
  --tw-border-opacity: 1;
  border-color: rgb(220 222 227 / var(--tw-border-opacity));
}

.border-\[\#E1E6EA\] {
  --tw-border-opacity: 1;
  border-color: rgb(225 230 234 / var(--tw-border-opacity));
}

.border-\[\#EFF0F2\] {
  --tw-border-opacity: 1;
  border-color: rgb(239 240 242 / var(--tw-border-opacity));
}

.border-\[\#F4F4F4\] {
  --tw-border-opacity: 1;
  border-color: rgb(244 244 244 / var(--tw-border-opacity));
}

.border-\[\#ccc\] {
  --tw-border-opacity: 1;
  border-color: rgb(204 204 204 / var(--tw-border-opacity));
}

.border-\[\#fff\] {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}

.border-\[transparent\] {
  border-color: transparent;
}

.border-main-color {
  --tw-border-opacity: 1;
  border-color: rgb(57 151 200 / var(--tw-border-opacity));
}

.\!bg-\[\#225DE0\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(34 93 224 / var(--tw-bg-opacity)) !important;
}

.\!bg-\[\#3997C8\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(57 151 200 / var(--tw-bg-opacity)) !important;
}

.\!bg-\[\#DCDEE3\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 222 227 / var(--tw-bg-opacity)) !important;
}

.\!bg-\[\#FDAF2E\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(253 175 46 / var(--tw-bg-opacity)) !important;
}

.\!bg-\[\#FF3B30\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 59 48 / var(--tw-bg-opacity)) !important;
}

.\!bg-\[\#f5f5f5\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(245 245 245 / var(--tw-bg-opacity)) !important;
}

.\!bg-\[gray\] {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(128 128 128 / var(--tw-bg-opacity)) !important;
}

.\!bg-\[transparent\] {
  background-color: transparent !important;
}

.\!bg-main-color {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(57 151 200 / var(--tw-bg-opacity)) !important;
}

.\!bg-white {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
}

.bg-\[\#225DE0\] {
  --tw-bg-opacity: 1;
  background-color: rgb(34 93 224 / var(--tw-bg-opacity));
}

.bg-\[\#3997C8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(57 151 200 / var(--tw-bg-opacity));
}

.bg-\[\#9b9b9bcc\] {
  background-color: #9b9b9bcc;
}

.bg-\[\#BCE4E3\] {
  --tw-bg-opacity: 1;
  background-color: rgb(188 228 227 / var(--tw-bg-opacity));
}

.bg-\[\#CDCED4CC\] {
  background-color: #CDCED4CC;
}

.bg-\[\#E04C58\] {
  --tw-bg-opacity: 1;
  background-color: rgb(224 76 88 / var(--tw-bg-opacity));
}

.bg-\[\#EFF0F2\] {
  --tw-bg-opacity: 1;
  background-color: rgb(239 240 242 / var(--tw-bg-opacity));
}

.bg-\[\#F0F2F5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(240 242 245 / var(--tw-bg-opacity));
}

.bg-\[\#F2F3F8\] {
  --tw-bg-opacity: 1;
  background-color: rgb(242 243 248 / var(--tw-bg-opacity));
}

.bg-\[\#FF5F5F\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 95 95 / var(--tw-bg-opacity));
}

.bg-\[\#FF7648\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 118 72 / var(--tw-bg-opacity));
}

.bg-\[\#FFE3E3\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 227 227 / var(--tw-bg-opacity));
}

.bg-\[\#FFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-\[\#fff\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.bg-main-color {
  --tw-bg-opacity: 1;
  background-color: rgb(57 151 200 / var(--tw-bg-opacity));
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 0.75rem;
}

.p-4 {
  padding: 1rem;
}

.p-5 {
  padding: 1.25rem;
}

.p-6 {
  padding: 1.5rem;
}

.p-7 {
  padding: 1.75rem;
}

.p-9 {
  padding: 2.25rem;
}

.p-\[2px\] {
  padding: 2px;
}

.\!px-\[11px\] {
  padding-left: 11px !important;
  padding-right: 11px !important;
}

.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}

.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}

.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}

.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}

.px-9 {
  padding-left: 2.25rem;
  padding-right: 2.25rem;
}

.px-\[12px\] {
  padding-left: 12px;
  padding-right: 12px;
}

.px-\[16px\] {
  padding-left: 16px;
  padding-right: 16px;
}

.px-\[22px\] {
  padding-left: 22px;
  padding-right: 22px;
}

.px-\[24px\] {
  padding-left: 24px;
  padding-right: 24px;
}

.px-\[30px\] {
  padding-left: 30px;
  padding-right: 30px;
}

.px-\[5px\] {
  padding-left: 5px;
  padding-right: 5px;
}

.px-\[7px\] {
  padding-left: 7px;
  padding-right: 7px;
}

.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}

.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}

.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}

.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}

.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}

.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}

.py-\[10px\] {
  padding-top: 10px;
  padding-bottom: 10px;
}

.py-\[16px\] {
  padding-top: 16px;
  padding-bottom: 16px;
}

.py-\[22px\] {
  padding-top: 22px;
  padding-bottom: 22px;
}

.py-\[24px\] {
  padding-top: 24px;
  padding-bottom: 24px;
}

.py-\[2px\] {
  padding-top: 2px;
  padding-bottom: 2px;
}

.py-\[4px\] {
  padding-top: 4px;
  padding-bottom: 4px;
}

.py-\[8px\] {
  padding-top: 8px;
  padding-bottom: 8px;
}

.\!pt-0 {
  padding-top: 0px !important;
}

.pb-1 {
  padding-bottom: 0.25rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.pb-3 {
  padding-bottom: 0.75rem;
}

.pb-4 {
  padding-bottom: 1rem;
}

.pb-5 {
  padding-bottom: 1.25rem;
}

.pb-6 {
  padding-bottom: 1.5rem;
}

.pb-7 {
  padding-bottom: 1.75rem;
}

.pb-9 {
  padding-bottom: 2.25rem;
}

.pl-2 {
  padding-left: 0.5rem;
}

.pl-\[12px\] {
  padding-left: 12px;
}

.pr-3 {
  padding-right: 0.75rem;
}

.pr-\[10px\] {
  padding-right: 10px;
}

.pr-\[3rem\] {
  padding-right: 3rem;
}

.pt-3 {
  padding-top: 0.75rem;
}

.pt-4 {
  padding-top: 1rem;
}

.pt-6 {
  padding-top: 1.5rem;
}

.pt-\[225px\] {
  padding-top: 225px;
}

.pt-\[233px\] {
  padding-top: 233px;
}

.pt-\[245px\] {
  padding-top: 245px;
}

.text-center {
  text-align: center;
}

.\!text-right {
  text-align: right !important;
}

.text-right {
  text-align: right;
}

.\!text-sm {
  font-size: 0.875rem !important;
  line-height: 1.25rem !important;
}

.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}

.text-\[10px\] {
  font-size: 10px;
}

.text-\[12px\] {
  font-size: 12px;
}

.text-\[13px\] {
  font-size: 13px;
}

.text-\[14px\] {
  font-size: 14px;
}

.text-\[15px\] {
  font-size: 15px;
}

.text-\[16px\] {
  font-size: 16px;
}

.text-\[18px\] {
  font-size: 18px;
}

.text-\[20px\] {
  font-size: 20px;
}

.text-\[22px\] {
  font-size: 22px;
}

.text-\[24px\] {
  font-size: 24px;
}

.text-\[40px\] {
  font-size: 40px;
}

.text-\[8px\] {
  font-size: 8px;
}

.text-\[9px\] {
  font-size: 9px;
}

.text-base {
  font-size: 1rem;
  line-height: 1.5rem;
}

.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}

.text-sm {
  font-size: 0.875rem;
  line-height: 1.25rem;
}

.text-xl {
  font-size: 1.25rem;
  line-height: 1.75rem;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1rem;
}

.font-\[400\] {
  font-weight: 400;
}

.font-\[500\] {
  font-weight: 500;
}

.font-bold {
  font-weight: 700;
}

.font-light {
  font-weight: 300;
}

.font-medium {
  font-weight: 500;
}

.font-normal {
  font-weight: 400;
}

.font-semibold {
  font-weight: 600;
}

.italic {
  font-style: italic;
}

.leading-10 {
  line-height: 2.5rem;
}

.leading-3 {
  line-height: .75rem;
}

.leading-4 {
  line-height: 1rem;
}

.leading-5 {
  line-height: 1.25rem;
}

.leading-6 {
  line-height: 1.5rem;
}

.leading-7 {
  line-height: 1.75rem;
}

.leading-8 {
  line-height: 2rem;
}

.leading-9 {
  line-height: 2.25rem;
}

.leading-\[10px\] {
  line-height: 10px;
}

.leading-\[19px\] {
  line-height: 19px;
}

.leading-\[30px\] {
  line-height: 30px;
}

.\!text-\[\#225DE0\] {
  --tw-text-opacity: 1 !important;
  color: rgb(34 93 224 / var(--tw-text-opacity)) !important;
}

.\!text-\[\#363840\] {
  --tw-text-opacity: 1 !important;
  color: rgb(54 56 64 / var(--tw-text-opacity)) !important;
}

.\!text-\[\#3997C8\] {
  --tw-text-opacity: 1 !important;
  color: rgb(57 151 200 / var(--tw-text-opacity)) !important;
}

.\!text-\[\#9499A5\] {
  --tw-text-opacity: 1 !important;
  color: rgb(148 153 165 / var(--tw-text-opacity)) !important;
}

.\!text-\[\#EC980C\] {
  --tw-text-opacity: 1 !important;
  color: rgb(236 152 12 / var(--tw-text-opacity)) !important;
}

.\!text-\[\#FDAF2E\] {
  --tw-text-opacity: 1 !important;
  color: rgb(253 175 46 / var(--tw-text-opacity)) !important;
}

.\!text-\[\#FF3B30\] {
  --tw-text-opacity: 1 !important;
  color: rgb(255 59 48 / var(--tw-text-opacity)) !important;
}

.\!text-\[\#ff4d4f\] {
  --tw-text-opacity: 1 !important;
  color: rgb(255 77 79 / var(--tw-text-opacity)) !important;
}

.\!text-\[\#fff\] {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.\!text-\[rgba\(0\2c 0\2c 0\2c 0\.3\)\] {
  color: rgba(0,0,0,0.3) !important;
}

.\!text-\[rgba\(0\2c 0\2c 0\2c 0\.85\)\] {
  color: rgba(0,0,0,0.85) !important;
}

.\!text-main-color {
  --tw-text-opacity: 1 !important;
  color: rgb(57 151 200 / var(--tw-text-opacity)) !important;
}

.\!text-white {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.text-\[\#000000\] {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-\[\#000\] {
  --tw-text-opacity: 1;
  color: rgb(0 0 0 / var(--tw-text-opacity));
}

.text-\[\#029102\] {
  --tw-text-opacity: 1;
  color: rgb(2 145 2 / var(--tw-text-opacity));
}

.text-\[\#0692BC\] {
  --tw-text-opacity: 1;
  color: rgb(6 146 188 / var(--tw-text-opacity));
}

.text-\[\#121315\] {
  --tw-text-opacity: 1;
  color: rgb(18 19 21 / var(--tw-text-opacity));
}

.text-\[\#225DE0\] {
  --tw-text-opacity: 1;
  color: rgb(34 93 224 / var(--tw-text-opacity));
}

.text-\[\#24262B\] {
  --tw-text-opacity: 1;
  color: rgb(36 38 43 / var(--tw-text-opacity));
}

.text-\[\#363840\] {
  --tw-text-opacity: 1;
  color: rgb(54 56 64 / var(--tw-text-opacity));
}

.text-\[\#383B46\] {
  --tw-text-opacity: 1;
  color: rgb(56 59 70 / var(--tw-text-opacity));
}

.text-\[\#3997C8\] {
  --tw-text-opacity: 1;
  color: rgb(57 151 200 / var(--tw-text-opacity));
}

.text-\[\#414244\] {
  --tw-text-opacity: 1;
  color: rgb(65 66 68 / var(--tw-text-opacity));
}

.text-\[\#424242\] {
  --tw-text-opacity: 1;
  color: rgb(66 66 66 / var(--tw-text-opacity));
}

.text-\[\#474B55\] {
  --tw-text-opacity: 1;
  color: rgb(71 75 85 / var(--tw-text-opacity));
}

.text-\[\#606576\] {
  --tw-text-opacity: 1;
  color: rgb(96 101 118 / var(--tw-text-opacity));
}

.text-\[\#6B7180\] {
  --tw-text-opacity: 1;
  color: rgb(107 113 128 / var(--tw-text-opacity));
}

.text-\[\#7D7E82\] {
  --tw-text-opacity: 1;
  color: rgb(125 126 130 / var(--tw-text-opacity));
}

.text-\[\#7F8493\] {
  --tw-text-opacity: 1;
  color: rgb(127 132 147 / var(--tw-text-opacity));
}

.text-\[\#9499A5\] {
  --tw-text-opacity: 1;
  color: rgb(148 153 165 / var(--tw-text-opacity));
}

.text-\[\#999EAF\] {
  --tw-text-opacity: 1;
  color: rgb(153 158 175 / var(--tw-text-opacity));
}

.text-\[\#AAAAAA\] {
  --tw-text-opacity: 1;
  color: rgb(170 170 170 / var(--tw-text-opacity));
}

.text-\[\#C7CAD1\] {
  --tw-text-opacity: 1;
  color: rgb(199 202 209 / var(--tw-text-opacity));
}

.text-\[\#FDAF2E\] {
  --tw-text-opacity: 1;
  color: rgb(253 175 46 / var(--tw-text-opacity));
}

.text-\[\#FF3B30\] {
  --tw-text-opacity: 1;
  color: rgb(255 59 48 / var(--tw-text-opacity));
}

.text-\[\#FF5F5F\] {
  --tw-text-opacity: 1;
  color: rgb(255 95 95 / var(--tw-text-opacity));
}

.text-\[\#FFF\] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-\[\#c3c3c3\] {
  --tw-text-opacity: 1;
  color: rgb(195 195 195 / var(--tw-text-opacity));
}

.text-\[\#fff\] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.text-\[red\] {
  --tw-text-opacity: 1;
  color: rgb(255 0 0 / var(--tw-text-opacity));
}

.text-gray-dark {
  --tw-text-opacity: 1;
  color: rgb(39 52 68 / var(--tw-text-opacity));
}

.text-gray-light {
  --tw-text-opacity: 1;
  color: rgb(211 220 230 / var(--tw-text-opacity));
}

.text-main-black {
  --tw-text-opacity: 1;
  color: rgb(65 66 68 / var(--tw-text-opacity));
}

.text-main-color {
  --tw-text-opacity: 1;
  color: rgb(57 151 200 / var(--tw-text-opacity));
}

.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}

.underline {
  text-decoration-line: underline;
}

.no-underline {
  text-decoration-line: none;
}

.opacity-0 {
  opacity: 0;
}

.\!shadow-none {
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  -webkit-box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
          box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}

.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
          box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.outline {
  outline-style: solid;
}

.\!outline-0 {
  outline-width: 0px !important;
}

.invert {
  --tw-invert: invert(100%);
  -webkit-filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
          filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.filter {
  -webkit-filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
          filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.transition {
  -webkit-transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, -webkit-box-shadow, -webkit-transform, -webkit-filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, -webkit-box-shadow, -webkit-transform, -webkit-filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-box-shadow, -webkit-transform, -webkit-filter, -webkit-backdrop-filter;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transition-duration: 150ms;
          transition-duration: 150ms;
}

.transition-transform {
  -webkit-transition-property: -webkit-transform;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-transition-duration: 150ms;
          transition-duration: 150ms;
}

.duration-300 {
  -webkit-transition-duration: 300ms;
          transition-duration: 300ms;
}

.ease-in-out {
  -webkit-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
          transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.after\:ml-0\.5::after {
  content: var(--tw-content);
  margin-left: 0.125rem;
}

.after\:text-\[\#ff4d4f\]::after {
  content: var(--tw-content);
  --tw-text-opacity: 1;
  color: rgb(255 77 79 / var(--tw-text-opacity));
}

.after\:content-\[\'\*\'\]::after {
  --tw-content: '*';
  content: var(--tw-content);
}

.hover\:\!border-none:hover {
  border-style: none !important;
}

.hover\:\!border-\[\#225DE0\]:hover {
  --tw-border-opacity: 1 !important;
  border-color: rgb(34 93 224 / var(--tw-border-opacity)) !important;
}

.hover\:\!border-\[\#FDAF2E\]:hover {
  --tw-border-opacity: 1 !important;
  border-color: rgb(253 175 46 / var(--tw-border-opacity)) !important;
}

.hover\:\!border-\[\#FF3B30\]:hover {
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 59 48 / var(--tw-border-opacity)) !important;
}

.hover\:\!border-\[\#ff4d4f\]:hover {
  --tw-border-opacity: 1 !important;
  border-color: rgb(255 77 79 / var(--tw-border-opacity)) !important;
}

.hover\:border-\[\#0692BC\]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(6 146 188 / var(--tw-border-opacity));
}

.hover\:border-\[\#225DE0\]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(34 93 224 / var(--tw-border-opacity));
}

.hover\:border-\[\#3997C8\]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(57 151 200 / var(--tw-border-opacity));
}

.hover\:border-\[\#FDAF2E\]:hover {
  --tw-border-opacity: 1;
  border-color: rgb(253 175 46 / var(--tw-border-opacity));
}

.hover\:border-main-color:hover {
  --tw-border-opacity: 1;
  border-color: rgb(57 151 200 / var(--tw-border-opacity));
}

.hover\:\!bg-\[\#225DE0\]:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(34 93 224 / var(--tw-bg-opacity)) !important;
}

.hover\:\!bg-\[\#3997C8\]:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(57 151 200 / var(--tw-bg-opacity)) !important;
}

.hover\:\!bg-\[\#BCE4E3\]:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(188 228 227 / var(--tw-bg-opacity)) !important;
}

.hover\:\!bg-\[\#CDCED4CC\]:hover {
  background-color: #CDCED4CC !important;
}

.hover\:\!bg-\[\#FDAF2E\]:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(253 175 46 / var(--tw-bg-opacity)) !important;
}

.hover\:\!bg-\[\#FF7648\]:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 118 72 / var(--tw-bg-opacity)) !important;
}

.hover\:\!bg-\[gray\]:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(128 128 128 / var(--tw-bg-opacity)) !important;
}

.hover\:\!bg-\[transparent\]:hover {
  background-color: transparent !important;
}

.hover\:\!bg-main-color:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(57 151 200 / var(--tw-bg-opacity)) !important;
}

.hover\:\!bg-white:hover {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity)) !important;
}

.hover\:bg-\[\#3997C8a9\]:hover {
  background-color: #3997C8a9;
}

.hover\:bg-\[\#BCE4E3\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(188 228 227 / var(--tw-bg-opacity));
}

.hover\:bg-\[\#CDCED4CC\]:hover {
  background-color: #CDCED4CC;
}

.hover\:bg-\[\#E04C58\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(224 76 88 / var(--tw-bg-opacity));
}

.hover\:\!text-\[\#fff\]:hover {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important;
}

.hover\:opacity-40:hover {
  opacity: 0.4;
}

.hover\:opacity-50:hover {
  opacity: 0.5;
}

.hover\:opacity-70:hover {
  opacity: 0.7;
}

.hover\:opacity-85:hover {
  opacity: 0.85;
}

.hover\:opacity-90:hover {
  opacity: 0.9;
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -2px var(--tw-shadow-color);
  -webkit-box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
          box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

@media (min-width: 768px) {
  .md\:col-span-2 {
    grid-column: span 2 / span 2;
  }

  .md\:grid-cols-8 {
    grid-template-columns: repeat(8, minmax(0, 1fr));
  }
}

@media (min-width: 1024px) {
  .lg\:min-w-\[900px\] {
    min-width: 900px;
  }

  .lg\:grid-cols-11 {
    grid-template-columns: repeat(11, minmax(0, 1fr));
  }

  .lg\:flex-row {
    -webkit-box-orient: horizontal;
    -webkit-box-direction: normal;
        -ms-flex-direction: row;
            flex-direction: row;
  }
}

.\[\&_\.ItineraryItemList\]\:\!w-\[248px\] .ItineraryItemList {
  width: 248px !important;
}

.\[\&_\.ItineraryItemList\]\:\!w-full .ItineraryItemList {
  width: 100% !important;
}

.\[\&_\.ant-badge\]\:\!text-\[12px\] .ant-badge {
  font-size: 12px !important;
}

.\[\&_\.ant-badge\]\:\!text-\[14px\] .ant-badge {
  font-size: 14px !important;
}

.\[\&_\.ant-form-item-explain-error\]\:\!max-w-\[384px\] .ant-form-item-explain-error {
  max-width: 384px !important;
}

.\[\&_\.ant-form-item-explain-error\]\:break-words .ant-form-item-explain-error {
  overflow-wrap: break-word;
}

.\[\&_\.ant-form-item-explain-error\]\:\!text-\[10px\] .ant-form-item-explain-error {
  font-size: 10px !important;
}

.\[\&_\.ant-form-item-label_\>_label\.ant-form-item-required\:not\(\.ant-form-item-required-mark-optional\)\:\:before\]\:hidden .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before {
  display: none;
}

.\[\&_\.ant-input-affix-wrapper\]\:h-10 .ant-input-affix-wrapper {
  height: 2.5rem;
}

.\[\&_\.ant-input-affix-wrapper\]\:\!rounded .ant-input-affix-wrapper {
  border-radius: 0.25rem !important;
}

.\[\&_\.ant-input-affix-wrapper\]\:border-\[\#DCDEE3\] .ant-input-affix-wrapper {
  --tw-border-opacity: 1;
  border-color: rgb(220 222 227 / var(--tw-border-opacity));
}

.\[\&_\.ant-input-group-addon\]\:hidden .ant-input-group-addon {
  display: none;
}

.\[\&_\.ant-input-search-button\]\:h-10 .ant-input-search-button {
  height: 2.5rem;
}

.\[\&_\.ant-input\]\:\!rounded-\[4px\] .ant-input {
  border-radius: 4px !important;
}

.\[\&_\.ant-input\]\:\!rounded-lg .ant-input {
  border-radius: 0.5rem !important;
}

.\[\&_\.ant-modal-body\]\:\!h-full .ant-modal-body {
  height: 100% !important;
}

.\[\&_\.ant-modal-body\]\:\!p-0 .ant-modal-body {
  padding: 0px !important;
}

.\[\&_\.ant-modal-body\]\:\!px-2 .ant-modal-body {
  padding-left: 0.5rem !important;
  padding-right: 0.5rem !important;
}

.\[\&_\.ant-modal-body\]\:\!px-\[22px\] .ant-modal-body {
  padding-left: 22px !important;
  padding-right: 22px !important;
}

.\[\&_\.ant-modal-content\]\:\!h-\[90vh\] .ant-modal-content {
  height: 90vh !important;
}

.\[\&_\.ant-modal-content\]\:\!max-h-\[90vh\] .ant-modal-content {
  max-height: 90vh !important;
}

.\[\&_\.ant-modal-content\]\:max-h-\[90vh\] .ant-modal-content {
  max-height: 90vh;
}

.\[\&_\.ant-modal-content\]\:overflow-y-auto .ant-modal-content {
  overflow-y: auto;
}

.\[\&_\.ant-modal-content\]\:\!rounded-xl .ant-modal-content {
  border-radius: 0.75rem !important;
}

.\[\&_\.ant-pagination-next\]\:hidden .ant-pagination-next {
  display: none;
}

.\[\&_\.ant-pagination-prev\]\:hidden .ant-pagination-prev {
  display: none;
}

.\[\&_\.ant-pagination-total-text\]\:mr-6 .ant-pagination-total-text {
  margin-right: 1.5rem;
}

.\[\&_\.ant-picker-range\]\:h-\[40px\] .ant-picker-range {
  height: 40px;
}

.\[\&_\.ant-popover-inner-content\]\:\!w-full .ant-popover-inner-content {
  width: 100% !important;
}

.\[\&_\.ant-popover-inner-content\]\:\!p-2 .ant-popover-inner-content {
  padding: 0.5rem !important;
}

.\[\&_\.ant-popover-inner\]\:rounded-md .ant-popover-inner {
  border-radius: 0.375rem;
}

.\[\&_\.ant-scroll-number\]\:\!right-\[-22px\] .ant-scroll-number {
  right: -22px !important;
}

.\[\&_\.ant-scroll-number\]\:\!top-\[8px\] .ant-scroll-number {
  top: 8px !important;
}

.\[\&_\.ant-select-arrow\]\:mt-\[-8px\] .ant-select-arrow {
  margin-top: -8px;
}

.\[\&_\.ant-select-selection-item\]\:inline-block .ant-select-selection-item {
  display: inline-block;
}

.\[\&_\.ant-select-selection-item\]\:\!flex .ant-select-selection-item {
  display: -webkit-box !important;
  display: -ms-flexbox !important;
  display: flex !important;
}

.\[\&_\.ant-select-selection-item\]\:flex .ant-select-selection-item {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.\[\&_\.ant-select-selection-item\]\:\!items-center .ant-select-selection-item {
  -webkit-box-align: center !important;
      -ms-flex-align: center !important;
          align-items: center !important;
}

.\[\&_\.ant-select-selection-item\]\:items-center .ant-select-selection-item {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.\[\&_\.ant-select-selection-item\]\:truncate .ant-select-selection-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.\[\&_\.ant-select-selection-item\]\:leading-\[38px\] .ant-select-selection-item {
  line-height: 38px;
}

.\[\&_\.ant-select-selection-overflow\]\:\!flex-nowrap .ant-select-selection-overflow {
  -ms-flex-wrap: nowrap !important;
      flex-wrap: nowrap !important;
}

.\[\&_\.ant-select-selection-overflow\]\:\!overflow-hidden .ant-select-selection-overflow {
  overflow: hidden !important;
}

.\[\&_\.ant-select-selection-placeholder\]\:flex .ant-select-selection-placeholder {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.\[\&_\.ant-select-selection-placeholder\]\:items-center .ant-select-selection-placeholder {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.\[\&_\.ant-select-selection-search-input\]\:\!h-9 .ant-select-selection-search-input {
  height: 2.25rem !important;
}

.\[\&_\.ant-select-selection-search-input\]\:\!h-\[40px\] .ant-select-selection-search-input {
  height: 40px !important;
}

.\[\&_\.ant-select-selection-search-input\]\:\!min-h-\[40px\] .ant-select-selection-search-input {
  min-height: 40px !important;
}

.\[\&_\.ant-select-selector\]\:flex .ant-select-selector {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.\[\&_\.ant-select-selector\]\:\!h-10 .ant-select-selector {
  height: 2.5rem !important;
}

.\[\&_\.ant-select-selector\]\:\!h-9 .ant-select-selector {
  height: 2.25rem !important;
}

.\[\&_\.ant-select-selector\]\:\!h-fit .ant-select-selector {
  height: -webkit-fit-content !important;
  height: -moz-fit-content !important;
  height: fit-content !important;
}

.\[\&_\.ant-select-selector\]\:\!min-h-10 .ant-select-selector {
  min-height: 2.5rem !important;
}

.\[\&_\.ant-select-selector\]\:\!w-\[140px\] .ant-select-selector {
  width: 140px !important;
}

.\[\&_\.ant-select-selector\]\:\!items-center .ant-select-selector {
  -webkit-box-align: center !important;
      -ms-flex-align: center !important;
          align-items: center !important;
}

.\[\&_\.ant-select-selector\]\:items-center .ant-select-selector {
  -webkit-box-align: center;
      -ms-flex-align: center;
          align-items: center;
}

.\[\&_\.ant-select-selector\]\:\!rounded-\[6px\] .ant-select-selector {
  border-radius: 6px !important;
}

.\[\&_\.ant-select-selector\]\:\!rounded-lg .ant-select-selector {
  border-radius: 0.5rem !important;
}

.\[\&_\.ant-select-selector\]\:\!bg-\[\#DCDEE3\] .ant-select-selector {
  --tw-bg-opacity: 1 !important;
  background-color: rgb(220 222 227 / var(--tw-bg-opacity)) !important;
}

.\[\&_\.ant-skeleton-image\]\:\!h-\[60px\] .ant-skeleton-image {
  height: 60px !important;
}

.\[\&_\.ant-table_tfoot_\>_tr_\>_td\]\:border-0 .ant-table tfoot > tr > td {
  border-width: 0px;
}

.\[\&_\.ant-tabs-content-holder\]\:overflow-y-auto .ant-tabs-content-holder {
  overflow-y: auto;
}

.\[\&_\.ant-tabs-content-top\]\:h-full .ant-tabs-content-top {
  height: 100%;
}

.\[\&_\.ant-tabs-content\]\:\!h-full .ant-tabs-content {
  height: 100% !important;
}

.\[\&_\.ant-tabs-nav-operations\]\:\!hidden .ant-tabs-nav-operations {
  display: none !important;
}

.\[\&_\.ant-tabs-nav\]\:before\:\!border-none .ant-tabs-nav::before {
  content: var(--tw-content);
  border-style: none !important;
}

.\[\&_\.ant-tabs-tab-active\>p\]\:\!text-\[14px\] .ant-tabs-tab-active>p {
  font-size: 14px !important;
}

.\[\&_\.ant-tabs-tab-active\>p\]\:\!text-\[16px\] .ant-tabs-tab-active>p {
  font-size: 16px !important;
}

.\[\&_\.ant-tabs-tab-active_\.ant-badge\]\:\!text-\[14px\] .ant-tabs-tab-active .ant-badge {
  font-size: 14px !important;
}

.\[\&_\.ant-tabs-tab-active_\.ant-badge\]\:\!text-\[16px\] .ant-tabs-tab-active .ant-badge {
  font-size: 16px !important;
}

.\[\&_\.ant-tabs-tab-active_\.ant-badge\]\:\!text-\[\#000000\] .ant-tabs-tab-active .ant-badge {
  --tw-text-opacity: 1 !important;
  color: rgb(0 0 0 / var(--tw-text-opacity)) !important;
}

.\[\&_\.ant-tabs-tab\]\:\!text-\[14px\] .ant-tabs-tab {
  font-size: 14px !important;
}

.\[\&_\.ant-tabs-tab\]\:\!text-\[16px\] .ant-tabs-tab {
  font-size: 16px !important;
}

.\[\&_\.ant-tabs-tab_\.ant-badge\]\:\!text-\[16px\] .ant-tabs-tab .ant-badge {
  font-size: 16px !important;
}

.\[\&_\.ant-tabs-tab_\.ant-badge\]\:\!text-\[14px\] .ant-tabs-tab .ant-badge {
  font-size: 14px !important;
}

.\[\&_\.ant-tabs-tab_\.ant-badge\]\:\!text-\[12px\] .ant-tabs-tab .ant-badge {
  font-size: 12px !important;
}

.\[\&_\.ant-tabs-tab_\.ant-badge\]\:font-medium .ant-tabs-tab .ant-badge {
  font-weight: 500;
}

.\[\&_\.ant-tabs-tab_\.ant-badge\]\:text-\[\#7D7E82\] .ant-tabs-tab .ant-badge {
  --tw-text-opacity: 1;
  color: rgb(125 126 130 / var(--tw-text-opacity));
}

.\[\&_\.ant-tabs-tab_\.ant-tabs-tab-active\]\:\!text-\[14px\] .ant-tabs-tab .ant-tabs-tab-active {
  font-size: 14px !important;
}

.\[\&_\.ant-tabs-tab_\.ant-tabs-tab-active\]\:\!text-\[16px\] .ant-tabs-tab .ant-tabs-tab-active {
  font-size: 16px !important;
}

.\[\&_\.ant-tabs-tab_\.ant-tabs-tab-active\]\:\!text-\[red\] .ant-tabs-tab .ant-tabs-tab-active {
  --tw-text-opacity: 1 !important;
  color: rgb(255 0 0 / var(--tw-text-opacity)) !important;
}

.\[\&_\.ant-tabs-tab_\.ant-tabs-tab-active\]\:\!text-\[green\] .ant-tabs-tab .ant-tabs-tab-active {
  --tw-text-opacity: 1 !important;
  color: rgb(0 128 0 / var(--tw-text-opacity)) !important;
}

.\[\&_\.ant-tabs-tabpane-active\]\:h-full .ant-tabs-tabpane-active {
  height: 100%;
}

.\[\&_\.ant-tabs-tabpane-active\]\:overflow-y-auto .ant-tabs-tabpane-active {
  overflow-y: auto;
}

.\[\&_\.ant-tabs-tabpane\]\:\!h-full .ant-tabs-tabpane {
  height: 100% !important;
}

.\[\&_\.ant-tabs\]\:\!h-\[90\%\] .ant-tabs {
  height: 90% !important;
}

.\[\&_\.ant-upload-list-picture-card-container\]\:\!h-\[140px\] .ant-upload-list-picture-card-container {
  height: 140px !important;
}

.\[\&_\.ant-upload-list-picture-card-container\]\:\!w-\[140px\] .ant-upload-list-picture-card-container {
  width: 140px !important;
}

.\[\&_\.ant-upload-list-picture-card\]\:\!ml-4 .ant-upload-list-picture-card {
  margin-left: 1rem !important;
}

.\[\&_\.ant-upload-picture-card-wrapper\]\:w-0 .ant-upload-picture-card-wrapper {
  width: 0px;
}

.\[\&_\.ant-upload-select-picture-card\]\:\!mt-\[50px\] .ant-upload-select-picture-card {
  margin-top: 50px !important;
}

.\[\&_\.ant-upload-select-picture-card\]\:\!h-\[120px\] .ant-upload-select-picture-card {
  height: 120px !important;
}

.\[\&_\.ant-upload-select-picture-card\]\:\!w-\[160px\] .ant-upload-select-picture-card {
  width: 160px !important;
}

.\[\&_\.ant-upload-select-picture-card\]\:rounded-lg .ant-upload-select-picture-card {
  border-radius: 0.5rem;
}

.\[\&_\.ant-upload\]\:\!h-\[40px\] .ant-upload {
  height: 40px !important;
}

.\[\&_\.ant-upload\]\:\!w-\[140px\] .ant-upload {
  width: 140px !important;
}

.\[\&_\.modal_content\]\:\!h-full .modal content {
  height: 100% !important;
}

.\[\&_\.ql-container\]\:\!h-\[280px\] .ql-container {
  height: 280px !important;
}
