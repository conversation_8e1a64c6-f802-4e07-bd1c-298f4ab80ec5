type ErrorMessageFunction = (...args: any[]) => string;
export type FieldError = Record<
  string,
  {
    message: string;
    error_code: number;
  }
>;

export interface ErrorResponse {
  data: ErrorData;
}

export interface ErrorData {
  errors: {
    line: number[];
    fields: FieldError[];
  };
}

export const RULE_BY_FIELD = {
  min: {
    科目コード: 6,
    課税区分コード: 6,
    消費税区分コード: 6,
    集計科目コード: 6,
    勘定科目コード: 6,
    金種コード: 6,
    種別コード: 6,
    取引先コード: 6,
  },
  max: {
    科目コード: 10,
    課税区分コード: 10,
    消費税区分コード: 10,
    集計科目コード: 10,
    勘定科目コード: 10,
    金種コード: 10,
    種別コード: 10,
    取引先コード: 10,
  },
  in: {
    ステータス: ['有効', '無効'],
  },
};

export const ERROR_CODE: Record<number, string | ErrorMessageFunction> = {
  1001: (field: string) => `最小値は ${RULE_BY_FIELD.min?.[field]} です`,
  1002: (field: string) => `最大値は ${RULE_BY_FIELD.max?.[field]} を超えました`,
  1003: 'このフィールドは必須です',
  1004: '無効な値（データベースに存在しません）',
  1005: '値は一意でなければなりません',
  1006: `指定された日付より後でなければなりません`,
  1007: `指定された日付より前でなければなりません`,
  1008: (field: string) =>
    `値は ${RULE_BY_FIELD.min?.[field]} から ${RULE_BY_FIELD.max?.[field]} の間でなければなりません`,
  1009: '値は指定されたリストに含まれている必要があります',
  1010: '値は文字列でなければなりません',
  1011: '値は数値でなければなりません',
  1012: '値は日付でなければなりません',
  1013: '値は有効なメールアドレスでなければなりません',
  1014: '無効なファイルタイプ',
};

export const getFieldError = (fields: FieldError[]) => {
  const errors = Object.keys(fields[0])
    .map((key) => {
      const value = fields[0][key];
      return getErrorMessage(key, value.error_code);
    })
    .join('\n');
  return errors;
};

// Function to get error message
export function getErrorMessage(field: string, code: number): string {
  const errorMessage = ERROR_CODE[code];
  if (typeof errorMessage === 'function') {
    return field + errorMessage(field);
  }
  return field + errorMessage;
}

export const getFieldCsvErrorMessage = (fields: FieldError[]) => {
  const errors = Object.values(fields[0])
    .map((value) => {
      return value?.message;
    })
    .join('\n');

  return errors;
};
