export const taxCodeNotApplicable = ['05', '06', '07'];

export const optionMealOnHotel = [
  { value: 0, label: 'Hotel' },
  { value: 1, label: '〇' },
  { value: 2, label: '×' },
];
export const optionPaymentOnHotel = [
  { value: 0, label: 'None' },
  { value: 1, label: 'Cash' },
  { value: 2, label: 'Ticket' },
  { value: 3, label: 'Coupon' },
];
export const amountOptions = [
  { value: 1, label: '金額（税抜)' },
  { value: 0, label: '金額（税込）' },
];

export const paymentStatusTravel = [
  { value: 0, label: '未発行' },
  { value: 1, label: '仮請求書発行済み' },
  { value: 2, label: '本請求書発行済み' },
  { value: 3, label: '一部入金済み' },
  { value: 4, label: '入金済み' },
];

export const typeSGG = [
  { value: 1, label: 'Goal', textColor: '#225DE0', backgroundColor: '#EBF0FB' },
  { value: 2, label: 'Information', textColor: '#0B9F41', backgroundColor: '#E1FAEA' },
  { value: 3, label: 'Task', textColor: '#FDAF2E', backgroundColor: '#FFF1DA' },
];

export const defaultDepositType = 5;
export const defaultPaymentType = 4;
export const optionSubTotalCategoryPayment = [
  { value: 'category_payment_slip', label: '支払伝票別計' },
  { value: 'category_payment_date', label: '支払日別計' },
  { value: 'category_payment_recipient', label: '支払先別計' },
  { value: 'category_monthly', label: '月計' },
  { value: 'category_payment_type', label: '支払種別計' },
];
export const optionSubTotalCategoryDeposit = [
  { value: 'category_deposit_slip', label: '入金伝票別計' },
  { value: 'category_deposit_date', label: '入金日別計' },
  { value: 'category_deposit_recipient', label: '得意先別計' },
  { value: 'category_monthly', label: '月計' },
  { value: 'category_deposit_type', label: '入金種別計' },
];
export const optionReportTypeDate = [
  { value: 'voucher_posting_date', label: '計上日' },
  { value: 'departure_date', label: '出発日' },
  { value: 'return_date', label: '帰着日' },
];
export const optionReportDepositTypeDate = [
  { value: 'payment_date', label: '入金日' },
  { value: 'voucher_posting_date', label: '計上日' },
  { value: 'departure_date', label: '出発日' },
  { value: 'return_date', label: '帰着日' },
];
export const optionReportPaymentTypeDate = [
  { value: 'payment_date', label: '支払日' },
  { value: 'voucher_posting_date', label: '計上日' },
  { value: 'departure_date', label: '出発日' },
  { value: 'return_date', label: '帰着日' },
];
export const optionReportDepositType = [
  { value: 'report_deposit_list', label: '入金一覧表' },
  { value: 'report_deposit_list_unpaid_exp', label: '未入金一覧表（EXP確認用）' },
  {
    value: 'report_deposit_list_unpaid_collection_plan',
    label: '未入金一覧表（資金繰り用）',
  },
];
export const optionReportPaymentType = [
  { value: 'report_payment_list', label: '支払金一覧表' },
  {
    value: 'report_payment_list_unpaid',
    label: '未支払金一覧表（EXP確認用）',
  },
];
export const optionReportSaleType = [
  { value: 'report_by_travel', label: '旅行ID別 売上明細表', key: 'trip_id' },
  {
    value: 'report_by_business_partner',
    label: '得意先ID別 収支一覧表',
    key: 'customer_id',
  },
  { value: 'report_by_subject', label: '集計科目別 収支一覧表', key: 'summary_item' },
];
export const optionReportPurchaseType = [
  {
    value: 'report_business_partner_to_travel',
    label: '仕入一覧表（仕入先別）',
  },
  {
    value: 'report_travel_to_business_partner',
    label: '仕入一覧表（旅行ＩＤ別）',
  },
  {
    value: 'report_by_ranking_amount',
    label: '仕入実績順位表',
  },
];
export const optionMonthlyReportType = [
  { value: 'report_by_month', label: '月次売上 収支一覧表（旅行種別別）' },
  {
    value: 'report_by_year',
    label: '月次売上 収支一覧表（前年対比）',
  },
];
export const optionAnnualSaleReportType = [
  { value: 'report_by_year', label: '年次売上 収支一覧表（旅行種別別）' },
  {
    value: 'report_with_past_year',
    label: '年次売上 収支一覧表（前年対比）',
  },
];
export const optionRole = [
  { value: 1, label: '運営側', key: 'super_admin' },
  { value: 2, label: '管理者', key: 'admin' },
  { value: 3, label: '経理担当者', key: 'accounting' },
  { value: 4, label: '会計担当者', key: 'accountant' },
  { value: 5, label: '一般社員', key: 'staff' },
];

export const optionTaxInclusionType = [
  { value: 1, label: '内税' },
  { value: 2, label: '外税' },
  { value: 0, label: '対象外' },
];
export const optionQualifiedBusinessCategory = [
  { value: 1, label: 'はい' },
  { value: 0, label: 'いいえ' },
];

export const invoiceIssueOptions = [
  { value: 2, label: '済' },
  { value: 1, label: '一部' },
  { value: 0, label: '未' },
];

export const targetOptions = [
  { value: 1, label: '売上' },
  { value: 2, label: '仕入' },
  { value: 3, label: '入金' },
  { value: 4, label: '支払' },
];

export const countryDefault = [
  {
    id: 1,
    country_name_en: 'Republic of Iceland',
  },
  {
    id: 2,
    country_name_en: 'Ireland',
  },
  {
    id: 3,
    country_name_en: 'Republic of Azerbaijan',
  },
  {
    id: 4,
    country_name_en: 'Islamic Republic of Afghanistan',
  },
  {
    id: 5,
    country_name_en: 'United States of America',
  },
  {
    id: 6,
    country_name_en: 'United Arab Emirates',
  },
  {
    id: 7,
    country_name_en: "People's Democratic Republic of Algeria",
  },
  {
    id: 8,
    country_name_en: 'Argentine Republic',
  },
  {
    id: 9,
    country_name_en: 'Republic of Albania',
  },
  {
    id: 10,
    country_name_en: 'Republic of Armenia',
  },
  {
    id: 11,
    country_name_en: 'Republic of Angola',
  },
  {
    id: 12,
    country_name_en: 'Antigua and Barbuda',
  },
  {
    id: 13,
    country_name_en: 'Principality of Andorra',
  },
  {
    id: 14,
    country_name_en: 'Republic of Yemen',
  },
  {
    id: 15,
    country_name_en: 'State of Israel',
  },
  {
    id: 16,
    country_name_en: 'Italian Republic',
  },
  {
    id: 17,
    country_name_en: 'Republic of Iraq',
  },
  {
    id: 18,
    country_name_en: 'Islamic Republic of Iran',
  },
  {
    id: 19,
    country_name_en: 'India',
  },
  {
    id: 20,
    country_name_en: 'Republic of Indonesia',
  },
  {
    id: 21,
    country_name_en: 'Republic of Uganda',
  },
  {
    id: 22,
    country_name_en: 'Ukraine',
  },
  {
    id: 23,
    country_name_en: 'Republic of Uzbekistan',
  },
  {
    id: 24,
    country_name_en: 'Oriental Republic of Uruguay',
  },
  {
    id: 25,
    country_name_en: 'United Kingdom of Great Britain and Northern Ireland',
  },
  {
    id: 26,
    country_name_en: 'Republic of Ecuador',
  },
  {
    id: 27,
    country_name_en: 'Arab Republic of Egypt',
  },
  {
    id: 28,
    country_name_en: 'Republic of Estonia',
  },
  {
    id: 29,
    country_name_en: 'Kingdom of Eswatini',
  },
  {
    id: 30,
    country_name_en: 'Federal Democratic Republic of Ethiopia',
  },
  {
    id: 31,
    country_name_en: 'State of Eritrea',
  },
  {
    id: 32,
    country_name_en: 'Republic of El Salvador',
  },
  {
    id: 33,
    country_name_en: 'Australia',
  },
  {
    id: 34,
    country_name_en: 'Republic of Austria',
  },
  {
    id: 35,
    country_name_en: 'Sultanate of Oman',
  },
  {
    id: 36,
    country_name_en: 'Kingdom of the Netherlands',
  },
  {
    id: 37,
    country_name_en: 'Republic of Ghana',
  },
  {
    id: 38,
    country_name_en: 'Republic of Cabo Verde',
  },
  {
    id: 39,
    country_name_en: 'Republic of Guyana',
  },
  {
    id: 40,
    country_name_en: 'Republic of Kazakhstan',
  },
  {
    id: 41,
    country_name_en: 'State of Qatar',
  },
  {
    id: 42,
    country_name_en: 'Canada',
  },
  {
    id: 43,
    country_name_en: 'Gabonese Republic',
  },
  {
    id: 44,
    country_name_en: 'Republic of Cameroon',
  },
  {
    id: 45,
    country_name_en: 'Republic of The Gambia',
  },
  {
    id: 46,
    country_name_en: 'Kingdom of Cambodia',
  },
  {
    id: 47,
    country_name_en: 'Republic of North Macedonia',
  },
  {
    id: 48,
    country_name_en: 'Republic of Guinea',
  },
  {
    id: 49,
    country_name_en: 'Republic of Guinea-Bissau',
  },
  {
    id: 50,
    country_name_en: 'Republic of Cyprus',
  },
  {
    id: 51,
    country_name_en: 'Republic of Cuba',
  },
  {
    id: 52,
    country_name_en: 'Hellenic Republic',
  },
  {
    id: 53,
    country_name_en: 'Republic of Kiribati',
  },
  {
    id: 54,
    country_name_en: 'Kyrgyz Republic',
  },
  {
    id: 55,
    country_name_en: 'Republic of Guatemala',
  },
  {
    id: 56,
    country_name_en: 'State of Kuwait',
  },
  {
    id: 57,
    country_name_en: 'Cook Islands',
  },
  {
    id: 58,
    country_name_en: 'Grenada',
  },
  {
    id: 59,
    country_name_en: 'Republic of Croatia',
  },
  {
    id: 60,
    country_name_en: 'Republic of Kenya',
  },
  {
    id: 61,
    country_name_en: "Republic of Cote d'Ivoire",
  },
  {
    id: 62,
    country_name_en: 'Republic of Costa Rica',
  },
  {
    id: 63,
    country_name_en: 'Union of Comoros',
  },
  {
    id: 64,
    country_name_en: 'Republic of Colombia',
  },
  {
    id: 65,
    country_name_en: 'Republic of Congo',
  },
  {
    id: 66,
    country_name_en: 'Democratic Republic of the Congo',
  },
  {
    id: 67,
    country_name_en: 'Kingdom of Saudi Arabia',
  },
  {
    id: 68,
    country_name_en: 'Independent State of Samoa',
  },
  {
    id: 69,
    country_name_en: 'Democratic Republic of Sao Tome and Principe',
  },
  {
    id: 70,
    country_name_en: 'Republic of Zambia',
  },
  {
    id: 71,
    country_name_en: 'Republic of San Marino',
  },
  {
    id: 72,
    country_name_en: 'Republic of Sierra Leone',
  },
  {
    id: 73,
    country_name_en: 'Republic of Djibouti',
  },
  {
    id: 74,
    country_name_en: 'Jamaica',
  },
  {
    id: 75,
    country_name_en: 'Georgia',
  },
  {
    id: 76,
    country_name_en: 'Syrian Arab Republic',
  },
  {
    id: 77,
    country_name_en: 'Republic of Singapore',
  },
  {
    id: 78,
    country_name_en: 'Republic of Zimbabwe',
  },
  {
    id: 79,
    country_name_en: 'Swiss Confederation',
  },
  {
    id: 80,
    country_name_en: 'Kingdom of Sweden',
  },
  {
    id: 81,
    country_name_en: 'The Republic of the Sudan',
  },
  {
    id: 82,
    country_name_en: 'Kingdom of Spain',
  },
  {
    id: 83,
    country_name_en: 'Republic of Suriname',
  },
  {
    id: 84,
    country_name_en: 'Democratic Socialist Republic of Sri Lanka',
  },
  {
    id: 85,
    country_name_en: 'Slovak Republic',
  },
  {
    id: 86,
    country_name_en: 'Republic of Slovenia',
  },
  {
    id: 87,
    country_name_en: 'Republic of Seychelles',
  },
  {
    id: 88,
    country_name_en: 'Republic of Equatorial Guinea',
  },
  {
    id: 89,
    country_name_en: 'Republic of Senegal',
  },
  {
    id: 90,
    country_name_en: 'Republic of Serbia',
  },
  {
    id: 91,
    country_name_en: 'Saint Christopher and Nevis',
  },
  {
    id: 92,
    country_name_en: 'Saint Vincent and the Grenadines',
  },
  {
    id: 93,
    country_name_en: 'Saint Lucia',
  },
  {
    id: 94,
    country_name_en: 'Federal Republic of Somalia',
  },
  {
    id: 95,
    country_name_en: 'Solomon Islands',
  },
  {
    id: 96,
    country_name_en: 'Kingdom of Thailand',
  },
  {
    id: 97,
    country_name_en: 'Republic of Korea',
  },
  {
    id: 98,
    country_name_en: 'Taiwan',
  },
  {
    id: 99,
    country_name_en: 'Republic of Tajikistan',
  },
  {
    id: 100,
    country_name_en: 'United Republic of Tanzania',
  },
  {
    id: 101,
    country_name_en: 'Czech Republic',
  },
  {
    id: 102,
    country_name_en: 'Republic of Chad',
  },
  {
    id: 103,
    country_name_en: 'Central African Republic',
  },
  {
    id: 104,
    country_name_en: "People's Republic of China",
  },
  {
    id: 105,
    country_name_en: 'Republic of Tunisia',
  },
  {
    id: 106,
    country_name_en: 'North Korea',
  },
  {
    id: 107,
    country_name_en: 'Republic of Chile',
  },
  {
    id: 108,
    country_name_en: 'Tuvalu',
  },
  {
    id: 109,
    country_name_en: 'Kingdom of Denmark',
  },
  {
    id: 110,
    country_name_en: 'Federal Republic of Germany',
  },
  {
    id: 111,
    country_name_en: 'Republic of Togo',
  },
  {
    id: 112,
    country_name_en: 'Dominican Republic',
  },
  {
    id: 113,
    country_name_en: 'Commonwealth of Dominica',
  },
  {
    id: 114,
    country_name_en: 'Republic of Trinidad and Tobago',
  },
  {
    id: 115,
    country_name_en: 'Turkmenistan',
  },
  {
    id: 116,
    country_name_en: 'Republic of Turkey',
  },
  {
    id: 117,
    country_name_en: 'Kingdom of Tonga',
  },
  {
    id: 118,
    country_name_en: 'Federal Republic of Nigeria',
  },
  {
    id: 119,
    country_name_en: 'Republic of Nauru',
  },
  {
    id: 120,
    country_name_en: 'Republic of Namibia',
  },
  {
    id: 121,
    country_name_en: 'Niue',
  },
  {
    id: 122,
    country_name_en: 'Republic of Nicaragua',
  },
  {
    id: 123,
    country_name_en: 'Republic of Niger',
  },
  {
    id: 124,
    country_name_en: 'New Zealand',
  },
  {
    id: 125,
    country_name_en: 'Federal Democratic Republic of Nepal',
  },
  {
    id: 126,
    country_name_en: 'Kingdom of Norway',
  },
  {
    id: 127,
    country_name_en: 'Kingdom of Bahrain',
  },
  {
    id: 128,
    country_name_en: 'Republic of Haiti',
  },
  {
    id: 129,
    country_name_en: 'Islamic Republic of Pakistan',
  },
  {
    id: 130,
    country_name_en: 'Vatican',
  },
  {
    id: 131,
    country_name_en: 'Republic of Panama',
  },
  {
    id: 132,
    country_name_en: 'Republic of Vanuatu',
  },
  {
    id: 133,
    country_name_en: 'Commonwealth of The Bahamas',
  },
  {
    id: 134,
    country_name_en: 'Independent State of Papua New Guinea',
  },
  {
    id: 135,
    country_name_en: 'Republic of Palau',
  },
  {
    id: 136,
    country_name_en: 'Republic of Paraguay',
  },
  {
    id: 137,
    country_name_en: 'Barbados',
  },
  {
    id: 138,
    country_name_en: 'Palestine',
  },
  {
    id: 139,
    country_name_en: 'Hungary',
  },
  {
    id: 140,
    country_name_en: "People's Republic of Bangladesh",
  },
  {
    id: 141,
    country_name_en: 'The Democratic Republic of Timor-Leste',
  },
  {
    id: 142,
    country_name_en: 'Republic of Fiji',
  },
  {
    id: 143,
    country_name_en: 'Republic of the Philippines',
  },
  {
    id: 144,
    country_name_en: 'Republic of Finland',
  },
  {
    id: 145,
    country_name_en: 'Kingdom of Bhutan',
  },
  {
    id: 146,
    country_name_en: 'Federative Republic of Brazil',
  },
  {
    id: 147,
    country_name_en: 'French Republic',
  },
  {
    id: 148,
    country_name_en: 'Republic of Bulgaria',
  },
  {
    id: 149,
    country_name_en: 'Burkina Faso',
  },
  {
    id: 150,
    country_name_en: 'Brunei Darussalam',
  },
  {
    id: 151,
    country_name_en: 'Republic of Burundi',
  },
  {
    id: 152,
    country_name_en: 'Socialist Republic of Viet Nam',
  },
  {
    id: 153,
    country_name_en: 'Republic of Benin',
  },
  {
    id: 154,
    country_name_en: 'Bolivarian Republic of Venezuela',
  },
  {
    id: 155,
    country_name_en: 'Republic of Belarus',
  },
  {
    id: 156,
    country_name_en: 'Belize',
  },
  {
    id: 157,
    country_name_en: 'Republic of Peru',
  },
  {
    id: 158,
    country_name_en: 'Kingdom of Belgium',
  },
  {
    id: 159,
    country_name_en: 'Republic of Poland',
  },
  {
    id: 160,
    country_name_en: 'Bosnia and Herzegovina',
  },
  {
    id: 161,
    country_name_en: 'Republic of Botswana',
  },
  {
    id: 162,
    country_name_en: 'Plurinational State of Bolivia',
  },
  {
    id: 163,
    country_name_en: 'Portuguese Republic',
  },
  {
    id: 164,
    country_name_en: 'Hong Kong',
  },
  {
    id: 165,
    country_name_en: 'Republic of Honduras',
  },
  {
    id: 166,
    country_name_en: 'Republic of the Marshall Islands',
  },
  {
    id: 167,
    country_name_en: 'Macau',
  },
  {
    id: 168,
    country_name_en: 'Republic of Madagascar',
  },
  {
    id: 169,
    country_name_en: 'Republic of Malawi',
  },
  {
    id: 170,
    country_name_en: 'Republic of Mali',
  },
  {
    id: 171,
    country_name_en: 'Republic of Malta',
  },
  {
    id: 172,
    country_name_en: 'Malaysia',
  },
  {
    id: 173,
    country_name_en: 'Federated States of Micronesia',
  },
  {
    id: 174,
    country_name_en: 'Republic of South Africa',
  },
  {
    id: 175,
    country_name_en: 'The Republic of South Sudan',
  },
  {
    id: 176,
    country_name_en: 'Republic of the Union of Myanmar',
  },
  {
    id: 177,
    country_name_en: 'United Mexican States',
  },
  {
    id: 178,
    country_name_en: 'Republic of Mauritius',
  },
  {
    id: 179,
    country_name_en: 'Islamic Republic of Mauritania',
  },
  {
    id: 180,
    country_name_en: 'Republic of Mozambique',
  },
  {
    id: 181,
    country_name_en: 'Principality of Monaco',
  },
  {
    id: 182,
    country_name_en: 'Republic of Maldives',
  },
  {
    id: 183,
    country_name_en: 'Republic of Moldova',
  },
  {
    id: 184,
    country_name_en: 'Kingdom of Morocco',
  },
  {
    id: 185,
    country_name_en: 'Mongolia',
  },
  {
    id: 186,
    country_name_en: 'Montenegro',
  },
  {
    id: 187,
    country_name_en: 'Jordan',
  },
  {
    id: 188,
    country_name_en: "Lao People's Democratic Republic",
  },
  {
    id: 189,
    country_name_en: 'Republic of Latvia',
  },
  {
    id: 190,
    country_name_en: 'Republic of Lithuania',
  },
  {
    id: 191,
    country_name_en: 'Libya',
  },
  {
    id: 192,
    country_name_en: 'Principality of Liechtenstein',
  },
  {
    id: 193,
    country_name_en: 'Republic of Liberia',
  },
  {
    id: 194,
    country_name_en: 'Romania',
  },
  {
    id: 195,
    country_name_en: 'Grand Duchy of Luxembourg',
  },
  {
    id: 196,
    country_name_en: 'Republic of Rwanda',
  },
  {
    id: 197,
    country_name_en: 'Kingdom of Lesotho',
  },
  {
    id: 198,
    country_name_en: 'Lebanese Republic',
  },
  {
    id: 199,
    country_name_en: 'Russian Federation',
  },
];

export const DepositSlipType = [
  { value: '01', label: '入金(黒伝票)' },
  { value: '02', label: '入金取消(赤伝票)' },
  { value: '03', label: '入金訂正(黒伝票)' },
];

export const ReservationMethod = [
  {
    label: '電話',
    value: 'phone',
  },
  {
    label: 'メール',
    value: 'email',
  },
  {
    label: 'FAX',
    value: 'fax',
  },
  {
    label: 'その他',
    value: 'others',
  },
];

export const StatusArrangement = [
  {
    label: '未手配',
    value: 0,
  },
  {
    label: 'リクエスト',
    value: 1,
  },
  {
    label: '仮予約',
    value: 2,
  },
  {
    label: '予約済 ',
    value: 3,
  },
  {
    label: 'キャンセル',
    value: 4,
  },
];
