import { message } from 'antd';
import type { Rule } from 'antd/es/form';

type RuleFields =
  | 'requiredInput'
  | 'requiredSelect'
  | 'requiredSelectCity'
  | 'requiredInputUrl'
  | 'requiredEmail'
  | 'isEmail'
  | 'isPhoneNumber'
  | 'correctPasswordFormat'
  | 'isFax'
  //   | 'requiredPassword'
  | 'requiredInputArea'
  | 'isFee'
  //   | 'notRequiredPassword';
  | 'isRequired'
  | 'validateInputLength'
  | 'integerOnly'
  | 'isJapanName'
  | 'isEnglishName'
  | 'isNumber';

export const rules: Record<RuleFields, Rule[]> = {
  validateInputLength: [
    {
      validator: (n: number) => ({
        validator(_, value) {
          // return new Promise((resolve, reject) => {
          if (value && value.length > n) {
            return Promise.reject(`${n}文字を超えないでくだ`);
          } else if (!value) {
            return Promise.reject(`※必須項目が未入力です。`);
          } else if (value?.trim() === '') {
            return Promise.reject(`※空白を入力できません。`);
          } else {
            return Promise.resolve();
          }
          // });
        },
      }),
    },
  ],

  integerOnly: [
    () => ({
      validator: async (_, value: string) => {
        const reg = /^[0-9]*$/;
        if (value && !reg.test(value)) {
          return Promise.reject('整数のみを入力してください');
        } else if (!value) {
          return Promise.reject(`※必須項目が未入力です。`);
        } else {
          return Promise.resolve();
        }
      },
    }),
  ],

  isRequired: [
    {
      required: true,
      message: '※必須項目が未入力です。',
    },
  ],
  requiredInput: [
    {
      required: true,
      message: '※必須項目が未入力です。',
    },
    {
      whitespace: true,
      message: `※空白を入力できません。`,
    },
    // () => ({
    //   validator: async (_, value: string) => {
    //     if (value?.trim() === '') {
    //       return Promise.reject('Không được nhập khoảng trắng');
    //     }
    //     return Promise.resolve();
    //   },
    // }),
  ],
  requiredSelect: [
    {
      required: true,
      message: '※必須項目が未選択です。',
    },
  ],
  requiredSelectCity: [
    () => ({
      validator(_, value) {
        if (!value?.prefectures || !value?.municipalities) {
          return Promise.reject('必須項目が未選択です。');
        }
        return Promise.resolve();
      },
    }),
  ],
  requiredInputUrl: [{ type: 'url', message: '※正確なリンクを入力してください。' }],
  requiredEmail: [
    {
      required: true,
      message: '※必須項目が未入力です。',
    },
  ],
  isEmail: [
    {
      validator: (_, value) => {
        if (/\s/.test(value)) {
          return Promise.reject('※空白を入力できません。');
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (value && !emailRegex.test(value)) {
          return Promise.reject('入力したメールアドレスのフォーマットが正しくありません');
        }
        return Promise.resolve();
      },
    },
  ],
  isPhoneNumber: [
    () => ({
      validator(_, value) {
        const phoneNumberRegex = /^(?!-)(\d|-){10,15}(?<!-)$/;
        if (value && !phoneNumberRegex.test(value)) {
          return Promise.reject(new Error('無効な電話番号'));
        }

        return Promise.resolve();
      },
    }),
  ],

  isFax: [
    () => ({
      validator(_, value) {
        const faxRegex = /^(?!-)(\d|-){10,15}(?<!-)$/;
        if (value && !faxRegex.test(value)) {
          return Promise.reject(new Error('無効なFAX'));
        }
        return Promise.resolve();
      },
    }),
  ],

  requiredInputArea: [
    {
      required: true,
      message: '※必須項目が未入力です。',
    },
    {
      whitespace: true,
      message: `空白を入力できません。`,
    },
    {
      max: 750,
      message: '英数字で最大750文字までご記入ください。',
    },
  ],

  correctPasswordFormat: [
    () => ({
      validator(_, value) {
        const pwRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{8,}$/;
        if (value && !pwRegex.test(value)) {
          return Promise.reject(
            new Error(
              '半角英小文字大文字数字をそれぞれ1種類以上含む8文字以上でパスワードを設定ください。',
            ),
          );
        }
        return Promise.resolve();
      },
    }),
  ],
  isFee: [
    () => ({
      validator(_, value) {
        const faxRegex = /^\d{1,10}$/;
        if (value && !faxRegex.test(value)) {
          return Promise.reject(new Error('最大 10 桁の整数を入力する必要があります'));
        }
        return Promise.resolve();
      },
    }),
  ],

  isNumber: [
    {
      pattern: /^[1-9][0-9]*$/,
      message: '整数でなければなりません',
    },
  ],

  isEnglishName: [
    {
      //  /^[a-zA-Z0-9\s]+$/
      pattern: /^[a-zA-Z0-9\s!@#$%^&*()_+{}\[\]:;"'<>,.?\/\\|-]+$/,
      message: '英字のみを入力してください。',
    },
  ],
  isJapanName: [
    { required: true, message: '※必須項目が未入力です。' },
    {
      // /^[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF\s]+$/
      pattern: /^[\u3040-\u309F\u30A0-\u30FF\u4E00-\u9FAF0-9\s!@#$%^&*()_+{}\[\]:;"'<>,.?\/\\|-]+$/,
      message: '日本語のみを入力してください。',
    },
  ],
};
