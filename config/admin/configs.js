import defaultSettings from '../defaultSettings';
import proxy from '../proxy';

const { REACT_APP_ENV } = process.env;
const {
  API_URL,
  FILE_URL,
  API_CHATWOOT_SOCKET,
  NODE_ENV,
  EMAIL_SUPER_ADMIN,
  GG_MAP_KEY,
  IS_SUPER_ADMIN_TEMPLATE,
  API_CHATWOOT_URL,
  API_ACCESS_TOKEN_CHATWOOT,
} = process.env;
const { SERVICE_NAME, APM_SERVER_URL, ENVIRONMENT, APM_API_BASE_URL, MUL_PAY_SCRIPT_URL } =
  process.env;

export const configs = {
  hash: true,
  fastRefresh: {
    hash: true,
  },
  antd: {},
  dva: {
    immer: true,
  },
  history: {
    type: 'browser',
  },
  locale: {
    // default zh-CN\

    default: 'ja-JP',
    // default: 'en-US',
    antd: true,
    // default true, when it is true, will use `navigator.language` overwrite default
    baseNavigator: false,
  },
  dynamicImport: {
    loading: '@/components/PageLoading/index',
  },
  targets: {
    ie: 11,
  },

  // umi routes: https://umijs.org/docs/routing
  routes: [
    {
      path: '/',
      component: '../layouts/BlankLayout',
      routes: [
        {
          path: '/user',
          component: '../layouts/UserLayout',
          routes: [
            { path: '/user', redirect: '/user/login' },
            { path: '/user/login', component: './User/Login' },

            { path: '/user/forgot', component: './User/ForgotPassword' },
            {
              path: '/user/forgot/pass',
              component: './User/ResultForgotPassword/PassForgotPassword',
            },
            {
              path: '/user/reset-password',
              component: './User/ResultForgotPassword/ClickLinkResetPassword',
            },
          ],
        },
        {
          path: '/',
          component: '../layouts/BasicLayout',
          name: 'menu',
          Routes: ['src/pages/Authorized'],
          routes: [
            { path: '/', redirect: '/global-sgg' },
            //sgg
            {
              name: 'SGG',
              path: '/global-sgg',
              component: './sgg',
            },
            {
              authority: ['view_account'],
              name: 'SGG管理',
              path: '/sgg-management/list',
              component: './sgg/list',
            },
            {
              authority: ['view_account'],
              name: 'SGG作成',
              path: '/sgg-management/create',
              component: './sgg/create',
            },
            {
              authority: ['edit_account'],
              name: 'SGG詳細',
              path: '/sgg-management/:id/preview',
              component: './sgg/preview',
            },
            {
              name: 'SGG詳細',
              path: '/global-sgg/:id/preview',
              component: './sgg/preview-global',
            },
            {
              authority: ['edit_account'],
              name: 'SGG編集',
              path: '/sgg-management/:id/edit',
              component: './sgg/edit',
            },
            {
              authority: ['edit_account'],
              name: '画像設定',
              path: '/sgg-management/image-setting',
              component: './sgg/image-setting',
            },

            //itinerary-management
            {
              authority: ['edit_travel'],
              name: '旅程一覧',
              path: '/itinerary-management',
              component: './itinerary-management',
            },
            {
              authority: ['edit_travel'],
              name: '旅程詳細',
              path: '/itinerary-management/:id',
              component: './itinerary-management/[id]',
            },
            {
              name: '旅程表詳細',
              path: '/itinerary-management/:id/create/:plan_id',
              component: './Itinerary-creation',
            },
            {
              name: '見積書作成 ',
              path: '/itinerary-management/:id/estimate/:plan_id',
              component: './Itinerary-estimate-creation',
            },
            {
              authority: ['edit_travel'],
              name: '行程表プレビュー',
              path: '/itinerary-management/:id/preview/:plan_id',
              component: './itinerary-management/preview',
            },
            {
              authority: ['edit_travel'],
              name: '手配状況',
              path: '/itinerary-management/:id/plan/:plan_id/arrangement',
              component: './Itinerary-arrangement',
            },
            {
              authority: ['edit_travel'],
              name: '手配状況詳細',
              path: '/itinerary-management/:id/plan/:plan_id/arrangement/:arrangement_id',
              component: './Itinerary-arrangement/[id]',
            },
            // {
            //   name: 'itinerary-arrangement',
            //   path: '/itinerary-management/:id/arrangement/:plan_id',
            //   component: './Itinerary-arrangement',
            // },
            // {
            //   name: 'itinerary-arrangement-detail',
            //   path: '/itinerary-management/:id/arrangement/:plan_id/detail/:arrangement_id',
            //   component: './Itinerary-arrangement/[id]',
            // },
            //account-management
            {
              authority: ['view_account'],
              name: 'アカウント管理',
              path: '/account-management/list',
              component: './account-management/list',
            },

            //master purchase-and-material-management
            //--tourist-destinations
            {
              name: '観光地一覧',
              path: '/purchase-and-material-management/tourist-destinations',
              component: './purchase-and-material-management/tourist-destinations',
            },
            {
              name: '観光地作成',
              path: '/purchase-and-material-management/tourist-destinations/create',
              component:
                './purchase-and-material-management/tourist-destinations/CreateTouristSpot',
            },
            {
              name: '観光地編集 ',
              path: '/purchase-and-material-management/tourist-destinations/edit/:id',
              component: './purchase-and-material-management/tourist-destinations/EditTouristSpot',
            },
            {
              name: '観光地詳細 ',
              path: '/purchase-and-material-management/tourist-destinations/:id',
              component:
                './purchase-and-material-management/tourist-destinations/TouristSpotDetail',
            },
            {
              name: '観光地料金表',
              path: '/purchase-and-material-management/tourist-destinations/:id/price-list',
              component: './purchase-and-material-management/tourist-destinations/price-list',
            },
            {
              name: '観光地取消料情報',
              path: '/purchase-and-material-management/tourist-destinations/:id/cancellation-fee',
              component: './purchase-and-material-management/tourist-destinations/cancellation-fee',
            },
            {
              name: '観光地資料',
              path: '/purchase-and-material-management/tourist-destinations/:id/materials',
              component: './purchase-and-material-management/tourist-destinations/materials',
            },
            //--hotel
            {
              name: '宿泊施設一覧',
              path: '/purchase-and-material-management/hotel',
              component: './purchase-and-material-management/accommodation',
            },
            {
              name: '宿泊施設追加',
              path: '/purchase-and-material-management/hotel/create',
              component: './purchase-and-material-management/accommodation/create',
            },
            {
              name: '宿泊施設編集',
              path: '/purchase-and-material-management/hotel/edit/:id',
              component: './purchase-and-material-management/accommodation/edit',
            },
            {
              name: '宿泊施設詳細',
              path: '/purchase-and-material-management/hotel/:id',
              component: './purchase-and-material-management/accommodation/detail',
            },
            {
              name: '宿泊施設料金表',
              path: '/purchase-and-material-management/hotel/:id/price-list',
              component: './purchase-and-material-management/accommodation/price-list',
            },
            {
              name: '宿泊施設取消料情報',
              path: '/purchase-and-material-management/hotel/:id/cancellation-fee',
              component: './purchase-and-material-management/accommodation/cancellation-fee',
            },
            {
              name: '宿泊施設資料',
              path: '/purchase-and-material-management/hotel/:id/materials',
              component: './purchase-and-material-management/accommodation/materials',
            },
            //--restaurants
            {
              name: '飲食店一覧',
              path: '/purchase-and-material-management/restaurants',
              component: './purchase-and-material-management/restaurants',
            },
            {
              name: '飲食店追加',
              path: '/purchase-and-material-management/restaurants/create',
              component: './purchase-and-material-management/restaurants/create',
            },
            {
              name: '飲食店編集',
              path: '/purchase-and-material-management/restaurants/edit/:id',
              component: './purchase-and-material-management/restaurants/edit',
            },
            {
              name: '飲食店詳細',
              path: '/purchase-and-material-management/restaurants/:id',
              component: './purchase-and-material-management/restaurants/detail',
            },
            {
              name: '飲食店料金表',
              path: '/purchase-and-material-management/restaurants/:id/price-list',
              component: './purchase-and-material-management/restaurants/price-list',
            },
            {
              name: '飲食店取消料情報',
              path: '/purchase-and-material-management/restaurants/:id/cancellation-fee',
              component: './purchase-and-material-management/restaurants/cancellation-fee',
            },
            {
              name: '飲食店資料',
              path: '/purchase-and-material-management/restaurants/:id/materials',
              component: './purchase-and-material-management/restaurants/materials',
            },
            //--buses
            {
              name: 'バス一覧',
              path: '/purchase-and-material-management/buses',
              component: './purchase-and-material-management/buses',
            },
            {
              name: 'バス追加',
              path: '/purchase-and-material-management/buses/create',
              component: './purchase-and-material-management/buses/create',
            },
            {
              name: 'バス編集',
              path: '/purchase-and-material-management/buses/edit/:id',
              component: './purchase-and-material-management/buses/edit',
            },
            {
              name: 'バス詳細',
              path: '/purchase-and-material-management/buses/:id',
              component: './purchase-and-material-management/buses/detail',
            },
            {
              name: 'バス料金表',
              path: '/purchase-and-material-management/buses/:id/price-list',
              component: './purchase-and-material-management/buses/price-list',
            },
            {
              name: 'バス取消料情報',
              path: '/purchase-and-material-management/buses/:id/cancellation-fee',
              component: './purchase-and-material-management/buses/cancellation-fee',
            },
            {
              name: 'バス資料 ',
              path: '/purchase-and-material-management/buses/:id/materials',
              component: './purchase-and-material-management/buses/materials',
            },
            //--hire-cars
            {
              name: 'ハイヤ一覧',
              path: '/purchase-and-material-management/hire-cars',
              component: './purchase-and-material-management/hire-cars',
            },
            {
              name: 'ハイヤー追加',
              path: '/purchase-and-material-management/hire-cars/create',
              component: './purchase-and-material-management/hire-cars/create',
            },
            {
              name: 'ハイヤー編集',
              path: '/purchase-and-material-management/hire-cars/edit/:id',
              component: './purchase-and-material-management/hire-cars/edit',
            },
            {
              name: 'ハイヤー詳細',
              path: '/purchase-and-material-management/hire-cars/:id',
              component: './purchase-and-material-management/hire-cars/detail',
            },
            {
              name: 'ハイヤー料金表',
              path: '/purchase-and-material-management/hire-cars/:id/price-list',
              component: './purchase-and-material-management/hire-cars/price-list',
            },
            {
              name: 'ハイヤー取消料情報',
              path: '/purchase-and-material-management/hire-cars/:id/cancellation-fee',
              component: './purchase-and-material-management/hire-cars/cancellation-fee',
            },
            {
              name: 'ハイヤー資料',
              path: '/purchase-and-material-management/hire-cars/:id/materials',
              component: './purchase-and-material-management/hire-cars/materials',
            },
            //--railways
            {
              name: '鉄道一覧',
              path: '/purchase-and-material-management/railways',
              component: './purchase-and-material-management/railways',
            },
            {
              name: '鉄道追加',
              path: '/purchase-and-material-management/railways/create',
              component: './purchase-and-material-management/railways/create',
            },
            {
              name: '鉄道編集',
              path: '/purchase-and-material-management/railways/edit/:id',
              component: './purchase-and-material-management/railways/edit',
            },
            {
              name: '鉄道詳細',
              path: '/purchase-and-material-management/railways/:id',
              component: './purchase-and-material-management/railways/detail',
            },
            {
              name: '鉄道料金表',
              path: '/purchase-and-material-management/railways/:id/price-list',
              component: './purchase-and-material-management/railways/price-list',
            },
            {
              name: '鉄道取消料情報',
              path: '/purchase-and-material-management/railways/:id/cancellation-fee',
              component: './purchase-and-material-management/railways/cancellation-fee',
            },
            {
              name: '鉄道資料 ',
              path: '/purchase-and-material-management/railways/:id/materials',
              component: './purchase-and-material-management/railways/materials',
            },
            //--airlines
            {
              name: '航空券一覧',
              path: '/purchase-and-material-management/airlines',
              component: './purchase-and-material-management/airlines',
            },
            {
              name: '航空追加',
              path: '/purchase-and-material-management/airlines/create',
              component: './purchase-and-material-management/airlines/create',
            },
            {
              name: '航空編集',
              path: '/purchase-and-material-management/airlines/edit/:id',
              component: './purchase-and-material-management/airlines/edit',
            },
            {
              name: '航空詳細',
              path: '/purchase-and-material-management/airlines/:id',
              component: './purchase-and-material-management/airlines/detail',
            },
            {
              name: '航空券料金表',
              path: '/purchase-and-material-management/airlines/:id/price-list',
              component: './purchase-and-material-management/airlines/price-list',
            },
            {
              name: '航空券取消料情報',
              path: '/purchase-and-material-management/airlines/:id/cancellation-fee',
              component: './purchase-and-material-management/airlines/cancellation-fee',
            },
            {
              name: '航空券資料',
              path: '/purchase-and-material-management/airlines/:id/materials',
              component: './purchase-and-material-management/airlines/materials',
            },
            //--ships
            {
              name: '船一覧',
              path: '/purchase-and-material-management/ships',
              component: './purchase-and-material-management/ships',
            },
            {
              name: '船追加',
              path: '/purchase-and-material-management/ships/create',
              component: './purchase-and-material-management/ships/create',
            },
            {
              name: '船編集',
              path: '/purchase-and-material-management/ships/edit/:id',
              component: './purchase-and-material-management/ships/edit',
            },
            {
              name: '船詳細',
              path: '/purchase-and-material-management/ships/:id',
              component: './purchase-and-material-management/ships/detail',
            },
            {
              name: '船料金表',
              path: '/purchase-and-material-management/ships/:id/price-list',
              component: './purchase-and-material-management/ships/price-list',
            },
            {
              name: '船取消料情報',
              path: '/purchase-and-material-management/ships/:id/cancellation-fee',
              component: './purchase-and-material-management/ships/cancellation-fee',
            },
            {
              name: '船資料',
              path: '/purchase-and-material-management/ships/:id/materials',
              component: './purchase-and-material-management/ships/materials',
            },
            //--delivery
            {
              name: '宅配一覧',
              path: '/purchase-and-material-management/delivery',
              component: './purchase-and-material-management/delivery',
            },
            {
              name: '宅配追加',
              path: '/purchase-and-material-management/delivery/create',
              component: './purchase-and-material-management/delivery/create',
            },
            {
              name: '宅配編集',
              path: '/purchase-and-material-management/delivery/edit/:id',
              component: './purchase-and-material-management/delivery/edit',
            },
            {
              name: '宅配詳細',
              path: '/purchase-and-material-management/delivery/:id',
              component: './purchase-and-material-management/delivery/detail',
            },
            {
              name: '宅配料金表',
              path: '/purchase-and-material-management/delivery/:id/price-list',
              component: './purchase-and-material-management/delivery/price-list',
            },
            {
              name: '宅配取消料情報',
              path: '/purchase-and-material-management/delivery/:id/cancellation-fee',
              component: './purchase-and-material-management/delivery/cancellation-fee',
            },
            {
              name: '宅配資料',
              path: '/purchase-and-material-management/delivery/:id/materials',
              component: './purchase-and-material-management/delivery/materials',
            },
            //--guides
            {
              name: 'ガイド一覧',
              path: '/purchase-and-material-management/guides',
              component: './purchase-and-material-management/guides',
            },
            {
              name: 'ガイド追加',
              path: '/purchase-and-material-management/guides/create',
              component: './purchase-and-material-management/guides/create',
            },
            {
              name: 'ガイド編集',
              path: '/purchase-and-material-management/guides/edit/:id',
              component: './purchase-and-material-management/guides/edit',
            },
            {
              name: 'ガイド詳細',
              path: '/purchase-and-material-management/guides/:id',
              component: './purchase-and-material-management/guides/detail',
            },
            {
              name: 'ガイド料金表',
              path: '/purchase-and-material-management/guides/:id/price-list',
              component: './purchase-and-material-management/guides/price-list',
            },
            {
              name: 'ガイド取消料情報',
              path: '/purchase-and-material-management/guides/:id/cancellation-fee',
              component: './purchase-and-material-management/guides/cancellation-fee',
            },
            {
              name: 'ガイド資料',
              path: '/purchase-and-material-management/guides/:id/materials',
              component: './purchase-and-material-management/guides/materials',
            },
            //--other
            {
              name: 'その他一覧',
              path: '/purchase-and-material-management/other',
              component: './purchase-and-material-management/other',
            },
            {
              name: 'その他追加',
              path: '/purchase-and-material-management/other/create',
              component: './purchase-and-material-management/other/create',
            },
            {
              name: 'その他編集',
              path: '/purchase-and-material-management/other/edit/:id',
              component: './purchase-and-material-management/other/edit',
            },
            {
              name: 'その他詳細',
              path: '/purchase-and-material-management/other/:id',
              component: './purchase-and-material-management/other/detail',
            },
            {
              name: 'その他料金表',
              path: '/purchase-and-material-management/other/:id/price-list',
              component: './purchase-and-material-management/other/price-list',
            },
            {
              name: 'その他取消料情報',
              path: '/purchase-and-material-management/other/:id/cancellation-fee',
              component: './purchase-and-material-management/other/cancellation-fee',
            },
            {
              name: 'その他資料 ',
              path: '/purchase-and-material-management/other/:id/materials',
              component: './purchase-and-material-management/other/materials',
            },
            //master Tax
            {
              authority: ['view_master_data'],
              name: '税区分マスター',
              path: '/master-management/tax-category-master',
              component: './master-management/tax-category-master',
            },
            {
              authority: ['view_master_data'],
              name: '消費税マスター',
              path: '/master-management/consumption-tax-master',
              component: './master-management/consumption-tax-master',
            },
            {
              authority: ['view_master_data'],
              name: '集計科目マスター',
              path: '/master-management/aggregation-item-master',
              component: './master-management/aggregation-item-master',
            },
            {
              authority: ['view_master_data'],
              name: '科目マスター',
              path: '/master-management/item-master',
              component: './master-management/item-master',
            },
            {
              authority: ['view_master_data'],
              name: '勘定科目マスター',
              path: '/master-management/accounting-subject-master',
              component: './master-management/accounting-subject-master',
            },
            {
              authority: ['view_master_data'],
              name: '金種マスター',
              path: '/master-management/currency-type-master',
              component: './master-management/currency-type-master',
            },
            {
              authority: ['view_master_data'],
              name: '旅行種別マスター',
              path: '/master-management/travel-type-master',
              component: './master-management/travel-type-master',
            },
            {
              authority: ['view_master_data'],
              name: '自社マスター',
              path: '/master-management/company-master',
              component: './master-management/company/create',
            },

            //accounting-management
            //--travel-management
            {
              authority: ['view_travel'],
              name: '旅行管理',
              path: '/accounting-management/travel-management',
              component: './accounting-management/travel-management',
            },
            {
              authority: ['edit_travel'],
              name: '旅行詳細',
              path: '/accounting-management/travel-management/:id',
              component: './accounting-management/travel-management/[id]',
            },
            //--sales-management
            {
              authority: ['view_sale_slip'],
              name: '売上管理',
              path: '/accounting-management/sales-management',
              component: './accounting-management/sales-management',
            },
            //--purchase-management
            {
              authority: ['view_purchase_slip'],
              name: '仕入管理',
              path: '/accounting-management/purchase-management',
              component: './accounting-management/purchase-management',
            },
            {
              authority: ['create_purchase_slip'],
              name: '仕入伝票作成',
              path: '/accounting-management/purchase-management/invoices',
              component: './accounting-management/purchase-management/invoices',
            },
            {
              authority: ['create_purchase_slip'],
              name: '複数仕入伝票',
              path: '/accounting-management/purchase-management/bulk-create/invoices',
              component: './accounting-management/purchase-management/invoices/bulk-create',
            },
            {
              authority: ['edit_purchase_slip'],
              name: '仕入伝票編集',
              path: '/accounting-management/purchase-management/invoices/:id',
              component: './accounting-management/purchase-management/invoices/[id]',
            },
            //--payment-management
            {
              authority: ['view_payment_slip'],
              name: '支払管理',
              path: '/accounting-management/payment-management',
              component: './accounting-management/payment-management',
            },
            {
              authority: ['create_payment_slip'],
              name: '支払伝票作成',
              path: '/accounting-management/payment-management/creation',
              component: './accounting-management/payment-management/invoices',
            },
            {
              authority: ['edit_payment_slip'],
              name: '支払伝票編集',
              path: '/accounting-management/payment-management/edit/:id',
              component: './accounting-management/payment-management/invoices',
            },
            //--sales-management
            {
              authority: ['create_sale_slip'],
              name: '売上伝票作成',
              path: '/accounting-management/sales-management/invoices',
              component: './accounting-management/sales-management/invoices',
            },
            {
              authority: ['edit_purchase_slip'],
              name: '売上伝票編集',
              path: '/accounting-management/sales-management/invoices/:id',
              component: './accounting-management/sales-management/invoices',
            },
            //--deposit-management
            {
              authority: ['view_deposit_slip'],
              name: '入金管理',
              path: '/accounting-management/deposit-management',
              component: './accounting-management/deposit-management',
            },
            {
              authority: ['create_purchase_slip'],
              name: '入金伝票作成',
              path: '/accounting-management/deposit-management/creation',
              component: './accounting-management/deposit-management/invoices',
            },
            {
              authority: ['edit_purchase_slip'],
              name: '入金伝票編集',
              path: '/accounting-management/deposit-management/edit/:id',
              component: './accounting-management/deposit-management/invoices',
            },
            //--invoice-issuance
            {
              authority: ['view_invoice'],
              name: '請求書一覧',
              path: '/accounting-management/invoice-issuance',
              component: './accounting-management/invoice-issuance',
            },
            {
              authority: ['create_invoice'],
              name: '請求書作成',
              path: '/accounting-management/invoice-issuance/creation',
              component: './accounting-management/invoice-issuance/invoices',
            },
            {
              authority: ['edit_invoice'],
              name: '請求書作成',
              path: '/accounting-management/invoice-issuance/edit/:id',
              component: './accounting-management/invoice-issuance/invoices',
            },

            //accounting-management report-output

            {
              name: '売上収支一覧表',
              path: '/accounting-management/report-output/sales-balance-list',
              component: './accounting-management/report-output/sales-balance-list',
            },
            {
              name: '月次売上収支一覧表',
              path: '/accounting-management/report-output/monthly-sales-balance-list',
              component: './accounting-management/report-output/monthly-sales-balance-list',
            },
            {
              name: '年次売上帳票',
              path: '/accounting-management/report-output/annual-sales-report',
              component: './accounting-management/report-output/annual-sales-report',
            },
            {
              name: '仕入一覧表',
              path: '/accounting-management/report-output/purchase-list',
              component: './accounting-management/report-output/purchase-list',
            },
            {
              name: '入金一覧表',
              path: '/accounting-management/report-output/deposit-list',
              component: './accounting-management/report-output/deposit',
            },

            {
              name: '支払一覧表',
              path: '/accounting-management/report-output/payment-list',
              component: './accounting-management/report-output/payment',
            },

            // Business partner
            {
              authority: ['view_master_data'],
              name: '取引先マスター',
              path: '/master-management/business-partner-master',
              component: './master-management/business-partner',
            },
            {
              authority: ['create_master_data'],
              name: '取引先マスター',
              path: '/master-management/business-partner-master-create',
              component: './master-management/business-partner/create',
            },
            {
              authority: ['edit_master_data'],
              name: '取引先マスター',
              path: '/master-management/business-partner-master-edit/:id',
              component: './master-management/business-partner/edit',
            },
            {
              authority: ['view_master_data'],
              name: '取引先マスター',
              path: '/master-management/business-partner-master-detail/:id',
              component: './master-management/business-partner/detail',
            },

            //
            {
              name: 'パスワードの再設定',
              path: '/change-password',
              component: './User/ChangePassword',
            },
            {
              name: 'account-management',
              path: '/account-management',
              component: './account-management',
            },
            //
            {
              component: '404',
            },
          ],
        },
      ],
    },
  ],
  // Theme for antd: https://ant.design/docs/react/customize-theme-cn
  theme: {
    'primary-color': defaultSettings.primaryColor,
    'secondary-color': defaultSettings.secondaryColor,
    'menu-dark-bg': '#fff',

    'body-background': '#F2F2F2',
    'layout-sider-background': '#fff',
    // 'nav-header-height': '80',
    'layout-header-height': '50px !important',
    'layout-header-background': '#FFF',
    'layout-body-background': '#F2F2F2',
    'default-font': defaultSettings.defaultFont,
    'font-family': defaultSettings.defaultFont,
    'text-color': 'rgba(0, 0, 0, 0.85)',
    'font-size-base': '14px',
    'border-color': 'rgba(204, 204, 204, 1)',
    'font-weight': '700',
    'tertiary-color': defaultSettings.tertiaryColor,
    'alert-color': '#F6EBEB',

    // 'layout-sider-background': '#222222',
    // 'layout-trigger-height': '80px',
  },

  fixedHeader: false,
  title: false,
  ignoreMomentLocale: true,
  proxy: proxy[REACT_APP_ENV || 'dev'],
  manifest: {
    basePath: '/',
  },
  esbuild: {},
  define: {
    'process.env': {
      API_URL: API_URL,
      FILE_URL,
      API_CHATWOOT_URL: API_CHATWOOT_URL,
      API_CHATWOOT_SOCKET: API_CHATWOOT_SOCKET,
      NODE_ENV: NODE_ENV,
      EMAIL_SUPER_ADMIN: EMAIL_SUPER_ADMIN,
      GG_MAP_KEY: GG_MAP_KEY,
      IS_SUPER_ADMIN_TEMPLATE: IS_SUPER_ADMIN_TEMPLATE,
      API_ACCESS_TOKEN_CHATWOOT: API_ACCESS_TOKEN_CHATWOOT,
      SERVICE_NAME,
      APM_SERVER_URL,
      ENVIRONMENT,
      APM_API_BASE_URL,
      MUL_PAY_SCRIPT_URL,
    },
  },
  chainWebpack(config, { webpack }) {
    config.module
      .rule('mjs$')
      .test(/\.mjs$/)
      .include.add(/node_modules/)
      .end()
      .type('javascript/auto');
  },
};
