# This file was auto-generated by the Firebase CLI
# https://github.com/firebase/firebase-tools

name: Deploy to EC2 staging server
'on':
  push:
    branches:
      - staging
jobs:
  build_and_deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup node
        uses: actions/setup-node@v2
        with:
          node-version: 16
      - run: cp .env.staging.example .env && npm install --legacy-peer-deps && npm run build
      - name: Deploy staging server in EC2
        env:
          SECRET_API: ${{ secrets.SSH_PRIVATE_KEY }}
          SERVER_USER: ${{ secrets.SSH_USER }}
          SERVER_IP: ${{ secrets.SSH_PRIVATE_IP }}
          SERVER_DIR: ${{ secrets.REMOTE_PROJECT_DIR }}
        run: |
          echo start
          eval $(ssh-agent -s)
          bash -c 'ssh-add <(echo "$SECRET_API")'
          mkdir -p ~/.ssh
          ssh-keyscan -H $SERVER_IP >> ~/.ssh/known_hosts
          chmod 644 ~/.ssh/known_hosts
          echo 'ssh server'
          ssh -T $SERVER_USER@$SERVER_IP "pwd; cd $SERVER_DIR; git pull;"
          rsync -a -e "ssh" node_modules/ $SERVER_USER@$SERVER_IP:$SERVER_DIR/node_modules
          rsync -a -e "ssh" dist/ $SERVER_USER@$SERVER_IP:$SERVER_DIR/dist
          rsync -a -e "ssh" .env $SERVER_USER@$SERVER_IP:$SERVER_DIR/.env
          ssh -T $SERVER_USER@$SERVER_IP "pwd; source ~/.nvm/nvm.sh; pm2 restart admin-leisure"
