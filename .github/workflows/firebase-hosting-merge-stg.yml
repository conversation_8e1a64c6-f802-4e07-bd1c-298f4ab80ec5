# # This file was auto-generated by the Firebase CLI
# # https://github.com/firebase/firebase-tools

# name: Deploy to Firebase Hosting on merge (STG)
# 'on':
#   push:
#     branches:
#       - staging
# jobs:
#   build_and_deploy:
#     runs-on: ubuntu-latest
#     steps:
#       - uses: actions/checkout@v3
#       - name: Setup node
#         uses: actions/setup-node@v3
#         with:
#           node-version: 14
#       - run: cp .env.staging.example .env && npm install --legacy-peer-deps && npm run build
#       - uses: FirebaseExtended/action-hosting-deploy@v0
#         with:
#           repoToken: '${{ secrets.ACCESS_TOKEN }}'
#           firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_STAGING_LEISURE_ADMIN }}'
#           channelId: live
#           projectId: staging-leisure-admin
#           firebaseToolsVersion: v11.29.0
