{"compilerOptions": {"lib": ["dom", "dom.iterable", "esnext"], "baseUrl": ".", "paths": {"@/*": ["src/*"], "@@/*": ["src/.umi/*"]}, "strictNullChecks": false, "noImplicitAny": false, "downlevelIteration": true, "target": "ES2020", "allowJs": true, "allowSyntheticDefaultImports": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "resolveJsonModule": true, "noEmit": true, "jsx": "react-jsx", "composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true}, "exclude": ["**/node_modules", "**/.*/"], "include": ["next-env.d.ts", "./src/**/*.ts", "./src/**/*.tsx", "src", "styled.d.ts", "src/custom.d.ts"]}