# npm resolution error report

2022-01-23T14:25:41.096Z

While resolving: ant-design-pro@4.5.0
Found: @types/react@17.0.38
node_modules/@types/react
  dev @types/react@"^17.0.0" from the root project

Could not resolve dependency:
peer @types/react@"^15.0.0 || ^16.0.0" from react-google-maps@9.4.5
node_modules/react-google-maps
  react-google-maps@"^9.4.5" from the root project

Fix the upstream dependency conflict, or retry
this command with --force, or --legacy-peer-deps
to accept an incorrect (and potentially broken) dependency resolution.

Raw JSON explanation object:

{
  "code": "ERESOLVE",
  "current": {
    "name": "@types/react",
    "version": "17.0.38",
    "whileInstalling": {
      "name": "ant-design-pro",
      "version": "4.5.0",
      "path": "C:\\Users\\<USER>\\Desktop\\Super_Hankyu\\hanyu"
    },
    "location": "node_modules/@types/react",
    "isWorkspace": false,
    "dependents": [
      {
        "type": "dev",
        "name": "@types/react",
        "spec": "^17.0.0",
        "from": {
          "location": "C:\\Users\\<USER>\\Desktop\\Super_Hankyu\\hanyu"
        }
      }
    ]
  },
  "currentEdge": {
    "type": "dev",
    "name": "@types/react",
    "spec": "^17.0.0",
    "from": {
      "location": "C:\\Users\\<USER>\\Desktop\\Super_Hankyu\\hanyu"
    }
  },
  "edge": {
    "type": "peer",
    "name": "@types/react",
    "spec": "^15.0.0 || ^16.0.0",
    "error": "INVALID",
    "from": {
      "name": "react-google-maps",
      "version": "9.4.5",
      "whileInstalling": {
        "name": "ant-design-pro",
        "version": "4.5.0",
        "path": "C:\\Users\\<USER>\\Desktop\\Super_Hankyu\\hanyu"
      },
      "location": "node_modules/react-google-maps",
      "isWorkspace": false,
      "dependents": [
        {
          "type": "prod",
          "name": "react-google-maps",
          "spec": "^9.4.5",
          "from": {
            "location": "C:\\Users\\<USER>\\Desktop\\Super_Hankyu\\hanyu"
          }
        }
      ]
    }
  },
  "strictPeerDeps": false,
  "force": false
}
