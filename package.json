{"name": "ant-design-pro", "version": "4.5.0", "private": true, "description": "An out-of-box UI solution for enterprise applications", "scripts": {"analyze": "cross-env ANALYZE=1 umi build", "build": "umi build --openssl-legacy-provider", "deploy": "npm run build && npm run gh-pages", "dev": "npm run start:dev", "dev:tailwind": "tailwindcss -i src/styles/tailwind.css -o src/styles/index.css -w", "fetch:blocks": "pro fetch-blocks && npm run prettier", "gh-pages": "gh-pages -d dist", "i18n-remove": "pro i18n-remove --locale=zh-CN --write", "postinstall": "umi g tmp", "lint": "umi g tmp && npm run lint:js && npm run lint:style && npm run lint:prettier", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint:js": "eslint --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src", "lint:prettier": "prettier --check \"src/**/*\" --end-of-line auto", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "prettier": "prettier -c --write \"src/**/*\"", "start": " cross-env UMI_ENV=dev NODE_OPTIONS=--openssl-legacy-provider umi dev", "start:dev": "cross-env REACT_APP_ENV=dev MOCK=none UMI_ENV=dev NODE_OPTIONS=--openssl-legacy-provider umi dev", "start:no-mock": "cross-env MOCK=none UMI_ENV=dev NODE_OPTIONS=--openssl-legacy-provider umi dev", "start:no-ui": "cross-env UMI_UI=none UMI_ENV=dev NODE_OPTIONS=--openssl-legacy-provider umi dev", "start:pre": "cross-env REACT_APP_ENV=pre UMI_ENV=dev NODE_OPTIONS=--openssl-legacy-provider umi dev", "start:test": "cross-env REACT_APP_ENV=test MOCK=none UMI_ENV=dev NODE_OPTIONS=--openssl-legacy-provider umi dev", "pretest": "node ./tests/beforeTest", "test": "umi test", "test:all": "node ./tests/run-tests.js", "test:component": "umi test ./src/components", "tsc": "tsc --noEmit", "test-deploy": "git checkout cr/change-hotel-ui-plan && npm run build && firebase use test-leisure-admin && firebase deploy --only hosting", "staging-deploy": "git checkout staging && git pull && npm run build && firebase use staging-leisure-admin && firebase deploy --only hosting", "prepare": "husky"}, "lint-staged": {"**/*.less": "stylelint --syntax less", "**/*.{js,jsx,ts,tsx}": "npm run lint-staged:js", "**/*.{js,jsx,tsx,ts,less,md,json}": ["prettier --write"]}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 10"], "dependencies": {"@ant-design/icons": "^4.0.0", "@ant-design/pro-card": "^1.17.1", "@ant-design/pro-descriptions": "^1.2.0", "@ant-design/pro-form": "^1.3.0", "@ant-design/pro-layout": "^6.9.0", "@ant-design/pro-table": "^2.17.0", "@antv/data-set": "^0.11.0", "@antv/l7": "^2.1.9", "@antv/l7-react": "^2.1.9", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@elastic/apm-rum": "^5.15.0", "@reduxjs/toolkit": "^2.2.6", "@types/lodash.debounce": "^4.0.6", "@types/lodash.isequal": "^4.5.5", "@uidotdev/usehooks": "^2.4.1", "@umijs/hooks": "^1.9.3", "@umijs/route-utils": "^1.0.37", "antd": "^4.12.0", "antd-img-crop": "^4.1.0", "antd-table-infinity": "^1.1.6", "array-move": "^4.0.0", "axios": "^0.22.0", "classnames": "^2.2.6", "dva": "^2.4.0", "file-loader": "^6.2.0", "flat": "^5.0.2", "html-to-pdfmake": "^2.5.17", "html2canvas": "^1.4.1", "js-file-download": "^0.4.12", "lodash": "^4.17.11", "lodash-decorators": "^6.0.0", "lodash.debounce": "^4.0.8", "lodash.isequal": "^4.5.0", "moment": "^2.29.4", "nzh": "^1.0.3", "pdfmake": "^0.2.16", "prop-types": "^15.5.10", "qs": "^6.10.1", "quill-autoformat": "^0.1.2", "quill-image-resize-module-react": "^3.0.0", "react": "^18.3.1", "react-cookies": "^0.1.1", "react-copy-to-clipboard": "^5.0.4", "react-csv": "^2.2.2", "react-dev-inspector": "^1.1.1", "react-dom": "^18.3.1", "react-helmet-async": "^1.0.4", "react-icomoon": "^2.2.4", "react-icons": "^4.2.0", "react-iframe": "^1.8.5", "react-infinite-scroll-component": "^6.1.0", "react-intersection-observer": "^9.4.3", "react-loading-skeleton": "^3.1.0", "react-quill": "^2.0.0", "react-redux": "^9.1.2", "react-router": "^4.3.1", "react-router-config": "^5.1.1", "react-router-dom": "^6.3.0", "react-smooth-dnd": "^0.11.1", "react-virtuoso": "^4.0.3", "react-window": "^1.8.6", "redux-persist": "^6.0.0", "ts-curry": "^1.0.4", "umi": "^3.2.14", "umi-request": "^1.0.8", "use-url-search-params": "^2.3.13", "virtuallist-antd": "^0.5.8", "zeed": "^0.7.156"}, "devDependencies": {"@ant-design/pro-cli": "^1.0.28", "@babel/plugin-proposal-decorators": "^7.16.0", "@commitlint/cli": "^19.3.0", "@commitlint/config-conventional": "^19.2.2", "@types/classnames": "^2.2.7", "@types/express": "^4.17.0", "@types/history": "^4.7.2", "@types/jest": "^26.0.0", "@types/lodash": "^4.14.144", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-helmet": "^6.1.0", "@umijs/fabric": "^2.5.1", "@umijs/plugin-blocks": "^2.0.5", "@umijs/plugin-esbuild": "^1.0.1", "@umijs/preset-ant-design-pro": "^1.2.0", "@umijs/preset-react": "^1.4.8", "@umijs/yorkie": "^2.0.3", "autoprefixer": "^10.4.19", "carlo": "^0.9.46", "chalk": "^4.0.0", "cross-env": "^7.0.0", "cross-port-killer": "^1.1.1", "detect-installer": "^1.0.1", "enzyme": "^3.11.0", "eslint": "^7.1.0", "express": "^4.17.1", "gh-pages": "^3.0.0", "husky": "^9", "jsdom-global": "^3.0.2", "lint-staged": "^13", "mockjs": "^1.0.1-beta3", "postcss": "^8.4.38", "prettier": "^2", "puppeteer-core": "^7.0.1", "stylelint": "^13.0.0", "tailwindcss": "^3.4.4", "typescript": "^5", "umi-plugin-env": "^0.0.2"}, "engines": {"node": ">=10.0.0"}, "checkFiles": ["src/**/*.js*", "src/**/*.ts*", "src/**/*.less", "config/**/*.js*", "scripts/**/*.js"]}