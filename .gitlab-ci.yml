stages:
  - install_dependencies
  - build

.install_dependencies:
  image: node:16-alpine
  stage: install_dependencies
  cache:
    key: $CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
    policy: push
  script:
    - apk update && apk add npm git
    - npm install --legacy-peer-deps
  only:
    refs:
      - staging
    changes:
      # - .gitlab-ci.yml
      - package.json

.build:
  stage: build
  image: node:16-alpine3.11
  cache:
    key: $CI_COMMIT_REF_SLUG
    paths:
      - node_modules/
    policy: pull
  variables:
    GIT_STRATEGY: fetch
  before_script:
    - apk update && apk add openssh-client bash rsync git
    - apk --no-cache --virtual build-dependencies add python3 make g++
    - eval $(ssh-agent -s)
    - bash -c 'ssh-add <(echo "$SSH_PRIVATE_KEY")'
    - mkdir -p ~/.ssh
    - ssh-keyscan -p $SSH_PORT -H $SSH_SERVER_IP >> ~/.ssh/known_hosts
    - chmod 644 ~/.ssh/known_hosts

  script:
    - rm -f .env
    - cp .env.$CI_ENVIRONMENT_NAME.example .env
    - npm run build
    - rsync -a -e "ssh -p $SSH_PORT" dist/ $SSH_USER@$SSH_SERVER_IP:$REMOTE_PROJECT_DIR/dist
    - rsync -a -e "ssh -p $SSH_PORT" .env $SSH_USER@$SSH_SERVER_IP:$REMOTE_PROJECT_DIR/.env

    - ssh -T -oStrictHostKeyChecking=no -p $SSH_PORT $SSH_USER@$SSH_SERVER_IP "pwd;source ~/.nvm/nvm.sh; cd $REMOTE_PROJECT_DIR; git pull;"

install_dependencies_develop:
  extends: .install_dependencies
  only:
    refs:
      - develop
  environment:
    name: develop

install_dependencies_staging:
  extends: .install_dependencies
  only:
    refs:
      - staging
  environment:
    name: staging

build_develop:
  extends: .build
  only:
    refs:
      - develop
  environment:
    name: develop

build_staging:
  extends: .build
  only:
    refs:
      - staging
  environment:
    name: staging
