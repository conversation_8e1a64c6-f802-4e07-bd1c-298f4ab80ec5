---
name: User Story Template
about: Template for basic user story
---

### 0. Tell the developer which branch to start from

- [ ] Tạo branch, merge request từ nhánh : ????
<!-- Tell the developer which branch to start from -->

### 1. Goal:

As a <USER PERSONA> I want <FEATURE> so that I can <PURPOSE of FEATURE>  
i.e.

As DevOps I want Health liveness Probe on the microservice so that the health of the service can be monitored.

### 2. Features and implementations\*\* (List all of relating features and implementations here)

- [ ] [LIST ITEMS]  
      i.e.

### 3. Acceptance Criteria:\*\*

- [ ] Negative use-cases for Unit Tests in the "common streaming library"
- [ ] Integration tests in the "common streaming library"
- [ ] [LIST ITEMS]  
      i.e.

/label ~UserStory
