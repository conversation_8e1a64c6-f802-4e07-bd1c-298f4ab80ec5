### 0. Tell the developer which branch to start from

- [ ] Tạo branch, merge request từ nhánh : ????
<!-- Tell the developer which branch to start from -->

## 1. Summary (Tổng quan)

<!--
Please briefly describe what part of the code base needs to be refactored.
-->

## 2. Improvements (<PERSON><PERSON><PERSON><PERSON> cải thiện sau khi refactor)

<!--
Explain the benefits of refactoring this code.
See also https://about.gitlab.com/handbook/values/index.html#say-why-not-just-what
-->

- [ ] Improve 1
- [ ] Improve 2

## 3. Risks (rủi ro cần đảm bảo là không x<PERSON>y ra khi refactor)

<!--
Please list features that can break because of this refactoring and how you intend to solve that.
-->

- [ ] Risk 1
- [ ] Risk 2

## 4. Involved components (Những phần chương trình bị ảnh hưởng)

<!--
List files or directories that will be changed by the refactoring.
-->

- [ ] Component 1
- [ ] Component 2

## 5. Optional: Intended side effects (<PERSON>ỳ chọn : những ảnh hưởng gây ra có thể lường trước)

<!--
If the refactoring involves changes apart from the main improvements (such as a better UI), list them here.
It may be a good idea to create separate issues and link them here.
-->

- [ ] Side effect 1
- [ ] Side effect 2

## 6. Optional: Missing test coverage (Tuỳ chọn : thiếu những test cần bổ sung)

<!--
If you are aware of tests that need to be written or adjusted apart from unit tests for the changed components,
please list them here.
-->

- [ ] Testcase 1
- [ ] Testcase 2
- [ ] Development test 1
- [ ] Development test 2

/label ~"Maintenance::Refactor"
